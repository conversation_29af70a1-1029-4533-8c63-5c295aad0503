import Helper from '@codeceptjs/helper';

class <PERSON><PERSON><PERSON>el<PERSON> extends Helper {
  /**
   * Scroll to element
   * @param selector XPath selector
   */
  async scrollToElement(selector: string): Promise<void> {
    await this.helpers['Appium'].executeScript(function(sel) {
      const element = document.evaluate(
        sel, 
        document, 
        null, 
        XPathResult.FIRST_ORDERED_NODE_TYPE, 
        null
      ).singleNodeValue;
      
      if (element) {
        (element as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, selector);
    
    await this.helpers['Appium'].wait(1);
  }

  /**
   * Scroll to element and click using clickFixed
   * @param selector XPath selector
   */
  async scrollAndClick(selector: string): Promise<void> {
    await this.scrollToElement(selector);
    
    // Wait for the element to be clickable
    await this.helpers['Appium'].wait(1);
    
    // Call clickFixed through container
    const container = require('codeceptjs').container;
    const appiumSupport = container.helpers('AppiumSupportHelper');
    if (appiumSupport && appiumSupport.clickFixed) {
      await appiumSupport.clickFixed(selector);
      await this.helpers['Appium'].wait(1);
    } else {
      // Fallback if clickFixed is not found
      console.log('AppiumSupportHelper.clickFixed not found, using regular click');
      await this.helpers['AppiumSupportHelper'].clickFixed(selector);
    }
  }

  /**
   * Scroll to element and fill
   * @param selector XPath selector
   * @param value Value to fill
   */
  async scrollAndFill(selector: any, value: string): Promise<void> {
    await this.scrollToElement(selector);
    await this.helpers['Appium'].wait(1);
    await this.helpers['Appium'].fillField(selector, value);
  }
}

export = ScrollHelper;