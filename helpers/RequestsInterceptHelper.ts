import Helper from '@codeceptjs/helper';
import { CompletedRequest, GetRequestOptions, InterceptedRequest, OnlyCompletedRequests } from 'wdio-intercept-service';

// TODO: delete
// wdio-intercept-service package is not available as a plugin for codeceptjs, so import it directly.
// using wdio-intercept-service as a plugin causes a configuration error in codeceptjs.
// see. https://github.com/codeceptjs/CodeceptJS/issues/1747
class RequestsInterceptHelper extends Helper {
    interceptServiceLauncher: any;

    constructor(config: CodeceptJS.MainConfig) {
        console.debug('RequestsInterceptHelper: constructor');
        super(config);
        // import WebdriverAjax class directly from the index.js file
        const WebdriverAjax = require('wdio-intercept-service/index.js').default;
        this.interceptServiceLauncher = new WebdriverAjax();
    }

    /**
     * Hook executed before each test.
     */
    protected _before(): void {
        console.debug('RequestsInterceptHelper: _before');
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        this.interceptServiceLauncher.before(null, null, browser);
        this.interceptServiceLauncher.beforeTest();
    }

    /**
     * Setup interceptor
     * @returns interceptor
     */
    async setupInterceptor(): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        await browser.setupInterceptor();
    }

    /**
     * Get request
     * @param index
     * @param [options]
     * @returns request
     */
    async getRequest(
        index: number,
        options?: (GetRequestOptions & OnlyCompletedRequests) | GetRequestOptions,
    ): Promise<CompletedRequest | InterceptedRequest> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.getRequest(index, options);
    }

    /**
     * Gets requests
     * @param [options]
     * @returns requests
     */
    async getRequests(
        options?: (GetRequestOptions & OnlyCompletedRequests) | GetRequestOptions,
    ): Promise<CompletedRequest[] | InterceptedRequest[]> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.getRequests(options);
    }
}

export = RequestsInterceptHelper;
