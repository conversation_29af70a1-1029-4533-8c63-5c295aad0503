import Helper from '@codeceptjs/helper';

class NavigationHelper extends Helper {
  /**
   * Click back button to return to previous screen
   */
  async backToPreviousScreen(isPdf = false): Promise<void> {
    const backButton = isPdf ? '//*[@data-testid="common_rightSlide_close_id"]' : '//*[@data-testid="common_back_id"]';
    await this.helpers['Appium'].waitForElement(backButton, 2);
    
    // Use clickFixed if available
    const container = require('codeceptjs').container;
    const appiumSupport = container.helpers('AppiumSupportHelper');
    if (appiumSupport && appiumSupport.clickFixed) {
      await appiumSupport.clickFixed(backButton);
    } else {
      await this.helpers['Appium'].click(backButton);
    }
    
    await this.helpers['Appium'].wait(1);
  }
}

export = NavigationHelper;