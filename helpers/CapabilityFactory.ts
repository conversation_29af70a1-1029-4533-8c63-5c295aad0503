import appRootPath from 'app-root-path';

export class CapabilityFactory {
    static createAndroidCapabilities() {
        return {
            platformVersion: process.env.ANDROID_PLATFORM_VERSION || '16.0',
            automationName: 'UiAutomator2',
            deviceName: process.env.ANDROID_DEVICE_NAME || 'Android Emulator',
            udid: process.env.ANDROID_UDID || 'emulator-5554',
            noReset: true,
            app: appRootPath.resolve(process.env.ANDROID_APP_PATH || '/apps/android/app-isOpe.apk'),
            appPackage: process.env.ANDROID_APP_PACKAGE || 'inc.guide.kabuappNext.dev',
            autoGrantPermissions: true,
            nativeWebScreenshot: true,
            // TODO: just using for OPE mode
            // Add these for stability:
            // newCommandTimeout: 300,
            // launchTimeout: 300000,
            chromeOptions: {
                w3c: false,
                // args: ['--disable-gpu'],
                // args: ['--no-sandbox', '--disable-dev-shm-usage']
            },
            // Prevent warning timeout script
            timeouts: {
                implicit: 5000,
                pageLoad: 10000,
            },
            // newCommandTimeout: 300,
        };
    }
    static createIOSCapabilities() {
        return {
            platformVersion: process.env.IOS_PLATFORM_VERSION || '18.3',
            automationName: 'XCUITest',
            // Device
            udid: process.env.IOS_UDID, // For Real Device
            deviceName: process.env.IOS_DEVICE_NAME || 'iPhone SE (3rd generation)',
            noReset: true,
            bundleId: process.env.IOS_BUNDLE_ID || 'inc.guide.kabuappNext.dev',
            // App
            app: appRootPath.resolve(process.env.IOS_APP_PATH || '/apps/ios/iphonesimulator/Runner.app'),
            autoGrantPermissions: true,
            autoAcceptAlerts: true,
            // WebDriverAgent
            xcodeOrgId: process.env.IOS_XCODE_ORG_ID || 'UZM3W75GA2',
            xcodeSigningId: process.env.IOS_XCODE_SIGNING_ID || 'iPhone Developer',
            updatedWDABundleId: process.env.IOS_WDA_BUNDLE_ID || 'inc.guide.kabuappNext.dev.WebDriverAgentRunner',
            // useNewWDA: true,
            // Web Context
            // absoluteWebLocations: true,
            includeSafariInWebviews: true,
            // fullContextList: true, // Setting fullContextList to true will prevent I.switchToWeb() from working.
            // enableMultiWindows: true,
            isInspectable: true,
        };
    }
}
