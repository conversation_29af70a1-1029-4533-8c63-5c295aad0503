import Helper from '@codeceptjs/helper';
import { Context } from '@wdio/protocols';
import chaiWrapper from 'codeceptjs-chai';

/**
 * Appium Support helper
 * Extending AppiumHelper with Appium client(this.helpers['Appium'].browser[=browser object of webdriverio])
 */
class AppiumSupportHelper extends Helper {
    private async getStatusBarHeightOnIOS(): Promise<number> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const deviceScreenInfo = await browser.execute('mobile: deviceScreenInfo');
        console.debug('deviceScreenInfo.statusBarSize.height:', deviceScreenInfo['statusBarSize']?.height);
        return Math.round(deviceScreenInfo['statusBarSize']?.height || 0);
    }

    grabSessionId() {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return browser.sessionId;
    }

    async grabIsAndroid(): Promise<boolean> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return browser.isAndroid;
    }

    async grabIsIOS(): Promise<boolean> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return browser.isIOS;
    }

    async getAppSetting(settingName: string): Promise<string> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const appSettingListJon: string = await browser.execute(function (key) {
            return this.localStorage.getItem(key);
        }, 'app-setting-list');
        const appSettingList = JSON.parse(appSettingListJon);
        console.debug('appSettingList:', appSettingListJon);
        console.debug(`${settingName}:`, appSettingList?.[0]?.[settingName]);
        return appSettingList?.[0]?.[settingName];
    }

    async clearAppSettings(): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        await browser.execute(function (key) {
            delete this.localStorage?.[key];
        }, 'app-setting-list');
    }

    async amInWeb(): Promise<void> {
        const chai: chaiWrapper = this.helpers['ChaiWrapper'];
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        const currentContext: Context = await browser.getContext();
        console.debug('context:', currentContext);
        const inWebViewContext: boolean = currentContext.toString().includes('WEBVIEW');

        chai.assertEqual(inWebViewContext, true);
    }

    async haveWebVewContext(): Promise<void> {
        const chai: chaiWrapper = this.helpers['ChaiWrapper'];
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        const contexts: Context[] = await browser.getContexts();
        console.debug('all contexts:', contexts);
        const webViewContext: Context | undefined = contexts.find((context: Context) =>
            context.toString().includes('WEBVIEW'),
        );
        const hasWebVewContext = webViewContext ? true : false;

        chai.assertEqual(hasWebVewContext, true);
    }

    async existWebVewContext(): Promise<boolean> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        const contexts: Context[] = await browser.getContexts();
        console.debug('all contexts:', contexts);
        const webViewContext: Context | undefined = contexts.find((context: Context) =>
            context.toString().includes('WEBVIEW'),
        );
        const hasWebVewContext = webViewContext ? true : false;
        return hasWebVewContext;
    }

    async waitUntilHaveWebVewContext(timeout?: number, interval?: number, timeoutMsg?: string): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        await browser.waitUntil(
            async () => {
                const contexts: Context[] = await browser.getContexts();
                console.debug('all contexts:', contexts);
                const webViewContext: Context | undefined = contexts.find((context: Context) =>
                    context.toString().includes('WEBVIEW'),
                );
                return webViewContext ? true : false;
            },
            { timeout, interval, timeoutMsg },
        );
    }

    async activateApp(): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        console.debug('activateApp');
        const config: CodeceptJS.MainConfig = require('codeceptjs').config.get();
        const appId: string | undefined = browser.isAndroid
            ? config.helpers?.Appium?.desiredCapabilities?.appPackage
            : config.helpers?.Appium?.desiredCapabilities?.bundleId;
        console.debug(`appId: ${appId}`);

        if (appId) {
            console.debug('activate app.');
            await browser.activateApp(appId);
            if (browser.isAndroid) {
                await browser.execute('mobile: changePermissions', { permissions: 'all' });
            }
        }
    }

    async closeBrowser() {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        if (browser.isAndroid) {
            await browser.terminateApp('com.android.chrome');
        } else {
            await browser.terminateApp('com.apple.mobilesafari');
        }
    }

    async resetAppFixed(): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        console.debug('resetAppFixed');
        await this.closeBrowser();
        const config: CodeceptJS.MainConfig = require('codeceptjs').config.get();
        const appId: string | undefined = browser.isAndroid
            ? config.helpers?.Appium?.desiredCapabilities?.appPackage
            : config.helpers?.Appium?.desiredCapabilities?.bundleId;
        const appPath: string | undefined = config.helpers?.Appium?.desiredCapabilities?.app;
        console.debug(`appId: ${appId}`);
        console.debug(`appPath: ${appPath}`);
        if (appId) {
            console.debug('restart app.');
            await browser.terminateApp(appId);
            if (appPath) {
                console.debug('reinstall app to clear settings.');
                await browser.removeApp(appId);
                await browser.installApp(appPath);
            }
            await browser.activateApp(appId);
            if (browser.isAndroid) {
                await browser.execute('mobile: changePermissions', { permissions: 'all' });
            }
        }
    }

    async terminateAndActivateAppFixed(): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        console.debug('terminateAndActivateAppFixed');
        await this.closeBrowser();
        const config: CodeceptJS.MainConfig = require('codeceptjs').config.get();
        const appId: string | undefined = browser.isAndroid
            ? config.helpers?.Appium?.desiredCapabilities?.appPackage
            : config.helpers?.Appium?.desiredCapabilities?.bundleId;
        console.debug(`appId: ${appId}`);
        if (appId) {
            console.debug('terminate app.');
            await browser.terminateApp(appId);
            console.debug('active app.');
            await browser.activateApp(appId);
            console.debug('reload session.');
            await browser.reloadSession();
            if (browser.isAndroid) {
                await browser.execute('mobile: changePermissions', { permissions: 'all' });
            }
        }
    }

    // TODO: delete
    // This method is not needed because:
    // Setting includeSafariInWebviews to true makes I.switchToWeb() work on iOS as well.
    //
    // I.switchToWeb() work
    // includeSafariInWebviews: true and fullContextList: false
    // all contexts:  [ 'NATIVE_APP', 'WEBVIEW_1207.3' ]
    //
    // I.switchToWeb() not work
    // fullContextList: true
    // all contexts:  [
    //     { id: 'NATIVE_APP' },
    //     {
    //       id: 'WEBVIEW_1184.3',
    //       title: 'メールアドレス 登録方法のご案内',
    //       url: 'https://st.dua2a-stub.kcmsr.dev.guide.inc/mobile/setting/mail-registration/guidance',
    //       bundleId: 'inc.guide.kabuappNext.dev'
    //     }
    //   ]
    async switchToWebFixed(): Promise<void> {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        if (browser.isAndroid) {
            appium.switchToWeb();
        } else {
            const currentContext: Context = await browser.getContext();
            if (!currentContext.toString().includes('WEBVIEW')) {
                const contexts: Context[] = await browser.getContexts();
                console.debug('all contexts:', contexts);
                const webViewContext = contexts.find((context: Context) => {
                    if (typeof context === 'string') {
                        return context.includes('WEBVIEW');
                    } else {
                        return context.id?.includes('WEBVIEW');
                    }
                });
                if (webViewContext) {
                    // Switch to the WebView context
                    if (typeof webViewContext === 'string') {
                        await browser.switchContext(webViewContext);
                    } else {
                        await browser.switchContext(webViewContext.id);
                    }
                }
            }
        }
    }

    /**
     * Click fixed
     * @description Use this method for non-clickable element(button in bottom sheet / a link) on iOS
     * @param locator
     */
    async clickFixed(locator: CodeceptJS.LocatorOrString): Promise<void> {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        if (browser.isIOS) {
            // I.click() button doesn't work in bottom sheet on iOS
            // I.click() a link doesn't work on iOS
            await this.tapLocationOfElement(locator);
        } else {
            await appium.click(locator);
        }
    }

    /**
     * Grabs element bounding rect typed
     * @param locator
     * @returns element bounding rect
     */
    async grabElementBoundingRectTyped(locator: CodeceptJS.LocatorOrString): Promise<DOMRect> {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const rect: number | DOMRect = await appium.grabElementBoundingRect(locator);
        if (typeof rect === 'number') {
            throw new TypeError('Wrong type found, DOMRect expected');
        }
        console.debug(rect);
        return rect;
    }

    /**
     * Tap location of element
     * @param locator
     */
    async tapLocationOfElement(locator: CodeceptJS.LocatorOrString): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const x = Math.round(rect.x + rect.width / 2);
        const y = Math.round(rect.y + rect.height / 2);
        await this.tapAction(x, y);
    }

    // can not use -> I.touchPerform([{ action: 'tap', options: { x: 0, y: 0, } }]);
    // WebDriverIO's touchAction is not supported in Appium any longer. use action.
    // see. https://github.com/webdriverio/webdriverio/issues/12711#issuecomment-2062759621
    // see. https://webdriver.io/docs/api/element/touchAction/
    /**
     * TapAction
     * @param x
     * @param y
     */
    async tapAction(x: number, y: number): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        // correct y position on iOS
        const yOffset = browser.isIOS ? await this.getStatusBarHeightOnIOS() : 0;
        await browser
            .action('pointer', { parameters: { pointerType: 'touch' } })
            .move({ x: x, y: y + yOffset })
            .down({ button: 0 })
            .up({ button: 0 })
            .perform();
    }

    // 'mobile: swipe' works on iPhone SE 2nd, but doesn't work on iPhone 16 Pro

    // async swipeLeftFixed(locator: CodeceptJS.LocatorOrString) {
    //     const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
    //     const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
    //     if (browser.isAndroid) {
    //         await this.swipeLeftAction(locator);
    //     } else {
    //         const elements: WebdriverIO.Element[] = await appium._locate(locator);
    //         await browser.execute('mobile: swipe', {
    //             strategy: 'xpath',
    //             selector: elements[0]?.selector,
    //             direction: 'left',
    //         });
    //     }
    // }

    // async swipeRightFixed(locator: CodeceptJS.LocatorOrString) {
    //     const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
    //     const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
    //     if (browser.isAndroid) {
    //         await this.swipeRightAction(locator);
    //     } else {
    //         const elements: WebdriverIO.Element[] = await appium._locate(locator);
    //         // works on iPhone SE 2nd, but doesn't work on iPhone 16 Pro
    //         await browser.execute('mobile: swipe', {
    //             strategy: 'xpath',
    //             selector: elements[0]?.selector,
    //             direction: 'right',
    //         });
    //     }
    // }

    // async swipeUpFixed(locator: CodeceptJS.LocatorOrString) {
    //     const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
    //     const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
    //     if (browser.isAndroid) {
    //         await this.swipeUpAction(locator);
    //     } else {
    //         const elements: WebdriverIO.Element[] = await appium._locate(locator);
    //         await browser.execute('mobile: swipe', {
    //             strategy: 'xpath',
    //             selector: elements[0]?.selector,
    //             direction: 'up',
    //         });
    //     }
    // }

    // async swipeDownFixed(locator: CodeceptJS.LocatorOrString) {
    //     const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
    //     const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
    //     if (browser.isAndroid) {
    //         await this.swipeDownAction(locator);
    //     } else {
    //         const elements: WebdriverIO.Element[] = await appium._locate(locator);
    //         await browser.execute('mobile: swipe', {
    //             strategy: 'xpath',
    //             selector: elements[0]?.selector,
    //             direction: 'down',
    //         });
    //     }
    // }

    /**
     * Swipe left fixed with offset
     * @description Use this method to avoid "WebDriverError: move target out of bounds" on Android
     * @param locator
     * @param xOffset
     */
    async swipeLeftFixedWithOffset(locator: CodeceptJS.LocatorOrString, xOffset: number): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x + rect.width / 2) + xOffset,
            y: Math.round(rect.y + rect.height / 2),
        };
        const to = {
            x: Math.round(rect.x + rect.width / 2),
            y: Math.round(rect.y + rect.height / 2),
        };
        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    /**
     * Swipe right fixed with offset
     * @description Use this method to avoid "WebDriverError: move target out of bounds" on Android
     * @param locator
     * @param xOffset
     */
    async swipeRightFixedWithOffset(locator: CodeceptJS.LocatorOrString, xOffset: number): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x + rect.width / 2),
            y: Math.round(rect.y + rect.height / 2),
        };
        const to = {
            x: Math.round(rect.x + rect.width / 2) + xOffset,
            y: Math.round(rect.y + rect.height / 2),
        };
        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    /**
     * Swipe left fixed
     * @param locator
     */
    async swipeLeftFixed(locator: CodeceptJS.LocatorOrString): Promise<void> {
        // const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x + rect.width) - 10,
            y: Math.round(rect.y + rect.height / 2),
        };
        const to = {
            x: Math.round(rect.x) + 10,
            y: Math.round(rect.y + rect.height / 2),
        };

        // const elements: WebdriverIO.Element[] | undefined = await appium._locate(locator);
        // await browser.swipe({
        //     // from: from,
        //     // to: to,
        //     direction: 'left',
        //     percent: 0.75,
        //     scrollableElement: await browser.$(elements[0].selector),
        // });
        // const windowSize = await browser.getWindowSize();
        // console.debug('windowSize:', windowSize);
        // const windowWidth = Math.round(windowSize.width);
        // const fromXCorrected = Math.min(from.x, windowWidth - 10);
        // const toXCorrected = Math.min(to.x, windowWidth - 10);

        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    async swipeLeftWithOffsetX(locator: CodeceptJS.LocatorOrString, xOffset = 100): Promise<void> {
        // const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x + rect.width) - 10,
            y: Math.round(rect.y + rect.height / 2),
        };
        const to = {
            x: Math.round(rect.x) + xOffset,
            y: Math.round(rect.y + rect.height / 2),
        };

        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    async swipeRightWithOffsetX(locator: CodeceptJS.LocatorOrString, xOffset = 100): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x) + 10,
            y: Math.round(rect.y + rect.height / 2),
        };
        const to = {
            x: Math.round(rect.x + rect.width) - xOffset,
            y: Math.round(rect.y + rect.height / 2),
        };
        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    /**
     * Swipe right fixed
     * @param locator
     */
    async swipeRightFixed(locator: CodeceptJS.LocatorOrString): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x) + 10,
            y: Math.round(rect.y + rect.height / 2),
        };
        const to = {
            x: Math.round(rect.x + rect.width) - 10,
            y: Math.round(rect.y + rect.height / 2),
        };
        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    /**
     * Swipe up fixed
     * @param locator
     */
    async swipeUpFixed(locator: CodeceptJS.LocatorOrString): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x + rect.width / 2),
            y: Math.round(rect.y + rect.height) - 10,
        };
        const to = {
            x: Math.round(rect.x + rect.width / 2),
            y: Math.round(rect.y) + 10,
        };
        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    /**
     * Swipe down fixed
     * @param locator
     */
    async swipeDownFixed(locator: CodeceptJS.LocatorOrString): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const from = {
            x: Math.round(rect.x + rect.width / 2),
            y: Math.round(rect.y) + 10,
        };
        const to = {
            x: Math.round(rect.x + rect.width / 2),
            y: Math.round(rect.y + rect.height) - 10,
        };
        await this.swipeAction(from.x, from.y, to.x, to.y);
    }

    /**
     * SwipeAction
     * @param fromX
     * @param fromY
     * @param toX
     * @param toY
     */
    async swipeAction(fromX: number, fromY: number, toX: number, toY: number): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        // correct y position on iOS
        const yOffset = browser.isIOS ? await this.getStatusBarHeightOnIOS() : 0;
        // // correct x, y position by viewport size
        // // to avoid "WebDriverError: move target out of bounds" on Android
        // const windowSize = await browser.getWindowSize();
        // console.debug('windowSize:', windowSize);
        // const viewportRect: DOMRect = await browser.execute('mobile: viewportRect');
        // console.debug('viewportRect:', viewportRect);
        // const systemBars = await browser.getSystemBars();
        // console.debug('systemBars:', systemBars);
        // const displayDensity: number = await browser.getDisplayDensity();
        // console.debug('displayDensity:', displayDensity);
        // console.debug('width:', windowSize.width / (displayDensity / 160));
        // console.debug('height:', windowSize.height / (displayDensity / 160));

        // const windowWidth = Math.round(windowSize.width);
        // // const windowHeight = Math.round(windowSize.height);

        console.debug('fromX:', fromX);
        console.debug('fromY:', fromY);
        console.debug('toX:', toX);
        console.debug('toY:', toY);
        // const fromXCorrected = Math.min(fromX, windowWidth - 10);
        // // const fromYCorrected = Math.min(fromY + yOffset, windowHeight - 10);
        // const toXCorrected = Math.min(toX, windowWidth - 10);
        // // const toYCorrected = Math.min(toY + yOffset, windowHeight - 10);
        await browser
            .action('pointer', { parameters: { pointerType: 'touch' } })
            .move({ x: fromX, y: fromY + yOffset })
            .down()
            .pause(500)
            .move({ x: toX, y: toY + yOffset })
            .up()
            .perform();
    }

    /**
     * Swipe in specified direction
     */
    async swipeDirection(direction: 'up' | 'down' | 'left' | 'right', percent = 0.6, duration = 1000): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const { width, height } = await browser.getWindowSize();

        const centerX = Math.round(width / 2);
        const centerY = Math.round(height / 2);
        const percentOffset = percent / 2;

        const coordinates = {
            up: {
                start: { x: centerX, y: Math.round(height * (1 - percentOffset)) },
                end: { x: centerX, y: Math.round(height * percentOffset) },
            },
            down: {
                start: { x: centerX, y: Math.round(height * percentOffset) },
                end: { x: centerX, y: Math.round(height * (1 - percentOffset)) },
            },
            left: {
                start: { x: Math.round(width * (1 - percentOffset)), y: centerY },
                end: { x: Math.round(width * percentOffset), y: centerY },
            },
            right: {
                start: { x: Math.round(width * percentOffset), y: centerY },
                end: { x: Math.round(width * (1 - percentOffset)), y: centerY },
            },
        };

        const actions = [
            {
                type: 'pointer',
                id: 'finger1',
                parameters: { pointerType: 'touch' },
                actions: [
                    { type: 'pointerMove', duration: 0, ...coordinates[direction].start },
                    { type: 'pointerDown', button: 0 },
                    { type: 'pause', duration: 100 },
                    { type: 'pointerMove', duration, ...coordinates[direction].end },
                    { type: 'pointerUp', button: 0 },
                ],
            },
        ];

        await browser.performActions(actions).then(() => {
            console.debug('Swipe completed');
            browser.releaseActions();
        });
    }

    async swipeElementDirection(
        direction: 'up' | 'down' | 'left' | 'right',
        locator: CodeceptJS.LocatorOrString,
        percent = 0.6,
        duration = 1000,
    ) {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const { width, height } = await browser.getWindowSize();
        const { x, y, width: rectWidth, height: rectHeight } = await this.grabElementBoundingRectTyped(locator);

        const centerX = Math.round(x + rectWidth / 2);
        const centerY = Math.round(y + rectHeight / 2);
        const percentOffset = percent / 2;

        const coordinates = {
            up: {
                start: { x: centerX, y: Math.round(height * (1 - percentOffset)) },
                end: { x: centerX, y: Math.round(height * percentOffset) },
            },
            down: {
                start: { x: centerX, y: Math.round(height * percentOffset) },
                end: { x: centerX, y: Math.round(height * (1 - percentOffset)) },
            },
            left: {
                start: { x: Math.round(width * (1 - percentOffset)), y: centerY },
                end: { x: Math.round(width * percentOffset), y: centerY },
            },
            right: {
                start: { x: Math.round(width * percentOffset), y: centerY },
                end: { x: Math.round(width * (1 - percentOffset)), y: centerY },
            },
        };

        const actions = [
            {
                type: 'pointer',
                id: 'finger1',
                parameters: { pointerType: 'touch' },
                actions: [
                    { type: 'pointerMove', duration: 0, ...coordinates[direction].start },
                    { type: 'pointerDown', button: 0 },
                    { type: 'pause', duration: 100 },
                    { type: 'pointerMove', duration, ...coordinates[direction].end },
                    { type: 'pointerUp', button: 0 },
                ],
            },
        ];

        await browser.performActions(actions).then(() => {
            console.debug('Swipe completed');
            browser.releaseActions();
        });
    }

    // async scrollToElement(
    //     locator: CodeceptJS.Locator,
    //     scrollOptions: boolean | ScrollIntoViewOptions = { behavior: 'smooth', block: 'center', inline: 'center' },
    // ) {
    //     const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

    //     await browser.execute(
    //         (expression: string, options: ScrollIntoViewOptions) => {
    //             const el: HTMLElement = document.evaluate(
    //                 expression,
    //                 document,
    //                 null,
    //                 XPathResult.FIRST_ORDERED_NODE_TYPE,
    //                 null,
    //             ).singleNodeValue as HTMLElement;
    //             if (el) {
    //                 el.scrollIntoView(options);
    //             }
    //         },
    //         locator.toXPath(),
    //         scrollOptions,
    //     );
    // }

    /**
     * Press without release location of element [Same as tap on iOS]
     * @param locator
     */
    async pressWithoutReleaseLocationOfElement(locator: CodeceptJS.LocatorOrString): Promise<void> {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const x = Math.round(rect.x + rect.width / 2);
        const y = Math.round(rect.y + rect.height / 2);
        await this.pressWithoutReleaseAction(x, y); // Same as tap on iOS
        // TODO: can't separate press and release on iOS [as of 2024/03/13]
        // const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        // await browser.execute('mobile: forcePress', { x: x, y: y });
        // await browser.execute('mobile: touchAndHold', { duration: 2.0, x: x, y: y });
    }

    /**
     * Press without release action [Same as tap on iOS]
     * @param x
     * @param y
     */
    async pressWithoutReleaseAction(x: number, y: number): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        // correct y position on iOS
        const yOffset = browser.isIOS ? await this.getStatusBarHeightOnIOS() : 0;
        await browser
            .action('pointer', { parameters: { pointerType: 'touch' } })
            .move({ x: x, y: y + yOffset })
            .down({ button: 0 })
            .perform(true);
    }

    /**
     * ReleaseAction [No-op on iOS]
     */
    async releaseAction(): Promise<void> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        await browser.releaseActions();
        // TODO: On iOS, releaseActions is a no-op [as of 2024/03/13]
        // see. https://github.com/appium/appium-xcuitest-driver/blob/542e2f1ecf2eb21247c20e48d0264d9d755f8093/lib/commands/gesture.js#L96
    }

    // TODO: delete
    async getClickableElements(locator): Promise<WebdriverIO.Element[]> {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const elements: WebdriverIO.Element[] | undefined = await appium._locateClickable(locator);
        // const selector: Selector = elements[0]?.selector;
        // console.debug(`locateClickable: ${selector}`);
        return elements;
    }

    // TODO: delete
    // see. https://qiita.com/hirotaka23/items/e2ad6dcb1c0d7fb45dcd
    async forceClickFixed(locator) {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        const elements: WebdriverIO.Element[] | undefined = await appium._locateClickable(locator);

        // const y = Math.round((await elements[0]?.getLocation()).y) + 10;
        // await browser.execute('window.scrollTo(0,' + y + ')');
        // await this.tapAction((await elements[0]?.getLocation()).x, (await elements[0]?.getLocation()).y);

        // await elements[0]?.scrollIntoView();
        // await elements[0]?.click();
        await browser.execute('arguments[0]?.scrollIntoView();', elements[0]);
        await browser.execute('arguments[0]?.click();', elements[0]);
    }

    async tapByCoordinate(locator: CodeceptJS.LocatorOrString, offset?: { x: number; y: number }) {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;

        await appium.grabElementBoundingRect(locator).then(async (rect) => {
            if (typeof rect === 'number') {
                throw new TypeError('Wrong type found, DOMRect expected');
            }

            const x = Math.round(rect.x + rect.width / 2) + (offset?.x || 0);
            const y = Math.round(rect.y + rect.height / 2) + (offset?.y || 0);

            await browser
                .action('pointer', { parameters: { pointerType: 'touch' } })
                .move({ x, y })
                .down()
                .up()
                .perform();
        });
    }

    async performBrowserBack() {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        await browser.back();
    }

    async getLocalStorage(key: string): Promise<string> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.execute(`return window.localStorage.getItem('${key}');`);
    }

    async setLocalStorage(key: string, value: string): Promise<string> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.execute(`return window.localStorage.setItem('${key}', '${value}');`);
    }

    async getSessionStorage(key: string): Promise<string> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.execute(`return window.sessionStorage.getItem('${key}');`);
    }

    async setSessionStorage(key: string, value: string): Promise<string> {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.execute(`return window.sessionStorage.setItem('${key}', '${value}');`);
    }

    /**
     * Checks if the keyboard is currently displayed on the device
     */
    async isKeyboardShown() {
        const browser: WebdriverIO.Browser = this.helpers['Appium'].browser;
        return await browser.isKeyboardShown();
    }

    async getValueOfNativeContextElement(locator: CodeceptJS.LocatorOrString): Promise<string> {
        const appium: CodeceptJS.AppiumTs = this.helpers['Appium'];
        const elements: WebdriverIO.Element[] | undefined = await appium._locate(locator);
        if (elements && elements.length > 0) {
            return await this.grabIsAndroid() ? await elements[0].getText() : await elements[0].getValue();
        }
        throw new Error(`Element not found for locator: ${locator}`);
    }
}

export = AppiumSupportHelper;
