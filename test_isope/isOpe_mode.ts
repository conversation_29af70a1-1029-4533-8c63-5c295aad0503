import { COMMON_HEADER_TITLE, COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE, USER_ID } from "../const/constant";
import domesticStockCashtrading from "../pages/domesticStockCashtrading";
import favoritesList from '../pages/favoritesList';
import orderInquiry from "../pages/order-inquiry";
import tradeStock from "../pages/trade-stock";
import tradeFund from '../pages/trade-fund';
import transferCash from "../pages/transferCash";
import depositBankAccountRegistration from "../pages/depositBankAccountRegistration";
import electronicDelivery from "../pages/electronicDelivery";
import AppSetting from '../pages/appSetting';
import SettingCard from '../pages/settingCard';
import ChangeLoginPassword from '../pages/changeLoginPassword';
import Referral from '../pages/referral';
import ReferralMailInput from '../pages/referralMailInput';

Feature('isOpe Mode Tests');
const locators = AppSetting.locators;
const locatorsSettingCard = SettingCard.locators;
const closeBtn = '//button[text()="閉じる"]';
const resetWithdrawalPasswordLocators = {
    resetWithDrawalUrl: '/mobile/setting/password/reset-withdrawal-password',
    link: '//*[@data-testid="resetWithdrawalStep1_hereLink_id"]/span[2]',
    pageDescription: '//*[@id="__next"]/div/div[2]/div/div[2]/div[1]',
    telephoneNumberInput: '//*[@data-testid="resetWithdrawalStep1_input_0_id"]',
    step1Input: '//*[@data-testid="resetWithdrawalStep1_input_1_id"]',
    issueOneTimeButton: '//*[@data-testid="resetWithdrawalStep1_issueOneTimeCode_id"]',
    step2Input: '//*[@data-testid="resetWithdrawalStep2_oneTimeCode_id"]/div/input',
    step2WithdrawalPasswordInput: '//*[@data-testid="resetWithdrawalStep2_newWithdrawalPassword_id"]/div/input',
    step2WithdrawalPasswordConfirmInput:
        '//*[@data-testid="resetWithdrawalStep2_newWithdrawalPasswordConfirm_id"]/div/input',
    step2IssueOneTimeButton: '//*[@data-testid="resetWithdrawalStep2_setting_id"]',
    returnToTopButton: '//*[@data-testid="resetWithdrawalStep3_returnToTop_id"]',
};
const changeWithdrawalPasswordLocators = {
    currentPasswordInput: '//*[@data-testid="changeWithdrawalPasswordPage_currentWithdrawalPassword_id"]/div/input',
    newPasswordInput: '//*[@data-testid="changeWithdrawalPasswordPage_newWithdrawalPassword_id"]/div/input',
    newPasswordConfirmInput:
        '//*[@data-testid="changeWithdrawalPasswordPage_newWithdrawalPasswordForConfirmation_id"]/div/input',
    pageDescription: '//*[@id="__next"]/div/div[2]/div/form/div[1]',
    descriptionSelector: '//*[@id="__next"]/div/div[2]/div/form/div[5]',
    submitButton: '//*[@data-testid="changeWithdrawalPasswordPage_changeButton_id"]',
    modalSelector: '//*[@aria-modal="true"]',
    url: '/mobile/setting/password/change-withdrawal-password',
    link: '//*[@data-testid="changeWithdrawalPasswordPage_resetWithdrawalPassword_id"]',
};
Before(async ({ I }) => {
    console.debug('before - isOpe environment');
    await I.activateApp();
    await I.waitFor();
    console.debug('all contexts:', await I.grabAllContexts());
    console.debug('context:', await I.grabContext());
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user49 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after - isOpe environment');
    try {
        const modalSelector = '//div[contains(@class, "chakra-modal__content-container")]';
        const modalCount = await I.grabNumberOfVisibleElements(modalSelector);
        if (modalCount > 0) {
            const closeButton = '//button[text()="閉じる"]';
            const closeButtonCount = await I.grabNumberOfVisibleElements(closeButton);
            if (closeButtonCount > 0) {
                await I.clickFixed(closeButton);
                await I.waitFor();
            }
        }
    } catch (error) {
        console.log('No modal to close or close failed:', error.message);
    }

    I.switchToNative();
    await I.closeBrowser();
});
// 1937: OPE Login 
Scenario('1937: OPE Login - Auto login before running tests', async ({ I }) => {
    try {
        // Check if login button exists
        const loginButton = '//*[@data-testid="opeLogin_login_id"]';
        const isLoginButtonVisible = await I.grabNumberOfVisibleElements(loginButton);

        if (isLoginButtonVisible > 0) {
            // Not logged in -> perform login
            const loginInput = '//*[@data-testid="opeLogin_accountNumber_id"]';
            await I.waitForElement(loginInput, 1);
            I.scrollAndFill(loginInput, '********');
            await I.waitFor();
            await I.clickFixed(loginButton);
            await I.saveScreenshot(`1937.Test_item_No.1_Login_isOpe.png`);
            await I.waitFor();
            await I.switchToWeb();
            await I.waitFor();
            // ===========
            I.amOnPage('/mobile/mypage/portfolio');
            await I.waitFor('longWait');
            I.waitForText('ポートフォリオ', 3, 'body');
            I.waitForElement('#mypage-portfolio');

            const isDisplayTutorial = await I.getAppSetting('is-disp-tutorial');
            const isDisplayTopCoach = await I.getAppSetting('is-disp-top-coach');

            if (!isDisplayTutorial) {
                I.waitForText('新しい投資体験へ', 3, 'body');
                // Semantic locators can be used in web contexts (codeceptjs automatically searches for button text, label text, aria-label property, etc.)
                // see. https://codecept.io/locators/#semantic-locators
                // or "~cancel-btn": accessible id = aria-label (web context)
                I.click('//button[@aria-label="cancel-btn"]');
                await I.waitFor();
            }

            if (!isDisplayTopCoach) {
                I.waitForText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。', 3, 'body');
                // Touch and close coach modal.
                const coachModal = locate('div').withChild(
                    locate('p').withText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。'),
                );
                const rect = await I.grabElementBoundingRectTyped(coachModal);
                const x = Math.round(rect.x) + 100;
                const y = Math.round(rect.y) + 100;
                await I.tapAction(x, y);
                await I.waitFor();
            }
            // ============
            // Verify login success
            await I.waitForElement('~menu', 5);
            console.log('✅ Login successful');

        } else {
            // Already logged in
            console.log('ℹ️ Already logged in, skipping login step');
            await I.switchToWeb();
            await I.waitFor();

        }
    } catch (error) {
        console.error('❌ Error during login:', error);
        throw error;
    }
});

// 138.hamburger_menu_test.ts
Scenario('Test item No.27 Button Logout: isOpe', async ({ I, hamburgerMenu }) => {
    await hamburgerMenu.openMenu();

    const logoutButton = '//*[@data-testid="menu_logout_id"]';
    await I.waitForElement(logoutButton, 2);
    await I.scrollAndClick(logoutButton);
    await I.waitFor();
    await I.saveScreenshot('138.Test_item_No.27_Button_Logout_isOpe.png');
    await I.waitFor();
    //login again
    try {
        // Check if login button exists
        const loginButton = '//*[@data-testid="opeLogin_login_id"]';
        const isLoginButtonVisible = await I.grabNumberOfVisibleElements(loginButton);

        if (isLoginButtonVisible > 0) {
            // Not logged in -> perform login
            const loginInput = '//*[@data-testid="opeLogin_accountNumber_id"]';
            await I.waitForElement(loginInput, 1);
            I.scrollAndFill(loginInput, '********');
            await I.waitFor();
            await I.clickFixed(loginButton);
            await I.saveScreenshot(`1937.Test_item_No.1_Login_isOpe.png`);
            await I.waitFor();
            await I.switchToWeb();
            await I.waitFor();
            // ===========
            I.amOnPage('/mobile/mypage/portfolio');
            await I.waitFor('longWait');
            I.waitForText('ポートフォリオ', 3, 'body');
            I.waitForElement('#mypage-portfolio');

            const isDisplayTutorial = await I.getAppSetting('is-disp-tutorial');
            const isDisplayTopCoach = await I.getAppSetting('is-disp-top-coach');

            if (!isDisplayTutorial) {
                I.waitForText('新しい投資体験へ', 3, 'body');
                // Semantic locators can be used in web contexts (codeceptjs automatically searches for button text, label text, aria-label property, etc.)
                // see. https://codecept.io/locators/#semantic-locators
                // or "~cancel-btn": accessible id = aria-label (web context)
                I.click('//button[@aria-label="cancel-btn"]');
                await I.waitFor();
            }

            if (!isDisplayTopCoach) {
                I.waitForText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。', 3, 'body');
                // Touch and close coach modal.
                const coachModal = locate('div').withChild(
                    locate('p').withText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。'),
                );
                const rect = await I.grabElementBoundingRectTyped(coachModal);
                const x = Math.round(rect.x) + 100;
                const y = Math.round(rect.y) + 100;
                await I.tapAction(x, y);
                await I.waitFor();
            }
            // ============
            // Verify login success
            await I.waitForElement('~menu', 5);
            console.log('✅ Login successful');

        } else {
            // Already logged in
            console.log('ℹ️ Already logged in, skipping login step');
            await I.switchToWeb();
            await I.waitFor();

        }
    } catch (error) {
        console.error('❌ Error during login:', error);
        throw error;
    }

});
// 251.notify_details_individual_test.ts
Scenario('test Item 5 Confirm Button page: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitForElement('//*[@data-testid="common_noti_id"]');
    await I.clickFixed('//*[@data-testid="common_noti_id"]');
    await I.waitFor();

    const notifyIndividualTab = '//*[@data-testid="noticeTab_individualNoti_id"]';
    await I.waitForElement(notifyIndividualTab, 4);
    await I.clickFixed(notifyIndividualTab);
    await I.waitFor();

    const notifyListDetail = '//*[@data-testid="notifyIndividualList_notiItem_id_0"]';
    await I.waitForElement(notifyIndividualTab, 4);
    await I.clickFixed(notifyListDetail);
    await I.waitFor('mediumWait');

    const confirmButton = '//*[@data-testid="notifyDetailsIndividual_confirmationDeadline_id"]';
    await I.waitForElement(confirmButton, 4);
    await I.clickFixed(confirmButton);
    await I.waitFor();
    await I.saveScreenshot('251.Test_item_No.5_Confirm_Button_page_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
//365.favorites_list_test.ts
Scenario('test Item 20 Complete Button: isOpe', async ({ I }) => {
    await I.amOnPage('/mobile/favorite');
    await I.waitFor('mediumWait');
    await I.clickFixed('//*[@data-testid="favoritesList_edit_id"]');
    await I.waitFor();
    await I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
    await I.waitFor();
    await I.saveScreenshot('365.Test_item_No.20_editMode.png');
    await I.clickFixed(closeBtn);
    await I.waitFor('shortWait');

    // Click complete button
    await I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');

    await I.saveScreenshot('365.Test_item_No.20_completeButton_isOpe.png');
    await I.waitFor();
    I.refreshPage();
    await I.waitFor();

});
Scenario('test Item 30 Confirm Delete List: isOpe', async ({ I }) => {
    await I.amOnPage('/mobile/favorite');
    await I.waitFor('mediumWait');
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();


    // Click Delete
    await I.clickFixed('//p[contains(text(), "削除")]');
    await I.waitForElement('//div[contains(@class, "chakra-modal__content-container")]//button[contains(text(), "削除") and contains(@class, "chakra-button")]');
    await I.clickFixed('//div[contains(@class, "chakra-modal__content-container")]//button[contains(text(), "削除") and contains(@class, "chakra-button")]');
    await I.saveScreenshot('365.Test_item_No.30_confirm_delete_list_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
Scenario('test Item 34 Create New List: isOpe', async ({ I }) => {
    const settingName = '//*[@data-testid="favoritesList_setting_id"]';
    I.seeElement(settingName);
    await I.clickFixed(settingName);
    await I.waitFor();
    await I.waitForElement('//*[@data-testid="favoritesList_addFavoriteListButton_id"]', 2);
    await I.clickFixed('//*[@data-testid="favoritesList_addFavoriteListButton_id"]');
    await I.waitFor('shortWait');
    const searchInput = '//*[@data-testid="favoritesList_favoriteListName_id"]/input[@placeholder="リスト20"]';
    await I.waitForElement(searchInput);
    await I.clickFixed(searchInput);
    I.fillField(searchInput, '34');
    await I.waitFor('shortWait');
    await I.clickFixed('//*[@data-testid="favoritesList_createButton_id"]');
    await I.saveScreenshot('365.Test_item_No.34_create_new_list_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 674.domestic_stock_cash_trading_buy_order_confirm_test.ts
Scenario('Test item No.25 Order Confirmation: isOpe', async ({ I, stockCashOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user25 });
    await I.waitFor();
    await stockCashOrder.goToCashBuyOrderConfirm();
    // await I.waitFor('mediumWait');
    const passwordSelector = '//*[@data-testid="buyConfirm_password_id"]';
    await I.waitFor('mediumWait');
    await I.scrollAndClick(passwordSelector);
    await I.waitFor('shortWait');
    await I.scrollAndFill(passwordSelector, 'isOpe');
    const buyConfirmConfirmButton = '//*[@data-testid="buyConfirm_confirmOrderButton_id"]';
    await I.waitFor('shortWait');
    await I.scrollAndClick(buyConfirmConfirmButton);


    await I.saveScreenshot('674.Test_item_No.25_Transition_to_Uturn_order_isOpe.png');
    I.refreshPage();
    await I.waitFor();

});
// 700.domestic_stock_cash_trading_sell_order_order_confirm_test.ts
Scenario('Item 24. Go to the sell complete page: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user26 });
    await I.waitFor('mediumWait');
    I.setSessionStorage(SESSION_STORAGE_KEY.tradeStockSell, SESSION_STORAGE_VALUE.tradeStockSell());
    I.amOnPage(domesticStockCashtrading.urls.sell);
    await I.waitFor('mediumWait');
    I.see('現物売', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(domesticStockCashtrading.urls.sell);
    await I.seeAndClickElement(domesticStockCashtrading.locators.executionMethodOptionItem(2));
    await domesticStockCashtrading.seeAndFillText('groupNumberInput', '1000');
    await domesticStockCashtrading.goToConfirmPage();

    await domesticStockCashtrading.swipeToLocator('passwordInput');
    await domesticStockCashtrading.seeAndFillText('passwordInput', 'isOpe');
    await I.seeAndClickElement(domesticStockCashtrading.locators.passwordOmissionCheck);
    await I.seeAndClickElement(domesticStockCashtrading.locators.sellConfirmButton);
    await I.saveScreenshot('674.Test_item_No.24_go_to_the_sell_complete_page_isOpe.png');
    I.refreshPage();
    await I.waitFor();

});
// 742.domestic_stock_cash_trading_order_confirm_test.ts
Scenario(
    'domestic stock cash trading order confirm [22.訂正を確定]: isOpe',
    async ({ I }) => {
        I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user27 });

        await I.amOnPage(PAGE_URL.orderInquiryStock);
        await I.waitFor('mediumWait');
        await orderInquiry.stockList.clickItem(0);
        await I.waitFor();
        await orderInquiry.stockDetail.orderCorrection();
        await I.waitFor();
        await tradeStock.correction.doReplace();
        await I.waitFor();
        await tradeStock.confirm.fillPassword('isOpe');

        await tradeStock.confirm.togglePasswordOmissionCheck();

        const btn = locate(tradeStock.confirm.locators.confirmButton);
        await I.scrollToElement(btn.toXPath());
        await I.clickFixed(btn);
        await I.waitFor();
        await I.saveScreenshot('742.domestic_stock_cash_trading_order_confirm_22_apply_isOpe.png');
        I.refreshPage();
        await I.waitFor();


    },
);
// 747.domestic_stock_cash_trading_order_cancel_test.ts
Scenario(
    'domestic stock cash trading order cancel [12.パスワード][13.パスワード省略チェック][16.取消を確定]',
    async ({ I }) => {
        I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user27 });

        await I.amOnPage(PAGE_URL.orderInquiryStock);
        await I.waitFor('mediumWait');
        await orderInquiry.stockList.clickItem(0);
        await I.waitFor();
        await orderInquiry.stockDetail.cancelOrder();
        await I.waitFor();


        await tradeStock.cancel.fillPassword('isOpe');
        await I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_12_password.png');
        await tradeStock.cancel.togglePasswordOmissionCheck();
        await I.waitFor('shortWait');

        const btn = locate(tradeStock.cancel.locators.confirmButton);
        await I.scrollToElement(btn.toXPath());
        await I.clickFixed(btn);
        await I.waitFor('shortWait');
        await I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_16_apply_isOpe.png');
        I.refreshPage();
        await I.waitFor();

    },
);
// 770.domestic_stock_petit_trading_buy_order_confirm_test.ts
Scenario('Test item No.21-1 Order Confirmation: isOpe', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user29 });
    await stockPetitOrder.goToPetitBuyOrderConfirm();

    await I.waitFor('shortWait');
    const passwordSelector = '//*[@data-testid="tradePetitBuyConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitBuyConfirm_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitBuyConfirm_orderConfirm_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot('770.Test_item_No.21-1_Order_Confirmation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 787.domestic_stock_petit_trading_sell_order_confirm_test.ts
Scenario('Test item No.19-1 Order Confirmation: isOpe', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user32 });
    await stockPetitOrder.goToPetitSellOrderConfirm('8306');
    await I.waitFor('shortWait');
    const passwordSelector = '//*[@data-testid="tradePetitSellConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitSellConfirm_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitSellConfirm_orderConfirm_id"]';
    await I.scrollToElement(passwordSelector);
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot('787.Test_item_No.19-1_Order_Confirmation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 799.domestic_stock_petit_trading_cancel_test.ts
Scenario('Test item No.16-1 Confirm Cancellation: isOpe', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user29 });
    I.setSessionStorage(SESSION_STORAGE_KEY.tradePetitDetail, SESSION_STORAGE_VALUE.tradePetitDetail({ "orderId": "string" }));
    await I.amOnPage(PAGE_URL.orderInquiryPetitDetail);
    await I.waitFor('mediumWait');
    const cancelOrderButton = '//*[@data-testid="stockDetail_cancelOrder_id"]';
    await I.clickFixed(cancelOrderButton);
    await I.waitFor('mediumWait');
    const passwordSelector = '//*[@data-testid="tradePetitCancel_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitCancel_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitCancel_confirmCancel_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot('787.Test_item_No.16-1_Confirm_Cancellation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 817.domestic_stock_margin_trading_new_order_confirm_test.ts
Scenario('Test item No.23-1 Order Confirmation - For U-turn orders and otherwise: isOpe', async ({ I, stockMarginOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
    await I.waitFor('mediumWait');
    await stockMarginOrder.goToMarginNewUturnOrderConfirm();
    const softLimitConfirmWrapperSelector = '//*[@data-testid="marginNew_softlimitConflit_id"]';
    const softLimitConfirmButtonSelector = `${softLimitConfirmWrapperSelector}//button`;
    const passwordInputSelector = '//*[@data-testid="marginNew_passwordInput_id"]';
    const ommitPasswordSelector = '//*[@data-testid="marginNew_passwordInputCheckBox_id"]';
    const orderConfirmButtonSelector = '//*[@data-testid="marginNew_orderConfirmButton_id"]';
    if ((await I.grabNumberOfVisibleElements(softLimitConfirmButtonSelector)) > 0) {
        await I.clickFixed(softLimitConfirmButtonSelector);
    }
    I.fillField(passwordInputSelector, stockMarginOrder.inputValues.password);
    await I.clickFixed(ommitPasswordSelector);
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('817.Test_item_No.23-1_Order_Confirmation_for_U-turn_orders_isOpe.png');
    I.refreshPage();
    await I.waitFor();

});
//847.domestic_stock_margin_trading_payment_order_confirm_test.ts
Scenario('Test item No.25-3 Order Confirmation: isOpe', async ({ I, stockMarginPaymentOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user33 });
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    const passwordSelector = '//*[@data-testid="marginPaymentConfirm_password_id"]';
    const passwordOmittedSelector = '//*[@data-testid="marginPaymentConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginPaymentConfirm_confirmButton_id"]';
    await I.scrollAndFill(passwordSelector, stockMarginPaymentOrder.inputValues.password);

    await I.waitFor('shortWait');
    await I.clickFixed(passwordOmittedSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('847.Test_item_No.25-3_Order_Confirmation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 888.domestic_stock_margin_trading_correct_confirm_test.ts
Scenario('Test item No.7 Confirm the correction: isOpe', async ({ I, stockMarginCorrect }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCorrect.goToMarginCorrectConfirm();

    await I.waitFor('mediumWait');
    const passwordSelector = '//*[@data-testid="marginCorrectionConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCorrectionConfirm_passwordOmissionCheck_id"]';
    const softLimitCheckSelector = '//*[@data-testid="marginCorrectionConfirm_softLimitAgree_id"]//button';
    const confirmCorrectButton = '//*[@data-testid="marginCorrectionConfirm_confirmCorrect_id"]';
    await I.scrollToElement(confirmCorrectButton);
    if ((await I.grabNumberOfVisibleElements(softLimitCheckSelector)) > 0) {
        await I.clickFixed(softLimitCheckSelector);
    }
    I.fillField(passwordSelector, stockMarginCorrect.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(confirmCorrectButton);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('888.Test_item_No.7_Confirm_the_correction_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
//894.domestic_stock_margin_trading_cancel_test.ts
Scenario('Test item No.14 Confirm cancellation: isOpe', async ({ I, stockMarginCancel }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCancel.goToMarginCancel();
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    const passwordSelector = '//*[@data-testid="marginCancel_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCancel_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginCancel_confirmCancel_id"]';
    I.fillField(passwordSelector, stockMarginCancel.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('894.Test_item_No.14_Confirm_cancellation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 900.domestic_stock_margin_trading_cancel_receipt_delivery_test.ts
Scenario('Test item No.13 Confirm cancellation: isOpe', async ({ I, stockMarginCancel }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCancel.goToMarginCancelReceiptDelivery();
    await I.waitFor('mediumWait');


    await I.swipeDirection('up');
    const passwordSelector = '//*[@data-testid="marginCancelReceipt_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCancelReceipt_passwordOmmittedCheck_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginCancelReceipt_confirmCancel_id"]';
    I.fillField(passwordSelector, stockMarginCancel.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('900.Test_item_No.13_Confirm_cancellation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 917.domestic_stock_margin_trading_receipt_order_confirm_test.ts
Scenario('Test item No.15 Order Confirmation: isOpe', async ({ I, stockMarginReceiptOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginReceiptOrder.goToMarginReceiptOrderConfirm();
    await I.waitFor('mediumWait');

    const passwordSelector = '//*[@data-testid="marginReceiptConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginReceiptConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginReceiptConfirm_orderConfirm_id"]';

    I.fillField(passwordSelector, stockMarginReceiptOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('917.Test_item_No.15_Order_Confirmation_isOpe.png');
    I.refreshPage();
    await I.waitFor();



});
// 939.domestic_stock_margin_trading_delivery_order_confirm_test.ts
Scenario('Test item No.14 Order Confirmation: isOpe', async ({ I, stockMarginDelivery }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginDelivery.goToMarginDeliveryOrderConfirm();
    await I.waitFor('mediumWait');

    const passwordSelector = '//*[@data-testid="marginDeliveryConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginDeliveryConfirm_passwordOmissionCheck_id"]';
    const confirmButton = '//*[@data-testid="marginDeliveryConfirm_orderConfirmBtn_id"]';
    I.fillField(passwordSelector, stockMarginDelivery.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(confirmButton);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('939.Test_item_No.14_Order_Confirmation_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1049.trade_fund_buy_confirm_test.ts
Scenario('test trade fund buy confirm [25.申し込む]: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });

    await tradeFund.buyInput.goToPage({ fundCode: '02311862' });
    await I.waitFor(); // wait for page loaded
    await tradeFund.buyInput.goToConfirmScreen();
    await I.waitFor(); // wait for page loaded

    const apply = locate(tradeFund.buyConfirm.locators.apply);

    if (await tradeFund.buyConfirm.isOmmitPassword()) {
        await tradeFund.buyConfirm.togglePasswordInputCheck();
    } else {
        await tradeFund.buyConfirm.fillPassword('isOpe');
        await tradeFund.buyConfirm.togglePasswordOmissionCheck();
    }

    await I.clickFixed(apply);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('1049.test_trade_fund_buy_confirm_[25.申し込む]_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1069.trade_fund_sell_confirm_test.ts
Scenario('Test No.18 Confirm your order: isOpe', async ({ I }) => {

    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user38 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);

    await tradeFund.sellInput.goToTradeFundSellInputPage();
    await tradeFund.sellInput.fillGroupInputNumber();
    await tradeFund.sellInput.goToTradeFundConfirmPage();

    await tradeFund.sellInput.setSellConfirmSessionData({ isOmmitPassword: true });
    await I.waitFor();
    await I.waitForElement(tradeFund.sellInput.locators.passwordInputCheck);
    await I.scrollAndClick(locate(tradeFund.sellInput.locators.passwordInputCheck).toXPath());
    await I.waitFor('shortWait');


    await I.waitForElement(tradeFund.sellInput.locators.confirmOrderButton);
    await I.scrollAndClick(locate(tradeFund.sellInput.locators.confirmOrderButton).toXPath());
    await I.waitFor('mediumWait');
    await I.saveScreenshot('1069.Test_No.18_Confirm_your_order_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1082.trade_fund_sell_cancel_confirm_test.ts
Scenario('Test No.17 Cancel: isOpe', async ({ I }) => {
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user38 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await I.waitFor();
    await I.amOnPage(`${PAGE_URL.tradeFundCancelConfirm}?orderId=8006`);
    await I.waitFor('mediumWait');
    I.seeElement(tradeFund.sellInput.locators.cancelOrderButton);
    await I.clickFixed(tradeFund.sellInput.locators.cancelOrderButton);
    await I.waitFor();
    await I.saveScreenshot('1082.Test_No.17_Cancel_isOpe.png');
    I.refreshPage();
    await I.waitFor();


});
//1384.customer_reserve_petit_reserve_order_confirm_test.ts
Scenario('Test Item 18.1: Check Transition to "Savings - Petit Stocks - Savings Application Completion: isOpe', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user48 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor('mediumWait');
    await accumulation.goToReserveOrderConfirmPage();
    await I.waitFor();
    const applyButton = '//button[text()="このまま申込む"]';
    await I.waitForElement(applyButton, 5);
    await I.waitFor('longWait');
    await I.scrollAndClick(applyButton);
    await I.waitFor('longWait');
    const passwordInput = '//*[@data-testid="reserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    await I.scrollAndFill(passwordInput, 'isOpe');

    const orderButton = '//*[@data-testid="reserveOrderConfirm_order_id"]';
    await I.waitForElement(orderButton, 3);
    await I.scrollAndClick(orderButton);
    await I.saveScreenshot('1384.Test_item_No.18.1_Savings_application_completion_screen_isOpe.png');
    I.refreshPage();
    await I.waitFor();

});
// 1419.customer_reserve_petit_change_reserve_order_confirm_test.ts
Scenario('Test Item 15.3: Check Loading will be performed. Transition to "Savings - Petit Stocks - Savings Changes Completed', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 }); //09109903
    await I.waitFor(); ``
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="changeReserveOrderConfirm_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 1);
    await I.clickFixed(passwordInputCheck);
    await I.waitFor('shortWait');
    const confirmButton = '//*[@data-testid="changeReserveOrderConfirm_confirm_id"]';
    await I.waitForElement(confirmButton, 1);
    await I.clickFixed(confirmButton);
    await I.saveScreenshot('1419.Test_item_No.15.3_Check_Loading_will_be_performed_Transition_to_Savings_Petit_Stocks_Savings_Changes_Completed_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1432.customer_reserve_petit_cancel_reserve_order_confirm_test.ts
Scenario('Test Item 16.3: Loading redirects to cancel completion page: isOpe', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 });
    await I.waitFor();
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="cancelReserveOrder_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 1);
    await I.clickFixed(passwordInputCheck);
    await I.waitFor('shortWait');
    const confirmButton = '//*[@data-testid="cancelReserveOrder_confirm_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);
    await I.saveScreenshot('1432.Test_item_No.16.3_cancel_completion_page_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
//1476.customer_reserve_fund_reserve_order_confirm_test.ts
Scenario('Test Item 24.3: Check Loading will be performed. Transition to savings - investment trust - savings application completion: isOpe', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user52 });
    await I.waitFor();
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    await I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    await I.scrollAndClick(orderConfirmButton);
    await I.waitFor('mediumWait');
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");

    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 3);
    await I.scrollAndFill(passwordInput, 'isOpe');

    const confirmButton = '//*[@data-testid="fundReserveOrderConfirm_apply_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);
    await I.waitFor('mediumWait');
    await I.saveScreenshot('1476.Test_item_No.24.3_Check_Loading_will_be_performed_Transition_to_savings_investment_trust_savings_application_completion.png');
    I.refreshPage();
    await I.waitFor();
});
// 1509.customer_reserve_fund_change_reserve_order_confirm_test.ts
Scenario('Test Item 15.3: Check Execute loading - Transition to Accumulation - Investment Trust - Accumulation Change Complete: isOpe', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);

    const passwordInput = '//*[@data-testid="fundChangeReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, 'isOpe');

    const confirmButton = '//*[@data-testid="fundChangeReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollAndClick(confirmButton);
    await I.waitFor();
    await I.saveScreenshot('1509.Test_item_No.15.3_Check_Execute_loading_Transition_to_Accumulation_Investment_Trust_Accumulation_Change_Complete_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1523.customer_reserve_fund_cancel_reserve_order_confirm_test.ts
Scenario('Test Item 15.3: Check Execute loading - Transition to Accumulation - Investment Trust - Accumulation Cancel Complete:isOpe', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor();
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.waitFor()


    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, 'isOpe');

    const confirmButton = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollAndClick(confirmButton);
    await I.waitFor();
    await I.saveScreenshot('1523.Test_item_No.15.3_Check_Execute_loading_Transition_to_Accumulation_Investment_Trust_Accumulation_Cancel_Complete_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1537.reserve_point_usage_setting_test.ts
Scenario('Test Item 9: Check Set it up - Show Display completion message: isOpe', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user57 });
    await I.waitFor();
    await accumulation.goToReservePointUsageSetting();
    await I.waitFor();
    const setItUp = '//*[@data-testid="reservePointSetting_set_id"]';
    await I.waitForElement(setItUp, 1);
    await I.scrollAndClick(setItUp);
    await I.saveScreenshot('1537.Test_item_No.9_Check_Set_it_up_Show_Display_completion_message_isOpe.png');
    I.refreshPage();
    await I.waitFor();
});
// 1628.transfer_cash_confirm_test.ts
Scenario('Test Item 9-1: Go to transfer cash complete page: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user102 });
    await I.waitFor();
    await transferCash.goToCashConfirmPage();
    const passwordInput = '//*[@data-testid="transferCashConfirm_password_id"]';
    await I.waitForElement(passwordInput, 1);
    I.scrollAndFill(passwordInput, 'isOpe');
    // ========
    const transferCashInput_confirmScreen_id = '//*[@data-testid="transferCashConfirm_transfer_id"]';
    await I.waitForElement(transferCashInput_confirmScreen_id, 2);
    I.scrollAndClick(transferCashInput_confirmScreen_id);
    await I.waitFor();
    await I.saveScreenshot(`1628.Test_item_No.9-1_Go_to_transfer_cash_complete_page_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
// 1636.deposit_bank_account_registration_input_test.ts
Scenario(
    'Test Item 12-1: Go to confirmation screen -> Bank debit account registration -Move to confirmation : isOpe',
    async ({ I }) => {
        I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
        await I.waitFor();
        await depositBankAccountRegistration.goToInputPage();
        await I.waitFor();
        await depositBankAccountRegistration.focusAndFillField('$bankAccountRegistrationInput_password_id');

        await I.seeAndClickElement('$bankAccountRegistrationInput_toConfirmScreen_id');
        await I.saveScreenshot(`1636.Test_item_No.12-1_Go_to_confirmation_screen_Bank_debit_account_registration_Move_to_confirmation_isOpe.png`);
        I.refreshPage();
        await I.waitFor();

    },
);
// 1643.deposit_bank_account_registration_confirm_test.ts
Scenario('Test Item 12: Application: isOpe', async ({ I }) => {
    // await I.setSessionStorage('/bank-account-registration/depositbank/input', '{"bankcode":"0009"}');
    await I.setSessionStorage(SESSION_STORAGE_KEY.depositBankConfirm, '{"bankName":"三井住友銀行","nameKanji":"string string","nameKana":"string string","branchName":"string","temporaryRegistrationRequest":{"kinyuKikanCode":"0009","kinyuKikanShitenCode":"","kinyuKikanKouzaNo":"","kinyuKikanYokinSyubetsu":"YOKIN_SYUBETSU_FUTSUU","moushikomiDate":"2025-06-19T07:05:17.551Z","applyExecType":"APPLY_EXEC_TYPE_CHECK_ONLY","password":"asdasd"}}');
    await I.setSessionStorage(SESSION_STORAGE_KEY.depositBankInput, '{"shitenCode":"","kouzaNo":"","syubetsu":"YOKIN_SYUBETSU_FUTSUU"}');
    await I.setSessionStorage(SESSION_STORAGE_KEY.referralMailInput, '{"temporaryApplicationRequest":{"friends":[],"message":"","orderExecType":"ORDER_EXEC_TYPE_CHECK_ONLY"},"temporaryApplicationResponse":{"accountLastName":"string","accountFirstName":"string"}}');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
    await I.waitFor();
    I.amOnPage(PAGE_URL.bankAccountRegistrationConfirm);
    await I.waitFor('mediumWait');
    await I.scrollAndClick('//*[@data-testid="bankAccountRegistrationConfirm_apply_id"]');
    await I.waitFor();
    await I.saveScreenshot(`1636.Test_item_No.12_Application_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
//1674.easy_electronic_delivery_pre_contract_agreement_test.ts
Scenario('Test Item 9: Go to completion page: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user42 });
    await I.waitFor();

    await electronicDelivery.goToAgreementPage();
    await I.waitFor();

    await I.seeAndClickElement(electronicDelivery.locators.completeButton);
    await I.saveScreenshot(`1674.Test_item_No.9_Go_to_completion_page_isOpe.png`);

    I.refreshPage();
    await I.waitFor();
});

//1701.app_setting_test.ts
Scenario('Test item 9 Button Confirm Mail Notification: isOpe', async ({ I }) => {
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user59 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    AppSetting.goToPage()
    AppSetting.clickItem(locators.emailNotificationSettings);
    AppSetting.clickItem(locators.confirmBtn);
    await I.saveScreenshot(`1701.Test_item_No.9_Button_Confirm_Mail_Notification_isOpe.png`);

    I.refreshPage();
    await I.waitFor();
});
//1701.app_setting_test.ts - case 1752
Scenario('Test item 28 Button Confirm Priority Order: isOpe', async ({ I }) => {
    AppSetting.goToPage()
    await AppSetting.clickItem(locators.priorityOrderingMethodSettings);
    AppSetting.clickItem(locators.confirmBtn);
    await I.saveScreenshot(`1701.Test_item_No.28_Button_Confirm_Priority_Order_isOpe.png`);

    I.refreshPage();
    await I.waitFor();
});
//1701.app_setting_test.ts - case 1757
Scenario('Test item 54 Button Confirm Priority Order Future: isOpe', async ({ I }) => {
    AppSetting.goToPage()
    await AppSetting.clickItem(locators.priorityOrderMethodSettingFutures);
    AppSetting.clickItem(locators.confirmBtn);
    await I.saveScreenshot(`1701.Test_item_No.54_Button_Confirm_Priority_Order_Future_isOpe.png`);

    I.refreshPage();
    await I.waitFor();
});
//1701.app_setting_test.ts - case 1760
Scenario('Test item 32 Button Confirm Easy Setting: isOpe', async ({ I }) => {
    AppSetting.goToPage()
    await AppSetting.clickItem(locators.simpleScreenSettings);
    AppSetting.clickItem(locators.confirmBtn);
    await I.saveScreenshot(`1701.Test_item_No.32_Button_Confirm_Easy_Setting_isOpe.png`);

    I.refreshPage();
    await I.waitFor();
});
// 1773.card_agree_test.ts
Scenario('Test item 6 Next: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user60 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
    await SettingCard.goToPageCardAgree();
    await SettingCard.clickItem(locatorsSettingCard.agreeCheck);
    await SettingCard.clickItem(locatorsSettingCard.nextBtn);
    await I.saveScreenshot(`1773.Test_item_No.6_Next_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
// 1790.card_confirm_test.ts
Scenario('Test item 4 Register: isOpe', async ({ I }) => {
    await I.setSessionStorage(SESSION_STORAGE_KEY.cardAgree, SESSION_STORAGE_VALUE.cardAgree({ "crdNo4": "****-****- 1234" }));

    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user63 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
    await I.amOnPage(locatorsSettingCard.cardConfirm);
    await I.waitFor('mediumWait');
    SettingCard.fillInput(locatorsSettingCard.confirmPasswordInput, '111111');
    SettingCard.clickItem(locatorsSettingCard.confirmRegisterBtn);
    await I.waitFor('mediumWait');
    await I.saveScreenshot(`1790.Test_item_No.4_Register_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
//1796.card_common_register_test.ts
Scenario('Test item 12 Register: isOpe', async ({ I }) => {
    await I.setSessionStorage(SESSION_STORAGE_KEY.cardAgree, SESSION_STORAGE_VALUE.cardAgree({ "crdNo4": "****-****- 1234" }));

    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user64 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
    await SettingCard.goToPageCardCommonRegister();
    await SettingCard.clickItem(locatorsSettingCard.agreeCheckRegister);
    await SettingCard.clickItem(locatorsSettingCard.registerNextBtn);


    await SettingCard.fillInput(locatorsSettingCard.cardNumber, '1111111111');
    await SettingCard.fillInput(locatorsSettingCard.expiryDateMM, '12');
    await SettingCard.fillInput(locatorsSettingCard.expiryDateYY, '26');
    await SettingCard.fillInput(locatorsSettingCard.securityCode, '111111');
    await SettingCard.fillInput(locatorsSettingCard.inputPasswordRegister, '111111');
    await SettingCard.clickItem(locatorsSettingCard.registerBtn);
    await I.saveScreenshot(`1796.Test_item_No.12_Register_isOpe.png`);
    I.refreshPage();
    await I.waitFor();

});
//1810.card_common_unregister_test.ts
Scenario('Test item 5 UnRegister: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user64 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
    await SettingCard.goToPageCardCommonUnRegister();
    await SettingCard.fillInput(locatorsSettingCard.unRegisterPassword, '111111');
    await SettingCard.clickItem(locatorsSettingCard.unRegisterBtn);
    await I.saveScreenshot(`1810.Test_item_No.5_UnRegister_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
//1857.change_login_password_test.ts
Scenario('Test Item 4: Complete Password Change Form: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
    await ChangeLoginPassword.navigateToPage(ChangeLoginPassword.locators.pageDescription, ChangeLoginPassword.locators.url);
    await ChangeLoginPassword.fillPasswordField(ChangeLoginPassword.locators.currentPasswordInput, '123456');
    await I.waitFor();
    await ChangeLoginPassword.fillPasswordField(ChangeLoginPassword.locators.newPasswordInput, '654321');
    await I.waitFor();
    await ChangeLoginPassword.fillPasswordField(ChangeLoginPassword.locators.newPasswordConfirmInput, '654321');
    await I.waitFor();
    await ChangeLoginPassword.submitForm(
        ChangeLoginPassword.locators.descriptionSelector,
        ChangeLoginPassword.locators.submitButton,
        ChangeLoginPassword.locators.modalSelector,
        false,
    );
    await I.waitFor();
    await I.saveScreenshot(`1857.Test_item_No.4_Complete_Password_Change_Form_isOpe.png`);
    await I.refreshPage();
    await I.waitFor();

});
// 1863.change_withdrawal_password_test.ts
Scenario('1863 Test Item 5: Complete Password Change Form: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
    await ChangeLoginPassword.navigateToPage(
        '//*[@id="__next"]/div/div[2]/div/form/div[1]',
        '/mobile/setting/password/change-withdrawal-password',
    );
    await ChangeLoginPassword.fillPasswordField(changeWithdrawalPasswordLocators.currentPasswordInput, '123456');
    await ChangeLoginPassword.fillPasswordField(changeWithdrawalPasswordLocators.newPasswordInput, '654321');
    await ChangeLoginPassword.fillPasswordField(changeWithdrawalPasswordLocators.newPasswordConfirmInput, '654321');
    await ChangeLoginPassword.submitForm(
        changeWithdrawalPasswordLocators.descriptionSelector,
        changeWithdrawalPasswordLocators.submitButton,
        changeWithdrawalPasswordLocators.modalSelector,
        false,
    );
    await I.saveScreenshot(`1863.Test_item_No.5_Complete_Password_Change_Form_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
// 1870.reset_withDrawal_password_test.ts
Scenario('1870 Test Item 5: Complete Password Change Form: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
    // Display STEP1
    await ChangeLoginPassword.navigateToPage(
        resetWithdrawalPasswordLocators.pageDescription,
        resetWithdrawalPasswordLocators.resetWithDrawalUrl,
    );

    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.telephoneNumberInput, '123456');
    await I.scrollAndClick(resetWithdrawalPasswordLocators.issueOneTimeButton);
    await I.saveScreenshot(`1870.Test_item_No.5_Complete_Password_Change_Form_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
//1883.referral_test.ts
Scenario('Test Item 4: Refer by LINE: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
    I.amOnPage(PAGE_URL.referral);
    await I.waitFor('mediumWait');
    // Click on the LINE introduction link which opens an external URL
    await I.clickFixed(Referral.locators.lineIntroductionLink);
    await I.saveScreenshot(`1883.Test_item_No.4_Refer_by_LINE_isOpe.png`);
    I.refreshPage();
    await I.waitFor();
});
Scenario('Test Item 6: Issue referral URL: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
    I.amOnPage(PAGE_URL.referral);
    await I.waitFor('mediumWait');
    I.scrollAndClick(Referral.locators.snsTab);
    I.scrollAndClick(Referral.locators.snsButton);
});
// 1899.referral_mail_confirm_test.ts
Scenario('Test Item 7: Send: isOpe', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
    I.amOnPage(PAGE_URL.referralMailInput);
    await I.waitFor('mediumWait');
    I.see(
        `ご家族・ご友人
紹介プログラム`,
        '//*[@data-testid="common_header_title_id"]',
    );
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_page.png`);
    await ReferralMailInput.fillLastName('123456');
    await ReferralMailInput.fillFirstName('123456');
    await ReferralMailInput.fillLastNameKana('123456');
    await ReferralMailInput.fillFirstNameKana('123456');
    await ReferralMailInput.fillMailAddress('<EMAIL>');
    await ReferralMailInput.fillMessage('123456');
    await ReferralMailInput.clickConfirm();
    // Click on the send button
    const sendButton = '//*[@data-testid="referralMailConfirm_send_id"]';
    await I.clickFixed(sendButton);
    await I.saveScreenshot(`1899.Test_item_No.7_Send_isOpe.png`);
    I.refreshPage();
    await I.waitFor();

});
