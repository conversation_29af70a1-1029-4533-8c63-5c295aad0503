{"name": "kc-member-site-e2e-test", "version": "1.0.0", "private": true, "scripts": {"script:allow": "chmod +x ./scripts/*", "script:setup": "./scripts/setup.sh", "script:test-ios": "./scripts/run-tests.sh ios --steps", "script:test-android": "./scripts/run-tests.sh android --steps", "script:test-rtk-ios": "./scripts/run-tests.sh rtk.ios --steps", "script:test-rtk-android": "./scripts/run-tests.sh rtk.android --steps", "test:all-ios": "./scripts/run-platform-tests.sh ios range 1 1923", "test:all-android": "./scripts/run-platform-tests.sh android range 1 1923", "test:all": "npm run test:all-ios && npm run test:all-android", "test-ios": "npx codeceptjs run --steps --config codecept.ios.conf.ts", "test-android": "npx codeceptjs run --steps --config codecept.android.conf.ts", "test-ios-files": "./scripts/run-platform-tests.sh ios range", "test-android-files": "./scripts/run-platform-tests.sh android range", "test-ios-group": "./scripts/run-platform-tests.sh ios group", "test-android-group": "./scripts/run-platform-tests.sh android group", "test-rtk-ios": "npx codeceptjs run --steps --config codecept.rtk.ios.conf.ts", "test-rtk-android": "npx codeceptjs run --steps --config codecept.rtk.android.conf.ts", "test-android-debug": "npx codeceptjs run --steps --config codecept.android.conf.ts --debug --verbose", "test-android-debug-pause": "npx codeceptjs run --steps --config codecept.android.conf.ts --debug --verbose --plugins pauseOnFail", "test-ios-debug": "npx codeceptjs run --steps --config codecept.ios.conf.ts --debug --verbose", "test-ios-debug-pause": "npx codeceptjs run --steps --config codecept.ios.conf.ts --debug --verbose --plugins pauseOnFail", "report": "allure generate allure-results --clean && allure open", "def": "npx codeceptjs def", "check-android": "npx codeceptjs check --config codecept.android.conf.ts", "check-ios": "npx codeceptjs check --config codecept.ios.conf.ts", "gt": "npx codeceptjs generate:test", "gh": "npx codeceptjs generate:helper", "gpo": "npx codeceptjs generate:pageobject", "gso": "npx codeceptjs generate:object --type step", "lint": "eslint --fix", "format": "prettier --write .", "test:isope-ios": "./scripts/run-isope-tests.sh ios", "test:isope-android": "./scripts/run-isope-tests.sh android", "test:isope-ios:debug": "HEADLESS=false codeceptjs run --config=codecept.isope.ios.ts --verbose", "test:isope-android:debug": "HEADLESS=false codeceptjs run --config=codecept.isope.android.ts --verbose", "test:isope:single": "codeceptjs run", "test:all-isope": "npm run test && npm run test:isope-ios && npm run test:isope-android", "test:isope-ios:workers": "codeceptjs run-workers --config=codecept.isope.ios.ts", "test:isope-android:workers": "codeceptjs run-workers --config=codecept.isope.android.ts", "test:isope-ios:ui": "codeceptjs ui --config=codecept.isope.ios.ts", "test:isope-android:ui": "codeceptjs ui --config=codecept.isope.android.ts"}, "dependencies": {"app-root-path": "^3.1.0", "codeceptjs": "^3.7.2", "codeceptjs-chai": "^2.3.5", "dotenv": "^16.5.0", "webdriverio": "^9.12.0"}, "devDependencies": {"@codeceptjs/configure": "^1.0.2", "@eslint/js": "^9.22.0", "@types/node": "^22.13.10", "allure-codeceptjs": "^3.2.0", "chromedriver": "^137.0.4", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "globals": "^16.0.0", "prettier": "3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "wdio-intercept-service": "^4.4.1"}}