// Define custom steps here, use 'this' to access default methods of I.

// Use to avoid I.click error [element click intercepted: Element <...> is not clickable at point (x, y).] on Android
// see. https://stackoverflow.com/questions/73525437/elementclickinterceptedexception-element-click-intercepted-element-is-not-clic
export async function clickPieChart(this: CodeceptJS.I, locator: CodeceptJS.LocatorOrString): Promise<void> {
    const isAndroid = await this.grabIsAndroid();
    if (isAndroid) {
        const rect = await this.grabElementBoundingRectTyped(locator);
        const x = Math.round(rect.x) + 1;
        const y = Math.round(rect.y) + 1;
        await this.tapAction(x, y);
    } else {
        this.click(locator);
    }
}
