import { WaitConfig } from '../const/config';

const waitConfig: WaitConfig = {
    defaultWait: 1,
    shortWait: 0.5,
    mediumWait: 2,
    longWait: 3,
    extraLongWait: 5,
    android: {
        multiplier: parseFloat(process.env.ANDROID_MULTIPLIER || '1.0'),
    },
    ios: {
        multiplier: parseFloat(process.env.IOS_MULTIPLIER || '1.0'),
    },
};

const platform = process.env.PLATFORM;

/**
 * Wait for a specified amount of time
 * @param type - The type of wait time to use (defaultWait, shortWait, mediumWait, longWait, extraLongWait)
 * @param options.log - Whether to log the wait time
 */
export async function waitFor(
    type: keyof WaitConfig | number = 'defaultWait',
    options = { log: false },
): Promise<void> {
    // Calculate wait time
    let waitTime: number;

    if (typeof type === 'number') {
        // If a number is provided, use it directly
        waitTime = type;
    } else if (typeof type === 'string' && type in waitConfig) {
        // If a valid config key is provided, use that value
        waitTime = waitConfig[type] as number;
    } else {
        waitTime = waitConfig.defaultWait;
    }

    // Apply platform-specific multiplier
    if (platform === 'android') {
        waitTime *= waitConfig.android.multiplier;
    } else if (platform === 'ios') {
        waitTime *= waitConfig.ios.multiplier;
    }

    // Log if enabled
    if (options.log) {
        console.debug(`Waiting for ${waitTime} seconds`);
    }

    // Execute the wait
    this.wait(waitTime);
}
