const { I } = inject();

export async function login(accountNumber: string, password: string, isReloginAfterExpired?: boolean): Promise<void> {
    await I.switchToNative();
    // I.waitForElement('~ログイン', 10); // "~xxxx": xxxx = accessible id (use <PERSON>pp<PERSON> Inspector)
    await I.waitFor('mediumWait');
    const loginButton = '~ログイン';
    const isLoginButtonVisible = await I.grabNumberOfVisibleElements(loginButton);
    if (!isLoginButtonVisible) {
        I.switchToWeb();
        await I.waitFor();
        return
    }
    I.click({ android: '//*[@hint="口座番号を入力"]', ios: '//*[@name="口座番号を入力"]' }); // Accessible id are difficult to use in native contexts, so we use something like XPath, similar to WebDriver.IO.
    I.fillField({ android: '//*[@hint="口座番号を入力"]', ios: '//*[@name="口座番号を入力"]' }, accountNumber);
    await <PERSON><PERSON>waitFor('shortWait');
    I.click({ android: '//*[@hint="パスワードを入力"]', ios: '//*[@name="パスワードを入力"]' });
    I.fillField({ android: '//*[@hint="パスワードを入力"]', ios: '//*[@name="パスワードを入力"]' }, password);
    await I.waitFor('shortWait');
    if (await I.grabIsAndroid()) {
        // Hide the device keyboard only works on IOS if the language is English
        I.hideDeviceKeyboard();
    }
    await I.waitFor();
    I.click('~ログイン');
    await I.waitUntilHaveWebVewContext(60000, 2000);
    await I.waitFor('longWait');

    // switch to web view context
    // Check context
    console.debug('all contexts:', await I.grabAllContexts());
    console.debug('context:', await I.grabContext());
    await I.switchToWeb();
    await I.waitFor();
    // Check context
    console.debug('all contexts:', await I.grabAllContexts());
    console.debug('context:', await I.grabContext());
    if (isReloginAfterExpired) {
        await I.amOnPage('/');
        await I.waitFor('longWait');
        const currentUrl = await I.grabCurrentUrl();
        console.debug('Current URL', currentUrl);
    } else {
        const { tryTo } = require('codeceptjs/effects');
        const hasNotAuthenticationMailAddress = tryTo(
            () => I.waitForText('認証メールアドレスのご登録をお願いします', 3, 'body'), // The third argument is set to speed up the search.
        );
        if (hasNotAuthenticationMailAddress) {
            // Click goToTopPageButton '後で手続きをする(トップページへ)'
            I.click('//button[@data-testid="mailRegistrationGuidance_comeToTop_id"]');
            await I.waitFor();
        }
    }
}

export async function loginWithFilledInput(this: CodeceptJS.I): Promise<void> {
    await I.switchToNative();
    I.waitForElement('~ログイン', 10); // "~xxxx": xxxx = accessible id (use Appium Inspector)
    await I.waitFor();
    I.click('~ログイン');
    await I.waitUntilHaveWebVewContext(20000, 2000);
    await I.waitFor('longWait');

    // switch to web view context
    // Check context
    console.debug('all contexts:', await I.grabAllContexts());
    console.debug('context:', await I.grabContext());
    await I.switchToWeb();
    await I.waitFor();
    // Check context
    console.debug('all contexts:', await I.grabAllContexts());
    console.debug('context:', await I.grabContext());

    const { tryTo } = require('codeceptjs/effects');
    const hasNotAuthenticationMailAddress = tryTo(
        () => I.waitForText('認証メールアドレスのご登録をお願いします', 3, 'body'), // The third argument is set to speed up the search.
    );
    if (hasNotAuthenticationMailAddress) {
        // Click goToTopPageButton '後で手続きをする(トップページへ)'
        I.click('//button[@data-testid="mailRegistrationGuidance_comeToTop_id"]');
        await I.waitFor();
    }
}
