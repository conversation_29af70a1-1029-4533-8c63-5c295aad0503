import { WaitConfig } from '../const/config';

type WaitConfigKeys = keyof WaitConfig;
type WaitDuration = WaitConfigKeys | number;

/*
 * This function first checks if the element is visible on the page,
 * and if it is, it clicks the element (or elements) and waits for a specified duration.
 * @param locator - The locator(s) of the element(s) to be clicked.
 * @param options - Optional parameters.
 * @param options.waitFor - The duration to wait after clicking the element(s).
 * @param options.waitBetweenClicks - The delay between clicks when processing an array of locators.
 */
export async function seeAndClickElement(
    locator: CodeceptJS.LocatorOrString | CodeceptJS.LocatorOrString[],
    options?: {
        waitFor?: WaitDuration;
        waitBetweenClicks?: WaitDuration;
    },
): Promise<void> {
    const { waitFor, waitBetweenClicks } = options || {};

    // Handle single locator (backward compatibility)
    if (!Array.isArray(locator)) {
        this.seeElement(locator);
        await this.clickFixed(locator);
        await this.waitFor(waitFor || 'defaultWait');
        return;
    }

    // Handle array of locators
    const locators = locator as CodeceptJS.LocatorOrString[];

    if (locators.length === 0) {
        this.say('Empty locator array provided');
        return;
    }

    // Click all elements in sequence with optional delay
    for (let i = 0; i < locators.length; i++) {
        const currentLocator = locators[i];

        this.seeElement(currentLocator);
        await this.clickFixed(currentLocator);

        // Add delay between clicks if specified and not the last element
        if (i < locators.length - 1 && !!waitBetweenClicks) {
            await this.waitFor(options.waitBetweenClicks);
        }
    }

    // Wait for the specified duration after clicking all elements, if waitBetweenClicks is not set
    if (!waitBetweenClicks && waitFor) {
        await this.waitFor(waitFor);
    }
}
