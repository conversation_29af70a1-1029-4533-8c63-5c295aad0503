import dotenv from 'dotenv';
import { isOpeBaseConfig } from './codecept.isope.base.conf'; 
import { CapabilityFactory } from './helpers/CapabilityFactory';

// Load Android environment variables
const result = dotenv.config({ path: '.env.isope.android', override: true });

if (result.error) {
    console.error('Environment file not found: .env.isope.android');
    process.exit(1);
}

const androidAppiumHelperConfig = {
    Appium: {
        ...isOpeBaseConfig.helpers.Appium,
        platform: 'android',
        desiredCapabilities: CapabilityFactory.createAndroidCapabilities(),
    },
};

const config = { ...isOpeBaseConfig };
config.helpers = { ...config.helpers, ...androidAppiumHelperConfig };

exports.config = config;
