import dotenv from 'dotenv';
import { baseConfig } from './codecept.base.conf';
import { CapabilityFactory } from './helpers/CapabilityFactory';

// Load iOS environment variables
const result = dotenv.config({ path: '.env.rtk.ios', override: true });

if (result.error) {
    console.error('Environment file not found: .env.rtk.ios');
    process.exit(1);
}

const iosAppiumHelperConfig = {
    Appium: {
        ...baseConfig.helpers.Appium,
        // Remote TestKit
        hostname: 'gwjp.appkitbox.com',
        port: 443, // Port for HTTPS
        path: '/wd/hub',
        protocol: 'https',
        noSessionOverwriting: true,
        // iOS
        platform: 'iOS',
        desiredCapabilities: {
            ...CapabilityFactory.createIOSCapabilities(),
            accessToken: process.env.RTK_ACCESS_TOKEN,
            app: process.env.IOS_APP_PATH,
            additionalWebviewBundleIds: ['*'],
        },
    },
};

const config = { ...baseConfig };
config.helpers = { ...config.helpers, ...iosAppiumHelperConfig };

exports.config = config;
