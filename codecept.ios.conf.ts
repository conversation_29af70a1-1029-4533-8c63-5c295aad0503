import dotenv from 'dotenv';
import { baseConfig } from './codecept.base.conf';
import { CapabilityFactory } from './helpers/CapabilityFactory';

// Load iOS environment variables
const result = dotenv.config({ path: '.env.ios', override: true });

if (result.error) {
    console.error('Environment file not found: .env.ios');
    process.exit(1);
}

const iosAppiumHelperConfig = {
    Appium: {
        ...baseConfig.helpers.Appium,
        platform: 'ios',
        desiredCapabilities: CapabilityFactory.createIOSCapabilities(),
    },
};

const config = { ...baseConfig };
config.helpers = { ...config.helpers, ...iosAppiumHelperConfig };

exports.config = config;
