export const testIsOpeSubmit = async (I: CodeceptJS.I, options: {
    navigateToPage: () => Promise<void>,
    submitButtonSelector: string,
    screenshotName: string
}) => {
    const { navigateToPage, submitButtonSelector, screenshotName } = options;

    // Navigate to target page
    await navigateToPage();
    await I.waitFor();

    // Find and click submit button
    await I.waitForElement(submitButtonSelector, 5);
    await I.clickFixed(submitButtonSelector);
    await I.waitFor();

    // Verify error dialog appears
    const errorDialog = '//*[contains(@text, "opeエラー")]';
    await I.waitForElement(errorDialog, 5);
    
    // Take screenshot
    I.saveScreenshot(`${screenshotName}_ope_error_dialog.png`);
    
    // Verify no subsequent processing
    await I.waitFor(2); // Wait to ensure no further actions
};