import { setCommonPlugins, setHeadlessWhen } from '@codeceptjs/configure';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// turn on headless mode when running with HEADLESS=true environment variable
setHeadlessWhen(process.env.HEADLESS);

// enable all common plugins
setCommonPlugins();

// CodeceptJS Shared Configuration
export const baseConfig: CodeceptJS.MainConfig = {
    tests: process.env.TEST_PATH_PATTERN || './tests/*.ts',
    output: './output',
    helpers: {
        Appium: {
            appiumV2: true,
            platform: process.env.PLATFORM || 'android',
            // host: 'localhost', // Appium default
            // port: 4723, // Appium default
            // path: '/wd/hub', // Appium default
            url: process.env.APP_URL || 'https://st.dua2b-stub.kcmsr.dev.guide.inc',
            smartWait: parseInt(process.env.SMART_WAIT || '3000'),
            waitForTimeout: parseInt(process.env.WAIT_TIMEOUT || '3000'),
        },
        AppiumSupportHelper: {
            require: './helpers/AppiumSupportHelper.ts',
        },
        RequestsInterceptHelper: {
            require: './helpers/RequestsInterceptHelper.ts',
        },
        ChaiWrapper: {
            require: 'codeceptjs-chai',
        },
        ScrollHelper: {
            require: './helpers/ScrollHelper.ts',
        },
        NavigationHelper: {
            require: './helpers/NavigationHelper.ts',
        },
    },
    plugins: {
        customLocator: {
            enabled: true,
            prefix: '$',
            attribute: 'data-testid',
        },
        auth: {
            enabled: true,
            saveToFile: true,
            inject: 'loginAndSwitchToWebAs',
            users: {
                user1: {
                    login: async (I: CodeceptJS.I) => {
                        console.debug('login');
                        const sessionId = await I.grabSessionId();
                        console.log('Session log:', `https://gwjp.appkitbox.com/wd/hub/session/${sessionId}/server_log`)
                        await I.login(process.env.LOGIN_USERNAME || '09109911', process.env.LOGIN_PASSWORD || '111111');
                    },
                    check: async (I: CodeceptJS.I) => {
                        console.debug('check');
                        if (await I.existWebVewContext()) {
                            await I.switchToWeb();
                        }
                        await I.waitFor();
                        I.waitForElement('//*[@data-testid="common_menu_id"]', 3);
                    },
                    restore: () => { }, // don't restore cookie
                },
            },
        },
        allure: {
            enabled: true,
            require: 'allure-codeceptjs',
        },
        stepByStepReport: {
            enabled: true,
            screenshotsForAllureReport: true,
            deleteSuccessful: false,
        },
        retryFailedStep: {
            enabled: true,
        },
        autoDelay: {
            enabled: true,
            methods: [
                'click',
                'fillField',
                'checkOption',
                'pressKey',
                'doubleClick',
                'rightClick',
                'hideDeviceKeyboard',
                'clickPieChart',
                'clickFixed',
                'swipeLeftFixedWithOffset',
                'swipeRightFixedWithOffset',
                'swipeLeftFixed',
                'swipeRightFixed',
                'swipeUpFixed',
                'swipeDownFixed',
                'tapLocationOfElement',
                'tapAction',
                'swipeAction',
                'pressWithoutReleaseLocationOfElement',
                'pressWithoutReleaseAction',
                'releaseAction',
                'getSessionStorage',
            ],
            delayBefore: '100',
            delayAfter: '500',
        },
        tryTo: {
            enabled: false, // Deprecated in codeceptjs v3.7
        },
        retryTo: {
            enabled: false, // Deprecated in codeceptjs v3.7
        },
    },
    include: {
        I: './steps_file',
        timeLine: './pages/timeLine.ts',
        hamburgerMenu: './pages/hamburgerMenu.ts',
        market: './pages/market.ts',
        portfolioTopPage: './pages/portfolioTop.ts',
        investPerfAssetBalancePage: './pages/investPerfAssetBalance.ts',
        stockDetailBasicPage: './pages/stockDetailBasic.ts',
        investmentProfitLossPage: './pages/investmentProfitLoss.ts',
        newsList: './pages/newsList.ts',
        newsDetail: './pages/newsDetail.ts',
        stockOrderBuyInput: './pages/stockOrderBuyInput.ts',
        search: './pages/search.ts',
        stockBuyInput: './pages/stockBuyInput.ts',
        orderStatus: './pages/orderStatus.ts',
        stockMarginOrder: './pages/stockMarginOrder.ts',
        stockMarginPaymentOrder: './pages/stockMarginPaymentOrder.ts',
        stockPetitOrder: './pages/stockPetitOrder.ts',
        stockMarginCorrect: './pages/stockMarginCorrect.ts',
        stockMarginDelivery: './pages/stockMarginDelivery.ts',
        stockMarginCancel: './pages/stockMarginCancel.ts',
        stockMarginReceiptOrder: './pages/stockMarginReceiptOrder.ts',
        accumulation: './pages/accumulation.ts',
        stockCashOrder: './pages/stockCashOrder.ts',
        dividendHistory: './pages/dividendHistory.ts',
        infoFund: './pages/infoFund.ts',
    },
    name: 'kc-member-site-e2e-test',
};
