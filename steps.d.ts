/// <reference types='codeceptjs' />
type steps_file = typeof import('./steps_file');
type portfolioTopPage = typeof import('./pages/portfolioTop');
type timeLine = typeof import('./pages/timeLine');
type market = typeof import('./pages/market');
type hamburgerMenu = typeof import('./pages/hamburgerMenu');
type investPerfAssetBalancePage = typeof import('./pages/investPerfAssetBalance');
type stockDetailBasicPage = typeof import('./pages/stockDetailBasic');
type stockBuyInputPage = typeof import('./pages/stockBuyInput');
type investmentProfitLossPage = typeof import('./pages/investmentProfitLoss');
type stockOrderBuyInput = typeof import('./pages/stockOrderBuyInput');
type indicator = typeof import('./pages/indicator');
type newsList = typeof import('./pages/newsList');
type newsDetail = typeof import('./pages/newsDetail');
type search = typeof import('./pages/search');
type AppiumSupportHelper = import('./helpers/AppiumSupportHelper');
type RequestsInterceptHelper = import('./helpers/RequestsInterceptHelper');
type ChaiWrapper = import('codeceptjs-chai');
type orderStatus = typeof import('./pages/orderStatus');
type stockMarginOrder = typeof import('./pages/stockMarginOrder');
type stockMarginPaymentOrder = typeof import('./pages/stockMarginPaymentOrder');
type stockPetitOrder = typeof import('./pages/stockPetitOrder');
type stockMarginCorrect = typeof import('./pages/stockMarginCorrect');
type stockMarginDelivery = typeof import('./pages/stockMarginDelivery');
type stockMarginCancel = typeof import('./pages/stockMarginCancel');
type stockMarginReceiptOrder = typeof import('./pages/stockMarginReceiptOrder');
type accumulation = typeof import('./pages/accumulation');
type stockCashOrder = typeof import('./pages/stockCashOrder');
type dividendHistory = typeof import('./pages/dividendHistory');
type ScrollHelper = import('./helpers/ScrollHelper');
type NavigationHelper = import('./helpers/NavigationHelper');
type infoFund = import('./pages/infoFund');
type closeIsOpe = import('./pages/closeIsOpe');
declare namespace CodeceptJS {
    interface SupportObject {
        I: I;
        current: any;
        timeLine: timeLine;
        hamburgerMenu: hamburgerMenu;
        market: market;
        loginAndSwitchToWebAs: any;
        portfolioTopPage: portfolioTopPage;
        investPerfAssetBalancePage: investPerfAssetBalancePage;
        stockDetailBasicPage: stockDetailBasicPage;
        stockBuyInputPage: stockBuyInputPage;
        investmentProfitLossPage: investmentProfitLossPage;
        indicator: indicator;
        newsList: newsList;
        newsDetail: newsDetail;
        stockOrderBuyInput: stockOrderBuyInput;
        search: search;
        orderStatus: orderStatus;
        stockMarginOrder: stockMarginOrder;
        stockMarginPaymentOrder: stockMarginPaymentOrder,
        stockPetitOrder: stockPetitOrder,
        stockMarginCorrect: stockMarginCorrect;
        stockMarginDelivery: stockMarginDelivery;
        stockMarginCancel: stockMarginCancel;
        stockMarginReceiptOrder: stockMarginReceiptOrder;
        accumulation: accumulation;
        stockCashOrder: stockCashOrder;
        dividendHistory: dividendHistory;
        ScrollHelper: ScrollHelper;
        NavigationHelper: NavigationHelper;
        infoFund: infoFund;
        closeIsOpe: closeIsOpe;
    }
    interface Methods extends Appium, AppiumSupportHelper, RequestsInterceptHelper, ChaiWrapper, ScrollHelper, NavigationHelper {}
    interface I extends ReturnType<steps_file>, WithTranslation<Methods> {
        grabWebView(): Promise<any>;
    }
    namespace Translation {
        interface Actions {}
    }
}
