import { setHeadlessWhen } from '@codeceptjs/configure';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: `.env.isope.${process.env.PLATFORM}`, override: true });

// turn on headless mode when running with HEADLESS=true environment variable
setHeadlessWhen(process.env.HEADLESS);


// isOpe Base Configuration (NO autoLogin plugin)
export const isOpeBaseConfig: CodeceptJS.MainConfig = {
    tests: process.env.TEST_PATH_PATTERN || './test_isope/*.ts',
    output: './output',
    helpers: {
        Appium: {
            appiumV2: true,
            platform: process.env.PLATFORM || 'android',
            // host: 'localhost', // Appium default
            // port: 4723, // Appium default
            // path: '/wd/hub', // Appium default
            url: process.env.APP_URL || 'https://auto-test-stub.kcmsr.dev.guide.inc',
            smartWait: parseInt(process.env.SMART_WAIT || '3000'),
            waitForTimeout: parseInt(process.env.WAIT_TIMEOUT || '3000'),
        },
        AppiumSupportHelper: {
            require: './helpers/AppiumSupportHelper.ts',
        },
        RequestsInterceptHelper: {
            require: './helpers/RequestsInterceptHelper.ts',
        },
        ChaiWrapper: {
            require: 'codeceptjs-chai',
        },
        ScrollHelper: {
            require: './helpers/ScrollHelper.ts',
        },
        NavigationHelper: {
            require: './helpers/NavigationHelper.ts',
        },
    },
    mocha: {
        retries: parseInt(process.env.MAX_RETRY_ATTEMPTS || '3'), // Retry up to `MAX_RETRY_ATTEMPTS` times (default is 3) for any failed Scenario
    },
    plugins: {
        customLocator: {
            enabled: true,
            prefix: '$',
            attribute: 'data-testid',
        },
       
        allure: {
            enabled: true,
            require: 'allure-codeceptjs',
        },
        stepByStepReport: {
            enabled: true,
            screenshotsForAllureReport: true,
            deleteSuccessful: false,
        },
        retryFailedStep: {
            enabled: true,
        },
        autoDelay: {
            enabled: true,
            methods: [
                'click',
                'fillField',
                'checkOption',
                'pressKey',
                'doubleClick',
                'rightClick',
                'hideDeviceKeyboard',
                'clickPieChart',
                'clickFixed',
                'swipeLeftFixedWithOffset',
                'swipeRightFixedWithOffset',
                'swipeLeftFixed',
                'swipeRightFixed',
                'swipeUpFixed',
                'swipeDownFixed',
                'tapLocationOfElement',
                'tapAction',
                'swipeAction',
                'pressWithoutReleaseLocationOfElement',
                'pressWithoutReleaseAction',
                'releaseAction',
                'getSessionStorage',
            ],
            delayBefore: '100',
            delayAfter: '500',
        },
        tryTo: {
            enabled: false, // Deprecated in codeceptjs v3.7
        },
        retryTo: {
            enabled: false, // Deprecated in codeceptjs v3.7
        },
    },
    include: {
        I: './steps_file',
        timeLine: './pages/timeLine.ts',
        hamburgerMenu: './pages/hamburgerMenu.ts',
        market: './pages/market.ts',
        portfolioTopPage: './pages/portfolioTop.ts',
        investPerfAssetBalancePage: './pages/investPerfAssetBalance.ts',
        stockDetailBasicPage: './pages/stockDetailBasic.ts',
        investmentProfitLossPage: './pages/investmentProfitLoss.ts',
        newsList: './pages/newsList.ts',
        newsDetail: './pages/newsDetail.ts',
        stockOrderBuyInput: './pages/stockOrderBuyInput.ts',
        search: './pages/search.ts',
        stockBuyInput: './pages/stockBuyInput.ts',
        orderStatus: './pages/orderStatus.ts',
        stockMarginOrder: './pages/stockMarginOrder.ts',
        stockMarginPaymentOrder: './pages/stockMarginPaymentOrder.ts',
        stockPetitOrder: './pages/stockPetitOrder.ts',
        stockMarginCorrect: './pages/stockMarginCorrect.ts',
        stockMarginDelivery: './pages/stockMarginDelivery.ts',
        stockMarginCancel: './pages/stockMarginCancel.ts',
        stockMarginReceiptOrder: './pages/stockMarginReceiptOrder.ts',
        accumulation: './pages/accumulation.ts',
        stockCashOrder: './pages/stockCashOrder.ts',
        dividendHistory: './pages/dividendHistory.ts',
        infoFund: './pages/infoFund.ts',
    },
    name: 'kc-member-site-e2e-test',
};