import { COOKIE_KEY, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE, USER_ID } from '../const/constant';
import depositBankAccountRegistration from '../pages/depositBankAccountRegistration';

Feature('Deposits_Withdrawals - DepositBankAccountRegistration');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Deposit Bank Account Registration-Input', async ({ I }) => {
    await I.setSessionStorage(SESSION_STORAGE_KEY.depositBankInput, SESSION_STORAGE_VALUE.depositBankInput());
    await I.waitFor();
    await depositBankAccountRegistration.goToInputPage();
    I.saveScreenshot(`${depositBankAccountRegistration.prefix.input}.0_Deposit_Bank_Account_Registration-Input.png`);
});

// 6.店番コード -> 数値キーボードを開く
Scenario('Test Item 6: Store code -> Open the numeric keyboard', async ({ I }) => {
    await depositBankAccountRegistration.focusAndFillField('$bankAccountRegistrationInput_branchCode_id');
    I.saveScreenshot(`${depositBankAccountRegistration.prefix.input}.6_Store_code-Open_the_numeric_keyboard.png`);
});

// 7.口座番号 -> 数値キーボードを開く
Scenario('Test Item 7: Account number -> Open the numeric keyboard', async ({ I }) => {
    await depositBankAccountRegistration.focusAndFillField('$bankAccountRegistrationInput_accountID_id');
    I.saveScreenshot(`${depositBankAccountRegistration.prefix.input}.7_account_number-Open_the_numeric_keyboard.png`);
});

// 8.預金科目 -> タップした項目を選択状態とする
Scenario('Test Item 8: Deposit subject -> Select the item you tapped', async ({ I }) => {
    const count = await I.grabNumberOfVisibleElements(depositBankAccountRegistration.locators.depositSubjectItem);
    if (count > 0) {
        for (let i = 1; i <= count; i++) {
            await I.seeAndClickElement(`${depositBankAccountRegistration.locators.depositSubjectItem}[${i}]`);
        }
        I.saveScreenshot(
            `${depositBankAccountRegistration.prefix.input}.8_Deposit_subject-Select_the_item_you_tapped.png`,
        );
    } else {
        I.say("I don't see any deposit subject items");
    }
});

// 11.パスワード -> 英数字記号のテキスト入力フォームを開く
Scenario('Test Item 11: Password -> Open the alphanumeric text input form', async ({ I }) => {
    await depositBankAccountRegistration.focusAndFillField('$bankAccountRegistrationInput_password_id');
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.input}.11_Password-Open_the_alphanumeric_text_input_form.png`,
    );
});

// 12.確認画面へ -> 銀行引落口座登録-確認画面に遷移
Scenario(
    'Test Item 12-2: Go to confirmation screen -> Bank debit account registration -Move to confirmation ',
    async ({ I }) => {
        await I.seeAndClickElement('$bankAccountRegistrationInput_toConfirmScreen_id');
        I.waitForText('銀行引落口座\n登録申込', 5, 'p');
        I.seeInCurrentUrl(depositBankAccountRegistration.urls.confirm);
        I.saveScreenshot(`${depositBankAccountRegistration.prefix.input}.12-2_Go_to_confirmation_screen.png`);
    },
);
