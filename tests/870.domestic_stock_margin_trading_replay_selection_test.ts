import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingRelaySelection');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
    await stockMarginOrder.goToMarginNewRelaySelection();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading Relay selection', async ({ I, stockMarginOrder }) => {
    I.see('リレー選択信用取引', 'body');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/trade/margin/new', 'URL does not contain expected path');
    stockMarginOrder.takeScreenshot.marginRelaySelection('Display_Domestic_Stock_Margin_Trading_Relay_selection');
});

Scenario('Test item No.1 Stock selection', async ({ I, stockMarginOrder }) => {
    const symbolFilterSelector = '//*[@data-testid="marginRelay_symbolFilter_id"]';
    await I.clickFixed(symbolFilterSelector);
    await I.waitFor();
    stockMarginOrder.takeScreenshot.marginRelaySelection('Test_item_No.1_A_drop_down_menu_will_be_displayed');
});

Scenario('Test item No.2 Sort', async ({ I, stockMarginOrder }) => {
    const sortSelector = '//*[@data-testid="marginRelay_sort_id"]';
    await I.clickFixed(sortSelector);
    await I.waitFor();
    stockMarginOrder.takeScreenshot.marginRelaySelection('Test_item_No.2_A_drop_down_menu_will_be_displayed');
});

Scenario('Test item No.3+4 Number page', async ({ I, stockMarginOrder }) => {
    const secondPageNumberSelector = '//button[contains(text(), "2")]';
    await I.clickFixed(secondPageNumberSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(secondPageNumberSelector, 'color'),
        'rgba(255, 86, 0, 1)',
        'Color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginRelaySelection('Test_item_No.3_4_Perform_common_UI_pagination_processing');
});

Scenario('Test item No.5 Order list - Tap', async ({ I, stockMarginOrder }) => {
    const checkBoxItemSelector =
        '(//table[@data-testid="marginRelay_orderList_id"]//label[input[contains(@type, "checkbox")]])[1]';
    const checkBoxIconItemSelector = `${checkBoxItemSelector}/div/div/div[1]`;
    await I.clickFixed(checkBoxItemSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(checkBoxIconItemSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginRelaySelection(
        'Test_item_No.5_Select_the_order_you_tapped_and_deselect_the_rest',
    );
});

Scenario('Test item No.6 Order confirmation', async ({ I, stockMarginOrder }) => {
    const checkBoxItemSelector =
        '(//table[@data-testid="marginRelay_orderList_id"]//label[input[contains(@type, "checkbox")]])[1]';
    const confirmButtonSelector = '//*[@data-testid="marginRelay_orderConfirmButton_id"]';
    await I.clickFixed(checkBoxItemSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor();
    stockMarginOrder.takeScreenshot.marginRelaySelection(
        'Test_item_No.6_On_the_original_screen_place_a_provisional_order_using_the_parameters_selected_on_this_screen',
    );
});
