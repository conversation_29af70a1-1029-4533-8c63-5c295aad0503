import { COOKIE_KEY, USER_ID } from '../const/constant';
import depositBankAccountRegistration from '../pages/depositBankAccountRegistration';
import common from '../pages/search/common';

Feature('Deposits_Withdrawals - DepositBankAccountRegistration');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Deposit Bank Account Registration-Cancel Complete', async ({ I }) => {
    await depositBankAccountRegistration.goToCancelCompletePage();
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.complete}.0_Deposit_Bank_Account_Registration-Cancen_Complete.png`,
    );
});

// 1.らくらく電子契約 -> 以下のURLに遷移
// {member-site-url}/iphone/personal/dkstatus/dk01101.asp
Scenario('Test Item 1: Deposit Bank Account Registration-Cancel Complete', async ({ I }) => {
    await depositBankAccountRegistration.goToCancelCompletePage();
    const item = '//*[@data-testid="bankAccountRegistrationCancelComplete_easyElectronicContract_id"]';
    await I.scrollToElement(item);
    await common.clickCardItem(item, '/iphone/personal/dkstatus/dk01101.asp', 'kcMemberSite');
});
