import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Feature('Symbol_ProductSearch - SearchDomesticStockThemeDetail');

Before(async ({ I, loginAndSwitchToWebAs, search }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user18 });
    await search.goToDomesticStockThemeList();
    await search.goToDomesticStockThemeDetail();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Theme detail page', async ({ I }) => {
    I.assertContain(await I.grabCurrentUrl(), '/search/theme/detail', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Display_Domestic_Stock_Theme_detail_page.png`);
});

Scenario('Test item No.4 Swipe left and right Theme list', async ({ I }) => {
    const themeListSelector = '//*[@data-testid="themeDetail_themeList_id"]';
    await I.swipeLeftFixed(themeListSelector);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.4_Swipe_left_theme_list.png`);
    await I.swipeRightFixed(themeListSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.4_Swipe_right_theme_list.png`);
});

Scenario('Test item No.1 Parent/child theme name link', async ({ I }) => {
    const parentThemeNameLink = '//*[@data-testid="themeDetail_themeName_id"]';
    await I.clickFixed(parentThemeNameLink);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.1_Click_parent_theme_name_link.png`);
});

Scenario('Test item No.3 See all', async ({ I }) => {
    const seeAllSelector = '//*[@data-testid="themeDetail_viewAll_id"]';
    await I.clickFixed(seeAllSelector);
    await I.waitFor();
    I.waitForElement('#bottom-sheet-container');
    I.see('関連テーマ', 'body');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.3_Show_related_themes_modal.png`);
});

Scenario('Test item No.5 Individual Themes', async ({ I }) => {
    const secondIndividualThemeItemelector = '//*[@data-testid="themeDetail_indiviualTheme_id_1"]';
    await I.clickFixed(secondIndividualThemeItemelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.5_Click_individual_theme_item.png`);
});

Scenario('Test item No.13 Individual Themes', async ({ I }) => {
    const seeAllSelector = '//*[@data-testid="themeDetail_viewAll_id"]';
    const secondIndividualThemeItemSelector = '//*[@data-testid="themeDetail_indiviualTheme_id_1"]';
    const secondIndividualThemeItemText = await I.grabTextFrom(secondIndividualThemeItemSelector);
    const secondIndividualThemeItemModalSelector = `//div[@id="bottom-sheet-container"]//button[contains(text(), "${secondIndividualThemeItemText}")]`;
    await I.clickFixed(seeAllSelector);
    await I.waitFor();
    await I.clickFixed(secondIndividualThemeItemModalSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.13_Click_individual_theme_item.png`);
});

Scenario('Test item No.7 Related Stock Details', async ({ I }) => {
    const relatedStockItemSelector = '//*[@data-testid="themeDetail_cardItem_id_0"]';
    await I.clickFixed(relatedStockItemSelector);
    await I.waitFor('mediumWait');
    I.see('銘柄詳細', 'body');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/stock/basic', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.7_Click_Related_Stock_go_to_detail_page.png`);
});

Scenario('Test item No.11 Close related theme modal', async ({ I }) => {
    const seeAllSelector = '//*[@data-testid="themeDetail_viewAll_id"]';
    const closeButtonSelector = '//*[@data-testid="themeDetail_close_id"]';
    await I.clickFixed(seeAllSelector);
    await I.waitFor();
    await I.clickFixed(closeButtonSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeDetail}_Test_item_No.11_Close_related_theme_modal.png`);
});
