import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('InvestmentProducts - DomesticStockDetailQuarterlyReport');

Before(async ({ I, loginAndSwitchToWebAs, stockDetailBasicPage }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToDetailQuarterlyTab();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Detail Quarterly report', async ({ stockDetailBasicPage }) => {
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/quarterly');
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Display_Domestic_Stock_Detail_Quarterly_report');
});

Scenario('Test item No.2 Previous', async ({ I, stockDetailBasicPage }) => {
    const previousButtonSelector = '//div[contains(@class, "slick-prev")]';
    await I.clickFixed(previousButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.boardInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/board');
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.2_Click_previous_go_to_Detail_Quote_tab');
});

Scenario('Test item No.3 Detail Quote display tab', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.detailQuoteTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.boardInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/board');
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.3_Click_Detail_Quote_tab');
});

Scenario('Test item No.5 Next', async ({ I, stockDetailBasicPage }) => {
    const nextButtonSelector = '//div[contains(@class, "slick-next")]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.financialInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/financial');
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.5_Click_next_go_to_detail_financial_tab');
});

Scenario('Test item No.4 Financial tab', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.financialTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.financialInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/financial');
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.4_Click_Financial_tab');
});

Scenario('Test item No.23 url', async () => {
    const url = '//tr[td[contains(text(), "URL")]]/td[2]//a';
    // Open URL in a new tab
    await common.clickCardItem(url, 'https://www.itoen.jp/', 'external');
});

Scenario('Test item No.103 url', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user77 });
    await stockDetailBasicPage.goToDetailQuarterlyTab();
    const url = '//tr[td[contains(text(), "URL")]]/td[2]//a';
    // Open URL in a new tab
    await common.clickCardItem(url, 'https://www.ichigo-hotel.co.jp/', 'external');
});

Scenario('Test item No.120 How to read and use the Nikkei Financial Quarterly Report', async ({ I }) => {
    const capitalTransferTab = '//button[contains(text(), "資本異動")]';
    const nikkeiFinalcialQuarterlyReportUrl = '//a[contains(text(), "四季報の見方・使い方 ▶")]';
    await I.clickFixed(capitalTransferTab);
    await I.waitFor();
    // Go to the following URL in a new tab: https://kabu.com/investment/market/quarterly/guide.html
    await common.clickCardItem(nikkeiFinalcialQuarterlyReportUrl, 'https://kabu.com/investment/market/quarterly/guide.html', 'external');
});

Scenario('Test item No.121 How to read and use ETFs/REITs', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user77 });
    await stockDetailBasicPage.goToDetailQuarterlyTab();
    const etfsReitsUrl = '//a[contains(text(), "ETF/REITの見方・使い方 ▶")]';
    await I.waitFor();
    // Go to the following URL in a new tab: https://kabu.com/investment/market/quarterly/etfguide.html
    await common.clickCardItem(etfsReitsUrl, 'https://kabu.com/investment/market/quarterly/etfguide.html', 'external');
});

Scenario('Test item No.6 Scroll Quarterly Information', async ({ I, stockDetailBasicPage }) => {
    await I.swipeUpFixed('//*[@data-testid="tabPanelsSwipe_3"]/div[1]');
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.6_Scroll_Quarterly_information');
});

Scenario('Test item No.43 List of Achievements', async ({ I, stockDetailBasicPage}) => {
    const performanceAndFinancesTabSelector = '//button[.//p[contains(text(), "業績・財務")]]';
    const achievementsListSelector = '.simplebar-content-wrapper';
    await I.clickFixed(performanceAndFinancesTabSelector);
    await I.waitFor();
    await I.swipeLeftFixedWithOffset(achievementsListSelector, 100);
    await I.waitFor();
    await stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.43_Scroll_right_List_of_Achievements');
    await I.swipeRightFixedWithOffset(achievementsListSelector, 100);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailQuarterlyReport('Test_item_No.43_Scroll_left_List_of_Achievements');
});
