import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveFundChangeReserveOrderInput');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Fund Change ReserveOrder Input', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.saveScreenshot('1491.Test_item_No.0_UI_of_Reserve-Fund-Change-ReserveOrder-Input.png');
});
Scenario('Test Item 7: Payment method Tap Select the item you tapped', async ({ I, accumulation }) => {
    const paymentMethodLabels = '//*[@data-testid="fundChangeReserveOrderInput_paymentMethod_id"]//label';

    // scroll to label
    I.waitForElement(paymentMethodLabels, 1);
    I.scrollToElement(paymentMethodLabels);

    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(paymentMethodLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${paymentMethodLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1491.Test_item_No.7_Payment_Method_Label_${i}.png`);
    }
});
Scenario('Test Item 7a: Check Payment method help tooltip', async ({ I }) => {
    const paymentMethodHelp = '//p[contains(text(), "決済方法")]';
    await I.waitForElement(paymentMethodHelp, 1);
    I.clickFixed(paymentMethodHelp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.7a_Payment_Method_Help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 7c: Check Mitsubishi UFJ Card - Register', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user100 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    const mitsubishiUfjCardRegister = '//*[@data-testid="cardCommon_mitsubishiUFJCardRegister_id"]';
    await I.waitForElement(mitsubishiUfjCardRegister, 1);
    I.clickFixed(mitsubishiUfjCardRegister);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.7c_Mitsubishi_UFJ_Card_Register.png');
});
Scenario('Test Item 7d: Check Mitsubishi UFJ Card - More information', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user100 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    const mitsubishiUfjCardMoreInformation = '//*[@data-testid="cardCommon_mitsubishiUFJCardDetailsHere_id"]';
    await I.waitForElement(mitsubishiUfjCardMoreInformation, 2);
    I.scrollToElement(mitsubishiUfjCardMoreInformation);
    await common.clickCardItem(mitsubishiUfjCardMoreInformation, 'https://kabu.com/item/fund/mitsubishi_ufj_card/default.html', 'external');
});
Scenario('Test Item 7e: Check au PAY Card - Register', async ({ I, accumulation }) => {
    const auPayCardRegister = '//*[@data-testid="cardCommon_auPAYCardRegister_id"]';
    // + C.投信-積立マスタAPI.creditCardAvailableInfo.creditCardStatus = AUPAY_CREDITCARD_REGISTER_CODE_AUIDの場合: 下記URLに遷移 {member-site-url}/iphone/personal/auID/auIDRuleacceptAppsm.asp
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user81 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    await common.clickCardItem(auPayCardRegister, 'iphone/personal/auID/auIDRuleacceptAppsm.asp', 'external');

    // + Other than the above: Go to the third-party provision consent screen
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user100 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
   
    await I.waitForElement(auPayCardRegister, 2);
    I.scrollToElement(auPayCardRegister);
    await I.clickFixed(auPayCardRegister);
    await I.saveScreenshot('1491.Test_item_No.7e_au_PAY_Card_Register.png');
});

Scenario('Test Item 14: Check Specified date of each month For selectable DatePicker: Select the tapped date', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundReserveOrderInputPage();
    const paymentMethodLabels = '//*[@data-testid="fundChangeReserveOrderInput_paymentMethod_id"]//label';

    // scroll to label
    I.waitForElement(paymentMethodLabels, 1);
    I.scrollToElement(paymentMethodLabels);
    // get the number of labels
    // click each label and take screenshot
    I.clickFixed(`(${paymentMethodLabels})[1]`);
    await I.waitFor('shortWait');

    const datePicker = '//*[@data-testid="reserveOrderInputBox_monthlySpecifiedDate_id"]//div[19]';
    await I.waitForElement(datePicker, 1);
    I.scrollAndClick(datePicker);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.14_Specified_Date_of_Each_Month_Date_Picker.png');
});
Scenario('test Item 15: Check Switch ON/OFF Increase designation', async ({ I }) => {
    const increaseDesignationSwitch = '//label[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';
    await I.waitForElement(increaseDesignationSwitch, 5);
    I.scrollToElement(increaseDesignationSwitch);
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.saveScreenshot('1491.Test_item_No.15_Increase_Designation_Switch_OFF.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
        I.saveScreenshot('1491.Test_item_No.15_Increase_Designation_Switch_ON.png');
    } else {

        I.saveScreenshot('1491.Test_item_No.15_Increase_Designation_Switch_ON.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
        I.saveScreenshot('1491.Test_item_No.15_Increase_Designation_Switch_OFF.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();

    }
});
Scenario('Test Item 17: Check Increase amount', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';

    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }

    // Click plus button 2 times
    const plusButton = '//button[@data-testid="groupInputNumber_plus_id"]';
    const minusButton = '//button[@data-testid="groupInputNumber_minus_id"]';
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(minusButton);
    I.clickFixed(minusButton);
    await I.waitFor('shortWait');

    I.saveScreenshot('1491.Test_item_No.17_Increase_amount.png');

});
Scenario('Test Item 18-19: Check Increase Month 1 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }
    const firstDropdown = '//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")][1]';
    I.clickFixed(firstDropdown);
    await I.waitFor();

    I.saveScreenshot('1491.Test_item_No.18_Increase_Month_1_Dropdown_Opened.png');

    const firstOption = '//div[@data-testid="common_MenuList_id"]//button[@value="3"]';
    I.clickFixed(firstOption);
    await I.waitFor();
    I.saveScreenshot('1491.Test_item_No.18_Increase_Month_1_Selected.png');
    const secondDropdown = '(//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(secondDropdown);
    await I.waitFor();
    I.saveScreenshot('1491.Test_item_No.19_Increase_Month_2_Dropdown_Opened.png');
    const secondOption = '(//div[@data-testid="common_MenuList_id"]//button[@value="11"])[2]';
    I.clickFixed(secondOption);
    await I.waitFor();
    I.saveScreenshot('1491.Test_item_No.19_Increase_Month_2_Selected.png');
});

Scenario('Test Item 21: Check Change Confirmation Screen', async ({ I }) => {
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);
    I.see('以下の内容で設定の変更を受け付けます。ご確認の上、「変更を確定」ボタンを押してください。', '//p[contains(text(), "以下の内容で設定の変更を受け付けます。ご確認の上、「変更を確定」ボタンを押してください。")]');
    I.saveScreenshot('1491.Test_item_No.21_Change_Confirmation_Screen.png');
    // back
    I.backToPreviousScreen();
});
Scenario('Test Item 22: Check Caution statement', async ({ I }) => {
    const cautionStatement = '//*[@data-testid="fundChangeReserveOrderInput_caution_id"]';
    await I.waitForElement(cautionStatement, 2);
    I.scrollAndClick(cautionStatement);
    const chakraCollapse = cautionStatement + '//*[@class="chakra-collapse"]';
    await I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1491.Test_item_No.22_Check_Caution_Statement.png');
    const arrowUpSelector = cautionStatement + '//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.clickFixed(arrowUpSelector);
    I.saveScreenshot('1491.Test_item_No.22_Check_Caution_Statement_Close.png');
});
Scenario('Test Item 32: Check Open the following URL in a new tab', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user56 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    const hereLink = '//p[@data-testid="reserveOrderInputBox_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 3);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
   
});
Scenario('Test Item 39: Check Savings settings before change Open/close accordion', async ({ I }) => {
    const arrowUpIconUp = '//div[p[contains(text(), "変更前の積立設定内容")]]//img[@data-testid="common_dropDown_arrow_id_up"]';
    const arrowUpIconDown = '//div[p[contains(text(), "変更前の積立設定内容")]]//img[@data-testid="common_dropDown_arrow_id_down"]';
    await I.waitForElement(arrowUpIconUp, 2);
    I.scrollAndClick(arrowUpIconUp);
    await I.waitFor(1);
    I.saveScreenshot('1491.Test_item_No.39_Savings_settings_before_change_Close.png');
    I.clickFixed(arrowUpIconDown);
    await I.waitFor(1);
    I.saveScreenshot('1491.Test_item_No.39_Savings_settings_before_change_Open.png');
});
Scenario('Test Item 43: Check Add the amount according to the button you tapped to the input value of the specified amount', async ({ I }) => {
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    I.scrollToElement(suggestInputButtonGroup);
    const count = await I.grabNumberOfVisibleElements(suggestInputButtonGroup);
    for (let i = 1; i <= count - 1; i++) {
        const button = `(${suggestInputButtonGroup})[${i}]`;
        I.clickFixed(button);
        await I.waitFor('shortWait');
        I.saveScreenshot(`1491.Test_item_No.43_Suggest_Input_Button_${i}.png`);
    }
    const cancelButton = `(${suggestInputButtonGroup})[${count}]`;
    I.clickFixed(cancelButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.43_Suggest_Input_Button_Cancel.png');
});

Scenario('Test Item 46: Check Important information Open and close the accordion', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    
    const importantItemsArrow = '//div[p[contains(text(), "重要事項(必ずお読みください)")]]//img[@data-testid="common_dropDown_arrow_id_down"]';
    
    await I.waitForElement(importantItemsArrow, 3);
    I.scrollAndClick(importantItemsArrow);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.46_Important_information_accordion_opened.png');
    
    const importantItemsArrowUp = '//div[p[contains(text(), "重要事項(必ずお読みください)")]]//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.clickFixed(importantItemsArrowUp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.46_Important_information_accordion_closed.png');
});
Scenario('Test Item 48c-2: Check Terms of use agreement - Navigation', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user68 }); // 利用する
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    
    const agreementButton = '//*[@data-testid="fundCommonPointUsageSetting_setting_id"]';
    await I.waitForElement(agreementButton, 3);
    I.scrollAndClick(agreementButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1491.Test_item_No.48c-2_Terms_of_use_agreement_navigation.png');
     // back
     await I.backToPreviousScreen();
});
Scenario('Test Item 48c-1: Check auID registration - Navigation', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user86 }); // auID登録
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();

    const auIdRegisterButton = '//*[@data-testid="fundCommonPointUsageSetting_setting_id"]';
    await I.waitForElement(auIdRegisterButton, 3);
    
    await common.clickCardItem(
        auIdRegisterButton,
        '/ap/iphone/personal/pontapointdoui/agree',
        'kcMemberSite'
    );
});

Scenario('Test Item 48c-3: Check use button - Navigation', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user87 }); // 利用規約同意
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    // Uses default userId '08509910' or similar for default state
    await accumulation.goToReserveFundReserveOrderInputPage();
    
    const useButton = '//*[@data-testid="fundCommonPointUsageSetting_setting_id"]';
    await I.waitForElement(useButton, 3);
    await common.clickCardItem(
        useButton,
        '/ap/iphone/personal/pontapointdoui/agree',
        'kcMemberSite'
    );
});