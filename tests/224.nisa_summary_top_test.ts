Feature('NISAAccountStatus - Page');
import { COOKIE_KEY, USER_ID } from '../const/constant';
import NisaSummaryTop from '../pages/nisaSummaryTop';

const locators = NisaSummaryTop.locators;

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user9 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Go to Nisa Summary Top page', () => {
    NisaSummaryTop.goToPage();
    NisaSummaryTop.takeScreenshot('224_nisa_summary_top_page.png');
});

Scenario('Test item 1 Realized profit/loss', ({ I }) => {
    NisaSummaryTop.clickItem(locators.profitLoss);
    // Dispay Realized profit/loss tab
    I.seeElement(locators.profitLossTab);
    I.waitForText('商品ごとの実現損益', 3, 'body');
    NisaSummaryTop.takeScreenshot('224_Test_item_1_Realized_profit_loss.png');
});

Scenario('Test item 31 NISA investment limit', ({ I }) => {
    NisaSummaryTop.clickItem(locators.nisaInvestmentLimit);
    // Dispay NISA investment limit tab
    I.seeElement(locators.investmentLimitTab);
    I.waitForText('NISA積立プラン', 3, 'body');
    NisaSummaryTop.takeScreenshot('224_Test_item_31_NISA_investment_limit.png');
});

Scenario('Test item 6 NISA investment limit modal', ({ I }) => {
    NisaSummaryTop.clickItem(locators.viewInvestmentLimitModal);
    // Open NISA investment limit modal
    I.seeElement('//*[@id="bottom-sheet-container"]');
    I.waitForText('NISA口座状況（NISA投資枠等）の見方', 3, 'body');
    NisaSummaryTop.takeScreenshot('224_Test_item_6_NISA_investment_limit_modal.png');
    // Close NISA investment limit modal
    NisaSummaryTop.clickItem(locators.closeModal);
});

Scenario('Test item 20 Plan Product tab', async ({ I }) => {
    await I.swipeElementDirection('up', 'body');
    // Click Petit Stock tab
    const petitStockTab = `${locators.planProductTab}//button[2]`;
    await NisaSummaryTop.clickItem(petitStockTab);
    NisaSummaryTop.takeScreenshot('224_Test_item_20_Plan_Product_tab_type_petit_stock.png');
    // Click Fund tab
    const fundTab = `${locators.planProductTab}//button[1]`;
    await NisaSummaryTop.clickItem(fundTab);
    NisaSummaryTop.takeScreenshot('224_Test_item_20_Plan_Product_tab_type_fund.png');
});

Scenario('Test item 23 Reserve plan card', async ({ I }) => {
    // product = TSUMITATE_PRODUCT_TYPE_FUND: go to ReservePlanDetail-Investment trust
    await NisaSummaryTop.clickItem(locators.reservePlanCardFund);
    I.amOnPage(locators.reservePlanFund);
    I.seeElement('//*[@data-testid="reservePlan_change_id"]');
    NisaSummaryTop.takeScreenshot('224_Test_item_23 Reserve_plan_card_fund.png');
    await NisaSummaryTop.back();

    // product = TSUMITATE_PRODUCT_TYPE_PETIT_STOCK: go to ReservePlanDetail-Petit
    await NisaSummaryTop.clickItem(locators.reservePlanCardPetit);
    I.amOnPage(locators.reservePlanPetit);
    I.seeElement('//*[@data-testid="reservePlan_individualStockInformation_id"]');
    NisaSummaryTop.takeScreenshot('224_Test_item_23 Reserve_plan_card_petit.png');
    NisaSummaryTop.back();
});

Scenario('Test item 29.See more', async ({ I }) => {
    NisaSummaryTop.clickItem(locators.seeMore);
    await I.swipeElementDirection('up', 'body');
    NisaSummaryTop.takeScreenshot('224_Test_item_29_See_more.png');
});
