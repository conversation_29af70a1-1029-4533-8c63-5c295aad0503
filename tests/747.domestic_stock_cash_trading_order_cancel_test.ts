import { COOKIE_KEY, PAGE_URL, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import orderInquiry from '../pages/order-inquiry';
import tradeStock from '../pages/trade-stock';

Feature('InvestmentProducts - DomesticStockCashTradingOrderCancel');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user27 });

    await I.amOnPage(PAGE_URL.orderInquiryStock);
    await I.waitFor();
    await orderInquiry.stockList.clickItem(0);
    await I.waitFor();
    await orderInquiry.stockDetail.cancelOrder();
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('domestic stock cash trading order cancel [レイアウト]', async ({ I }) => {
    I.waitInUrl('/mobile/trade/stock/cancel', 5);
    await commonUi.header.verifyTitle('現物取消');
    I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_layout.png');
});

// 12.パスワード
// 13.パスワード省略チェック
// 16.取消を確定
Scenario(
    'domestic stock cash trading order cancel [12.パスワード][13.パスワード省略チェック][16.取消を確定]',
    async ({ I }) => {
        await tradeStock.cancel.fillPassword('111111');
        I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_12_password.png');
        await tradeStock.cancel.togglePasswordOmissionCheck();
        I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_13_password_omission.png');
        await tradeStock.cancel.apply();
        I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_16_apply.png');
    },
);

// 15.パスワード入力チェック
Scenario('domestic stock cash trading order cancel [15.パスワード入力チェック]', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user28 });
    await I.refreshPage();
    await I.waitFor('mediumWait');

    await tradeStock.cancel.togglePasswordInputCheck();
    I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_15_password_input_check.png');
});

// 17.注文照会画面に戻る
Scenario('domestic stock cash trading order cancel [17.注文照会画面に戻る]', async ({ I }) => {
    await tradeStock.cancel.backToOrderInquiry();
    I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_17_back_to_order_inquiry.png');
});

// 18.ご注意
Scenario('domestic stock cash trading order cancel [18.ご注意]', async ({ I }) => {
    await tradeStock.cancel.verifyCaution();
    I.saveScreenshot('747.domestic_stock_cash_trading_order_cancel_18_caution.png');
});
