import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from '../pages/search/common';
Feature('Reserve - CustomerReservePlan');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user46 });
    await I.waitFor();

});


After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('Test Item 0: Check UI of Position Inquiry Domestic Stock Unit Stocks Page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    I.saveScreenshot('1250.Test_item_No.0_UI_of_Customer_Reserve_Plan_Page.png');
});
Scenario('Test Item 1: Check Go To Savings Calendar', async ({ I }) => {
    const reserveCalendarTab = '//*[@data-testid="reservePlan_reserveCalendar_tab"]';
    I.clickFixed(reserveCalendarTab);
    I.saveScreenshot('1250.Test_item_No.1_Go_To_Savings_Calendar.png');
});
Scenario('Test Item 2: Check Go To Savings History', async ({ I }) => {
    const reserveHistoryTab = '//*[@data-testid="reservePlan_reserveHistory_tab"]';
    I.clickFixed(reserveHistoryTab);
    I.saveScreenshot('1250.Test_item_No.2_Go_To_Savings_History.png');
});
Scenario('Test Item 28: Check Go To Next Page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const nextPageButton = '//*[@data-testid="reservePlan_pagination_next"]';
    I.saveScreenshot('1250.Test_item_No.28_Go_To_Next_Page_before_click.png');
    I.clickFixed(nextPageButton);
    I.saveScreenshot('1250.Test_item_No.28_Go_To_Next_Page_after_click.png');
});
Scenario('Test Item 26: Check Go To Previous Page', async ({ I }) => {
    const previousPageButton = '//*[@data-testid="reservePlan_pagination_prev"]';
    I.saveScreenshot('1250.Test_item_No.26_Go_To_Previous_Page_before_click.png');
    I.clickFixed(previousPageButton);
    I.saveScreenshot('1250.Test_item_No.26_Go_To_Previous_Page_after_click.png');
});
Scenario('Test Item 27: Click on first enabled pagination button', async ({ I }) => {
    // Find the first enabled pagination button
    const enabledButton = '//div[@data-testid="reservePlan_pagination_page123123123"]//button[not(@disabled)]';

    try {
        // Wait and check if there is an enabled button
        if (await I.grabNumberOfVisibleElements(enabledButton) > 0) {
            // Click on the first enabled button
            I.clickFixed(`(${enabledButton})[1]`);
            await I.waitFor('shortWait');
            I.saveScreenshot('1250.Test_item_No.27_click_enabled_pagination_button.png');
        } else {
            console.log('No enabled pagination buttons found');
            I.saveScreenshot('1250.Test_item_No.27_no_enabled_buttons.png');
        }
    } catch (e) {
        console.log('Error when clicking pagination button:', e);
        I.saveScreenshot('1250.Test_item_No.27_error.png');
    }
});
Scenario('Test Item 8: Check Go To Savings Detail', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const savingsDetailButton = '//*[@data-testid="reservePlan_reserveDetail_1_0_id"]';
    I.clickFixed(savingsDetailButton);
    I.saveScreenshot('1250.Test_item_No.8_Go_To_Savings_Detail.png');
    const cancelButton = '//button[@aria-label="cancel-btn"]';
    I.clickFixed(cancelButton);


});
Scenario('Test Item 15-1: Check navigation to Petit Stocks savings change page', async ({ I }) => {
    // Findd div  ontoinsntainswithpan with texand"プチ株" a ncitn it
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
    I.clickFixed(reservePlanChangeId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.15-1_Go_To_Petit_Stock_Savings_Change.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 15-2: Check navigation to Investment Fund savings change page', async ({ I }) => {
    // Find div  contains span with text "投信" and click on it
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
    I.clickFixed(reservePlanChangeId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.15-2_Go_To_Investment_Fund_Savings_Change.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 15-3: Check navigation to Foreign MMF savings change page', async ({ I }) => {
    
    // Find div  contains span with text "外貨MMF" and click on it
    const foreignMMFItem = '//span[contains(@class, "chakra-badge") and contains(text(), "外M")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(foreignMMFItem, 2);
    I.scrollAndClick(foreignMMFItem);
    
    const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
    await common.clickCardItem(reservePlanChangeId, '/iPhone/Trade/TeikiKaitsuke/GaikaMMF/teikiedit/GE01101.asp', 'kcMemberSite');
   
});
Scenario('Test Item 16-1: Check navigation to Petit Stocks savings cancel page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    I.waitFor();
    const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
    // Find div  contains span with text "プチ株" and click on it
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    I.clickFixed(reservePlanCancelId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.16-1_Go_To_Petit_Stock_Savings_Cancel.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 16-2: Check navigation to Investment Fund savings cancel page', async ({ I }) => {
    // Find div  contains span with text "投信" and click on it
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
    I.clickFixed(reservePlanCancelId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.16-2_Go_To_Investment_Fund_Savings_Cancel.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 16-3: Check navigation to Foreign MMF savings cancel page', async ({ I }) => {
    // Find div  contains span with text "外貨MMF" and click on it
    const foreignMMFItem = '//span[contains(@class, "chakra-badge") and contains(text(), "外M")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(foreignMMFItem, 2);
    I.scrollAndClick(foreignMMFItem);
    
    const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
    await common.clickCardItem(reservePlanCancelId, '/iPhone/Trade/TeikiKaitsuke/GaikaMMF/teikiedit/GE01101.asp', 'kcMemberSite');
    
});
Scenario('Test Item 17-1: Check navigation to Petit Stocks plan detail page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    I.waitFor();
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    // Find div  contains span with text "プチ株" and click on it
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.17-1_Go_To_Petit_Stock_Plan_Detail.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 17-2: Check navigation to Investment Fund plan detail page', async ({ I }) => {
    // Find div  contains span with text "投信" and click on it
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.17-2_Go_To_Investment_Fund_Plan_Detail.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 17-3: Check navigation to Foreign MMF plan detail page', async ({ I }) => {
    // Find div  contains span with text "外貨MMF" and click on it
    const foreignMMFItem = '//span[contains(@class, "chakra-badge") and contains(text(), "外M")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(foreignMMFItem, 2);
    I.scrollAndClick(foreignMMFItem);
    
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    await common.clickCardItem(reservePlanDetailId, '/iPhone/account/teikikaitsuke_sp/SetteiDetailGaikaMMF.asp', 'kcMemberSite');
});
Scenario('Test Item 18-1: Check navigation to Domestic Stock Detail', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    I.waitFor();
    // Find div  contains span with text "プチ株" and click on it
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);

    // Click on link stock detail
    const stockDetailLink = '//*[@data-testid="reservePlan_individualSymbolInfo_id"]';
    I.clickFixed(stockDetailLink);
    await I.waitFor('shortWait');

    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.18-1_Go_To_Domestic_Stock_Detail.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 18-2: Check navigation to Investment Fund Detail', async ({ I }) => {
    // Find div  contains span with text "投信" and click on it
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reservePlan_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);

    // Click on link fund detail
    const fundDetailLink = '//*[@data-testid="reservePlan_individualSymbolInfo_id"]';
    I.clickFixed(fundDetailLink);
    await I.waitFor('shortWait');

    // Verify navigation
    I.saveScreenshot('1250.Test_item_No.18-2_Go_To_Fund_Detail.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 21: Check Date specified by our company "Open the following URL in a new tab"', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const scheduleLink = '//*[@data-testid="reservePlan_dateSpecified_id"]';
    I.waitForElement(scheduleLink, 2);
    I.scrollAndClick(scheduleLink);
    I.activateApp();
});
Scenario('Test Item 39: Check button filter', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const filterButton = '//*[contains(@class, "chakra-text") and contains(text(), "条件未設定")]';
    I.clickFixed(filterButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.39_Open_Filter.png');
});
Scenario('Test Item 40: Check button display symbol', async ({ I }) => {
    const displaySymbolButton = '//*[@data-testid="reservePlan_displaySymbol_id"]';
    I.clickFixed(displaySymbolButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.40_Open_Display_Symbol.png');
});
Scenario('Test Item 43 46 49: Check button investment trust symbol list', async ({ I }) => {
    const displaySymbolButton = '//*[@data-testid="reservePlan_displaySymbol_id"]';
    const investmentTrustSymbolButton = '//*[@data-testid="reservePlan_investmentTrustSymbol_0_id"]';
    I.clickFixed(investmentTrustSymbolButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.43_Open_Investment_Trust_Symbol_List.png');
    //click dropdown again
    I.clickFixed(displaySymbolButton);
    const petitSymbolButton = '//*[@data-testid="reservePlan_petitSymbol_0_id"]';
    I.clickFixed(petitSymbolButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.43_Open_Petit_Symbol.png');
    //click dropdown again
    I.clickFixed(displaySymbolButton);
    const foreignMMFButton = '//*[@data-testid="reservePlan_mmfSymbol_0_id"]';
    I.clickFixed(foreignMMFButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.49_Open_Foreign_MMF_Symbol.png');
});
Scenario('Test Item 50.1: Check upper limit reset when lower limit is larger', async ({ I }) => {
    // 1. Choose value 1000 for upper limit (button 2)
    const upperLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(upperLimitButton);
    await I.waitFor('shortWait');
    // Choose value 1000 from menu of button 2
    const upperValue1000 = '(//div[contains(@class, "chakra-menu__menu-list")])[2]//button[@value="1000"]';
    I.clickFixed(upperValue1000);
    await I.waitFor('shortWait');

    // 2. Choose value 5000 for lower limit (button 1)
    const lowerLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[1]';
    I.clickFixed(lowerLimitButton);
    await I.waitFor('shortWait');
    // Choose value 5000 from menu of button 1
    const lowerValue5000 = '(//div[contains(@class, "chakra-menu__menu-list")])[1]//button[@value="5000"]';
    I.clickFixed(lowerValue5000);
    await I.waitFor('shortWait');

    // 3. Verify upper limit automatically changes to "指定なし"
    I.see('指定なし', upperLimitButton);
    I.saveScreenshot('1250.Test_item_No.50.1_Upper_Limit_Reset.png');
});
Scenario('Test Item 50.2: Check lower limit reset when upper limit is smaller', async ({ I }) => {
    // 1. Choose value 5000 for lower limit (button 1)
    const lowerLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[1]';
    I.clickFixed(lowerLimitButton);
    await I.waitFor('shortWait');
    const lowerValue5000 = '(//div[contains(@class, "chakra-menu__menu-list")])[1]//button[@value="5000"]';
    I.clickFixed(lowerValue5000);
    await I.waitFor('shortWait');

    // 2. Choose value 1000 for upper limit (button 2)
    const upperLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(upperLimitButton);
    await I.waitFor('shortWait');
    const upperValue1000 = '(//div[contains(@class, "chakra-menu__menu-list")])[2]//button[@value="1000"]';
    I.clickFixed(upperValue1000);
    await I.waitFor('shortWait');

    // 3. Verify lower limit automatically changes to "指定なし"
    I.see('指定なし', lowerLimitButton);
    I.saveScreenshot('1250.Test_item_No.50.2_Lower_Limit_Reset.png');
});
Scenario('Test Item 51: Check Switch product category ON/OFF', async ({ I }) => {
    const listLabel = '//*[@data-testid="reservePlan_productType_id"]//label';
    I.waitForElement(listLabel, 1);
    I.scrollToElement(listLabel); 
    const count = await I.grabNumberOfVisibleElements(listLabel);

    try {
        for (let i = 1; i <= count; i++) {
            const label = `(${listLabel})[${i}]`;
            I.waitForElement(label, 5);
            I.clickFixed(label);
            await I.waitFor('shortWait');
            I.saveScreenshot(`1250.Test_item_No.51_Switch_Product_Category_${i}.png`);
        }

    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1250.Test_item_No.51_error.png');
    }
});
Scenario('Test Item 52: Check Switch payment method ON/OFF', async ({ I }) => {
    const listLabel = '//*[@data-testid="reservePlan_paymentMethod_id"]//label';
    I.waitForElement(listLabel, 1);
    I.scrollToElement(listLabel);
    const count = await I.grabNumberOfVisibleElements(listLabel);

    try {
        for (let i = 1; i <= count; i++) {
            const label = `(${listLabel})[${i}]`;
            I.waitForElement(label, 5);
            I.clickFixed(label);
            await I.waitFor('shortWait');
            I.saveScreenshot(`1250.Test_item_No.52_Switch_Payment_Method_${i}.png`);
        }
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1250.Test_item_No.52_error.png');
    }
});
Scenario('Test Item 53: Check Switch increase specification ON/OFF', async ({ I }) => {
    const listLabel = '//*[@data-testid="reservePlan_specifyIncrease_id"]//label';
    I.waitForElement(listLabel, 1);
    I.scrollToElement(listLabel);
    const count = await I.grabNumberOfVisibleElements(listLabel);

    try {
        for (let i = 1; i <= count; i++) {
            const label = `(${listLabel})[${i}]`;
            I.waitForElement(label, 5);
            I.clickFixed(label);
            await I.waitFor('shortWait');
            I.saveScreenshot(`1250.Test_item_No.53_Switch_Increase_Specification_${i}.png`);
        }
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1250.Test_item_No.53_error.png');
    }
});
Scenario('Test Item 54: Check Switch display account ON/OFF', async ({ I }) => {
    const listLabel = '//*[@data-testid="reservePlan_displayAccount_id"]//label';
    I.waitForElement(listLabel, 1);
    I.scrollToElement(listLabel);
    const count = await I.grabNumberOfVisibleElements(listLabel);

    try {
        for (let i = 1; i <= count; i++) {
            const label = `(${listLabel})[${i}]`;
            I.waitForElement(label, 5);
            I.clickFixed(label);
            await I.waitFor('shortWait');
            I.saveScreenshot(`1250.Test_item_No.54_Switch_Display_Account_${i}.png`);
        }
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1250.Test_item_No.53_error.png');
    }
});
Scenario('Test Item 55: Check initial display', async ({ I }) => {
    const clearButton = '//button[contains(text(), "クリア")]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.55_Initial_Display.png');
});
Scenario('Test Item 56: Filter and sort based on entered parameters', async ({ I }) => {
    const submitButton = '//button[contains(text(), "確定する")]';
    I.clickFixed(submitButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.56_Filter_And_Sort.png');
});
Scenario('Test Item 57b: Check  Go to the accumulated points usage setting', async ({ I }) => {
    const settingButton = '//*[@data-testid="reservePlan_setup_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.57b_Go_To_Accumulated_Points_Usage_Setting.png');
    // back
    await I.backToPreviousScreen();
});
Scenario('Test Item 59: Check scroll top', async ({ I }) => {
    const dropDownArrow = '//*[@data-testid="common_dropDown_arrow_id_down"]';
    I.waitForElement(dropDownArrow, 2);
    I.scrollAndClick(dropDownArrow);
    const scrollToTopButton = '//*[@id="scrollButton"]';
    I.saveScreenshot('1250.Test_item_No.59_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1250.Test_item_No.59_scrollToTop_dont_see_button.png');
});
