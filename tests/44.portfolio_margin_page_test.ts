import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioMarginPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to margin page
    await portfolio.top.goToPage();
    await portfolio.top.margin();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio margin page', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.verifyProductName(margin.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_layout.png`);
});

Scenario('test portfolio margin page [11.銘柄カード]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '燦キャピタル');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_11_symbol_card.png`);
});

Scenario('test portfolio margin page [22.新規買]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '燦キャピタル');
    await margin.newBuy();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_22_new_buy.png`);
});

Scenario('test portfolio margin page [23.新規売]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '燦キャピタル');
    await margin.newSell();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_23_new_sell.png`);
});

Scenario('test portfolio margin page [24.返済]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '燦キャピタル');
    await margin.payment();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_24_payment.png`);
});

Scenario('test portfolio margin page [25.品受]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '買');
    await margin.receipt();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_25_receipt.png`);
});

Scenario('test portfolio margin page [26.品渡]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '売');
    await margin.delivery();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_26_delivery.png`);
});

Scenario('test portfolio margin page [27.銘柄情報を見る]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '燦キャピタル');
    await margin.seeInfo();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_27_see_info.png`);
});

Scenario('test portfolio margin page [28.残高照会]', async ({ I }) => {
    const { common, margin } = portfolio;

    await common.clickSymbolCard(margin.productType, '燦キャピタル');
    await margin.positionInquiry();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMarginPage}_28_position_inquiry.png`);
});
