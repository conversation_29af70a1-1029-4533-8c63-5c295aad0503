import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';

Feature('InvestmentProducts - USStockInfoSummary');

Before(async ({ I }) => {
    console.debug('before');
    // reset context    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    // await I.login(USER_ID.user6, '111111');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Check UI US Stock Information-Summary', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/summary?symbol=123';
    I.amOnPage(pageUrl);
    I.see('米国株式\n個別銘柄情報', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(pageUrl);
    I.saveScreenshot('605.usstock_info_summary_No.0_access_the_usstock_info_summary_page.png');
});

// 15.時系列エリア -> アコーディオンを開閉する
Scenario('Item 15. Opening and closing the time series area accordion', async ({ I }) => {
    const locator = `//*[@data-testid="usStockSummary_timeSeriesArea_id"]/div[1]`;
    await I.scrollToElement(locator);
    await I.waitFor('shortWait');
    await I.seeAndClickElement(locator);
    I.saveScreenshot('605.usstock_info_summary_No.15_opening_the_time_series_area_accordion.png');

    await I.waitFor('shortWait');
    await I.clickFixed(locator);
    I.saveScreenshot('605.usstock_info_summary_No.15_closing_the_time_series_area_accordion.png');
});

// 24.時価情報エリア -> アコーディオンを開閉する
Scenario('Item 24. Opening and closing the market price info area accordion', async ({ I }) => {
    const locator = `//*[@data-testid="usStockSummary_marketPriceInfoArea_id"]/div[1]`;
    await I.waitFor('shortWait');
    await I.seeAndClickElement(locator);
    I.saveScreenshot('605.usstock_info_summary_No.24_opening_the_market_price_info_area_accordion.png');

    await I.waitFor('shortWait');
    await I.clickFixed(locator);
    I.saveScreenshot('605.usstock_info_summary_No.24_closing_the_market_price_info_area_accordion.png');
});
