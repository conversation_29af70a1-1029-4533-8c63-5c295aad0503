Feature('Referral - MailCompletePage');

// Import the ReferralMailInput page object
import { COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import ReferralMailConfirm from '../pages/referralMailConfirm';
import ReferralMailInput from '../pages/referralMailInput';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I<PERSON>closeBrowser();
});

Scenario('go to referral mail input page', async ({ I }) => {
    I.amOnPage(PAGE_URL.referralMailInput);;
    await I.waitFor('mediumWait');
    I.see(
        `ご家族・ご友人
紹介プログラム`,
        '//*[@data-testid="common_header_title_id"]',
    );
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_page.png`);
    await ReferralMailInput.fillLastName('123456');
    await ReferralMailInput.fillFirstName('123456');
    await ReferralMailInput.fillLastNameKana('123456');
    await ReferralMailInput.fillFirstNameKana('123456');
    await ReferralMailInput.fillMailAddress('<EMAIL>');
    await ReferralMailInput.fillMessage('123456');
    await ReferralMailInput.clickConfirm();
    await ReferralMailConfirm.clickSend();
});

Scenario('Test Item 1: Go to FriendIntroduction program', async ({ I }) => {
    const confirmBtn = '//*[@data-testid="referralMailComplete_goToFriendIntroductionTOP_id"]';
    I.seeElement(confirmBtn);
    await I.tapLocationOfElement(confirmBtn);
    await I.waitFor();
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, 'mobile/referral', 'URL should contain the program page path');
});
