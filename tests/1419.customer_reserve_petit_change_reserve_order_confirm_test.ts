import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReservePetitChangeReserveOrderConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 }); //08409912
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Savings Petit Stocks Change Reserve Order Confirm', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    I.saveScreenshot('1419.Test_item_No.0_UI_of_Savings_Petit_Stocks_Change_Reserve_Order_Confirm.png');
});
Scenario('Test Item 11-12: Check password Tap Opens a text input form with alphanumeric characters and password omission check', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user47 });
    await I.waitFor()
    await accumulation.goToReservePetitChangeOrderConfirmPage(); 
    const passwordInput = '//*[@data-testid="changeReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.scrollAndFill(passwordInput, '123@abc'); 
    // click img alt="eye" to see password
    const eyeIcon = '//img[@alt="eye"]';
    await I.waitForElement(eyeIcon, 1);
    I.clickFixed(eyeIcon);
    await I.waitFor('shortWait');
    I.saveScreenshot('1419.Test_item_No.11_Password_Tap_Opens_a_text_input_form_with_alphanumeric_characters_and_see_password.png');
    const passwordInputOmissionCheck = '//*[@data-testid="changeReserveOrderConfirm_passwordInputOmission_id"]';
    await I.waitForElement(passwordInputOmissionCheck, 1);
    // ON
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1419.Test_item_No.12_Password_omission_check_ON.png');
    // OFF
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1419.Test_item_No.12_Password_omission_check_OFF.png');
});

Scenario('Test Item 14: Check Password input check: Switch between ON and OFF', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="changeReserveOrderConfirm_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 3);
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1419.Test_item_No.14_Password_input_check_1.png');
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1419.Test_item_No.14_Password_input_check_2.png');
});
//TODO: 15.1: Skip bacause cannot check maintenance
Scenario.skip('Test Item 15.2: Browser back during loading redirects to reserve plan', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    
    const passwordInput = '//*[@data-testid="changeReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="changeReserveOrderConfirm_confirm_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1419.Test_item_No.15.2_before_click_button.png');
    I.clickFixed(confirmButton);
    
    I.executeScript(() => window.history.back());
    
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1419.Test_item_No.15.2_redirect_to_reserve_plan.png');
});

Scenario('Test Item 15.3: Check Loading will be performed. Transition to "Savings - Petit Stocks - Savings Changes Completed', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="changeReserveOrderConfirm_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 1);
    I.clickFixed(passwordInputCheck);
    await I.waitFor('shortWait');
    const confirmButton = '//*[@data-testid="changeReserveOrderConfirm_confirm_id"]';
    await I.waitForElement(confirmButton, 1);
    I.clickFixed(confirmButton);
    await I.waitFor('shortWait');
    I.seeElement('//p[contains(text(), "積立変更が完了しました。")]');
    I.saveScreenshot('1419.Test_item_No.15.3_Check_Loading_will_be_performed_Transition_to_Savings_Petit_Stocks_Savings_Changes_Completed.png');
    // back to change reserve order confirm page

});
Scenario('Test Item 16: Check Terms and Conditions for Regular Fractional Share Accumulation Transactions ', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    const caution = '//*[@data-testid="changeReserveOrderConfirm_caution_id"]';
    await I.waitForElement(caution, 3);
    I.scrollAndClick(caution);

    const chakraCollapse = '//*[@class="chakra-collapse"]';
    I.waitForElement(chakraCollapse, 2);
    I.scrollToElement(chakraCollapse);
    await I.waitFor('shortWait');
    I.saveScreenshot('1419.Test_item_No.16_Terms_and_Conditions_for_Regular_Fractional_Share_Accumulation_Transactions.png');
    const arrowUp = '//*[@data-testid="changeReserveOrderConfirm_caution_id"]//*[@data-testid="common_dropDown_arrow_id_up"]';
    await I.waitForElement(arrowUp, 2);
    I.clickFixed(arrowUp);
    I.saveScreenshot('1419.Test_item_No.16_Terms_and_Conditions_for_Regular_Fractional_Share_Accumulation_Transactions_closed.png');
});
Scenario('Test Item 19: Check Click Open the following URL in a new tab', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user51 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReservePetitChangeOrderConfirmPage();
    const hereLink = '//p[@data-testid="changeReserveOrderConfirm_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 1);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
});