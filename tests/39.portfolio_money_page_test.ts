import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioMoneyPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to money page
    await portfolio.top.goToPage();
    await portfolio.top.money();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio money page', async ({ I }) => {
    const { common, money } = portfolio;

    await common.verifyProductName(money.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMoneyPage}_layout.png`);
});

Scenario('test portfolio money page [3.銘柄カード]', async ({ I }) => {
    const { common, money } = portfolio;

    await common.clickSymbolCard(money.productType, '信用保証金現金');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMoneyPage}_3_symbol_card.png`);
});

Scenario('test portfolio money page [8.買付出金可能額]', async ({ I }) => {
    const { common, money } = portfolio;

    await common.clickSymbolCard(money.productType, '信用保証金現金');
    await money.availableAmount();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMoneyPage}_8_available_amount.png`);
});

Scenario('test portfolio money page [9.外国株式買付可能額]', async ({ I }) => {
    const { common, money } = portfolio;

    await common.clickSymbolCard(money.productType, '信用保証金現金');
    await money.foreignStockAvailableAmount();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMoneyPage}_9_foreign_stock_available_amount.png`);
});

Scenario('test portfolio money page [10.先物・オプション建玉可能額]', async ({ I }) => {
    const { common, money } = portfolio;

    await common.clickSymbolCard(money.productType, '信用保証金現金');
    await money.futureOptionAvailableAmount();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioMoneyPage}_10_future_option_available_amount.png`);
});
