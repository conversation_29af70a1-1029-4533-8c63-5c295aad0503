import { COMMON_BACK_BUTTON, COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundSellInput');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user38 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await I.waitFor();
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test trade fund sell input', async ({ I }) => {
    await tradeFund.sellInput.goToTradeFundSellInputPage();
    I.saveScreenshot(`1063.${tradeFund.sellInput.screenShotPrefix.fundSell}.0_Go_to_trade_fund_sell_input.png`);
});

// 13.数量	->	共通UI-数値ステッパ参照
Scenario('Test No.13: Quantity > Common UI Numeric Stepper See', async ({ I }) => {
    await tradeFund.sellInput.fillGroupInputNumber();
    I.saveScreenshot(
        `1063.${tradeFund.sellInput.screenShotPrefix.fundSell}.13_Quantity-Common_UI_Numeric_Stepper_See.png`,
    );
});

// 18.注文確認画面へ -> 投信売却確認に遷移
Scenario(
    'Test No.18: Go to order confirmation screen -> Transition to Investment Trust Sales Confirmation',
    async ({ I }) => {
        await tradeFund.sellInput.goToTradeFundConfirmPage();
        I.saveScreenshot(
            `1063.${tradeFund.sellInput.screenShotPrefix.fundSell}.18_Go_to_order_confirmation_screen.png`,
        );
        await I.waitFor();
        I.seeElement(COMMON_BACK_BUTTON);
        await I.clickFixed(COMMON_BACK_BUTTON);
        await I.waitFor();
    },
);

// 20.ご注意文言 -> アコーディオンを開閉する
Scenario('Test No.20: Caution -> Open/close the accordion', async ({ I }) => {
    I.swipeElementDirection('up', tradeFund.sellInput.locators.cautionMessage);
    await I.waitFor();
    await I.clickFixed(tradeFund.sellInput.locators.cautionMessage);
    I.swipeElementDirection('up', tradeFund.sellInput.locators.cautionMessage);
    I.saveScreenshot(`1063.${tradeFund.sellInput.screenShotPrefix.fundSell}.20_Caution-Open_the_accordion.png`);
    await I.waitFor();
    await I.clickFixed(tradeFund.sellInput.locators.cautionMessage);
    I.saveScreenshot(`1063.${tradeFund.sellInput.screenShotPrefix.fundSell}.20_Caution-Close_the_accordion.png`);
});

// 22.取引種別 -> 選択状態を変更
Scenario('Test No.22: Trade Type -> Change Selection Status', async ({ I }) => {
    I.seeElement(tradeFund.sellInput.locators.tradeType);
    await I.clickFixed(tradeFund.sellInput.locators.tradeType2Item);
    I.saveScreenshot(`1063.${tradeFund.sellInput.screenShotPrefix.fundSell}.22_Trade_Type-Change_Selection_Status.png`);
});

// 23.解約・買取請求の違いとは -> "以下の画面を別タブで開く
// https://kabu.com/investment/guide/syoukenzeisei/tax/fund.html
Scenario('Test No.23: Open the following screen in a new tab', async () => {
    await tradeFund.sellInput.goToTradeFundSellInputPage();
    await common.clickCardItem(tradeFund.sellInput.locators.buyRequestLink, 'https://kabu.com/investment/guide/syoukenzeisei/tax/fund.html', 'external');
});
