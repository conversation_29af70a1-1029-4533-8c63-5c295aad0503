import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';
import domesticStockCashtrading from '../pages/domesticStockCashtrading';

Feature('InvestmentProducts - DomesticStockCashtrading');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor('mediumWait');

    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user26 });
    await domesticStockCashtrading.goToStockSellCompletedPage();
    await I.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('Item 0. Access to the trade stock sell order complete page', async ({ I }) => {
    await <PERSON>.waitFor('mediumWait');
    I.see('現物売', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(domesticStockCashtrading.urls.complete);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.complete}.0_Access_to_the_trade_stock_sell_order_complete_page.png`,
    );
});

// "2.注文番号	-> 注文照会-国内株式-単元株-詳細に遷移する
Scenario('Item 2. Go to the order inquiry stock detail', async ({ I }) => {
    I.waitForElement(domesticStockCashtrading.locators.orderLinkId, 5);
    await I.clickFixed(domesticStockCashtrading.locators.orderLinkId);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl(domesticStockCashtrading.urls.orderInquiry);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.complete}.2_Go_to_the_OrderStatus-DomesticStock-UnitStock-List_page.png`,
    );
    await I.seeAndClickElement('$common_back_id');
});

// "3.注文照会	-> 注文照会-国内株式-単元株-一覧に遷移する
Scenario('Item 3. Go to the order inquiry unit stock list page', async ({ I }) => {
    await I.seeAndClickElement(domesticStockCashtrading.locators.orderStatusButton);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl(domesticStockCashtrading.urls.orderInquiryList);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.complete}.3_Go_to_the_order_inquiry_unit_stock_list_page.png`,
    );
    await I.seeAndClickElement('$common_back_id');
});

// "4.残高照会 -> 残高照会-国内株式-単元株-一覧に遷移する

Scenario('Item 3. Go to the position inquiry stock', async ({ I }) => {
    await I.seeAndClickElement(domesticStockCashtrading.locators.positionInquiryButton);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl(domesticStockCashtrading.urls.positionInquiryStock);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.complete}.3_Go_to_the_position_inquiry_stock.png`,
    );

    // 	ブラウザバック	銘柄/商品検索_総合検索画面TOPに遷移するようブラウザのヒストリを更新する
    await I.seeAndClickElement('$common_back_id');
    I.say('Browser back to the top of the search page');
    await I.performBrowserBack();
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.complete}.3_Browser_back_to_the_top_of_the_search_page.png`,
    );
});
