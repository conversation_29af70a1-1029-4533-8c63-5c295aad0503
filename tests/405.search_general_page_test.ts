import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('Symbol_ProductSearch - SearchGeneralPage');

Before(async ({ I, loginAndSwitchToWebAs, search }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user5 });
    await search.goToPage();
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Display Search General page', async ({ I }) => {
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search', 'URL does not contain expected path');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Display_search_general_page.png`);
});

Scenario('Test item No.1 Keyword input', async ({ I }) => {
    const keyWordInput = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    await I.clickFixed(keyWordInput);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.1_Click_search_input_to_move_to_the_stock_search.png`);
});

Scenario('Test item No.2 Domestic Theme Stocks', async ({ I }) => {
    const domesticThemeStockSelector = '//*[@data-testid="searchTop_domesticThemeStock_id"]';
    await I.clickFixed(domesticThemeStockSelector);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search/theme', 'URL does not contain expected path');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.2_Go_to_Domestic_theme_stocks_page.png`);
});

Scenario('Test item No.3 Domestic stock rankings', async ({ I }) => {
    const domesticStockRankingSelector = '//*[@data-testid="searchTop_domesticStockRanking_id"]';
    await I.clickFixed(domesticStockRankingSelector);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/ranking', 'URL does not contain expected path');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.3_Go_to_Domestic_stock_rankings_page.png`);
});

Scenario('Test item No.4 Shareholder benefits', async ({ I }) => {
    const benefitSelector = '//*[@data-testid="searchTop_specialBenefit_id"]';
    await I.clickFixed(benefitSelector);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/benefit', 'URL does not contain expected path');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.3_Go_to_Shareholder_benefits_page.png`);
});

Scenario('Test item No.5 US Theme Search', async () => {
    const usStockThemeSearch = '//*[@data-testid="searchTop_usStockThemeSearch_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/InvInfo/USMarket/Theme/List
    await common.clickCardItem(usStockThemeSearch, '/ap/iPhone/InvInfo/USMarket/Theme/List', 'kcMemberSite');
});

Scenario('Test item No.6 US Rankings', async () => {
    const usRanking = '//*[@data-testid="searchTop_usRanking_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/InvInfo/Ranking/Market/USStock
    await common.clickCardItem(usRanking, '/ap/iPhone/InvInfo/Ranking/Market/USStock', 'kcMemberSite');
});

Scenario('Test item No.7 Fund search', async ({ I }) => {
    const fundSearch = '//*[@data-testid="searchTop_fundSearch_id"]';
    await I.clickFixed(fundSearch);
    await I.waitFor('mediumWait');
    // Go to the Fund Search
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/fund/search', 'URL does not contain expected path');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.7_Go_to_fund_search.png`);
});

Scenario('Test item No.8 Mutual Fund Rankings', async () => {
    const mutualFundRanking = '//*[@data-testid="searchTop_investmentTrustRanking_id"]';
    // Go to the following screen: {member-site-url}/iPhone/TradeTool/ranking/ToshinRankingMenu.asp
    await common.clickCardItem(mutualFundRanking, '/iPhone/TradeTool/ranking/ToshinRankingMenu.asp', 'kcMemberSite');
});

Scenario('Test item No.9 Savings', async () => {
    const saving = '//*[@data-testid="searchTop_reserve_id"]';
    // Go to the following screen: {member-site-url}/iphone/tradetool/teikikaitsuke/toushin_sp/meigaralist.asp
    await common.clickCardItem(saving, '/iphone/tradetool/teikikaitsuke/toushin_sp/meigaralist.asp', 'kcMemberSite');
});

Scenario('Test item No.10 Credit Card Savings', async ({ I, search }) => {
    const creditCardSavings = '//*[@data-testid="searchTop_creditCardReserve_id"]';
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user71 });
    await search.goToPage();
    // + Registered: {member-site-url}/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp
    await common.clickCardItem(creditCardSavings, '/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp', 'kcMemberSite');
    // + Not registered: Select card
    await I.waitFor('mediumWait');
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user5 });
    await search.goToPage();
    // Navigate to card-select
    await I.clickFixed(creditCardSavings);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl('/mobile/setting/card-select');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.10_Go_to_card_select.png`);
});

Scenario('Test item No.11 Futures', async () => {
    const futures = '//*[@data-testid="searchTop_assetStatusFuture_id"]';
    // Go to the following screen: {member-site-url}/iPhone/Trade/FuturesNew/NFuturesSelect.asp
    await common.clickCardItem(futures, '/iPhone/Trade/FuturesNew/NFuturesSelect.asp', 'kcMemberSite');
});

Scenario('Test item No.12 Futures Option', async () => {
    const futuresOption = '//*[@data-testid="searchTop_option_id"]';
    // Go to the following screen: {member-site-url}/iPhone/Trade/OptionNew/NOptionSelect.asp
    await common.clickCardItem(futuresOption, '/iPhone/Trade/OptionNew/NOptionSelect.asp', 'kcMemberSite');
});

Scenario('Test item No.13 NISA', async ({ I, search }) => {
    const menuItem = '//*[@data-testid="searchTop_nisa_id"]';
    // G. Customer transaction master API call successful and G. Customer transaction master API.isNisaAccountOpenPast=false: Transition to {member-site-url}/ap/iPhone/Nisa/Summary/Top
    await common.clickCardItem(menuItem, '/iPhone/nisa/notapply3.asp', 'kcMemberSite');
    await I.switchToWeb();
    await I.waitFor();

    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user8 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await search.goToPage();
    // + Other than the above: Transition to NISA general menu
    await common.clickCardItem(menuItem, '/mobile/nisa/menu');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.13_Click_NISA_go_to_nisa_menu.png`);
});

Scenario('Test item No.14 Credit Robo-Advisor', async ({ I, search }) => {
    const menuItem = '//*[@data-testid="searchTop_marginRoboAd_id"]';
    // + A. Credit Account Opening Status Acquisition API.isValidServiceRoboAd=false: Transition to {member-site-url}/ap/iPhone/Personal/MarginRoboFlow/List (transition in the same tab)
    await common.clickCardItem(menuItem, '/ap/iPhone/Personal/MarginRoboFlow/List', 'kcMemberSite');
});

Scenario('Test item No.15 Savings', async () => {
    const savings = '//*[@data-testid="searchTop_reserve_id_2"]';
    // Go to the following screen: {member-site-url}/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp
    await common.clickCardItem(savings, '/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp', 'kcMemberSite');
});

Scenario('Test item No.16 Previous OP Navigation', async () => {
    const previousOPNavigation = '//*[@data-testid="searchTop_senOPNavi_id"]';
    // Open ${dsUrl} in a new tab
    await common.clickCardItem(previousOPNavigation, 'https://aukabucom.ds.dev.guide.inc/', 'external');
});

Scenario('Test item No.17 Futures Board Flash', async () => {
    const futureBoardFlash = '//*[@data-testid="searchTop_futuresBoardFlash_id"]';
    // Transition to the following screen: {member-site-url}/iPhone/tradetool/KBF/KabucomFuturesBoardFlash.asp
    await common.clickCardItem(futureBoardFlash, '/iPhone/tradetool/KBF/KabucomFuturesBoardFlash.asp', 'kcMemberSite');
});

Scenario('Test item No.18 OP Board Flash', async () => {
    const opBoardFlash = '//*[@data-testid="searchTop_opBoardFlash_id"]';
    // Transition to the following screen: {member-site-url}/iPhone/tradetool/KBF/KabucomOptionBoardFlash.asp
    await common.clickCardItem(opBoardFlash, '/iPhone/tradetool/KBF/KabucomOptionBoardFlash.asp', 'kcMemberSite');
});

Scenario('Test item No.24 General credit (long-term/day trading) stocks available for short selling', async () => {
    const generalMargin = '//*[@data-testid="searchTop_generalMargin_id"]';
    // Go to the following screen {member-site-url}/ap/iPhone/Stocks/Margin/MarginSymbol/GeneralSellList
    await common.clickCardItem(generalMargin, '/ap/iPhone/Stocks/Margin/MarginSymbol/GeneralSellList', 'kcMemberSite');
});

Scenario('Test item No.25 US Stock Search', async () => {
    const usStockSearch = '//*[@data-testid="searchTop_usStockSearchSystem_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/InvInfo/USMarket/Search/ByKeyword
    await common.clickCardItem(usStockSearch, '/ap/iPhone/InvInfo/USMarket/Search/ByKeyword', 'kcMemberSite');
});

Scenario('Test item No.26 Foreign Currency MMF', async () => {
    const foreigCurrencyMMF = '//*[@data-testid="searchTop_foreignCurrencyMMF_id"]';
    // Go to the following screen: {member-site-url}/iPhone/Trade/MultiCurMMFselect.asp
    await common.clickCardItem(foreigCurrencyMMF, '/iPhone/Trade/MultiCurMMFselect.asp', 'kcMemberSite');
});

Scenario('Test item No.27 Foreign Currency MMF Closed Days Calendar', async () => {
    const foreigCurrencyMMFClosedDaysCanlendar = '//*[@data-testid="searchTop_holidayCalendar_id"]';
    // Go to the following screen {member-site-url}/iPhone/TradeTool/McMMFKyugyou/McMMFKyugyou.asp
    await common.clickCardItem(foreigCurrencyMMFClosedDaysCanlendar, '/iPhone/TradeTool/McMMFKyugyou/McMMFKyugyou.asp', 'kcMemberSite');
});

Scenario('Test item No.21 Stock Items', async ({ I }) => {
    const stockItemSelector = '//div[starts-with(@data-testid, "searchTop_symbolItem_id_")]';
    await I.clickFixed(stockItemSelector);
    await I.waitFor('mediumWait');
    I.see('銘柄詳細', 'body');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/stock/basic', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.21_Select_stock_item_go_to_chart_display.png`);
});

Scenario('Test item No.21-1 Favorites (Domestic Stocks)', async ({ I }) => {
    const favoriteButtonSelector = '//div[starts-with(@data-testid, "searchTop_symbolItem_id_")]/div[1]';
    await I.clickFixed(favoriteButtonSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="common_rightSlide_favorite_id"]');
    I.see('お気に入り追加', 'body');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.21-1_Open_Favorites_Domestic_Stocks_modal.png`);
});

Scenario('Test item No.23 List of stocks available for margin trading', async ({ I }) => {
    const marginTradingSelector = '//*[@data-testid="searchTop_marginTradingAvailable_id"]';
    await I.clickFixed(marginTradingSelector);
    await I.waitFor('mediumWait');
    I.see('信用取引', 'body');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search-margin/result', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.23_Transition_to_Margin_trading_search_result_page.png`);
});

Scenario('Test item No.28 Mitsubishi UFJ eSmart Securities FX', async ({ I }) => {
    const menuItem = '//*[@data-testid="searchTop_securitiesFX_id"]';
    const openAccountBtn = '//button[contains(text(), "口座開設")]';
    await I.clickFixed(menuItem);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.28_Display_dialog.png`);
    // Display dialog/transition URL according to the process described in the credit transaction link specifications
    await common.clickCardItem(openAccountBtn, '/ap/iphone/Personal/WebExaminationFX/ExaminationInput', 'kcMemberSite');
});

Scenario('Test item No.29 Click 365', async ({ I }) => {
    const click365ItemSelector = '//*[@data-testid="searchTop_click365_id"]';
    const dialogSelector = '//section[@role="dialog"]';
    await I.clickFixed(click365ItemSelector);
    await I.waitFor();
    I.seeElement(dialogSelector);
    I.see('以下の内容をご確認ください。', dialogSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.29_Click_365_open_dialog.png`);
});

Scenario('Test item No.30 Exchange CFD', async ({ I }) => {
    const exchangeCFDItemSelector = '//*[@data-testid="searchTop_exchangeCFD_id"]';
    const dialogSelector = '//section[@role="dialog"]';
    await I.clickFixed(exchangeCFDItemSelector);
    await I.waitFor();
    I.seeElement(dialogSelector);
    I.see('以下の内容をご確認ください。', dialogSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchGeneralPage}_Test_item_No.30_Click_Exchange_CFD_open_dialog.png`);
});

Scenario('Test item No.32 Kabucom iDeCo', async () => {
    const menuItem = '//*[@data-testid="searchTop_kabucomiDeCo_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/Ideco/Ideco/Top
    await common.clickCardItem(menuItem, '/ap/iPhone/Ideco/Ideco/Top', 'kcMemberSite');
});

Scenario('Test item No.34 Topics Calendar', async () => {
    const menuItem = '//*[@data-testid="searchTop_topicsCalendar_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/Calendar/Home/MonthlySP
    await common.clickCardItem(menuItem, '/ap/iPhone/Calendar/Home/MonthlySP', 'kcMemberSite');
});

Scenario('Test item No.35 KabuBoard Flash', async () => {
    const menuItem = '//*[@data-testid="searchTop_kabuBoardFlash_id"]';
    // Go to the following screen: {member-site-url}/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp
    await common.clickCardItem(menuItem, '/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp', 'kcMemberSite');
});
