import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('Symbol_ProductSearch - SearchSuggestion');

Before(async ({ I, loginAndSwitchToWebAs, search }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Brand/Product Search page', async ({ I }) => {
    I.seeElement('//*[@data-testid="searchTop_keywordInput_id"]');
    I.seeTextEquals('銘柄検索', '//*[@data-testid="common_header_title_id"]');
    <PERSON>.assertContain(await I.grabCurrentUrl(), '/mobile/search', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Display_Brand_Product_Search_Screen_page.png`);
});

Scenario('Test item No.1 Keyword input', async ({ I }) => {
    const keywordInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const searchHistorySelector = '//*[@data-testid="search_searchHistory_id"]';
    await I.clickFixed(keywordInputSelector);
    await I.waitFor();
    I.seeElement(searchHistorySelector);
    I.see('検索履歴', searchHistorySelector);
    I.assertEqual(await I.isKeyboardShown(), true, 'The keyboard must be shown');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.1_Show_Keyword_input.png`);
    I.fillField(keywordInputSelector, '101');
    I.pressKey('Enter');
    await I.waitFor('mediumWait');
    I.seeElement('//*[@data-testid="searchResult_symbolList_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search/result', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.1_Move_to_the_brand_product_search_result_list_page.png`);
});

Scenario('Test item No.2 Cancel', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const searchResultInputSelector = '//*[@data-testid="searchResult_keywordInput_id"]//input';
    const searchCancelButtonSelector = '//*[@data-testid="search_cancel_id"]';
    const searchResultListSelector = '//*[@data-testid="searchResult_symbolList_id"]';
    await I.clickFixed(searchTopInputSelector);
    await I.waitFor();
    I.fillField(searchTopInputSelector, '101');
    I.pressKey('Enter');
    await I.waitFor('mediumWait');
    I.seeElement(searchResultListSelector);
    await I.clickFixed(searchResultInputSelector);
    await I.clickFixed(searchCancelButtonSelector);
    await I.waitFor();
    I.see('国内株式・米国株式・投資信託の銘柄検索が可能です', 'body');
    I.see('国内株式を探す', 'body');
    I.waitForElement(searchTopInputSelector, 3);
    const inputValue = await I.executeScript(() => {
        const input = document.querySelector('[data-testid="searchTop_keywordInput_id"] input') as HTMLInputElement;
        return input.value;
    });
    I.assertEmpty(inputValue, 'Input field is not empty after clicking cancel');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.2_Click_cancel_search_button.png`);
});

Scenario('Test item No.4 History text', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const firstHistoryTextSelector = '//*[@data-testid="search_historyText_id_0"]';
    await I.clickFixed(searchTopInputSelector);
    I.seeElement('//*[@data-testid="search_searchHistory_id"]');
    const firstHistoryText = await I.grabTextFrom(`${firstHistoryTextSelector}/p`);
    await I.clickFixed(firstHistoryTextSelector);
    const inputValue = await I.executeScript(() => {
        const input = document.querySelector('[data-testid="searchTop_keywordInput_id"] input') as HTMLInputElement;
        return input.value;
    });
    I.assertEqual(inputValue, firstHistoryText, 'Input value does not match the history text');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.4_Click_history_text.png`);
});

Scenario('Test item No.7 Search Suggestions (Domestic Stocks) Items', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const firstDomesticStockSearchSuggestionSelector = '//*[@data-testid="searchSuggestion_domesticStockItem_id_0"]';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    await I.clickFixed(firstDomesticStockSearchSuggestionSelector);
    await I.waitFor();
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/stock/basic', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.7_Search_Suggestions_Domestic_Stocks_Items.png`);
});

Scenario('Test item No.12 Search Suggestions (US Stocks) Item', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user17 });
    await search.goToPage();
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const firstUSStockSearchSuggestionSelector = '//*[@data-testid="searchSuggestion_usStockItem_id_0"]';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    await I.clickFixed(firstUSStockSearchSuggestionSelector);
    await I.waitFor();
    // US Stock Information - Go to Summary
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/usstock/summary', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.12_Search_Suggestions_US_Stocks_Items.png`);
});

Scenario('Test item No.18 Search Suggestions (US Stock Theme) Item', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const firstUSStockThemeSearchSuggestionSelector = '//*[@data-testid="searchSuggestion_usStockThemeItem_id_0"]';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    // Specify a Ticker and move to the following URL {member-site-url}/ap/iPhone/InvInfo/USMarket/Theme/Detail?Id=${themeId}
    await common.clickCardItem(firstUSStockThemeSearchSuggestionSelector, '/ap/iPhone/InvInfo/USMarket/Theme/Detail', 'kcMemberSite');
});

Scenario('Test item No.16 Search suggestions (domestic stock theme) items', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const firstDomesticStockThemeSearchSuggestionSelector = '//*[@data-testid="searchSuggestion_domesticStockThemeItem_id_0"]';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    await I.clickFixed(firstDomesticStockThemeSearchSuggestionSelector);
    await I.waitFor();
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search/theme/detail', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.16_Search_Suggestions_Domestic_Stocks_theme_Items.png`);
});

Scenario('Test item No.25 Filter conditions', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const filterConditionsSelector = '//*[@data-testid="searchSuggestion_filterCondition_id"]';
    const filterConsitionsItemSelector = '//*[@data-testid="common_MenuItem_id"][contains(@aria-checked, "false")]';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    await I.clickFixed(filterConditionsSelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.25_Open_Filter_conditions.png`);
    await I.clickFixed(filterConsitionsItemSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.25_Click_Filter_conditions_item.png`);
});

Scenario('Test item No.28 Search suggestions (investment trust) items', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const firstInvestmentTrustSearchSuggestionSelector = '//*[@data-testid="searchSuggestion_investmentTrustItem_id_0"]';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    await I.clickFixed(firstInvestmentTrustSearchSuggestionSelector);
    await I.waitFor('mediumWait');
    // Go to fund details
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/fund/detail', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.28_Go_to_fund_details.png`);
});

Scenario('Test item No.3 Swipe up or down Search history', async ({ I }) => {
    const searchTopInputSelector = '//*[@data-testid="searchTop_keywordInput_id"]//input';
    const searchHistorySelector = '//*[@data-testid="search_searchHistory_id"]/..';
    await I.clickFixed(searchTopInputSelector);
    I.fillField(searchTopInputSelector, '101');
    await I.waitFor();
    I.blur(searchTopInputSelector);
    await I.swipeUpFixed(searchHistorySelector);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.3_Swipe_up_Search_history.png`);
    await I.swipeDownFixed(searchHistorySelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchSuggestion}_Test_item_No.3_Swipe_down_Search_history.png`);
});
