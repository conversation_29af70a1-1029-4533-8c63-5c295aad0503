import { event } from 'codeceptjs';
import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockPetitTradingCancel');

let userId = USER_ID.user29;
const scenarioItemsWithCondition = ['Test item No.20 Check for entered password'];
event.dispatcher.on(event.test.before, (test) => {
    userId = scenarioItemsWithCondition.includes(test.title) ? USER_ID.user78 : USER_ID.user29;
});

Before(async ({ I, loginAndSwitchToWebAs, stockPetitOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: userId });
    await stockPetitOrder.goToPetitOrderCancel('8306');
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Petit Trading Cancel', async ({ stockPetitOrder }) => {
    await stockPetitOrder.compareUrl('/mobile/trade/petit/cancel');
    stockPetitOrder.takeScreenshot.petitOrderCancel('Display_Domestic_Stock_Petit_Trading_Cancel');
});

Scenario('Test item No.14+15 Fill Password + Check for omitted password', async ({ I, stockPetitOrder }) => {
    const passwordSelector = '//*[@data-testid="tradePetitCancel_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitCancel_passwordOmissionCheck_id"]';
    const cancelConfirmButton = '//*[@data-testid="tradePetitCancel_confirmCancel_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(cancelConfirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitOrderCancel('Test_item_No.14_15_Check_state_of_Cancel_Confirmation');
});

Scenario(scenarioItemsWithCondition[0], async ({ I, stockPetitOrder }) => {
    const inputPasswordCheckSelector = '//*[@data-testid="tradePetitCancel_checkInputPassword_id"]';
    const cancelConfirmButton = '//*[@data-testid="tradePetitCancel_confirmCancel_id"]';
    await I.clickFixed(inputPasswordCheckSelector);
    await I.scrollToElement(cancelConfirmButton);
    I.assertEqual(
        await I.grabCssPropertyFrom(cancelConfirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitOrderCancel('Test_item_No.20_Check_state_of_Cancel_Confirmation');
});

Scenario('Test item No.16-3 Confirm Cancellation', async ({ I, stockPetitOrder }) => {
    const passwordSelector = '//*[@data-testid="tradePetitCancel_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitCancel_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitCancel_confirmCancel_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor();
    I.seeElement('#order-inquiry-detail');
    await stockPetitOrder.compareUrl('/mobile/order-inquiry/petit/detail');
    stockPetitOrder.takeScreenshot.petitOrderCancel(
        'Test_item_No.16-2_Tap_Confirm_Cancellation_to_transition_to_detail_page',
    );
});

Scenario('Test item No.17 Return to the order inquiry screen', async ({ I, stockPetitOrder }) => {
    const backToOrderInquirySelector = '//*[@data-testid="tradePetitCancel_backToOrderInquiry_id"]';
    await I.clickFixed(backToOrderInquirySelector);
    await I.waitFor();
    I.seeElement('#order-inquiry-detail');
    await stockPetitOrder.compareUrl('/mobile/order-inquiry/petit/detail');
    stockPetitOrder.takeScreenshot.petitOrderCancel('Test_item_No.17_Transition_to_order_inquiry_details_page');
});
