import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundBuyComplete');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });

    await tradeFund.buyInput.goToPage({ fundCode: '02311862' });
    await I.waitFor(); // wait for page loaded
    await tradeFund.buyInput.goToConfirmScreen();
    await I.waitFor(); // wait for page loaded
    await tradeFund.buyConfirm.apply();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('trade fund buy complete [レイアウト]', async ({ I }) => {
    I.waitInUrl('/mobile/trade/fund/buy/complete', 5);
    await commonUi.header.verifyTitle('投資信託買注文');
    I.saveScreenshot('1057.trade_fund_buy_complete_page.png');
});

// 1.注文番号
Scenario('trade fund buy complete [1.注文番号]', async ({ I }) => {
    await tradeFund.complete.verifyOrderId('20250520A02000394140');
    I.saveScreenshot('1057.trade_fund_buy_complete_1_order_id.png');
});

// 2.注文照会
Scenario('trade fund buy complete [2.注文照会]', async ({ I }) => {
    await tradeFund.complete.goToOrderInquiry();
    I.saveScreenshot('1057.trade_fund_buy_complete_2_order_inquiry.png');
});

// 3.注文取消
Scenario('trade fund buy complete [3.注文取消]', async ({ I }) => {
    await tradeFund.complete.goToOrderCancellation();
    I.saveScreenshot('1057.trade_fund_buy_complete_3_order_cancellation.png');
});

// 4.ファンド検索
Scenario('trade fund buy complete [4.ファンド検索]', async ({ I }) => {
    await tradeFund.complete.goToFundSearch();
    I.saveScreenshot('1057.trade_fund_buy_complete_4_fund_search.png');
});

// ブラウザバック
Scenario('trade fund buy complete [ブラウザバック]', async ({ I }) => {
    await tradeFund.complete.browserBack();
    I.saveScreenshot('1057.trade_fund_buy_complete_browser_back.png');
});
