import { COMMON_BACK_BUTTON, COOKIE_KEY, USER_ID } from '../const/constant';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundSellInput');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user38 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await <PERSON>.waitFor();
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test trade fund sell confirm', async ({ I }) => {
    await tradeFund.sellInput.goToTradeFundSellInputPage();
    await tradeFund.sellInput.fillGroupInputNumber();
    await tradeFund.sellInput.goToTradeFundConfirmPage();
    I.saveScreenshot(`1069.${tradeFund.sellInput.screenShotPrefix.fundSell}.0_Go_to_trade_fund_sell_confirm.png`);
});

// 10.予定受渡金額ヘルプ -> ツールチップが表示されること
Scenario('Test No.10 Scheduled delivery amount help -> Tooltips should be displayed', async ({ I }) => {
    I.seeElement(tradeFund.sellInput.locators.amountHelp);
    await I.clickFixed(tradeFund.sellInput.locators.amountHelp);
    await I.saveScreenshot(
        `1070.${tradeFund.sellInput.screenShotPrefix.fundConfirm}.10_Scheduled_sdelivery_amount_help-Tooltips_should_be_displayed.png`,
    );
    await I.waitFor();
    await I.clickFixed(tradeFund.sellInput.locators.amountHelp);
});

// 14.パスワード -> 国内株式現物取引-買注文-注文確認参照
Scenario('Test No.14 Password -> Domestic Stock Pure Trading-Buy Order-See Order Confirmation', async ({ I }) => {
    await tradeFund.sellInput.setSellConfirmSessionData({ isOmmitPassword: false });
    await I.waitFor();
    I.seeElement(tradeFund.sellInput.locators.passwordInput);
    I.fillField(tradeFund.sellInput.locators.passwordInput, '123');
    I.saveScreenshot(
        `1071.${tradeFund.sellInput.screenShotPrefix.fundConfirm}.14_fill_input_password-Domestic_stock_trading-Buy_order-See_Order_confirmation.png`,
    );
});

// 15.パスワード省略チェック -> 国内株式現物取引-買注文-注文確認参照
Scenario(
    'Test No.15 Check password omitted -> Domestic stock trading -Buy order -See Order confirmation',
    async ({ I }) => {
        I.seeElement(tradeFund.sellInput.locators.passwordOmissionCheck);
        await I.clickFixed(tradeFund.sellInput.locators.passwordOmissionCheck);
        I.saveScreenshot(
            `1071.${tradeFund.sellInput.screenShotPrefix.fundConfirm}.15_Check_password_omitted-Domestic_stock_trading-Buy_order-See_Order_confirmation.png`,
        );
    },
);

// 17.パスワード入力チェック -> 国内株式現物取引-買注文-注文確認参照
Scenario(
    'Test No.17 Check password input -> Domestic Stock Pure Trading-Buy Order-See Order Confirmation',
    async ({ I }) => {
        await tradeFund.sellInput.setSellConfirmSessionData({ isOmmitPassword: true });
        await I.waitFor();
        I.seeElement(tradeFund.sellInput.locators.passwordInputCheck);
        await I.clickFixed(tradeFund.sellInput.locators.passwordInputCheck);
        I.saveScreenshot(
            `1071.${tradeFund.sellInput.screenShotPrefix.fundConfirm}.15_Check_password_input-Domestic_stock_trading-Buy_order-See_Order_confirmation.png`,
        );
    },
);

// 18.注文を確定
Scenario('Test No.18 Confirm your order', async ({ I }) => {
    // Temporarily skip these 2 cases because they cannot be reproduced
    // 1. isOpe=trueの場合は、opeエラーダイアログを表示し、後続処理を行わない
    // 2. ローディング中にブラウザバックされた場合: 注文照会-投資信託-一覧に遷移

    // 投信売却完了に遷移する
    I.seeElement(tradeFund.sellInput.locators.confirmOrderButton);
    await I.clickFixed(tradeFund.sellInput.locators.confirmOrderButton);
    I.saveScreenshot(
        `1074.${tradeFund.sellInput.screenShotPrefix.fundConfirm}.15_Check_password_input-Domestic_stock_trading-Buy_order-See_Order_confirmation.png`,
    );
});

// 19.ご注意文言 -> アコーディオンを開閉する
Scenario('Test No.19 Opening and closing the accordion', async ({ I }) => {
    await tradeFund.sellInput.goToTradeFundSellInputPage();
    await tradeFund.sellInput.fillGroupInputNumber();
    await tradeFund.sellInput.goToTradeFundConfirmPage();
    await I.waitFor();
    I.seeElement(tradeFund.sellInput.locators.sellConfirmCautionMessage);
    await I.clickFixed(tradeFund.sellInput.locators.sellConfirmCautionMessage);
    await I.swipeElementDirection('up', tradeFund.sellInput.locators.sellConfirmCautionMessage);
    I.saveScreenshot(`${tradeFund.sellInput.screenShotPrefix.fundConfirm}.19_Open_the_accordion.png`);
    await I.waitFor();
    await I.clickFixed(tradeFund.sellInput.locators.sellConfirmCautionMessage);
    I.saveScreenshot(`1075.${tradeFund.sellInput.screenShotPrefix.fundConfirm}.19_Close_the_accordion.png`);
});
