import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundBuyInput');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });

    await tradeFund.buyInput.goToPage({ fundCode: '02311862' });
    await I.waitFor(); // wait for page loaded
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test trade fund buy input', async ({ I }) => {
    I.waitInUrl('/mobile/trade/fund/buy/input', 5);
    await commonUi.header.verifyTitle('投資信託買注文');
    I.saveScreenshot('1024.trade_fund_buy_input_page.png');
});

// 11.入金依頼
Scenario('test trade fund buy input [11.入金依頼]', async ({ I }) => {
    await tradeFund.buyInput.deposit();
    await I.saveScreenshot('1024.trade_fund_buy_input_11_deposit.png');
});

// 13.口座区分ヘルプ
Scenario('test trade fund buy input [13.口座区分ヘルプ]', async ({ I }) => {
    await tradeFund.buyInput.openAccountTypeHelpTooltip();
    await I.saveScreenshot('1024.trade_fund_buy_input_13_account_type_help.png');
});

// 14.口座区分
Scenario('test trade fund buy input [14.口座区分]', async ({ I }) => {
    await tradeFund.buyInput.verifyAcountTypeSelection();
    await I.saveScreenshot('1024.trade_fund_buy_input_14_account_type.png');
});

// 16.NISA利用枠ヘルプ
Scenario('test trade fund buy input [16.NISA利用枠ヘルプ]', async ({ I }) => {
    await tradeFund.buyInput.selectAccountType('NISA');
    await tradeFund.buyInput.openNISAUtilizationHelpTooltip();
    await I.saveScreenshot('1024.trade_fund_buy_input_16_nisa_utilization_help.png');
});

// 23.分配金ヘルプ
Scenario('test trade fund buy input [23.分配金ヘルプ]', async ({ I }) => {
    await tradeFund.buyInput.openDistributionHelpTooltip();
    await I.saveScreenshot('1024.trade_fund_buy_input_23_distribution_help.png');
});

// 24.分配金
Scenario('test trade fund buy input [24.分配金]', async ({ I }) => {
    await tradeFund.buyInput.verifyDistributionSelection();
    await I.saveScreenshot('1024.trade_fund_buy_input_24_distribution.png');
});

// 26.金額入力テキストボックス
Scenario('test trade fund buy input [26.金額入力テキストボックス]', async ({ I }) => {
    await tradeFund.buyInput.fillSuggestInputNumber();
    await I.saveScreenshot('1024.trade_fund_buy_input_26_suggest_input_number.png');
});

// 29.入力補助ボタン
Scenario('test trade fund buy input [29.入力補助ボタン]', async () => {
    await tradeFund.buyInput.selectSuggestInputNumberGroup('1024.trade_fund_buy_input_29_suggest_input_number');
});

// 30.Ponta利用
Scenario('test trade fund buy input [30.Ponta利用]', async ({ I }) => {
    await I.saveScreenshot('1024.trade_fund_buy_input_30_ponta_before.png');
    await tradeFund.buyInput.toggleSwitchMoneyCell();
    await I.saveScreenshot('1024.trade_fund_buy_input_30_ponta_after.png');
});

// 32.ポイント入力テキストボックス
Scenario('test trade fund buy input [32.ポイント入力テキストボックス]', async ({ I }) => {
    await tradeFund.buyInput.fillGroupInputNumber();
    await I.saveScreenshot('1024.trade_fund_buy_input_32_group_input_number.png');
});

// 33.全て利用
Scenario('test trade fund buy input [33.全て利用]', async ({ I }) => {
    await tradeFund.buyInput.useAllGroupInputNumber();
    await I.saveScreenshot('1024.trade_fund_buy_input_33_group_input_number_use_all.png');
});

// 37.受渡方法ヘルプ
Scenario('test trade fund buy input [37.受渡方法ヘルプ]', async ({ I }) => {
    await tradeFund.buyInput.openPaymentMethodHelpTooltip();
    await I.saveScreenshot('1024.trade_fund_buy_input_37_payment_method_help.png');
});

// 39.約定日ヘルプ
Scenario('test trade fund buy input [39.約定日ヘルプ]', async ({ I }) => {
    await tradeFund.buyInput.openTradeDateHelpTooltip();
    await I.saveScreenshot('1024.trade_fund_buy_input_39_trade_date_help.png');
});

// 41.受渡日ヘルプ
Scenario('test trade fund buy input [41.受渡日ヘルプ]', async ({ I }) => {
    await tradeFund.buyInput.openSettlementDateHelpTooltip();
    await I.saveScreenshot('1024.trade_fund_buy_input_41_settlement_date_help.png');
});

// 46.交付目論見書-リンク
Scenario('test trade fund buy input [46.交付目論見書-リンク]', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user37 });
    await I.refreshPage();
    await I.scrollToElement(locate(tradeFund.buyInput.locators.deliveryProspectusLink).toXPath());
    await I.waitFor();
    await tradeFund.buyInput.openDeliveryProspectusLink();
    await I.saveScreenshot('1024.trade_fund_buy_input_46_delivery_prospectus_link.png');
});

// 47.交付目論見書-確認ボタン
Scenario('test trade fund buy input [47.交付目論見書-確認ボタン]', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user37 });
    await I.refreshPage();
    await I.scrollToElement(locate(tradeFund.buyInput.locators.deliveryProspectusConfirmButton).toXPath());
    await I.waitFor();
    await tradeFund.buyInput.openDeliveryProspectusConfirmButton();
    await I.saveScreenshot('1024.trade_fund_buy_input_47_delivery_prospectus_confirm_button.png');
});

// 49.目論見書補完書面-リンク
Scenario('test trade fund buy input [49.目論見書補完書面-リンク]', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user37 });
    await I.refreshPage();
    await I.scrollToElement(locate(tradeFund.buyInput.locators.spdLink).toXPath());
    await I.waitFor();
    await tradeFund.buyInput.openSupplementaryProspectusDocumentLink();
    await I.saveScreenshot('1024.trade_fund_buy_input_49_supplementary_prospectus_document_link.png');
});

// 50.目論見書補完書面-確認ボタン
Scenario('test trade fund buy input [50.目論見書補完書面-確認ボタン]', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user37 });
    await I.refreshPage();
    await I.scrollToElement(locate(tradeFund.buyInput.locators.spdConfirmButton).toXPath());
    await I.waitFor();
    await tradeFund.buyInput.openSupplementaryProspectusDocumentConfirmButton();
    await I.saveScreenshot('1024.trade_fund_buy_input_50_supplementary_prospectus_document_confirm_button.png');
});

// 52.ご確認事項-リンク
Scenario('test trade fund buy input [52.ご確認事項-リンク]', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user37 });
    await I.refreshPage();
    await I.scrollToElement(locate(tradeFund.buyInput.locators.confirmationItemLink).toXPath());
    await I.waitFor();
    await tradeFund.buyInput.openConfirmationItemLink();
    await I.saveScreenshot('1024.trade_fund_buy_input_52_confirmation_item_link.png');
});

// 53.ご確認事項-確認ボタン
Scenario('test trade fund buy input [53.ご確認事項-確認ボタン]', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user37 });
    await I.refreshPage();
    await I.scrollToElement(locate(tradeFund.buyInput.locators.confirmationItemConfirmButton).toXPath());
    await I.waitFor();
    await tradeFund.buyInput.openConfirmationItemConfirmButton();
    await I.saveScreenshot('1024.trade_fund_buy_input_53_confirmation_item_confirm_button.png');
});

// 56.非課税投資同意チェック
Scenario('test trade fund buy input [56.非課税投資同意チェック]', async ({ I }) => {
    await tradeFund.buyInput.selectAccountType('NISA');
    await tradeFund.buyInput.toggleTaxFreeInvestmentAgreeCheck();
    await I.saveScreenshot('1024.trade_fund_buy_input_56_tax_free_investment_agree_check_before.png');
    await I.waitFor();
    await tradeFund.buyInput.toggleTaxFreeInvestmentAgreeCheck();
    await I.saveScreenshot('1024.trade_fund_buy_input_56_tax_free_investment_agree_check_after.png');
});

// 57.注文確認画面へ
Scenario('test trade fund buy input [57.注文確認画面へ]', async ({ I }) => {
    await tradeFund.buyInput.goToConfirmScreen();
    await I.saveScreenshot('1024.trade_fund_buy_input_57_go_to_confirm_screen.png');
});

// 58.重要事項
Scenario('test trade fund buy input [58.重要事項]', async ({ I }) => {
    await I.scrollPageToBottom();
    await tradeFund.buyInput.expandImportantItem();
    await I.swipeDirection('up', 0.6, 600);
    await I.waitFor();
    await I.saveScreenshot('1024.trade_fund_buy_input_58_important_item.png');
});

// 59.ご注意文言
Scenario('test trade fund buy input [59.ご注意文言]', async ({ I }) => {
    await I.scrollPageToBottom();
    await tradeFund.buyInput.expandCautionMessage();
    await I.swipeDirection('up', 0.6, 600);
    await I.waitFor();
    await I.saveScreenshot('1024.trade_fund_buy_input_59_caution_message.png');
});
