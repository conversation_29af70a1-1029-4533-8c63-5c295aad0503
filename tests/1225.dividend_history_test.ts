import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('DividendHistory - Page');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await <PERSON><PERSON>waitFor();
    await <PERSON>.switchToWeb();
    await <PERSON>.waitFor();
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user45 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario(
    'Test Item 1: Check UI of dividend history page & jumto distribution history page',
    async ({ I, dividendHistory }) => {
        await dividendHistory.goToDividendHistoryPage();
        I.seeElement(dividendHistory.locators.distributionTabButton);
        I.clickFixed(dividendHistory.locators.distributionTabButton);
        await <PERSON><PERSON>waitFor('shortWait');
        I.seeInCurrentUrl(dividendHistory.urls.distribution);
        I.saveScreenshot(`${dividendHistory.screenshotPrefix}.1_jumto_distribution_history_list.png`);
    },
);

Scenario('Test Item 2: Show this screen view modal', async ({ I, dividendHistory }) => {
    // switch to dividend history page
    I.seeElement(dividendHistory.locators.dividendTabButton);
    I.clickFixed(dividendHistory.locators.dividendTabButton);
    // find the view this screen icon & click it
    const locator = dividendHistory.locators.viewThisScreenOpenIcon('dividendHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.waitForElement(dividendHistory.locators.viewScreenModal);
    I.see('配当金履歴の見方');
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.2_show_this_screen_view_modal.png`);
    I.clickFixed(dividendHistory.locators.viewScreenModalCloseButton);
});

Scenario('Test Item 3: Open screen display settings', async ({ I, dividendHistory }) => {
    await dividendHistory.openDisplaySetting('dividendHistoryList');
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.3_open_screen_display_setting.png`);
    dividendHistory.closeDisplaySetting();
});

Scenario('Test Item 14: Scroll to the top of the screen', async ({ I, dividendHistory }) => {
    await I.swipeElementDirection('up', dividendHistory.locators.swipeToElement('dividend'));
    await I.waitFor();
    I.seeElement(dividendHistory.locators.scrollToTopButton);
    I.click(dividendHistory.locators.scrollToTopButton);
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.14_scroll_to_the_top_of_the_screen.png`);
});

Scenario('Test Item 15: Switch ON/OFF product type display setting', async ({ I, dividendHistory }) => {
    await dividendHistory.openDisplaySetting('dividendHistoryList');
    const productTypes = ['DIVIDEND_PRODUCT_TYPE_STOCK', 'DIVIDEND_PRODUCT_TYPE_US_STOCK'];
    await dividendHistory.toggleChecker(productTypes);
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.15_switch_on_off_product_type_display_setting.png`);
});

Scenario('Test Item 16: toggle show display order drop-down menu', async ({ I, dividendHistory }) => {
    const locator = dividendHistory.locators.displayOrderButton('dividendHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.16_toggle_show_display_order.png`);
    const secondOption = `${locator}//p[2]`;
    I.seeElement(secondOption);
    I.clickFixed(secondOption);
    I.say('Select the second value option');
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.16_select_the_second_value_option.png`);
});

Scenario('Test Item 17: Switch ON/OFF display account filter display setting', async ({ I, dividendHistory }) => {
    const titleLocator = locate('p').withText('表示口座');
    const titleElement = await I.grabNumberOfVisibleElements(titleLocator);
    if (titleElement > 0) {
        const accountFilers = [
            'DIVIDEND_ACCOUNT_TYPE_KAZEI',
            'DIVIDEND_ACCOUNT_TYPE_SOGONISA_GLOW',
            'DIVIDEND_ACCOUNT_TYPE_NISA',
        ];
        await dividendHistory.toggleChecker(accountFilers);
        I.saveScreenshot(
            `${dividendHistory.screenshotPrefix}.17_switch_on_off_display_account_filter_display_setting.png`,
        );
    } else {
        I.say("I don't see the 表示口座, so I skip this step");
        I.saveScreenshot(`${dividendHistory.screenshotPrefix}.17_dont't_see_表示口座_skip_display_account_filter.png`);
    }
});

Scenario('Test Item 18: Clear items 15, 16, 17 to default', async ({ I, dividendHistory }) => {
    const locator = dividendHistory.locators.displaySettingClearButton('dividendHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.say("Click 'Clear' button to reset items 15, 16, 17 to default");
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.18_clear_items_15_16_17_to_default.png`);
});

Scenario('Test Item 19: Close the display settings modal', async ({ I, dividendHistory }) => {
    const locator = dividendHistory.locators.displaySettingConfirmButton('dividendHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.say("Click 'Confirm' button to close the display settings modal");
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.19_close_the_display_settings_modal.png`);
});

Scenario('Test Item 20: toggle show display period drop-down menu', async ({ I, dividendHistory }) => {
    await dividendHistory.openDisplaySetting('dividendHistoryList');
    const locator = dividendHistory.locators.displayPeriodButton('dividendHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.20_toggle_show_display_period.png`);
    const secondOption = `${locator}//p[2]`;
    I.seeElement(secondOption);
    I.clickFixed(secondOption);
    I.say('Select the second value option');
    const confirmLocator = dividendHistory.locators.displaySettingConfirmButton('dividendHistoryList');
    I.seeElement(confirmLocator);
    I.clickFixed(confirmLocator);
    I.saveScreenshot(`${dividendHistory.screenshotPrefix}.20_select_the_second_value_option.png`);
});
