import { COOKIE_KEY, USER_ID } from '../const/constant';
import notifyPage from '../pages/notifyPage';

Feature('Notify - NotifyTabPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyTab.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON>.closeBrowser();
    await I.switchToNative();
});

Scenario('test notify tab page', async ({ I }) => {
    I.waitForElement('//*[@data-testid="common_noti_id"]');
    I.click('//*[@data-testid="common_noti_id"]');
    await I.waitFor('mediumWait');
    I.saveScreenshot('232.notify_tab_notifyTab.png');
});

Scenario('test Item 1 New Information page', async ({ I }) => {
    const newInformationTab = '//*[@data-testid="noticeTab_newInformation_id"]';
    const noticeParent = '//*[@id="Notice-GeneralTab"]/..';
    await notifyPage.notifyTabView(newInformationTab, noticeParent);
    I.saveScreenshot('232.notify_tab_newInformationTab.png');
});

Scenario('test Item 2 Individual noti page', async ({ I }) => {
    const individualNotiTab = '//*[@data-testid="noticeTab_individualNoti_id"]';
    const noticeParent = '//*[@id="Notice-CustomerTab"]/..';
    await notifyPage.notifyTabView(individualNotiTab, noticeParent);
    I.saveScreenshot('232.notify_tab_individualNotiTab.png');
});

Scenario('test Item 3 Trading noti page', async ({ I }) => {
    const tradingNotiTab = '//*[@data-testid="noticeTab_tradingNoti_id"]';
    const noticeParent = '//*[@id="Notice-TransactionTab"]/..';
    await notifyPage.notifyTabView(tradingNotiTab, noticeParent);
    I.saveScreenshot('232.notify_tab_tradingNotiTab.png');
    await I.waitFor();
});
