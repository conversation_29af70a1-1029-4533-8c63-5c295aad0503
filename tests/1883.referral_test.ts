Feature('Referral - Page');
import { COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import Referral from '../pages/referral';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('go to referral page', async ({ I }) => {
    I.amOnPage(PAGE_URL.referral);
    await <PERSON><PERSON>waitFor('mediumWait');
    I.see(
        `ご家族・ご友人
紹介プログラム`,
        '//*[@data-testid="common_header_title_id"]',
    );
});

Scenario('Test Item 1: Referral method', async () => {
    // Test opening the collapse
    await Referral.openReferralMethodCollapse();
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_method_collapse_open.png`);

    // Test closing the collapse
    await Referral.closeReferralMethodCollapse();
    Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_method_collapse_closed.png`);
});

Scenario('Test Item 2: Benefit details', async () => {
    // Test opening the benefit details collapse
    await Referral.openBenefitDetailsCollapse();
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_benefit_details_collapse_open.png`);

    // Test closing the benefit details collapse
    await Referral.closeBenefitDetailsCollapse();
    Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_benefit_details_collapse_closed.png`);
});

Scenario('Test Item 7: Referral program for family and friends', async ({ I }) => {
    // Test opening the benefit details collapse
    await Referral.openBenefitDetailsCollapse();
    // Click on the referral program link which opens an external URL
    await Referral.clickReferralProgramLink();
    // Take a final screenshot
    Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_program_after_return.png`);
});

Scenario('Test Item 4: Refer by LINE', async ({ I }) => {
    // Take a screenshot before clicking
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_line_referral_before_click.png`);

    // Click on the LINE introduction link which opens an external URL
    const href = await Referral.clickLineIntroductionLink();

    // Verify the URL is correct
    I.assertContain(href, Referral.urls.linePage, 'External URL should be the LINE page');

    // Take a final screenshot
    Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_line_referral_after_return.png`);
});

Scenario('Test Item 5: Refer by mail', async ({ I }) => {
    // Take a screenshot before starting
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_email_referral_before_click.png`);

    // Navigate to the email referral flow
    const currentUrl = await Referral.navigateToEmailReferral();
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_email_referral_mail_input_page.png`);

    // Verify the URL contains the expected path
    I.assertContain(currentUrl, Referral.urls.emailInputPage, 'URL should contain the email input path');

    // Navigate back to the main referral page
    await Referral.navigateBackFromEmailInput();
    Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_email_referral_after_return.png`);
});

Scenario('Test Item 6: Issue referral URL', async ({ I }) => {
    // Take a screenshot before starting
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_sns_referral_before_click.png`);

    // Navigate to the SNS referral flow
    const currentUrl = await Referral.navigateToSnsReferral();
    await Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_sns_referral_generate_url_page.png`);

    // Verify the URL contains the expected path
    I.assertContain(currentUrl, Referral.urls.snsPage, 'URL should contain the SNS page path');

    // Navigate back to the main referral page
    await Referral.navigateBackFromEmailInput();
    Referral.takeScreenshot(`${SCREENSHOT_PREFIX.referral}_sns_referral_after_return.png`);
});
