import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveFundCancelReserveOrderConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor();


});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Fund Cancel Reserve Order Confirm', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.saveScreenshot('1523.Test_item_No.0_Check_UI_Reserve_Fund_Cancel_Reserve_Order_Confirm.png');
});
Scenario('Test Item 7: Check Withdrawal Result Notification - Notification Service Contact Settings', async ({ I, accumulation }) => {
    const withdrawalResultNotification = '//*[@data-testid="fundCancelReserveOrderConfirm_withdrawalResultNotification_id"]';
    await I.waitForElement(withdrawalResultNotification, 3);
    const span = withdrawalResultNotification + '//span[contains(text(), "通知サービス連絡先設定")]';
    await common.clickCardItem(span, '/ap/iPhone/personal/contact/List', 'kcMemberSite');
   

});
Scenario('Test Item 11: Check Password Text Input Form', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 1);
    I.scrollAndFill(passwordInput, '123@abc'); 
     // click img alt="eye" to see password
     const eyeIcon = '//img[@alt="eye"]';
     await I.waitForElement(eyeIcon, 1);
     I.clickFixed(eyeIcon);
     await I.waitFor('shortWait');
    I.saveScreenshot('1523.Test_item_No.11_Password_Text_Input_Form.png');
});
Scenario('Test Item 12: Check Password omission check: Switch ON/OFF', async ({ I }) => {

    const passwordInputOmissionCheck = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordOmissionCheck_id"]';
    await I.waitForElement(passwordInputOmissionCheck, 1);
    // ON
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1523.Test_item_No.12_Password_omission_check_ON.png');
    // OFF
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1523.Test_item_No.12_Password_omission_check_OFF.png');
});
Scenario('Test Item 14: Check Password input check: Switch between ON and OFF', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user56 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.waitFor()
    const passwordInputCheck = '//*[@data-testid="fundCancelReserveOrderConfirm_checkInputPassword_id"]//button';
    await I.waitForElement(passwordInputCheck, 3);
    I.scrollAndClick(passwordInputCheck);
    I.saveScreenshot('1523.Test_item_No.14_Password_input_check_ON.png');
    // OFF
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1523.Test_item_No.14_Password_input_check_OFF.png');
});
Scenario.skip('Test Item 15.2: backed up during loading, the page will transition to the savings plan page', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);

    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1523.Test_item_No.15.2_before_click_button.png');
    I.clickFixed(confirmButton);
    
    I.executeScript(() => window.history.back());
    await I.waitFor('shortWait');
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1523.Test_item_No.15.2_redirect_to_reserve_plan.png');
    //back 
    await I.backToPreviousScreen();

});
Scenario('Test Item 15.3: Check Execute loading - Transition to Accumulation - Investment Trust - Accumulation Cancel Complete', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.waitFor()
    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    await I.scrollAndClick(passwordInput);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1523.Test_item_No.15.3_before_click_button.png');
    I.clickFixed(confirmButton);
    await I.waitFor();
    I.seeElement('//p[contains(text(), "積立中止が完了しました。")]');
    I.saveScreenshot('1523.Test_item_No.15.3_Check_Execute_loading_Transition_to_Accumulation_Investment_Trust_Accumulation_Cancel_Complete.png');
});
Scenario('Test Item 16: Check Caution Message', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.waitFor()
    const cautionMessage = '//*[@data-testid="fundCancelReserveOrderConfirm_caution_id"]';
    await I.waitForElement(cautionMessage, 1);
    I.scrollAndClick(cautionMessage);
    const chakraCollapse = cautionMessage + '//*[@class="chakra-collapse"]';
    await I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1523.Test_item_No.16_Check_Caution_Message.png');
});
Scenario('Test Item 20: Check Open URL in new tab', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user56 });
    await I.waitFor()
    await I.refreshPage();
    I.waitFor()
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    I.waitFor()
    const linkElement = '//span[text()="こちら"]';
    await I.waitForElement(linkElement, 5);
    I.scrollToElement(linkElement);
    await common.clickCardItem(linkElement, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
    
    I.saveScreenshot('1523.Test_item_No.20_Check_Open_URL_in_new_tab.png');
});
