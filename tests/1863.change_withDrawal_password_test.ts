Feature('Settings_Entry - ChangeWithdrawalPasswordPage');
import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import ChangeLoginPassword from '../pages/changeLoginPassword';
import common from '../pages/search/common';

// Import the ChangeWithdrawalPassword page object
const changeWithdrawalPasswordLocators = {
    currentPasswordInput: '//*[@data-testid="changeWithdrawalPasswordPage_currentWithdrawalPassword_id"]/div/input',
    newPasswordInput: '//*[@data-testid="changeWithdrawalPasswordPage_newWithdrawalPassword_id"]/div/input',
    newPasswordConfirmInput:
        '//*[@data-testid="changeWithdrawalPasswordPage_newWithdrawalPasswordForConfirmation_id"]/div/input',
    pageDescription: '//*[@id="__next"]/div/div[2]/div/form/div[1]',
    descriptionSelector: '//*[@id="__next"]/div/div[2]/div/form/div[5]',
    submitButton: '//*[@data-testid="changeWithdrawalPasswordPage_changeButton_id"]',
    modalSelector: '//*[@aria-modal="true"]',
    url: '/mobile/setting/password/change-withdrawal-password',
    link: '//*[@data-testid="changeWithdrawalPasswordPage_resetWithdrawalPassword_id"]',
};

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('go to change withdrawal password page', async () => {
    await ChangeLoginPassword.navigateToPage(
        '//*[@id="__next"]/div/div[2]/div/form/div[1]',
        '/mobile/setting/password/change-withdrawal-password',
    );
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeWithdrawalPassword}_page.png`);
});

Scenario('Test Item 1: Reset Withdrawal Password', async ({ I }) => {
    I.seeElement(changeWithdrawalPasswordLocators.link);
    I.click(changeWithdrawalPasswordLocators.link);
    await I.waitFor('mediumWait');
    const url = await I.grabCurrentUrl();
    I.assertContain(url, 'setting/password/reset-withdrawal-password');
    I.click('//*[@data-testid="common_back_id"]');
    await I.waitFor('mediumWait');
});

Scenario('Test Item 2: Current Withdrawal Password', async () => {
    await ChangeLoginPassword.fillPasswordField(changeWithdrawalPasswordLocators.currentPasswordInput, '123456');
    ChangeLoginPassword.takeScreenshot(
        `${SCREENSHOT_PREFIX.changeWithdrawalPassword}_current_withdrawal_password_input.png`,
    );
});

Scenario('Test Item 3: New Withdrawal Password', async () => {
    await ChangeLoginPassword.fillPasswordField(changeWithdrawalPasswordLocators.newPasswordInput, '654321');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeWithdrawalPassword}_new_password_input.png`);
});

Scenario('Test Item 4: New Withdrawal Password (for confirmation)', async () => {
    await ChangeLoginPassword.fillPasswordField(changeWithdrawalPasswordLocators.newPasswordConfirmInput, '654321');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeWithdrawalPassword}_new_password_confirm_input.png`);
});

Scenario('Test Item 5: Complete Password Change Form', async ({ I }) => {
    await ChangeLoginPassword.submitForm(
        changeWithdrawalPasswordLocators.descriptionSelector,
        changeWithdrawalPasswordLocators.submitButton,
        changeWithdrawalPasswordLocators.modalSelector,
        false,
        `${SCREENSHOT_PREFIX.changeWithdrawalPassword}_complete_password_change_form.png`
    );
    await common.checkAndLoginAgain();
});
