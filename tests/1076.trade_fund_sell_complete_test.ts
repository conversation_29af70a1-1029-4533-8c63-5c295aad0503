import { COOKIE_KEY, USER_ID } from '../const/constant';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundSellInput');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user38 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await I.waitFor();
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test trade fund sell complete', async ({ I }) => {
    await tradeFund.sellInput.goToTradeFundSellInputPage();
    await tradeFund.sellInput.fillGroupInputNumber();
    await tradeFund.sellInput.goToTradeFundConfirmPage();
    await tradeFund.sellInput.goToTradeFundCompletePage();
    I.saveScreenshot(`1076.${tradeFund.sellInput.screenShotPrefix.fundComplete}.0_Go_to_trade_fund_sell_complete.png`);
});

// 1.注文番号 -> 注文照会-投資信託-一覧に遷移
Scenario('Test No.1 Order number -> Order inquiry-Investment trust-Move to list', async ({ I }) => {
    I.seeElement(tradeFund.sellInput.locators.fundOrderItem);
    await I.clickFixed(tradeFund.sellInput.locators.fundOrderItem);
    I.waitForText('注文約定照会\n投資信託', 5, 'p');
    I.saveScreenshot(
        `1077.${tradeFund.sellInput.screenShotPrefix.fundComplete}.1_Order_number-Order_inquiry-Investment_trust-Move_to_list.png`,
    );
    await tradeFund.sellInput.backPage();
});

// 2.注文照会 -> 注文照会-投資信託-一覧に遷移
Scenario('Test No.2 Order number -> Order inquiry-Investment trust-Move to list', async ({ I }) => {
    I.seeElement(tradeFund.sellInput.locators.orderInquiryButton);
    await I.clickFixed(tradeFund.sellInput.locators.orderInquiryButton);
    I.waitForText('注文約定照会\n投資信託', 5, 'p');
    I.saveScreenshot(
        `1078.${tradeFund.sellInput.screenShotPrefix.fundComplete}.2_Order_number-Order_inquiry-Investment_trust-Move_to_list.png`,
    );
    await tradeFund.sellInput.backPage();
});

// 3.注文取消 -> 投信取消確認に遷移
Scenario('Test No.3 Order cancellation -> Transition to Investment Trust Cancellation', async ({ I }) => {
    I.seeElement(tradeFund.sellInput.locators.orderCancellationButton);
    await I.clickFixed(tradeFund.sellInput.locators.orderCancellationButton);
    I.waitForText('投資信託\n注文取消', 5, 'p');
    I.saveScreenshot(
        `1079.${tradeFund.sellInput.screenShotPrefix.fundComplete}.3_Order_cancellation-Transition_to_Investment_Trust_Cancellation.png`,
    );
    await tradeFund.sellInput.backPage();
});

// 4.ファンド検索 -> ファンド検索に遷移
Scenario('Test No.4 Fund Search -> Move to Fund Search', async ({ I }) => {
    I.seeElement(tradeFund.sellInput.locators.fundSearchButton);
    await I.clickFixed(tradeFund.sellInput.locators.fundSearchButton);
    I.waitForText('ファンド検索', 5, 'p');
    I.saveScreenshot(`1080.${tradeFund.sellInput.screenShotPrefix.fundComplete}.4_Fund_Search-Move_to_Fund_Search.png`);
    await tradeFund.sellInput.backPage();
    // ブラウザバック -> 注文照会-投資信託-一覧に遷移
    // Browser Back -> Order Inquiry-Investment Trust-Move to list
    I.performBrowserBack();
    I.saveScreenshot(
        `1081.${tradeFund.sellInput.screenShotPrefix.fundComplete}.4_Browser_back_go_to_Order_Inquiry-Investment_Trust-Move_to_list.png`,
    );
});
