import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';

Feature('InvestmentProducts - USStockInfoSummary');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Check UI US Stock Information-News', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/analysis?symbol=123';
    I.amOnPage(pageUrl);
    I.see('米国株式\n個別銘柄情報', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(pageUrl);
    I.saveScreenshot('617.usstock_info_analysis_No.0_access_the_usstock_info_news_page.png');
});

// C1.銘柄名 -> 米国株式銘柄情報-サマリに遷移
Scenario('Item C1. Navigate to the U.S. Stock Information - Summary page', async ({ I }) => {
    const locator = '//a[@data-testid="usStockAnalysis_symbolName1_id_FTEK"]';
    await I.scrollToElement(locator);
    await I.saveScreenshot('617.usstock_info_analysis_C1_swipe_to_related_stock_area.png');
    await I.seeAndClickElement(locator, { waitFor: 'mediumWait' });
    I.seeInCurrentUrl('/mobile/info/usstock/summary');
    await I.saveScreenshot('617.usstock_info_analysis_C1_navigate_to_the_summury.png');
});
