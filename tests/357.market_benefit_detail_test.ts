import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('Market - BenefitDetail');

Before(async ({ I, loginAndSwitchToWebAs, market }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user14 });
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.waitFor();
    await I.clickFixed('//*[@data-testid="benefitList_benefitDetail_id"]');
    await I.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await <PERSON>.switchToNative();
});

Scenario('Test Display Market Benefit Detail page', async ({ I }) => {
    I.seeElement('//*[@data-testid="benefitDetail_cashBuyOrder_id"]');
    I.seeElement('//*[@data-testid="benefitDetail_marginNewSell_id"]');
    I.seeElement('//*[@data-testid="benefitDetail_symbolInfo_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/benefit/detail', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Display_Benefit_Detail_page.png`);
});

Scenario('Test item No.5 Favorites', async ({ I }) => {
    const favoriteButton = '//*[@data-testid="benefitDetail_favorite_id"]';
    const favoriteModal = '//*[@data-testid="common_rightSlide_favorite_id"]';
    await I.clickFixed(favoriteButton);
    await I.waitFor();
    I.seeElement(favoriteModal);
    I.see('お気に入り追加', favoriteModal);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.5_Show_favorite_registration_modal.png`);
});

Scenario('Test item No.8 Company website', async ({ I }) => {
    const companyLink = '//*[@data-testid="benefitDetail_corporateHP_id"]';
    await common.clickCardItem(companyLink, 'https://www.besterra.co.jp', 'external');
});

Scenario('Test item No.20 Photos - Page Control', async ({ I }) => {
    const commonSwiperBulletsSelector = '//*[@data-testid="common_swiper_bullets_id"]';
    const firstSwiperBulletSelector = `${commonSwiperBulletsSelector}/ul/li[1]`;
    const secondSwiperBulletSelector = `${commonSwiperBulletsSelector}/ul/li[2]`;
    await I.swipeUpFixed('//*[@data-testid="benefitDetail_symbolInfo_id"]/..');
    await I.clickFixed(secondSwiperBulletSelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.20_Photos_Page_Control_page_2.png`);
    await I.clickFixed(firstSwiperBulletSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.20_Photos_Page_Control_page_1.png`);
});

Scenario('Test item No.26 Spot purchase order', async ({ I }) => {
    const cashBuyOrderSelector = '//*[@data-testid="benefitDetail_cashBuyOrder_id"]';
    await I.clickFixed(cashBuyOrderSelector);
    await I.waitFor('mediumWait');
    I.see('現物買', '//*[@data-testid="common_header_title_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/trade/stock/buy', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.26_Transition_to_domestic_stock_spot_trading_Buy_order_entry.png`);
});

Scenario('Test item No.27 New margin sell', async ({ I }) => {
    const marginNewSellSelector = '//*[@data-testid="benefitDetail_marginNewSell_id"]';
    await I.clickFixed(marginNewSellSelector);
    await I.waitFor('mediumWait');
    I.see('信用新規', '//*[@data-testid="common_header_title_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/trade/margin/new', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.27_Transition_to_domestic_stock_margin_trading_new_order_entry.png`);
});

Scenario('Test item No.28 View stock information', async ({ I }) => {
    const viewStockInformationSelector = '//*[@data-testid="benefitDetail_symbolInfo_id"]';
    await I.clickFixed(viewStockInformationSelector);
    await I.waitFor('mediumWait');
    I.seeElement('//*[@data-testid="stockDetailBasic_basicInfo_id"]');
    I.seeElement('//*[@data-testid="commonChart_chartContainer_id"]');
    I.see('銘柄詳細', '//*[@data-testid="common_header_title_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/stock/basic', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.28_View_stock_information_go_to_chart_display.png`);
});

Scenario('Test item No.18 Photo Swipe left or right', async ({ I }) => {
    const slideSwiperSelector = '//*[@data-testid="common_swiper_bullets_id"]/..';
    await I.swipeUpFixed('//*[@data-testid="benefitDetail_symbolInfo_id"]/..');
    await I.swipeLeftFixed(slideSwiperSelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.18_Photo_Swipe_left.png`);
    await I.swipeRightFixed(slideSwiperSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitDetail}_Test_item_No.18_Photo_Swipe_right.png`);
});
