import { COL<PERSON>, COOKIE_KEY, PAGE_URL, USER_ID } from '../const/constant';

Feature('AssetStatus - DatePickerPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/InvestmentResult/DatePicker.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user4 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test date picker page', async ({ I }) => {
    await I.amOnPage(PAGE_URL.mypagePortfolio);
    await I.waitFor('mediumWait');
    const processingButton = '//*[@data-testid="portfolio_investmentResult_id"]';
    await I.clickFixed(processingButton);
    await I.waitFor('longWait');

    const performanceButton = '//*[@data-testid="investmentResult_assetPerformance_id"]';
    I.waitForElement(performanceButton);
    await I.clickFixed(performanceButton);
    await I.waitFor();
    const datePickerButton = '//*[@data-testid="assetTransition_dateType_id"]/label[5]';
    I.waitForElement(datePickerButton);
    await I.clickFixed(datePickerButton);
    I.saveScreenshot('101.date_picker_datePickerPage.png');
});

Scenario('test Item 1 close button page', async ({ I }) => {
    I.saveScreenshot('101.date_picker_datePickerPageItem1.png');
    I.waitForElement('//*[@data-testid="datePickerSheet_close_id"]');
    I.seeElement('//*[@data-testid="datePickerSheet_close_id"]');
    await I.waitFor();
});

Scenario(
    'test Item 2 Month/Year selection, Item 9 Year selection, Item 10 Month selection page',
    async ({ I }) => {
        await I.saveScreenshot('101.date_picker_datePickerPageItem2.png');
        const monthYearPicker = '//*[@data-testid="datePickerSheet_monthYearPicker_id"]';
        I.waitForElement(monthYearPicker);
        await I.clickFixed(monthYearPicker);
        await I.waitFor();
        await I.saveScreenshot('101.date_picker_datePickerPageItem910.png');
        const yearWrapper = '//*[@id="years_wrapper"]';
        const monthWrapper = '//*[@id="months_wrapper"]';
        I.seeElement(yearWrapper);
        I.seeElement(monthWrapper);
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1;

        await I.swipeDownFixed(`//*[@id="${currentYear}"]`);
        const isSelectedYear = await I.grabCssPropertyFrom(`//*[@id="${currentYear - 3}"]`, 'font-size');
        I.assertContain(isSelectedYear, '20px', 'font-size is not 20px');
        await I.saveScreenshot('101.date_picker_datePickerPageItem910.png');

        await I.swipeUpFixed(`//*[@id="1_${currentMonth}"]`);
        await I.saveScreenshot('101.date_picker_datePickerPageItem5.png');
        await I.waitFor();
        await I.tapLocationOfElement(monthYearPicker);
        await I.waitFor();
        I.saveScreenshot('101.date_picker_datePickerPageItem910.png');
    },
);

Scenario('test Item 3 previous month, Item 4 next month page', async ({ I }) => {
    await I.saveScreenshot('101.date_picker_datePickerPageItem34_current_month.png');
    const previousMonth = '//*[@id="arrow-down"]';
    const nextMonth = '//*[@id="arrow-down"][2]';
    I.waitForElement(previousMonth);
    await I.clickFixed(previousMonth);
    await I.waitFor();
    await I.saveScreenshot('101.date_picker_datePickerPageItem34_previous_month.png');
    
    I.waitForElement(nextMonth);
    await I.clickFixed(nextMonth);
    await I.waitFor();
    I.saveScreenshot('101.date_picker_datePickerPageItem34_next_month.png');
});

Scenario('test Item 5 calender page', async ({ I }) => {
    I.saveScreenshot('101.date_picker_datePickerPageItem5.png');
    const dateElement = '(//div[contains(@class, "DayPicker-Day")][contains(@aria-disabled, "false")])[1]';
    I.waitForElement(dateElement);
    I.click(dateElement);
    await I.waitFor();
    const dateDivElement = `${dateElement}//div[contains(@class, "cell-day")]`;
    const backgroundColor = await I.grabCssPropertyFrom({ css: dateDivElement }, 'background-color');
    I.assertContain(backgroundColor, COLOR.mainColor, `backgroundColor is not ${COLOR.mainColor}`);
    I.saveScreenshot('101.date_picker_datePickerPageItem5.png');
});

Scenario('test Item 7 Reset button page', async ({ I }) => {
    const resetButton = '//*[@data-testid="datePickerSheet_reset_id"]';
    I.waitForElement(resetButton);
    I.tapLocationOfElement(resetButton);
    await I.waitFor();
    I.saveScreenshot('101.date_picker_datePickerPageItem7.png');
    I.dontSeeElement('//*[@id="arrow-down"][2]');
    const monthYearText = await I.grabTextFrom('//*[@data-testid="datePickerSheet_monthYearPicker_id"]/p[1]');
    const monthYearToCheck = `${new Date().getFullYear()}年${new Date().getMonth() + 1}月`;
    I.assertEqual(monthYearText, monthYearToCheck, `${monthYearToCheck} is not ${monthYearText}`);
});

Scenario('test Item 8 Confirm button page', async ({ I }) => {
    I.saveScreenshot('101.date_picker_datePickerPageItem8.png');
    const confirmButton = '//*[@data-testid="datePickerSheet_confirm_id"]';
    I.waitForElement(confirmButton);
    I.click(confirmButton);
    await I.waitFor();
    I.saveScreenshot('101.date_picker_datePickerPageItem8.png');
    const monthRangeText = await I.grabTextFrom('//*[@id="mypage-inves"]/div/div[2]/p[2]');
    I.assertEqual(monthRangeText, '表示期間：-～-', 'monthRangeText is not 表示期間：-～-');
});
