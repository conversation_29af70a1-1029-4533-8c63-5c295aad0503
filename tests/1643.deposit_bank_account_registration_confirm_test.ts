import { COOKIE_KEY, USER_ID } from '../const/constant';
import depositBankAccountRegistration from '../pages/depositBankAccountRegistration';

Feature('Deposits_Withdrawals - DepositBankAccountRegistration');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Deposit Bank Account Registration-Confirm', async ({ I }) => {
    await depositBankAccountRegistration.goToInputPage();
    await depositBankAccountRegistration.focusAndFillField('$bankAccountRegistrationInput_password_id');
    await depositBankAccountRegistration.goToConfirmPage();
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.confirm}.0_Deposit_Bank_Account_Registration-Confirm.png`,
    );
});

// 12.申込 -> Formリクエストを行う
Scenario('Test Item 12: Application', async ({ I }) => {
    await I.seeAndClickElement('$bankAccountRegistrationConfirm_apply_id', { waitFor: 'mediumWait' });
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.confirm}.12_Deposit_Bank_Account_Registration-confirm.png`,
    );

    await I.waitFor();
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
});

// 13.申込しない ->  銀行引落口座登録-入力画面に遷移
Scenario('Test Item 13: Do not apply -> Bank debit account registration - Go to input screen', async ({ I }) => {
    await I.seeAndClickElement('$bankAccountRegistrationConfirm_doNotApply_id', { waitFor: 'mediumWait' });
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.confirm}.13_Do_not_apply-Bank_debit_account_registration-Go_to_input_screen.png`,
    );
});
