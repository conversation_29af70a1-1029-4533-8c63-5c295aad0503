import { COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';
import transferCash from '../pages/transferCash';

Feature('Deposits_Withdrawals - TransferCashComplete');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user102 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await <PERSON>.closeBrowser();
});

Scenario('Test Item 0: Transfer Cash Complete', async ({ I }) => {
    await transferCash.goToCashCompletePage();
    I.saveScreenshot(`${transferCash.prefix.input}.0_Transfer_Cash_Confirm.png`);
});
Scenario('Test Item 2: Tap "Deposit transfer inquiry" to move to the deposit transfer inquiry screen.', async ({ I }) => {
    const transferCashInquiry = '//*[@data-testid="transferCashComplete_transferCashInquiry_id"]';
    I.waitForElement(transferCashInquiry, 2);
    await common.clickCardItem(transferCashInquiry, 'ap/iPhone/CashFlow/Transfer/Cash/Inquiry', 'kcMemberSite');
  
});
Scenario('Test Item 3: Tap "Deposit transfer inquiry" to move to the deposit transfer inquiry screen.', async ({ I }) => {
    await transferCash.goToCashCompletePage();
    const returnToTransfer = '//*[@data-testid="transferCashComplete_returnToTransfer_id"]';
    I.waitForElement(returnToTransfer, 2);
    await I.clickFixed(returnToTransfer);
    await I.waitFor();
    I.saveScreenshot(`${transferCash.prefix.input}.3_Tap_Deposit_transfer_inquiry_to_move_to_the_deposit_transfer_inquiry_screen.png`);
});