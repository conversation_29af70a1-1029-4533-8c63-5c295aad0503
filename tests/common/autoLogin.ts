// @/common/autoLogin.ts
Feature('Auto Login');
Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await <PERSON><PERSON>closeBrowser();
    await <PERSON><PERSON>waitFor();
    await I.switchToNative();
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

// 50 secords timeout for restart + login
Scenario('Auto login before running tests', { timeout: 50 }, async ({ I }) => {
    try {
        // Check if login button exists
        const loginButton = '~ログイン'
        const isLoginButtonVisible = await I.grabNumberOfVisibleElements(loginButton);

        if (isLoginButtonVisible > 0) {
            console.log('🔑 Login required, starting login process...');
            // Perform login
            await I.login(process.env.LOGIN_USERNAME || '09109911', process.env.LOGIN_PASSWORD || '111111');
            await I.waitFor(2);
            await I.switchToWeb();
            await I.waitFor(2);
            // ===========
            I.amOnPage('/mobile/mypage/portfolio');
            await I.waitFor('longWait');
            I.waitForText('ポートフォリオ', 3, 'body');
            I.waitForElement('#mypage-portfolio');

            const isDisplayTutorial = await I.getAppSetting('is-disp-tutorial');
            const isDisplayTopCoach = await I.getAppSetting('is-disp-top-coach');

            if (!isDisplayTutorial) {
                I.waitForText('新しい投資体験へ', 3, 'body');
                // Semantic locators can be used in web contexts (codeceptjs automatically searches for button text, label text, aria-label property, etc.)
                // see. https://codecept.io/locators/#semantic-locators
                // or "~cancel-btn": accessible id = aria-label (web context)
                I.click('//button[@aria-label="cancel-btn"]');
                await I.waitFor();
            }

            if (!isDisplayTopCoach) {
                I.waitForText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。', 3, 'body');
                // Touch and close coach modal.
                const coachModal = locate('div').withChild(
                    locate('p').withText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。'),
                );
                const rect = await I.grabElementBoundingRectTyped(coachModal);
                const x = Math.round(rect.x) + 100;
                const y = Math.round(rect.y) + 100;
                await I.tapAction(x, y);
                await I.waitFor();
            }
            // ============
            // Verify login success
            await I.waitForElement('~menu', 10);
            console.log('✅ App restart and login successful');
        } else {
            // Already logged in (shouldn't happen after reset, but just in case)
            console.log('ℹ️ App restarted, login not required');
        }
    } catch (error) {
        console.error('❌ Error during login:', error);
        throw error;
    }
});