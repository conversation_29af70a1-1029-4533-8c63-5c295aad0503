import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Login');

Before(async ({ I }) => {
    console.debug('before');
    // reset application and context
    await I.resetAppFixed();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('test login', async ({ I, loginAndSwitchToWebAs, portfolioTopPage }) => {
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });

    // Check that the top page is displayed
    await I.haveWebVewContext();
    await I.amInWeb();

    await portfolioTopPage.goToPage();

    await I.setupInterceptor(); // For test intellisense TODO: Remove this line
    await I.tapAction(0, 0); // For test intellisense TODO: Remove this line
});
