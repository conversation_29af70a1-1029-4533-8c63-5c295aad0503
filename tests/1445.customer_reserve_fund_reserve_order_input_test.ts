import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveFundReserveOrderInput');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user52 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Fund ReserveOrder Input', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    I.saveScreenshot('1445.Test_item_No.0_UI_of_Reserve-Fund-ReserveOrder_Input.png');
});
Scenario('Test Item 8: Check Account Classification Help Tap Show Tooltip', async ({ I }) => {
    const accountClassificationHelp = '//*[contains(text(), "口座区分")]';
    await I.waitForElement(accountClassificationHelp, 3);
    I.clickFixed(accountClassificationHelp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1360.Test_item_No.10a_Account_Classification_Help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 9: Check Account classification method Select the item you tapped', async ({ I }) => {
    // get all labels in payment type radio group
    const accountTypeLabels = '//*[@data-testid="fundReserveOrderInput_accountType_id"]//label';

    // scroll to label
    I.waitForElement(accountTypeLabels, 3);
    I.scrollToElement(accountTypeLabels);

    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(accountTypeLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${accountTypeLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1445.Test_item_No.9_Account_Classification_Label_${i}.png`);
    }
});
Scenario('Test Item 11: Display NISA usage quota help tooltip', async ({ I }) => {
    const nisaUsageQuotaHelp = '//*[contains(text(), "NISA利用枠")]';
    await I.waitForElement(nisaUsageQuotaHelp, 3);
    I.clickFixed(nisaUsageQuotaHelp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1445.Test_item_No.11_NISA_Usage_Quota_Help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 12: Toggle NISA usage quota ON/OFF', async ({ I }) => {
    // get all labels in payment type radio group
    const nisaUsageLimitLabels = '//*[@data-testid="fundReserveOrderInput_NISAUsageLimit_id"]//label';

    // scroll to label
    I.waitForElement(nisaUsageLimitLabels, 3);
    I.scrollToElement(nisaUsageLimitLabels);

    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(nisaUsageLimitLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${nisaUsageLimitLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1445.Test_item_No.12_NISA_Usage_Quota_Label_${i}.png`);
    }
});

Scenario('Test Item 18: Check Dividend Help Show tooltip', async ({ I }) => {
    //click to Nisa
    const accountTypeLabels = '//*[@data-testid="fundReserveOrderInput_accountType_id"]//label';

    // scroll to label
    I.waitForElement(accountTypeLabels, 3);
    I.scrollToElement(accountTypeLabels);

    const allocationHelp = '//*[contains(text(), "分配金")]';
    await I.waitForElement(allocationHelp, 3);
    I.clickFixed(allocationHelp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1445.Test_item_No.18_Dividend_Help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 19: Toggle Dividend ON/OFF', async ({ I }) => {

    // get all labels in payment type radio group
    const dividendLabels = '//*[@data-testid="fundReserveOrderInput_distribution_id"]//label';

    // scroll to label
    I.waitForElement(dividendLabels, 3);
    I.scrollToElement(dividendLabels);

    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(dividendLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${dividendLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1445.Test_item_No.19_Dividend_Label_${i}.png`);
    }
});
Scenario('Test Item 21: Check Payment method Select the item you tapped', async ({ I }) => {
    // get all labels in payment type radio group
    const paymentMethodLabels = '//*[@data-testid="fundReserveOrderInput_paymentMethod_id"]//label';

    // scroll to label
    I.waitForElement(paymentMethodLabels, 3);
    I.scrollToElement(paymentMethodLabels);

    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(paymentMethodLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${paymentMethodLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1445.Test_item_No.21_Payment_Method_Label_${i}.png`);
    }
});
Scenario('Test Item 21a: Check Payment method help tooltip', async ({ I }) => {
    const paymentMethodHelp = '//*[contains(text(), "決済方法")]';
    await I.waitForElement(paymentMethodHelp, 3);
    I.clickFixed(paymentMethodHelp);
    await I.waitFor();
    I.saveScreenshot('1445.Test_item_No.21a_Payment_Method_Help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 21c: Check  Mitsubishi UFJ Card - Registration General-purpose - Transition to card registration', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const mitsubishiUfjCardRegistration = '//*[@data-testid="cardCommon_mitsubishiUFJCardRegister_id"]';
    await I.waitForElement(mitsubishiUfjCardRegistration, 3);
    I.clickFixed(mitsubishiUfjCardRegistration);
    await I.waitFor('mediumWait');
    I.saveScreenshot('1445.Test_item_No.21c_Mitsubishi_UFJ_Card_Registration.png');
});
Scenario('Test Item 21d: Check Mitsubishi UFJ Card - For more information 21d. Mitsubishi UFJ Card - For more information, click here. "Open the following URL in a new tab. https://kabu.com/item/fund/mitsubishi_ufj_card/default.html" ', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await accumulation.goToCustomerReserveFundPage();
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor('mediumWait');
    const mitsubishiUfjCardDetailsHere = '//*[@data-testid="cardCommon_mitsubishiUFJCardDetailsHere_id"]';
    await I.waitForElement(mitsubishiUfjCardDetailsHere, 3);
    await common.clickCardItem(mitsubishiUfjCardDetailsHere, 'https://kabu.com/item/fund/mitsubishi_ufj_card/default.html', 'external');
});
Scenario('Test Item 21e: Check au PAY カード-登録 case 1', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user54 });
    await accumulation.goToCustomerReserveFundPage();
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor('mediumWait');
    const auPayCardRegistration = '//*[@data-testid="cardCommon_auPAYCardRegister_id"]';
    await I.waitForElement(auPayCardRegistration, 3);
    I.executeScript(function (selector) {
        const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
        if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, auPayCardRegistration);
    await I.waitFor('shortWait');
    const currentUrl = await I.grabCurrentUrl();
    console.log('Current URL before click:', currentUrl);
    await common.clickCardItem(auPayCardRegistration, '/iphone/personal/auID/auIDRuleacceptAppsm.asp', 'external');
});
Scenario('Test Item 21f: Check au PAY カード-詳しくはこちら', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user54 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToCustomerReserveFundPage();
    const auPayCardMoreInfo = '//*[@data-testid="cardCommon_auPAYCardDetailsHere_id"]';
    await I.waitForElement(auPayCardMoreInfo, 3);
    await common.clickCardItem(auPayCardMoreInfo, 'https://kabu.com/company/lp/lp200.html', 'external');
});
Scenario('Test Item 25: Check Amount Text Field', async ({ I }) => {
    const suggestInput = '//*[@data-testid="suggestInputNumber_id"]';
    await I.waitForElement(suggestInput, 3);
    I.scrollAndFill(suggestInput, '100000');
    await I.waitFor('shortWait');
    I.saveScreenshot('1445.Test_item_No.25_Amount_Text_Field.png');
});
Scenario('Test Item 28: Check Add the amount according to the button you tapped to the input value of the specified amount', async ({ I }) => {
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    I.scrollToElement(suggestInputButtonGroup);
    const count = await I.grabNumberOfVisibleElements(suggestInputButtonGroup);
    for (let i = 1; i <= count - 1; i++) {
        const button = `(${suggestInputButtonGroup})[${i}]`;
        I.clickFixed(button);
        await I.waitFor('shortWait');
        I.saveScreenshot(`1445.Test_item_No.28_Suggest_Input_Button_${i}.png`);
    }
    const cancelButton = `(${suggestInputButtonGroup})[${count}]`;
    I.clickFixed(cancelButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1445.Test_item_No.28_Suggest_Input_Button_Cancel.png');
});
Scenario('Test Item 29: Check Specified date of each month For selectable DatePicker: Select the tapped date', async ({ I }) => {
    const datePicker = '//*[@data-testid="reserveOrderInputBox_monthlySpecifiedDate_id"]//div[19]';
    await I.waitForElement(datePicker, 3);
    I.scrollAndClick(datePicker);
    await I.waitFor('shortWait');
    I.saveScreenshot('1445.Test_item_No.29_Specified_Date_of_Each_Month_Date_Picker.png');
});
Scenario('Test Item 31: Check Open the following URL in a new tab', async ({ I }) => {
    await I.refreshPage();
    await I.waitFor();
    const fundReserveOrderInput_paymentMethod_id = '//*[@data-testid="fundReserveOrderInput_paymentMethod_id"]//label';

    await I.waitForElement(fundReserveOrderInput_paymentMethod_id, 3);
    I.scrollAndClick(`(${fundReserveOrderInput_paymentMethod_id})[4]`);
    await I.waitFor('shortWait');
    const hereLink = '//p[@data-testid="reserveOrderInputBox_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 3);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');

});
Scenario('test Item 32: Check Switch ON/OFF Increase designation', async ({ I }) => {
    const increaseDesignationSwitch = '//label[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';
    await I.waitForElement(increaseDesignationSwitch, 5);
    I.scrollToElement(increaseDesignationSwitch);
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.saveScreenshot('1445.Test_item_No.32_Increase_Designation_Switch_OFF.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
        I.saveScreenshot('1445.Test_item_No.32_Increase_Designation_Switch_ON.png');
    } else {

        I.saveScreenshot('1445.Test_item_No.32_Increase_Designation_Switch_ON.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
        I.saveScreenshot('1445.Test_item_No.32_Increase_Designation_Switch_OFF.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();

    }
});

Scenario('Test Item 34: Check Increase amount', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';

    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }

    // Click plus button 2 times
    const plusButton = '//button[@data-testid="groupInputNumber_plus_id"]';
    const minusButton = '//button[@data-testid="groupInputNumber_minus_id"]';
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(minusButton);
    I.clickFixed(minusButton);
    await I.waitFor('shortWait');

    I.saveScreenshot('1445.Test_item_No.34_Increase_amount.png');

});
Scenario('Test Item 35: Check Increase Month 1 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }
    const firstDropdown = '//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")][1]';
    I.clickFixed(firstDropdown);
    await I.waitFor();

    I.saveScreenshot('1445.Test_item_No.35_Increase_Month_1_Dropdown_Opened.png');

    const option = '//div[@data-testid="common_MenuList_id"]//button[@value="3"]';
    I.clickFixed(option);
    await I.waitFor();
    I.saveScreenshot('1445.Test_item_No.35_Increase_Month_1_Selected.png');
});
Scenario('Test Item 36: Check Increase Month 2 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInputBox_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }
    const firstDropdown = '(//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(firstDropdown);
    await I.waitFor();
    I.saveScreenshot('1445.Test_item_No.36_Increase_Month_2_Dropdown_Opened.png');
    const option = '(//div[@data-testid="common_MenuList_id"]//button[@value="11"])[2]';
    I.clickFixed(option);
    await I.waitFor();
    I.saveScreenshot('1445.Test_item_No.36_Increase_Month_2_Selected.png');
});
Scenario('Test Item 40: Check Prospectus to be delivered - Link Shows the prospectus modal', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const fundReserveOrderInput_deliveryProspectusLink_id = '//*[@data-testid="fundReserveOrderInput_deliveryProspectusLink_id"]';
    await I.waitForElement(fundReserveOrderInput_deliveryProspectusLink_id, 3);
    I.scrollAndClick(fundReserveOrderInput_deliveryProspectusLink_id);
    I.saveScreenshot('1445.Test_item_No.40_Prospectus_to_be_delivered_Link_Shows_the_prospectus_modal.png');
    I.waitFor('shortWait')
    //back
    await I.backToPreviousScreen(true);
});
Scenario('Test Item 41: Check Prospectus to be delivered - Confirmation button Shows the prospectus modal', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const deliveryProspectusConfirmButton = '//*[@data-testid="fundReserveOrderInput_deliveryProspectusConfirmButton_id"]';
    await I.waitForElement(deliveryProspectusConfirmButton, 3);
    I.scrollAndClick(deliveryProspectusConfirmButton);
    I.saveScreenshot('1445.Test_item_No.41_Prospectus_to_be_delivered_Confirmation_button_Shows_the_prospectus_modal.png');
    I.waitFor('shortWait')
    //back
    await I.backToPreviousScreen(true);
});
Scenario('Test Item 43: Check Supplemental prospectus - Link Shows the prospectus modal', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const supplementaryProspectusLink = '//*[@data-testid="fundReserveOrderInput_supplementaryProspectusLink_id"]';
    await I.waitForElement(supplementaryProspectusLink, 3);
    I.scrollAndClick(supplementaryProspectusLink);
    I.saveScreenshot('1445.Test_item_No.43_Supplemental_prospectus_Link_Shows_the_prospectus_modal.png');
    I.waitFor('shortWait')
    //back
    await I.backToPreviousScreen(true);
});
Scenario('Test Item 44: Check Supplemental prospectus - Confirmation button Shows the prospectus modal', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const supplementaryProspectusConfirmButton = '//*[@data-testid="fundReserveOrderInput_supplementaryProspectusConfirmButton_id"]';
    await I.waitForElement(supplementaryProspectusConfirmButton, 3);
    I.scrollAndClick(supplementaryProspectusConfirmButton);
    I.saveScreenshot('1445.Test_item_No.44_Supplemental_prospectus_Confirmation_button_Shows_the_prospectus_modal.png');
    I.waitFor('shortWait')
    //back
    await I.backToPreviousScreen(true);
});
Scenario('Test Item 46: Check Confirmation items - Link Shows the mutual fund confirmation items modal', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await accumulation.goToCustomerReserveFundPage();
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const confirmationItemsLink = '//*[@data-testid="fundReserveOrderInput_confirmationItemsLink_id"]';
    await I.waitForElement(confirmationItemsLink, 3);
    I.scrollAndClick(confirmationItemsLink);
    I.saveScreenshot('1445.Test_item_No.46_Confirmation_items_Link_Shows_the_mutual_fund_confirmation_items_modal.png');
    I.waitFor('shortWait')
    //back
    await I.backToPreviousScreen(true);
});
Scenario('Test Item 47: Check Confirmation items - Confirmation button Shows the mutual fund confirmation items modal', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await accumulation.goToCustomerReserveFundPage();
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    const confirmationItemsConfirmButton = '//*[@data-testid="fundReserveOrderInput_confirmationItemsConfirmButton_id"]';
    await I.waitForElement(confirmationItemsConfirmButton, 3);
    I.scrollAndClick(confirmationItemsConfirmButton);
    I.saveScreenshot('1445.Test_item_No.47_Confirmation_items_Confirmation_button_Shows_the_mutual_fund_confirmation_items_modal.png');
    I.waitFor('shortWait')
    //back
    await I.backToPreviousScreen(true);
});
Scenario('Test Item 50: Check Tax-free investment consent check Switch the selection status ON/OFF', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    await I.refreshPage();
    await I.waitFor();
    // get all labels in payment type radio group
    const accountTypeLabels = '//*[@data-testid="fundReserveOrderInput_accountType_id"]//label';
    // scroll to label
    I.waitForElement(accountTypeLabels, 2);
    I.scrollAndClick(`(${accountTypeLabels})[3]`);
    const button = '//*[@data-testid="fundReserveOrderInput_taxFreeInvestmentAgreeCheck_id"]//button';
    await I.waitForElement(button, 3);
    I.scrollAndClick(button);
    I.saveScreenshot('1445.Test_item_No.50_Tax-free_investment_consent_check_Switch_the_selection_statusOFF.png');
    I.scrollAndClick(button);
    I.saveScreenshot('1445.Test_item_No.50_Tax-free_investment_consent_check_Switch_the_selection_statusON.png');
});
Scenario('Test Item 51: Check Order confirmation screen - Transition to the order confirmation screen', async ({ I }) => {
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 2);
    I.scrollToElement(orderConfirmButton);
    I.saveScreenshot('1445.Test_item_No.51_disabled_confirm_button.png');

    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    I.scrollToElement(orderConfirmButton);
    I.saveScreenshot('1445.Test_item_No.51_enabled_confirm_button.png');
    I.clickFixed(orderConfirmButton);
    I.waitFor();
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");
    I.saveScreenshot('1445.Test_item_No.51_Go_to_order_confirmation_screen.png');
    //back
    await I.backToPreviousScreen();

});
Scenario('Test Item 52: Check Important items - Accordion opens and closes', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const importantContent = '//*[@data-testid="fundReserveOrderInput_importantContent_id"]';
    await I.waitForElement(importantContent, 3);
    I.scrollAndClick(importantContent);


    const chakraCollapse = importantContent + '//*[@class="chakra-collapse"]';
    await I.waitForElement(chakraCollapse, 3);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1445.Test_item_No.52_Important_items_Accordion_opens.png');
    const arrowUpSelector = '//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.clickFixed(arrowUpSelector);
    I.saveScreenshot('1445.Test_item_No.52_Important_items_Accordion_closes.png');
});
Scenario('Test Item 56c-2: Check the button to use - Navigation', async ({ I }) => {
    // Uses default userId '09050360'
    const useButton = '//*[@data-testid="fundCommonPointUsageSetting_setting_id"]';
    await I.waitForElement(useButton, 3);
    I.scrollAndClick(useButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1445.Test_item_No.56c-2_go_to_point_setting.png');
    // back
    await I.backToPreviousScreen();
});
Scenario('Test Item 56c-1: Check the button to register your auID - Navigation', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user53 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();

    const auIdRegisterButton = '//*[@data-testid="fundCommonPointUsageSetting_setting_id"]';
    await I.waitForElement(auIdRegisterButton, 3);

    // Test navigation to pontapointdoui/agree
    await common.clickCardItem(
        auIdRegisterButton,
        '/ap/iphone/personal/pontapointdoui/agree',
        'kcMemberSite'
    );
});
Scenario('Test Item 56c-3: Check agreement button - Navigation', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user54 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToCustomerReserveFundPage();
    const agreementButton = '//*[@data-testid="fundCommonPointUsageSetting_setting_id"]';
    await I.waitForElement(agreementButton, 3);

    // Test navigation to pontapointdoui/agree
    await common.clickCardItem(
        agreementButton,
        '/ap/iphone/personal/pontapointdoui/agree',
        'kcMemberSite'
    );
});