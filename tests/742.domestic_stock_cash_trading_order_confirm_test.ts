import { COOKIE_KEY, PAGE_URL, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import orderInquiry from '../pages/order-inquiry';
import tradeStock from '../pages/trade-stock';

Feature('InvestmentProducts - DomesticStockCashTradingOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user27 });

    await I.amOnPage(PAGE_URL.orderInquiryStock);
    await I.waitFor();
    await orderInquiry.stockList.clickItem(0);
    await I.waitFor();
    await orderInquiry.stockDetail.orderCorrection();
    await I.waitFor();
    await tradeStock.correction.doReplace();
    await <PERSON><PERSON>waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('domestic stock cash trading order confirm [レイアウト]', async ({ I }) => {
    I.waitInUrl('/mobile/trade/stock/correction/confirm', 5);
    await commonUi.header.verifyTitle('現物訂正確認');
    I.saveScreenshot('742.domestic_stock_cash_trading_order_confirm_layout.png');
});

// 18.パスワード
// 19.パスワード省略チェック
// 22.訂正を確定
Scenario(
    'domestic stock cash trading order confirm [18.パスワード][19.パスワード省略チェック][22.訂正を確定]',
    async ({ I }) => {
        await tradeStock.confirm.fillPassword('111111');
        I.saveScreenshot('742.domestic_stock_cash_trading_order_confirm_18_password.png');
        await tradeStock.confirm.togglePasswordOmissionCheck();
        I.saveScreenshot('742.domestic_stock_cash_trading_order_confirm_19_password_omission.png');
        await tradeStock.confirm.apply();
        I.saveScreenshot('742.domestic_stock_cash_trading_order_confirm_22_apply.png');
    },
);

// 21.パスワード入力チェック
Scenario('domestic stock cash trading order confirm [21.パスワード入力チェック]', async ({ I }) => {
    const storageObject = await tradeStock.confirm.sessionData();
    const modifyStorageObject = {
        ...storageObject,
        isOmmitPassword: true,
    };
    await tradeStock.confirm.setSessionData(modifyStorageObject);
    await I.refreshPage();

    await tradeStock.confirm.toggleCheckInputPassword();
    I.saveScreenshot('742.domestic_stock_cash_trading_order_confirm_21_password_input_check.png');
});
