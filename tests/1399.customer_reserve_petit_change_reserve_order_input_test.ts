import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReservePetitChangeReserveOrderInput');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 }); //08409912
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Savings Petit Stocks Input Savings Changes', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderInputPage();
    I.saveScreenshot('1399.Test_item_No.0_UI_of_Savings_Petit_Stocks_Input_Savings_Changes.png');
});
//TODO: 4 - text not link
Scenario('Test Item 10: Payment method Select the item you tapped', async ({ I }) => {
    const paymentTypeLabels = '//*[@data-testid="changeReserveOrderInput_paymentMethod_id"]//div[@role="radiogroup"]//label';
    await I.waitForElement(paymentTypeLabels, 1);
    I.scrollToElement(paymentTypeLabels);
    await I.waitFor('shortWait');
    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(paymentTypeLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${paymentTypeLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1399.Test_item_No.10_Payment_Type_Label_${i}.png`);
    }
});
Scenario('Test Item 12: Check specified amount "Perform the process described in the Common UI - Numeric Stepper with the following parameters.', async ({ I }) => {
    const plusButton = '//p[contains(text(), "毎月の指定金額")]/parent::div//button[@data-testid="groupInputNumber_plus_id"]';
    await I.waitForElement(plusButton, 5);
    I.scrollAndClick(plusButton);
    I.saveScreenshot('1399.Test_item_No.12_Check_specified_amount.png');

});
Scenario('Test Item 13: Check selectable DatePicker: Select the date you tap', async ({ I }) => {
    const datePicker = '//*[@data-testid="reserveOrderInputBox_monthlySpecifiedDate_id"]';
    // Scroll to datepicker
    await I.waitForElement(datePicker, 1);
    I.scrollToElement(datePicker);
    // Click on a random available date
    const availableDates = '//*[@data-testid="reserveOrderInputBox_monthlySpecifiedDate_id"]//div[3]';
    I.clickFixed(availableDates);
    await I.waitFor('shortWait');
    I.saveScreenshot('1399.Test_item_No.13_Check_selectable_DatePicker.png');
});
Scenario('Test Item 14: Check turn on/off the switch Increase designation', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';

    await I.waitForElement(increaseDesignationSwitch, 5);
    I.scrollAndClick(increaseDesignationSwitch);
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible > 0) {
        I.saveScreenshot('1399.Test_item_No.14_Increase_designation_switch_ON.png');
    } else {
        I.saveScreenshot('1399.Test_item_No.14_Increase_designation_switch_OFF.png');
        I.clickFixed(increaseDesignationSwitch);
    }

});
Scenario('Test Item 16: Check Increase amount', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';

    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }

    // Click plus button 2 times
    const plusButton = '//p[contains(text(), "増額金額")]/following-sibling::*//button[@data-testid="groupInputNumber_plus_id"]';
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    await I.waitFor('shortWait');

    I.saveScreenshot('1399.Test_item_No.16_increase_amount.png');

});
Scenario('Test Item 17: Check Increase Month 1 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }
    const firstDropdown = '//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")][1]';
    I.clickFixed(firstDropdown);
    await I.waitFor();

    I.saveScreenshot('1399.Test_item_No.17_increase_month1_dropdown_opened.png');

    const option = '//div[@data-testid="common_MenuList_id"]//button[@value="3"]';
    I.clickFixed(option);
    await I.waitFor();
    I.saveScreenshot('1399.Test_item_No.17_increase_month1_selected.png');
});
Scenario('Test Item 18: Check Increase Month 2 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }
    const firstDropdown = '(//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(firstDropdown);
    await I.waitFor();
    I.saveScreenshot('1399.Test_item_No.18_increase_month2_dropdown_opened.png');
    const option = '(//div[@data-testid="common_MenuList_id"]//button[@value="5"])[2]';
    I.clickFixed(option);
    await I.waitFor();
    I.saveScreenshot('1399.Test_item_No.18_increase_month2_selected.png');
});
Scenario('Test Item 19: Check Withdrawal result notification - Notification service contact settings', async ({ I }) => {
    // Find text `通知サービス連絡先設定` in data-testid="changeReserveOrderInput_withdrawalResultNotification_id"
    const withdrawalResultNotification = '//*[@data-testid="changeReserveOrderInput_withdrawalResultNotification_id"]//span[contains(text(), "通知サービス連絡先設定")]';
    await I.waitFor('shortWait');
    await common.clickCardItem(withdrawalResultNotification, 'ap/iPhone/personal/contact/List', 'kcMemberSite');
});
Scenario('Test Item 20: Check Insider confirmation panel Switch the selection status ON/OFF', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user47 });
    await I.waitFor()

    await accumulation.goToReservePetitChangeOrderInputPage();
    const button = '//div[@data-testid="changeReserveOrderInput_notInsiderTradeConfirm_id"]//button';
    await I.waitForElement(button, 2);
    I.scrollToElement(button);
    I.clickFixed(button);
    I.saveScreenshot('1399.Test_item_No.20_Insider_confirmation_panel_switch_ON.png');
    I.clickFixed(button);
    I.saveScreenshot('1399.Test_item_No.20_Insider_confirmation_panel_switch_OFF.png');
    I.clickFixed(button);
});
Scenario('Test Item 21: Proceed to Accumulation - Petit Stock - Accumulation Change Confirmation', async ({ I }) => {
    const button = '//*[@data-testid="changeReserveOrderInput_confirm_id"]';
    await I.waitForElement(button, 1);
    I.scrollAndClick(button);
    I.saveScreenshot('1399.Test_item_No.21_Proceed_to_Accumulation_Petit_Stock_Accumulation_Change_Confirmation.png');
    await I.waitFor('shortWait');
    // back
    I.backToPreviousScreen();
    await I.waitFor('shortWait');
});
Scenario('Test Item 22: Check Caution statement Accordion', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderInputPage();
    const button = '//*[@data-testid="changeReserveOrderInput_caution_id"]';
    await I.waitForElement(button, 1);
    I.scrollAndClick(button);

    const chakraCollapse = '//*[@data-testid="changeReserveOrderInput_caution_id"]//div[@class="chakra-collapse"]';
    await I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1399.Test_item_No.22_Caution_statement_Accordion.png');
    const arrowUp = '//*[@data-testid="changeReserveOrderInput_caution_id"]//*[@data-testid="common_dropDown_arrow_id_up"]';
    await I.waitForElement(arrowUp, 1);
    I.clickFixed(arrowUp);
    I.saveScreenshot('1399.Test_item_No.22_Caution_statement_Accordion_closed.png');
});
Scenario('Test Item 27: Check Click Open the following URL in a new tab', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user51 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReservePetitChangeOrderInputPage();
    const hereLink = '//p[@data-testid="reserveOrderInputBox_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 1);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
});
Scenario('Test Item 28a: Check Brand Name - Domestic Stock Investment Information - Brand Details - Chart Display Transition', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderInputPage();
    await I.waitFor();
    const changeReserveOrderInput = '//*[@data-testid="changeReserveOrderInput_symbolName_id"]';
    await I.waitForElement(changeReserveOrderInput, 1);
    I.scrollAndClick(changeReserveOrderInput);
    I.saveScreenshot('1399.Test_item_No.28a_Brand_Name_Domestic_Stock_Investment_Information_Brand_Details_Chart_Display_Transition.png');
    // back
    I.backToPreviousScreen();
    await I.waitFor('shortWait');
});
Scenario('Test Item 33: Check Trade Restrictions and Trade Caution Information', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderInputPage();
    const tradingCautionSelector = '//*[@data-testid="changeReserveOrderInput_tradingCautionInfo_id"]';
    await I.clickFixed(tradingCautionSelector);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    I.saveScreenshot('1399.Test_item_No.33_Trading_Restrictions_and_Trade_Caution_Information.png');
    I.clickFixed('//*[@data-testid="common_rightSlide_close_id"]');
});
Scenario('Test Item 38: Check Tax-free investment consent check Switch the selection status ON/OFF', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderInputPage();
    const taxFreeInvestmentAgree = '//*[@data-testid="changeReserveOrderInput_taxFreeInvestmentAgree_id"]';
    await I.waitForElement(taxFreeInvestmentAgree, 1);
    I.scrollToElement(taxFreeInvestmentAgree);

    // Find button in next sibling div
    const button = taxFreeInvestmentAgree + '//button';
    I.clickFixed(button);
    await I.waitFor('shortWait');
    I.saveScreenshot('1399.Test_item_No.38_Tax-free_investment_consent_check_Switch_the_selection_status_OFF.png');

    // Click again to uncheck
    I.clickFixed(button);
    await I.waitFor('shortWait');
    I.saveScreenshot('1399.Test_item_No.38_Tax-free_investment_consent_check_Switch_the_selection_status_ON.png');
});
Scenario('Test Item 39: Check Funding Status - Funding Status Header', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderInputPage();
    const button = '//*[@data-testid="changeReserveOrderInput_reserveSettingStatus_id"]/div[1]';
    const companySpecifiedDate = '//*[contains(text(), "当社指定日")]';
    await I.waitForElement(button, 1);
    await I.scrollAndClick(button);
    await I.scrollToElement(button);
    await I.saveScreenshot('1399.Test_item_No.39-2_Funding_Status_Funding_Status_Header.png');
    await I.clickFixed(button);
    await I.saveScreenshot('1399.Test_item_No.39-2_Funding_Status_Funding_Status_Header_closed.png');
    await I.clickFixed(button);
    await I.waitFor('shortWait');
    await I.scrollToElement(companySpecifiedDate);
    await common.clickCardItem(companySpecifiedDate, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
});
Scenario('Test Item 40: Check Savings settings before change', async ({ I }) => {
    const button = '//*[@data-testid="changeReserveOrderInput_reserveSettingsBeforeChange_id"]';
    await I.waitForElement(button, 1);
    I.scrollToElement(button);
    I.saveScreenshot('1399.Test_item_No.40_Savings_settings_before_change.png');
    const arrowUp = '//*[@data-testid="changeReserveOrderInput_reserveSettingsBeforeChange_id"]//*[@data-testid="common_dropDown_arrow_id_up"]';
    await I.waitForElement(arrowUp, 1);
    I.clickFixed(arrowUp);
    I.saveScreenshot('1399.Test_item_No.40_Savings_settings_before_change_closed.png');
});