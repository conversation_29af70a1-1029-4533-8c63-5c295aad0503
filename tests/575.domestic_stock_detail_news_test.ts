import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockDetailNews');

Before(async ({ I, loginAndSwitchToWebAs, stockDetailBasicPage }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user23 });
    await stockDetailBasicPage.goToDetailNewsTab();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Detail News page', async ({ stockDetailBasicPage }) => {
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/news');
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Display_Domestic_Stock_Detail_News_page');
});

Scenario('Test item No.2 Previous', async ({ I, stockDetailBasicPage }) => {
    const previousButtonSelector = '//div[contains(@class, "slick-prev")]';
    await I.clickFixed(previousButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.financialInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/financial');
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Test_item_No.2_Click_previous_go_to_Detail_financial_tab');
});

Scenario('Test item No.3 Financial information', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.financialTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.financialInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/financial');
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Test_item_No.3_Click_financial_tab');
});

Scenario('Test item No.5 Next', async ({ I, stockDetailBasicPage }) => {
    const nextButtonSelector = '//div[contains(@class, "slick-next")]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="stockDetailBasic_basicInfo_id"]');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/basic');
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Test_item_No.5_Click_next_go_to_stock_detail_basic_tab');
});

Scenario('Test item No.4 Basic tab', async ({ I, stockDetailBasicPage }) => {
    const basicTabSelector = '//div[@aria-hidden="false"][.//p[contains(text(), "基本情報")]]'
    await I.clickFixed(basicTabSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="stockDetailBasic_basicInfo_id"]');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/basic');
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Test_item_No.4_Click_basic_tab');
});

Scenario('Test item No.8 Click News item', async ({ I, stockDetailBasicPage }) => {
    const firstNewsItemSelector = '//*[@data-testid="stockDetailNews_newsList_id"]/div[1]'
    await I.clickFixed(firstNewsItemSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="marketNewsDetailPage_detail_id"]');
    await stockDetailBasicPage.compareUrl('/mobile/market/news/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Test_item_No.8_Click_news_item');
});

Scenario('Test item No.6 Scroll News list', async ({ I, stockDetailBasicPage }) => {
    await I.swipeUpFixed(stockDetailBasicPage.locator.newsContainer);
    await I.waitFor();
    await I.swipeUpFixed(stockDetailBasicPage.locator.newsContainer);
    await I.waitFor('mediumWait');
    await I.swipeUpFixed(stockDetailBasicPage.locator.newsContainer);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailNews('Test_item_No.6_Scroll_news_list_reaches_the_bottom_to_load_more_item');
});
