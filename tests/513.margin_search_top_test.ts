import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Symbol_ProductSearch - MarrginTradingSearchTop');

Before(async ({ I, loginAndSwitchToWebAs, search }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user21 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await search.goToMarginTradingSearchTop();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Margin Trading Search TOP', async ({ search }) => {
    await search.compareUrl('/mobile/search-margin');
    search.takeScreenshot.marginTradingSearchTop('Display_Margin_Trading_Search_TOP');
});

Scenario('Test item No.1 Enter keywords', async ({ I, search }) => {
    const searchMarginKeyInputSelector = '//*[@data-testid="searchMargin_keywordInput_id"]//input';
    await I.clickFixed(searchMarginKeyInputSelector);
    await I.waitFor('shortWait');
    I.fillField(searchMarginKeyInputSelector, '1069');
    await I.waitFor('shortWait');
    await search.takeScreenshot.marginTradingSearchTop('Test_item_No.1_Main_screen_display_Switch_to_suggestion_display_and_open_input_pad');
    I.pressKey('Enter');
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/search-margin/result');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.1_Suggestion_display_Transition_to_margin_trading_search_results');
});

Scenario('Test item No.1-1 Enter keywords', async ({ I, search }) => {
    const searchMarginKeyInputSelector = '//*[@data-testid="searchMargin_keywordInput_id"]//input';
    const suggestionItemSelector = '//*[@data-testid="searchMargin_searchSuggestion_0_id"]';
    await I.clickFixed(searchMarginKeyInputSelector);
    await I.waitFor('shortWait');
    I.fillField(searchMarginKeyInputSelector, '1069');
    await I.waitFor('shortWait');
    await I.clickFixed(suggestionItemSelector);
    await I.waitFor('mediumWait');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.1-1_Go_to_Stock_Product_Search_Domestic_Stocks');
});

Scenario('Test item No.2 Theme', async ({ I, search }) => {
    const themeSelector = '//*[@data-testid="searchMargin_theme_id"]';
    await I.clickFixed(themeSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/search/theme');
    I.see('テーマから探す', 'body');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.2_Go_to_Market_Ranking_Ranking_Domestic_Stocks');
});

Scenario('Test item No.4 Shareholder benefits', async ({ I, search }) => {
    const specialBenefitSelector = '//*[@data-testid="searchMargin_specialBenefit_id"]';
    await I.clickFixed(specialBenefitSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/benefit');
    I.see('株主優待', 'body');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.4_Go_to_list_of_shareholder_benefits');
});

Scenario('Test item No.5 List of stocks available for margin trading', async ({ I, search }) => {
    const marginTradingSelector = '//*[@data-testid="searchMargin_marginTrading_id"]';
    await I.clickFixed(marginTradingSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/search-margin/result');
    I.see('信用取引', 'body');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.5_Go_to_margin_trading_search_results');
});

Scenario('Test item No.6 General credit (long-term/day trading) stocks available for short selling', async () => {
    const generalCredit = '//*[@data-testid="searchMargin_generalMargin_id"]';
    // Go to the following URL {member-site-url}/ap/iPhone/Stocks/Margin/MarginSymbol/GeneralSellList
    await common.clickCardItem(generalCredit, '/ap/iPhone/Stocks/Margin/MarginSymbol/GeneralSellList', 'kcMemberSite');
});

Scenario('Test item No.7 Balance inquiry', async ({ I, search }) => {
    const positionInquirySelector = '//*[@data-testid="searchMargin_positionInquiry_id"]';
    await I.clickFixed(positionInquirySelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/position-inquiry/margin');
    I.see('残高照会\n信用建玉', 'body');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.7_Transition_to_Balance_inquiry_Domestic_stock_Credit_List');
});

Scenario('Test item No.8 Order inquiry', async ({ I, search }) => {
    const orderStatusSelector = '//*[@data-testid="searchMargin_orderStatus_id"]';
    await I.clickFixed(orderStatusSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/order-inquiry/margin');
    I.see('注文約定照会\n信用取引', 'body');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.8_Go_to_Order_Inquiry_Domestic_Stocks_Credit_List');
});

Scenario('Test item No.9 Transaction History', async () => {
    const transactionHistory = '//*[@data-testid="searchMargin_tradingHistory_id"]';
    // Go to the following URL: member-site-url}/ap/iPhone/Stocks/Margin/History/List
    await common.clickCardItem(transactionHistory, '/ap/iPhone/Stocks/Margin/History/List', 'kcMemberSite');
});

Scenario('Test item No.10 Possible purchase amount', async () => {
    const possiblePurchaseAmount = '//*[@data-testid="searchMargin_amountAvailable_id"]';
    // Transfer to the following URL {member-site-url}/ap/iphone/Assets/Kanougaku/Stock
    await common.clickCardItem(possiblePurchaseAmount, '/ap/iphone/Assets/Kanougaku/Stock', 'kcMemberSite');
});

Scenario('Test item No.11 Deposit transfer', async () => {
    const depositTransfer = '//*[@data-testid="searchMargin_depositTransfer_id"]';
    // Transfer to the following URL {member-site-url}/ap/iphone/CashFlow/Transfer/Cash/Input
    await common.clickCardItem(depositTransfer, '/ap/iphone/CashFlow/Transfer/Cash/Input', 'kcMemberSite');
});

Scenario('Test item No.12 KabuBoard Flash', async () => {
    const kabuBoardFlash = '//*[@data-testid="searchMargin_kabuBoardFlash_id"]';
    // Go to the following URL: {member-site-url}/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp
    await common.clickCardItem(kabuBoardFlash, '/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp', 'kcMemberSite');
});

Scenario('Test item No.13 Fees', async () => {
    const fees = '//*[@data-testid="searchMargin_commission_id"]';
    // Open the following URL in a new tab: https://kabu.com/cost/default.html#page_01
    await common.clickCardItem(fees, 'https://kabu.com/cost/default.html#page_01', 'external');
});

Scenario('Test item No.14 Trading rules', async () => {
    const fees = '//*[@data-testid="searchMargin_tradingRules_id"]';
    // Open the following URL in a new tab: https://kabu.com/item/shinyo/rule.html
    await common.clickCardItem(fees, 'https://kabu.com/item/shinyo/rule.html', 'external');
});

Scenario('Test item No.15 Cancel', async ({ I, search }) => {
    const searchMarginKeyInputSelector = '//*[@data-testid="searchMargin_keywordInput_id"]//input';
    const cancelButtonSelector = '//*[@data-testid="search_cancel_id"]';
    await I.clickFixed(searchMarginKeyInputSelector);
    await I.waitFor('shortWait');
    I.fillField(searchMarginKeyInputSelector, '1069');
    await I.waitFor();
    await I.clickFixed(cancelButtonSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.15_Clear_the_input_value_for_keyword_input_Switch_to_the_main_screen');
});

Scenario('Test item No.17 Search Suggestions (Domestic Stocks) Item', async ({ I, search }) => {
    const searchMarginKeyInputSelector = '//*[@data-testid="searchMargin_keywordInput_id"]//input';
    const suggestionItemSelector = '//*[@data-testid="searchMargin_searchSuggestion_0_id"]';
    await I.clickFixed(searchMarginKeyInputSelector);
    await I.waitFor('shortWait');
    I.fillField(searchMarginKeyInputSelector, '1069');
    await I.waitFor('shortWait');
    await I.clickFixed(suggestionItemSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/trade/margin/new');
    I.see('信用新規', 'body');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.17_Transition_to_domestic_stock_margin_trading_new_order_entry');
});

Scenario('Test item No.21 Credit Preferential Plan Application Status', async ({ I, search }) => {
    const creditPreferential = '//*[@data-testid="searchMargin_ApplicationStatus_id"]';
    // Go to: /mobile/personal/margin-preferential
    await I.scrollToElement(creditPreferential);
    await I.waitFor('shortWait');
    await I.clickFixed(creditPreferential);
    await I.waitFor('mediumWait');
    I.waitInUrl('/mobile/personal/margin-preferential');
    search.takeScreenshot.marginTradingSearchTop('Test_item_No.21_Transition_to_personal_margin_preferential');
});
