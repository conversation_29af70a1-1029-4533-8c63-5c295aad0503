import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import commonUI from '../pages/common-ui';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioTopPieChart');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to top page
    await portfolio.top.goToPage();
    await I.waitFor('mediumWait'); // Wait for the page to load
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio page common [1.パイチャート]', async ({ I }) => {
    await portfolio.common.verifyPieChart();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_1_pie_chart.png`);
});

Scenario('test portfolio page common [1.投資成績]', async ({ I }) => {
    await portfolio.common.verifyInvestmentResult();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_1_investment_result.png`);
});

Scenario('test portfolio page common [2.戻る]', async ({ I }) => {
    const { top, common, stock } = portfolio;

    await top.usStock();
    await common.clickHeaderBackButton();
    await common.verifyProductName(stock.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_2_back.png`);
});

Scenario('test portfolio page common [4.次へ]', async ({ I }) => {
    const { top, common, fund } = portfolio;

    await top.usStock();
    await common.clickHeaderNextButton();
    await common.verifyProductName(fund.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_4_next.png`);
});

Scenario('test portfolio page common [1.戻る]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.stock();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_1_top_stock.png`);
    await common.clickBack();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_1_back.png`);
});

Scenario('test portfolio page common [2.現物系-表示切り替え]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.stock();
    await common.switchAssetValuationTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_2_switch_asset_valuation_tab.png`);
    await common.switchCurrentValueTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_2_switch_current_value_tab.png`);
});

Scenario('test portfolio page common [3.与信系-表示切り替え]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.margin();
    await common.switchQuantityTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_3_switch_quantity_tab.png`);
    await common.switchCurrentValueTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_3_switch_current_value_tab.png`);
});

Scenario('test portfolio page common [5.資産評価額ソート]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.stock();
    await common.switchAssetValuationTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_5_switch_asset_valuation_tab.png`);
    await common.clickValuationSort();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_5_click_valuation_sort.png`);
});

Scenario('test portfolio page common [6.評価損益ソート]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.stock();
    await common.switchAssetValuationTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_6_switch_asset_valuation_tab.png`);
    await common.clickEvaluationProfitLossSort();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_6_click_evaluation_profit_loss_sort.png`);
});

Scenario('test portfolio page common [7.現在値ソート]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.stock();
    await common.switchCurrentValueTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_7_switch_current_value_tab.png`);
    await common.clickCurrentSort();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_7_click_current_sort.png`);
});

Scenario('test portfolio page common [8.売買ソート]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.margin();
    await common.switchQuantityTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_8_switch_quantity_tab.png`);
    await common.clickBuySellSort();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_8_click_buy_sell_sort.png`);
});

Scenario('test portfolio page common [9.数量ソート]', async ({ I }) => {
    const { top, common } = portfolio;

    await top.margin();
    await common.switchQuantityTab();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_9_switch_quantity_tab.png`);
    await common.clickQuantitySort();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_9_click_quantity_sort.png`);
});

Scenario('test portfolio page common [11.上にスクロール]', async ({ I }) => {
    const { other } = commonUI;

    await I.swipeDirection('up');
    await I.waitFor('mediumWait');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_11_scroll_up.png`);
    await other.clickScrollUp();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioCommonUi}_11_click_scroll_up.png`);
});
