import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from '../pages/search/common';
Feature('Reserve - CustomerReservePlanDetailFund');


Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user69 }); //08309912
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});
// ReservePlanDetail-Fund
Scenario('Test Item 0: Check UI Investment Fund plan detail page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const fundItem = '//div[@data-testid="reservePlan_reservePetitDetail_0_id"]';
    I.waitForElement(fundItem, 2);
    <PERSON><PERSON>scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');

});
Scenario('Test Item 1: Check Go To Savings Calendar', async ({ I }) => {
   
    const reserveCalendarTab = '//*[@data-testid="reservePlan_reserveCalendar_tab"]';
    I.clickFixed(reserveCalendarTab);
    I.saveScreenshot('1250.Test_item_No.1_Go_To_Savings_Calendar.png');
});
Scenario('Test Item 2: Check Go To Savings History', async ({ I }) => {
    const reserveHistoryTab = '//*[@data-testid="reservePlan_reserveHistory_tab"]';
    I.clickFixed(reserveHistoryTab);
    I.saveScreenshot('1250.Test_item_No.2_Go_To_Savings_History.png');
});
Scenario('Test Item 13: Check Go To Fund Detail', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const fundItem = '//*[@data-testid="reservePlan_reservePetitDetail_0_id"]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    const reservePlan_individualStockInformation_id = '//*[@data-testid="reservePlan_individualStockInformation_id"]';
    I.waitForElement(reservePlan_individualStockInformation_id, 2);
    I.scrollAndClick(reservePlan_individualStockInformation_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.13_Go_To_Fund_Detail.png');

});

Scenario('Test Item 22: Check Go To Schedule', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const fundItem = '//*[@data-testid="reservePlan_reservePetitDetail_0_id"]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    const scheduleLink = '//span[contains(text(), "当社指定日")]';
    I.waitForElement(scheduleLink, 2);
    I.scrollAndClick(scheduleLink);
    await common.clickCardItem(scheduleLink, 'kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
    I.saveScreenshot('1250.Test_item_No.22_Go_To_Schedule.png');
});
Scenario('Test Item 24: Check Go To Input Savings Changes', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const fundItem = '//*[@data-testid="reservePlan_reservePetitDetail_0_id"]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);


    const changeLink = '//*[@data-testid="reservePlan_change_id"]';
    I.waitForElement(changeLink, 2);
    I.scrollAndClick(changeLink);

    I.saveScreenshot('1250.Test_item_No.24_Go_To_Input_Savings_Changes.png');
    // back 
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 25: Check Go To Confirm Cancellation', async ({ I }) => {
    const cancelLink = '//*[@data-testid="reservePlan_cancel_id"]';
    I.waitForElement(cancelLink, 2);
    I.scrollAndClick(cancelLink);
    I.saveScreenshot('1250.Test_item_No.25_Go_To_Confirm_Cancellation.png');
    // back 
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 28: Check Go To Point Usage', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user46 });
    await I.waitFor();
    await accumulation.goToCustomerReservePlanPage();
    const fundItem = '//*[@data-testid="reservePlan_reservePetitDetail_0_id"]';
    I.waitForElement(fundItem, 2);
    await I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    await I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    const pointUsageLink = '//span[contains(text(), "ポイント利用状況")]';
    I.waitForElement(pointUsageLink, 2);
    await common.clickCardItem(pointUsageLink, '/iPhone/Personal/UsePoint/List', 'kcMemberSite');
    await I.saveScreenshot('1250.Test_item_No.28_Go_To_Point_Usage.png');
});