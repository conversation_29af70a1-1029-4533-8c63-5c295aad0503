Feature('Settings_Entry - CardSelectPage');
import { COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';
import SettingCard from '../pages/settingCard';

const locators = SettingCard.locators;

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user60 }, { name: COOKIE_KEY.siteId, value: '1' }]);
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Go to card select page', async() => {
    await SettingCard.goToPageCardSelect();
    await SettingCard.takeScreenshot('1766_card_select_page.png');
});

Scenario('Test item 1 MitsubishiUFJCard Register', async ({ I }) => {
    await SettingCard.clickItem(locators.mitsubishiUFJCardRegister);
    await I.amOnPage(locators.cardCommonRegister);
    await SettingCard.takeScreenshot('1766_Test_Item_1_MitsubishiUFJCard_Register.png');
});

Scenario('Test item 2 MitsubishiUFJCard Click Here', async () => {
    await SettingCard.goToPageCardSelect();
    await SettingCard.clickExternalUrl(locators.mitsubishiUFJCardClickHere, locators.mitsubishiUfjCardUrl);
    await SettingCard.takeScreenshot('1766_Test_Item_2_MitsubishiUFJCard_Click_Here.png');
});

Scenario('Test item 3 MitsubishiUFJCard target confirm', async ({ I }) => {
    await SettingCard.clickItem(locators.mitsubishiUFJCardTargetCardConfirmMethod);
    I.waitForText('主な対象カード', 3, 'body');
    await SettingCard.takeScreenshot('1766_Test_Item_3 MitsubishiUFJCard_target_confirm.png');
    await SettingCard.closeRightModal();
});

Scenario('Test item 4-1 au PAY Card - Registration', async ({ I }) => {
    await SettingCard.goToPageCardSelect();
    // + In cases other than the above (including when A Call fails): Transition to the third-party provision consent screen
    await SettingCard.clickItem(locators.auPAYCardRegister);
    await I.seeInCurrentUrl('/mobile/setting/card/agree');
});

Scenario('Test item 4-2 au PAY Card - Registration', async ({ I }) => {
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user74 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await SettingCard.goToPageCardSelect();
    // + If A.Credit Card Information (Common) API.aupayCreditcardRegisterCode = AUPAY_CREDITCARD_REGISTER_CODE_AUID: Transition to the following URL {member-site-url}/iphone/personal/auID/auIDRuleacceptAppsm.asp
    await common.clickCardItem(locators.auPAYCardRegister, '/iphone/personal/auID/auIDRuleacceptAppsm.asp', 'external');
});

Scenario('Test item 5 auPAYCard Click Here', async () => {
    await SettingCard.goToPageCardSelect();
    await SettingCard.clickExternalUrl(locators.auPAYCardClickHere, locators.lp200Url);
    await SettingCard.takeScreenshot('1766_Test_Item_5_auPAYCard_Click_Here.png');
});

Scenario('Test item 6 Caution message', async () => {
    await SettingCard.clickItem(locators.cautionMessage);
    await SettingCard.takeScreenshot('1766_Test_Item_6_Caution_message_1.png');
    await SettingCard.clickItem(locators.cautionMessage);
    await SettingCard.takeScreenshot('1766_Test_Item_6_Caution_message_2.png');
});
