import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockPetitTradingSellOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockPetitOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user32 });
    await stockPetitOrder.goToPetitSellOrderInput('8306');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Petit Trading Sell Order input', async ({ I, stockPetitOrder }) => {
    I.seeElement(stockPetitOrder.locator.cautionInfoPetitSell);
    await stockPetitOrder.takeScreenshot.petitSellOrderInput('Display_Domestic_Stock_Petit_Trading_Sell_Order_input');
});

Scenario('Test item No.3 Trade Restrictions and Trade Caution Information', async ({ I, stockPetitOrder }) => {
    const closeButtonSelector = '//*[@data-testid="common_rightSlide_close_id"]';
    await I.clickFixed(stockPetitOrder.locator.cautionInfoPetitSell);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    I.seeElement(closeButtonSelector);
    stockPetitOrder.takeScreenshot.petitSellOrderInput(
        'Test_item_No.3_Tap_to_display_the_Trade_Caution_Information_modal',
    );
});

Scenario('Test item No.11 Account Category', async ({ I, stockPetitOrder }) => {
    const accountTypeSelector = '//*[@data-testid="tradePetitSell_accountType_id"]';
    const accountTypeCount = await I.grabNumberOfVisibleElements(`${accountTypeSelector}//label`);
    if (accountTypeCount > 0) {
        const acountType = `${accountTypeSelector}//label[${accountTypeCount}]`;
        await I.clickFixed(acountType);
        await I.waitFor('shortWait');
        I.assertEqual(
            await I.grabCssPropertyFrom(`${acountType}/p`, 'background-color'),
            'rgba(255, 86, 0, 1)',
            'Background color is not equal',
        );
    }
    stockPetitOrder.takeScreenshot.petitSellOrderInput('Test_item_No.11_Select_account_category_item');
});

Scenario('Test item No.16 Quantity', async ({ I, stockPetitOrder }) => {
    const minusButtonSelector = '//*[@data-testid="groupInputNumber_minus_id"]';
    const quantitySelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const plusButtonSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(minusButtonSelector);
    I.seeElement(quantitySelector);
    I.seeElement(minusButtonSelector);
    I.seeElement(plusButtonSelector);
    stockPetitOrder.takeScreenshot.petitSellOrderInput('Test_item_No.16_Quantity_See_Numeric_Stepper');
});

Scenario('Test item No.20 Go to the order confirmation screen', async ({ I, stockPetitOrder }) => {
    const plusButtonSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    const confirmButtonSelector = '//*[@data-testid="tradePetitSell_orderConfirm_id"]';
    const checkButtonSelector = '//button[@aria-label="Check"]';
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    if ((await I.grabNumberOfVisibleElements(checkButtonSelector)) > 0) {
        await I.clickFixed(checkButtonSelector);
    }
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement('//*[@data-testid="tradePetitSellConfirm_orderConfirm_id"]');
    await stockPetitOrder.compareUrl('/mobile/trade/petit/sell/confirm');
    stockPetitOrder.takeScreenshot.petitSellOrderInput('Test_item_No.20_Go_to_the_order_confirmation_screen');
});
