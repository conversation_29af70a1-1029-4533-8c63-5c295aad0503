import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('PositionInquiry - PositionInquiryMarginList');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user43 }]);
    await I.waitFor();
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI of Position Inquiry Margin List page', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    I.saveScreenshot('1179.Test_item_No.0_check_UI_of_Position_Inquiry_Margin_List_page.png');
});

Scenario('Test Item 1-1: Commodity tab - Spot', async ({ I, orderStatus }) => {
     await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const spotButton = '//button[.//p[contains(text(), "現物")]]';
    await I.clickFixed(spotButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot('1179.Test_item_No.1.1_after_click_spot_tab.png');
});
Scenario('Test Item 1-2: Commodity tab - Credit', async ({ I, orderStatus }) => {
     await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const creditButton = '//button[.//p[contains(text(), "信用")]]';
    await I.clickFixed(creditButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot(`1179.Test_item_No.1.2_after_click_Product_tab_Credit.png`);
});
Scenario('Test Item 1-3: Commodity tab - Futures OP', async ({ orderStatus }) => {
     await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const futuresOptionsButton = '//button[.//p[contains(text(), "先物OP")]]';
    // {member-site-url}/ap/iPhone/Account/AssetDeriv/PositionSelect へ遷移する
    await common.clickCardItem(futuresOptionsButton, '/ap/iPhone/Account/AssetDeriv/PositionSelect', 'kcMemberSite');
});
Scenario('Test Item 1-4: Commodity tab - Investment trusts', async ({ I, orderStatus }) => {
     await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const investmentTrustsButton = '//button[.//p[contains(text(), "投信")]]';
    // Go to Balance Inquiry - Investment Trust - List
    await I.clickFixed(investmentTrustsButton);
    await I.waitFor('mediumWait');
    await I.seeInCurrentUrl('/mobile/position-inquiry/fund');
    await I.saveScreenshot(`1179.Test_item_No.1.4_Investment_Trust_List.png`);
});
Scenario('Test Item 1-5: Commodity tab - Foreign stocks', async ({ orderStatus }) => {
     await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const foreignStocksButton = '//button[.//p[contains(text(), "外国株式")]]';
    // {member-site-url}/ap/iPhone/ForeignStocks/USStock/Position/List へ遷移する
    await common.clickCardItem(foreignStocksButton, '/ap/iPhone/ForeignStocks/USStock/Position/List', 'kcMemberSite');
});

Scenario('Test Item 2: Check button filter ', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const settingButton = '//*[@data-testid="marginList_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.2_show_filler_modal.png');
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.2_close_filler_modal.png');
});
Scenario('Test Item 3: Check Deposit List Open/Close', async ({ I }) => {
    const depositList = '//div[@data-testid="marginList_depositList_id"]//img';
    await I.clickFixed(depositList);
    await I.waitFor();
    await I.saveScreenshot('1179.Test_item_No.3_open_depositList.png');
    await I.clickFixed(depositList);
    await I.waitFor();
    await I.saveScreenshot('1179.Test_item_No.3_close_depositList.png');
});
Scenario('Test Item 41: Check Maintenance Rate', async ({ I }) => {
    const maintenanceRate = '//*[@data-testid="marginList_maintenanceRate_id"]//img';
    I.clickFixed(maintenanceRate);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.41_open_maintenanceRate.png');
    I.clickFixed(maintenanceRate);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.41_close_maintenanceRate.png');
});
Scenario('Test Item 11: Check Trade Summary', async ({ I }) => {
    const positionSummary = '//*[@data-testid="marginList_positionSummary_0_id"]';
    I.saveScreenshot('1179.Test_item_No.11_before_click_positionSummary.png');
    I.clickFixed(positionSummary);
    await I.waitFor('shortWait');
    I.saveScreenshot(
        '1179.Test_item_No.11_after_click_positionSummary_go_to_position_inquiry_domestic_credit_stocks_page.png',
    );
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 33 34 35 37: Check filter ', async ({ I }) => {
    const settingButton = '//*[@data-testid="marginList_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    const filter_displaySymbol_id = '//*[@data-testid="filter_displaySymbol_id"]';
    const filter_tradingType_id = '//*[@data-testid="filter_tradingType_id"]';
    const filter_marginType_id = '//*[@data-testid="filter_marginType_id"]';
    const filter_sort_id = '//*[@data-testid="filter_sort_id"]';
    // click 33 and screenShot
    I.clickFixed(filter_displaySymbol_id);
    await I.waitFor('shortWait');
    const hD2LongName = '//p[contains(text(), "三菱ＵＦＪ(8306)")]';
    await I.clickFixed(hD2LongName);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.33_click_hD2LongName.png');
    // click 34 and screenShot
    I.clickFixed(filter_tradingType_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.34_open_filter_tradingType.png');
    const buyOnly = '//p[contains(text(), "買建のみ")]';
    I.clickFixed(buyOnly);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.34_click_buyOnly.png');
    // click 35 and screenShot
    I.clickFixed(filter_marginType_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.35_open_filter_marginType.png');
    const generalCreditLongTerm = '//p[contains(text(), "一般信用長期")]';
    I.clickFixed(generalCreditLongTerm);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.35_click_generalCreditLongTerm.png');
    // click 37 and screenShot
    I.clickFixed(filter_sort_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.37_open_filter_sort.png');
    const sortByNearRepaymentDate = '//p[contains(text(), "直近返済期限の遠い順")]';
    I.clickFixed(sortByNearRepaymentDate);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.37_click_sortByNearRepaymentDate.png');
});
Scenario('Test Item 39: Set all displayed stocks to selected state', async ({ I }) => {
    const clearButton = '//button[@data-testid="filter_clear_id"]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.39_set_all_displayed_stocks_to_selected_state.png');
});
Scenario('Test Item 40: Confirm Close the modal and re-run the initial drawing shown on the screen', async ({ I }) => {
    const filter_displaySymbol_id = '//*[@data-testid="filter_displaySymbol_id"]';
    const filter_tradingType_id = '//*[@data-testid="filter_tradingType_id"]';
    const filter_marginType_id = '//*[@data-testid="filter_marginType_id"]';
    const filter_sort_id = '//*[@data-testid="filter_sort_id"]';
    // click 33
    I.clickFixed(filter_displaySymbol_id);
    await I.waitFor('shortWait');
    const filterSymbolCount = await I.grabNumberOfVisibleElements(`${filter_displaySymbol_id}//div/p`);
    if (filterSymbolCount > 0) {
        // const hD2LongName = '//p[contains(text(), "キリンHD2LongNameキリンHD2LongName(8306)")]';
        await I.clickFixed(`${filter_displaySymbol_id}//div/p[2]`);
        await I.waitFor('shortWait');
    }
    // click 34
    I.clickFixed(filter_tradingType_id);
    await I.waitFor('shortWait');
    const buyOnly = '//p[contains(text(), "買建のみ")]';
    I.clickFixed(buyOnly);
    await I.waitFor('shortWait');
    // click 35
    I.clickFixed(filter_marginType_id);
    await I.waitFor('shortWait');
    const generalCreditLongTerm = '//p[contains(text(), "一般信用長期")]';
    I.clickFixed(generalCreditLongTerm);
    await I.waitFor('shortWait');
    // click 37
    I.clickFixed(filter_sort_id);
    await I.waitFor('shortWait');
    const sortByNearRepaymentDate = '//p[contains(text(), "直近返済期限の遠い順")]';
    I.clickFixed(sortByNearRepaymentDate);
    await I.waitFor('shortWait');

    const filter_confirm_id = '//button[@data-testid="filter_confirm_id"]';
    I.clickFixed(filter_confirm_id);
    await I.waitFor('shortWait');
    I.saveScreenshot(
        '1179.Test_item_No.40_confirm_close_the_modal_and_re_run_the_initial_drawing_shown_on_the_screen.png',
    );
});
Scenario('Test Item 52: Show Credit Help Modal', async ({ I }) => {
    const marginList_moreInfo_id = '//*[@data-testid="marginList_moreInfo_id"]';
    I.clickFixed(marginList_moreInfo_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.52_show_credit_help_modal.png');
    const cancelButton = '//button[@aria-label="cancel-btn"]';
    I.clickFixed(cancelButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 47: Check Caution: Opening and closing', async ({ I }) => {
    const cautionDropdown = '//div[@data-testid="marginList_caution_id"]//img';
    I.waitForElement(cautionDropdown, 2);
    await I.scrollToElement(cautionDropdown);
    await I.waitFor();
    await I.clickFixed(cautionDropdown);
    const chakraCollapse = '//*[@class="chakra-collapse"]';
    await I.scrollToElement(chakraCollapse);
    await I.saveScreenshot('1179.Test_item_No.47_check_Caution_Open.png');

    await I.clickFixed(cautionDropdown);
    await I.saveScreenshot('1179.Test_item_No.53_check_Caution_Close.png');
});
// GO to detail page
Scenario('Test Item Layout: The layout must be displayed correctly on the screen.', async ({ I, orderStatus }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user44 });
    I.refreshPage();
    await I.waitFor();
    await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    const positionSummary = '//*[@data-testid="marginList_positionSummary_0_id"]';
    I.clickFixed(positionSummary);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_Layout_The_layout_must_be_displayed_correctly_on_the_screen.png');
});
Scenario('Test Item 3: Tap on the stock to go to domestic stock investment information - stock details_chart display',
    async ({ I }) => {
        const marginDetailList_symbol_id = '//*[@data-testid="marginDetailList_symbol_id"]';
        I.clickFixed(marginDetailList_symbol_id);
        await I.waitFor();
        I.saveScreenshot(
            '1179.Test_item_No.3_Tap_on_the_stock_to_go_to_domestic_stock_investment_information_stock_details_chart_display.png',
        );
        const backButton = '//*[@data-testid="common_back_id"]';
        I.clickFixed(backButton);
        await I.waitFor('shortWait');
    },
);
Scenario('Test Item 23: Check Transition to domestic stock margin trading - repayment - order entry ',
    async ({ I }) => {
        I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user44 });
        I.refreshPage();
        await I.waitFor('mediumWait');
        const marginDetailList_repayment_id = '//*[@data-testid="marginDetailList_repayment_id"]';
        if ((await I.grabNumberOfVisibleElements(marginDetailList_repayment_id)) > 0) {
            // I.waitForElement(marginDetailList_repayment_id, 2);
            I.scrollAndClick(marginDetailList_repayment_id);
            I.saveScreenshot(
                '1179.Test_item_No.23_check_Transition_to_domestic_stock_margin_trading_repayment_order_entry.png',
            );
            const backButton = '//*[@data-testid="common_back_id"]';
            I.clickFixed(backButton);
            await I.waitFor('shortWait');
        } else {
            I.say('No repayment button found, skipping test item 23');
        }
    },
);
Scenario('Test Item 24: Check Receipt/delivery In the case of receipt: Transition to domestic stock margin trading - receipt - order entry',
    async ({ I }) => {
        I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user44 });
        I.refreshPage();
        await I.waitFor('mediumWait');
        const marginDetailList_receipt_id = '//*[@data-testid="marginDetailList_receipt_id"]';
        if ((await I.grabNumberOfVisibleElements(marginDetailList_receipt_id)) > 0) {
            I.waitForElement(marginDetailList_receipt_id, 2);
            I.scrollAndClick(marginDetailList_receipt_id);
            I.saveScreenshot(
                '1179.Test_item_No.24_check_Receipt_delivery_in_the_case_of_receipt_Transition_to_domestic_stock_margin_trading_receipt_order_entry.png',
            );
            const backButton = '//*[@data-testid="common_back_id"]';
            I.clickFixed(backButton);
            await I.waitFor('shortWait');
        } else {
            I.say('No receipt button found, skipping test item 24');
        }
    },
);
Scenario('Test Item 25: Check Go to Balance Inquiry-Domestic Stock-Credit-Details', async ({ I }) => {
    const marginDetailList_detail_id = '//*[@data-testid="marginDetailList_detail_id"]';
    I.waitForElement;
    I.scrollAndClick(marginDetailList_detail_id);
    I.saveScreenshot('1179.Test_item_No.25_check_Go_to_Balance_Inquiry_Domestic_Stock_Credit_Details.png');
    await I.backToPreviousScreen();
  
});
Scenario('Test Item 26: Display credit help modal', async ({ I }) => {
    const marginDetailList_moreInfo_id = '//*[@data-testid="marginDetailList_moreInfo_id"]';
    I.clickFixed(marginDetailList_moreInfo_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1179.Test_item_No.26_display_credit_help_modal.png');
    const cancelButton = '//button[@aria-label="cancel-btn"]';
    I.clickFixed(cancelButton);
    await I.waitFor('shortWait');
});
// Margin Detail
Scenario('Test Item: Check Margin Detail The screen is displayed without any layout problems.', async ({ I, orderStatus }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user101 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    //click to detail
    const marginList_positionSummary_0_id = '//*[@data-testid="marginList_positionSummary_0_id"]';
    I.waitForElement(marginList_positionSummary_0_id, 2);
    await I.scrollAndClick(marginList_positionSummary_0_id);
    await I.waitFor();


    const marginDetailList_detail_id = '//*[@data-testid="marginDetailList_detail_id"]';
    I.waitForElement(marginDetailList_detail_id, 2);
    I.scrollAndClick(marginDetailList_detail_id);
    I.saveScreenshot('1179.Test_item_check_Margin_Detail_The_screen_is_displayed_without_any_layout_problems.png');
});
Scenario('Test Item 26: Trade Tap "Go to the following URL"', async ({ I }) => {
    const marginDetailList_trade_id = '//*[@data-testid="marginDetailList_trade_id"]';
    I.waitForElement(marginDetailList_trade_id, 2);
    I.scrollToElement(marginDetailList_trade_id);
    await common.clickCardItem(marginDetailList_trade_id, 'ap/iPhone/Stocks/Margin/History/Detail', 'kcMemberSite');
});
Scenario('Test Item 29: Caution statement Open and close', async ({ I, orderStatus }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user101 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await orderStatus.goToPositionInquiryDomesticCreditStocksPage();
    //click to detail
    const marginList_positionSummary_0_id = '//*[@data-testid="marginList_positionSummary_0_id"]';
    I.waitForElement(marginList_positionSummary_0_id, 2);
    await I.scrollAndClick(marginList_positionSummary_0_id);
    await I.waitFor();


    const marginDetailList_detail_id = '//*[@data-testid="marginDetailList_detail_id"]';
    I.waitForElement(marginDetailList_detail_id, 2);
    I.scrollAndClick(marginDetailList_detail_id);




    //click to caution
    const marginDetailList_caution_id = '//div[@data-testid="marginDetailList_caution_id"]';
    I.waitForElement(marginDetailList_caution_id, 2);
    I.scrollToElement(marginDetailList_caution_id);
    await I.waitFor();
    await I.clickFixed(marginDetailList_caution_id);
    await I.waitFor();
    const chakraCollapse = '//*[@class="chakra-collapse"]';
    I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1179.Test_item_No.29_Caution_statement_Open.png');
    // click again
    await I.waitFor();
    I.scrollAndClick(marginDetailList_caution_id);
    I.saveScreenshot('1179.Test_item_No.29_Caution_statement_Close.png');
});
