import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from '../pages/search/common';
Feature('PositionInquiry - DomesticStockUnitStocks');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user41 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 1: Check UI of Position Inquiry Domestic Stock Unit Stocks Page', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryDomesticStockUnitStocksPage();
    I.saveScreenshot('1156.Test_item_No.1_UI_of_Position_Inquiry_Domestic_Stock_Unit_Stocks_Page.png');
});
Scenario('Test Item 3: Check button filter ', async ({ I }) => {
    const settingButton = '//*[@data-testid="stockList_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1130.Test_item_No.2_show_filler_modal.png');
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.3_close_filler_modal.png');

});
Scenario('Test Item 16: Check scroll top', async ({ I }) => {
    const stockListCaution = '//*[@data-testid="stockList_caution_id"]';
    const scrollToTopButton = '//*[@id="scrollButton"]';

    // Scroll down
    await I.scrollToElement(stockListCaution);
    await I.waitFor('shortWait');

    // Test scroll to top button
    I.saveScreenshot('1156.Test_item_No.16_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');

    // Dont see scroll to top button
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1156.Test_item_No.16_scrollToTop_dont_see_button.png');
});
Scenario('Test Item 17: Display pull-down menu excluding duplicates', async ({ I }) => {
    const settingButton = '//*[@data-testid="stockList_filter_id"]';

    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 全銘柄
    const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';
    I.waitForElement(allMokuhyouButton, 5);
    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.17_display_pull-down_menu_excluding_duplicates.png');
    //click button have p text キリンＨＤ(2503)
    const foreignMiningButton = '//*[@data-testid="filter_displaySymbol_id"]//p[text()="キリンＨＤ(2503)"]';
    I.waitForElement(foreignMiningButton, 5);
    I.clickFixed(foreignMiningButton);
})
Scenario('Test Item 18: Display Order Display the drop-down menu', async ({ I }) => {

    //click button have p text 取得単価
    const getPriceButton = '//*[@data-testid="filter_unitPriceDisplay_id"]//p[text()="取得単価"]';

    I.clickFixed(getPriceButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.18_display_order_display_the_drop-down_menu.png');
    //click button have p text 買付単価
    const buyPriceButton = '//*[@data-testid="filter_unitPriceDisplay_id"]//p[text()="買付単価"]';

    I.clickFixed(buyPriceButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.18_display_order_display_the_drop-down_menu_buy_price.png');
})
Scenario('Test Item 19: Display Sort Order - Display the drop-down list for the target item', async ({ I }) => {

    //click button have p text 銘柄コードの昇順
    const codeAscendingButton = '//*[@data-testid="filter_sortOrder_id"]//p[text()="銘柄コードの昇順"]';

    I.clickFixed(codeAscendingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.19_display_sort_order_display_the_drop-down_list_for_the_target_item.png');
    //click button have p text 評価損益の昇順
    const evaluationProfitLossAscendingButton = '//*[@data-testid="filter_sortOrder_id"]//p[text()="評価損益の昇順"]';

    I.clickFixed(evaluationProfitLossAscendingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.19_display_sort_order_display_the_drop-down_list_for_the_target_item_evaluation_profit_loss_ascending.png');
})
Scenario('Test Item 20: Set initial display sticks and order', async ({ I }) => {
    const clearButton = '//button[contains(text(), "クリア")]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.20_set_initial_display_sticks_and_order.png');
    
})
Scenario('Test Item 21: Filter, switch display, and sort based on entered parameters', async ({ I }) => {     
     const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';
     I.clickFixed(allMokuhyouButton);
     await I.waitFor('shortWait');          
     const foreignMiningButton = '//*[@data-testid="filter_displaySymbol_id"]//p[text()="キリンＨＤ(2503)"]';
     I.clickFixed(foreignMiningButton);   
     const getPriceButton = '//*[@data-testid="filter_unitPriceDisplay_id"]//p[text()="取得単価"]';
 
     I.clickFixed(getPriceButton);
     await I.waitFor('shortWait');          
     const buyPriceButton = '//*[@data-testid="filter_unitPriceDisplay_id"]//p[text()="買付単価"]';
 
     I.clickFixed(buyPriceButton);
     await I.waitFor('shortWait');       
     const codeAscendingButton = '//*[@data-testid="filter_sortOrder_id"]//p[text()="銘柄コードの昇順"]';
 
     I.clickFixed(codeAscendingButton);
     await I.waitFor('shortWait');          
     const evaluationProfitLossAscendingButton = '//*[@data-testid="filter_sortOrder_id"]//p[text()="評価損益の昇順"]';
 
     I.clickFixed(evaluationProfitLossAscendingButton);
     await I.waitFor('shortWait');     


    const submitButton = '//button[contains(text(), "確定する")]';

    I.clickFixed(submitButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1130.Test_item_No.26_filter_and_sort_based_on_entered_parameters.png');


})
Scenario('Test Item 22: Close fillter and sort modal', async ({ I }) => {
    const settingButton = '//*[@data-testid="stockList_filter_id"]';
    
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.22_show_setting_and_sort_modal.png');
    const closeButton = '//*[@data-testid="common_rightSlide_close_id"]';
    
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.22_close_setting_and_sort_modal.png');
})
Scenario('Test Item 27: Check view this screen Display the actual help modal', async ({ I }) => {
    const helpButton = '//*[@data-testid="stockList_moreInfo_id"]';
    I.clickFixed(helpButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.27_check_view_this_screen_display_the_actual_help_modal.png');
    const closeButton = '//button[@aria-label="cancel-btn"]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.27_close_actual_help_modal.png');
})
Scenario('Test Item 24: Check Caution: Opening and closing the accordion', async ({ I }) => {
    const stockListCautionIdCollapse = '//div[@data-testid="stockList_caution_id"]';
    const dropDownArrow = '//img[@data-testid="common_dropDown_arrow_id_down"]';
    const dropDownArrowUp = '//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.waitForElement(dropDownArrow, 1);
    I.scrollAndClick(stockListCautionIdCollapse);
    const chakraCollapse = stockListCautionIdCollapse + '//div[@class="chakra-collapse"]';
    I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1156.Test_item_No.24_check_Caution_open_the_accordion.png');
    I.scrollAndClick(dropDownArrowUp);
    I.saveScreenshot('1156.Test_item_No.24_check_Caution_close_the_accordion.png');
})
Scenario('Test Item 28: Check 28. For more information, click here: "Open the following URL in a new tab https://kabu.com/sp/item/help/zandakashoukai.html#anc01"', async ({ I }) => {
    const helpButton = '//*[@data-testid="stockList_moreInfo_id"]';
    I.clickFixed(helpButton);
    await I.waitFor('shortWait');
    const helpSheetHelpLink = '//*[@data-testid="helpSheet_helpLink_id"]';
    await common.clickCardItem(helpSheetHelpLink, 'https://kabu.com/sp/item/help/zandakashoukai.html#anc01', 'external');

    
})