import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioFundPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to fund page
    await portfolio.top.goToPage();
    await portfolio.top.fund();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio fund page', async ({ I }) => {
    const { common, fund } = portfolio;

    await common.verifyProductName(fund.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFundPage}_layout.png`);
});

Scenario('test portfolio fund page [3.銘柄カード]', async ({ I }) => {
    const { common, fund } = portfolio;

    await common.clickSymbolCard(fund.productType, '米国NASDAQオープンAコース');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFundPage}_3_symbol_card.png`);
});

Scenario('test portfolio fund page [13.銘柄情報を見る]', async ({ I }) => {
    const { common, fund } = portfolio;

    await common.clickSymbolCard(fund.productType, '米国NASDAQオープンAコース');
    await I.clickFixed(fund.locators.seeInfo);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl('/mobile/info/fund/detail');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFundPage}_13_see_info.png`);
});

Scenario('test portfolio fund page [14.残高照会]', async ({ I }) => {
    const { common, fund } = portfolio;

    await common.clickSymbolCard(fund.productType, '米国NASDAQオープンAコース');
    await I.clickFixed(fund.locators.positionInquiry);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl('/mobile/position-inquiry/fund');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFundPage}_14_position_inquiry.png`);
});
