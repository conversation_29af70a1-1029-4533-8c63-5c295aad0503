import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('InvestmentProducts - USStockInfoSummary');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.switchToNative();
    await <PERSON>.closeBrowser();
});

Scenario('Item 0. Check UI US Stock Information-Corporate Info', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/corporateinfo?symbol=123';
    I.amOnPage(pageUrl);
    I.see('米国株式\n個別銘柄情報', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(pageUrl);
    I.saveScreenshot('608.usstock_info_corporateinfo_No.0_access_the_usstock_info_corporateinfo_info_page.png');
});

// 3.URL -> 別タブでURLを開く
Scenario('Item 3. Open URL in a new tab', async () => {
    await common.clickCardItem('$usStockCorporateInfo_url_id', 'https://www.genpact.com/', 'external');
});

// 7.テーマ名 -> 下記のURLに遷移
Scenario('Item 7. Go to the following URL', async ({ I }) => {
    // {member-site-url}/ap/iPhone/InvInfo/USMarket/Theme/Detail?id=A.米株-企業情報API.themes[n].id
    const themeName = '//a[starts-with(@data-testid, "usStockCorporateInfo_themeName_id_")]';
    await I.scrollToElement(themeName);
    await common.clickCardItem(themeName, '/ap/iPhone/InvInfo/USMarket/Theme/Detail', 'kcMemberSite');
});
