import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import hamburgerMenu from '../pages/hamburgerMenu';
import search from '../pages/search/index';

Feature('Symbol_ProductSearch - SearchDomesticStockCash');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });

    //
    await hamburgerMenu.goToMenu();
    await hamburgerMenu.clickItem(hamburgerMenu.locators.domesticCashStock);
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('test domestic cash stock search [レイアウト]', async ({ I }) => {
    await commonUi.header.verifyTitle('国内現物株');
    await I.saveScreenshot('469.domestic_cash_stock_search_page.png');
});

// 1.キーワード入力
Scenario('test domestic cash stock search [1.キーワード入力]', async ({ I }) => {
    const inputLocator = search.stock.locators.input;
    await search.common.search(inputLocator, 'バルカー');
    await I.saveScreenshot('469.domestic_cash_stock_search_1_search_suggestion.png');
});

// 2.テーマ
Scenario('test domestic cash stock search [2.テーマ]', async ({ I }) => {
    await search.stock.theme();
    await I.saveScreenshot('469.domestic_cash_stock_search_2_click_theme.png');
});

// 3.ランキング
Scenario('test domestic cash stock search [3.ランキング]', async ({ I }) => {
    await search.stock.ranking();
    await I.saveScreenshot('469.domestic_cash_stock_search_3_click_ranking.png');
});

// 4.株主優待
Scenario('test domestic cash stock search [4.株主優待]', async ({ I }) => {
    await search.stock.specialBenefit();
    await I.saveScreenshot('469.domestic_cash_stock_search_4_click_special_benefit.png');
});

// 5.残高照会
Scenario('test domestic cash stock search [5.残高照会]', async ({ I }) => {
    await search.stock.positionInquiry();
    await I.saveScreenshot('469.domestic_cash_stock_search_5_click_position_inquiry.png');
});

// 6.注文照会
Scenario('test domestic cash stock search [6.注文照会]', async ({ I }) => {
    await search.stock.orderStatus();
    await I.saveScreenshot('469.domestic_cash_stock_search_6_click_order_status.png');
});

// 7.取引履歴
Scenario('test domestic cash stock search [7.取引履歴]', async ({ I }) => {
    await search.stock.tradingHistory();
    await I.saveScreenshot('469.domestic_cash_stock_search_7_click_trading_history.png');
});

// 8.買付出金可能額
Scenario('test domestic cash stock search [8.買付出金可能額]', async ({ I }) => {
    await search.stock.availableAmount();
    await I.saveScreenshot('469.domestic_cash_stock_search_8_click_available_amount.png');
});

// 9.プチ株積立
Scenario('test domestic cash stock search [9.プチ株積立]', async ({ I }) => {
    await search.stock.petitReserve();
    await I.saveScreenshot('469.domestic_cash_stock_search_9_click_petit_reserve.png');
});

// 10.カブボードフラッシュ
Scenario('test domestic cash stock search [10.カブボードフラッシュ]', async ({ I }) => {
    await search.stock.kabuBoardFlash();
    await I.saveScreenshot('469.domestic_cash_stock_search_10_click_kabu_board_flash.png');
});

// 11.手数料
Scenario('test domestic cash stock search [11.手数料]', async ({ I }) => {
    await search.stock.commission();
    await I.saveScreenshot('469.domestic_cash_stock_search_11_click_commission.png');
});

// 12.取引ルール
Scenario('test domestic cash stock search [12.取引ルール]', async ({ I }) => {
    await search.stock.tradingRules();
    await I.saveScreenshot('469.domestic_cash_stock_search_12_click_trading_rules.png');
});

// 13.キャンセル
Scenario('test domestic cash stock search [13.キャンセル]', async ({ I }) => {
    const inputLocator = search.stock.locators.input;
    await search.common.search(inputLocator, 'test');
    await I.saveScreenshot('469.domestic_cash_stock_search_13_search_suggestion.png');
    await search.suggestion.cancel();
    await I.saveScreenshot('469.domestic_cash_stock_search_13_click_cancel.png');
});

// 15.検索候補(国内株式)アイテム
Scenario('test domestic cash stock search [15.検索候補(国内株式)アイテム]', async ({ I }) => {
    const keyword = 'バルカー';
    const inputLocator = search.stock.locators.input;

    await search.common.search(inputLocator, keyword);
    await I.saveScreenshot('469.domestic_cash_stock_search_15_search_suggestion.png');
    await I.clickFixed(search.stock.locators.searchSuggestionItem(0));
    I.waitInUrl('/mobile/trade/stock/buy', 5);
    await I.saveScreenshot('469.domestic_cash_stock_search_15_display_buy.png');
});

// 19.検索結果銘柄アイテム(国内株)
Scenario('test domestic cash stock search [19.検索結果銘柄アイテム(国内株)]', async ({ I }) => {
    const keyword = 'バルカー';
    const inputLocator = search.stock.locators.input;

    await search.common.searchResult(inputLocator, keyword);
    I.waitInUrl('/mobile/search-stock/result', 5);
    await I.saveScreenshot('469.domestic_cash_stock_search_19_search_result.png');
    await I.clickFixed(search.stock.locators.searchResultItem(0));
    I.waitInUrl('/mobile/trade/stock/buy', 5);
    await I.saveScreenshot('469.domestic_cash_stock_search_19_display_buy.png');
});

// 19-1.お気に入り(国内株)
Scenario('test domestic cash stock search [19-1.お気に入り(国内株)]', async ({ I }) => {
    const keyword = 'バルカー';
    const inputLocator = search.stock.locators.input;
    const favoriteButton = '//*[@data-testid="stockSearch_searchResultSymbolDomesticStockItem_id_0"]//div[1]';

    await search.common.searchResult(inputLocator, keyword);
    I.waitInUrl('/mobile/search-stock/result', 5);
    await I.clickFixed(favoriteButton);
    await I.saveScreenshot('469.domestic_cash_stock_search_19-1_show_favorite_registration_modal.png');
});

// - お気に入り(国内株)
// 20.続きを読み込む
Scenario('test domestic cash stock search [20.続きを読み込む]', async ({ I }) => {
    const keyword = 'アイリック';
    const inputLocator = search.stock.locators.input;
    const loadMoreLocator = search.stock.locators.loadMore;

    await search.common.searchResult(inputLocator, keyword);
    I.waitForVisible(search.stock.locators.searchResultItem(0), 5);
    await search.common.scrollToLoadMore(loadMoreLocator);
    await I.saveScreenshot('469.domestic_cash_stock_search_20_scroll_bottom_list.png');
    await search.common.performLoadMore(loadMoreLocator);
    await I.saveScreenshot('469.domestic_cash_stock_search_20_click_read_more.png');
});

// 21.上に戻る
Scenario('test domestic cash stock search [21.上に戻る]', async ({ I }) => {
    const keyword = 'バルカー';
    const inputLocator = search.stock.locators.input;

    await search.common.searchResult(inputLocator, keyword);
    I.waitForVisible(search.stock.locators.searchResultItem(0), 5);
    await I.swipeElementDirection('up', '#search-result');
    await I.waitFor('mediumWait');
    await I.saveScreenshot('469.domestic_cash_stock_search_21_swipe_up.png');
    await commonUi.other.clickScrollUp();
    await I.saveScreenshot('469.domestic_cash_stock_search_21_click_scroll_up.png');
});
