import { COLOR, COOKIE_KEY, LAST_EXTERNAL_URL, USER_ID } from '../const/constant';

Feature('Notify - NotifyIndividualListPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyIndividualList.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

Scenario('test notify individual list page', async ({ I }) => {
    I.waitForElement('//*[@data-testid="common_noti_id"]');
    I.click('//*[@data-testid="common_noti_id"]');
    await I.waitFor('mediumWait');
    I.saveScreenshot('242.notify_individual_list_notifyTab.png');

    const newInformationTab = '//*[@data-testid="noticeTab_individualNoti_id"]';
    I.waitForElement(newInformationTab);
    I.click(newInformationTab);
    await I.waitFor();
    I.saveScreenshot('242.notify_individual_list_individualListTab.png');
});

Scenario('test Item 1 notifyList page', async ({ I }) => {
    I.saveScreenshot('242.notify_individual_list_individualListTab.png');
    I.click('//*[@data-testid="notifyIndividualList_notifyList_id"]');
    await I.waitFor('extraLongWait');
    I.activateApp();
    const localStorage = await I.getLocalStorage(LAST_EXTERNAL_URL);
    I.say(localStorage);
    I.assertEqual(
        localStorage,
        'https://notice.kabu.co.jp/notifyList',
        'localStorage is not equal to https://notice.kabu.co.jp/notifyList',
    );
});

Scenario('test Item 3 SortIcon page', async ({ I }) => {
    I.click('//*[@data-testid="notifyIndividualList_customSortIcon_id"]');
    await I.waitFor('mediumWait');
    I.saveScreenshot('242.notify_individual_list_sortIcon.png');
    I.seeElement('//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[1]/p');
});

Scenario('test Item 12 Category Select Radio Button page', async ({ I }) => {
    I.saveScreenshot('242.notify_individual_list_categorySelectRadioButton.png');
    const categorySelectRadioButton = '//*[@data-testid="notifyIndividualList_categorySelect_id"]';
    I.seeElement(categorySelectRadioButton);
    I.click(`${categorySelectRadioButton}/label[2]`);
    const backgroundColor = await I.grabCssPropertyFrom(`${categorySelectRadioButton}/label[2]/p`, 'background-color');
    I.assertEqual(backgroundColor, COLOR.mainColor, `backgroundColor is not ${COLOR.mainColor}`);
});

Scenario('test Item 13 Select Unread Seen Radio Button page', async ({ I }) => {
    I.saveScreenshot('242.notify_individual_list_selectUnreadSeenRadioButton.png');
    const selectUnreadSeenRadioButton = '//*[@data-testid="notifyIndividualList_unReadSelect_id"]';
    I.seeElement(selectUnreadSeenRadioButton);
    I.click(`${selectUnreadSeenRadioButton}/label[2]`);
    const backgroundColor = await I.grabCssPropertyFrom(
        `${selectUnreadSeenRadioButton}/label[2]/p`,
        'background-color',
    );
    I.assertEqual(backgroundColor, COLOR.mainColor, `backgroundColor is not ${COLOR.mainColor}`);
});

Scenario('test Item 11 cancel Button page', async ({ I }) => {
    I.saveScreenshot('242.notify_individual_list_cancelButton.png');
    const cancelButton = '//*[@data-testid="notifyIndividualList_close_id"]';
    I.seeElement(cancelButton);
    I.click(cancelButton);
});

Scenario('test Item 9 Notify List page', async ({ I }) => {
    const notifyList = '//*[@data-testid="notifyIndividualList_notiItem_id_0"]';
    I.seeElement(notifyList);
    I.click(notifyList);
    await I.waitFor('mediumWait');
    I.saveScreenshot('242.notify_individual_list_notifyListDetail.png');
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, '/notice/customer/detail', 'currentUrl is not equal to /notice/customer/detail');
    I.click('//*[@data-testid="common_back_id"]');
});

Scenario('test Item 10 Scroll Top page', async ({ I }) => {
    I.swipeUp('//*[@data-testid="notifyIndividualList_notiItem_id_0"]');
    await I.waitFor();
    I.seeElement('//*[@id="scrollButton"]');
    I.click('//*[@id="scrollButton"]');
    I.saveScreenshot('242.notify_individual_list_notifyListDetail.png');
});

Scenario('test Item 14 Confirm Button page', async ({ I }) => {
    I.tapLocationOfElement('//*[@data-testid="notifyIndividualList_customSortIcon_id"]');
    await I.waitFor('mediumWait');
    I.seeElement('//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[1]/p');
    I.saveScreenshot('242.notify_individual_list_confirmButton.png');
    const confirmButton = '//*[@data-testid="notifyIndividualList_confirm_id"]';
    I.seeElement(confirmButton);
    I.click(confirmButton);
    await I.waitFor('mediumWait');
    I.dontSeeElement('//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[1]/p');
});
