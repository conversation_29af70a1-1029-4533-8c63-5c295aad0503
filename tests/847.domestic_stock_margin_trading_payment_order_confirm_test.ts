import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingPaymentOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user33 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading Payment Order confirm', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    I.see('信用返済', 'body');
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/confirm');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Display_Domestic_Stock_Margin_Trading_Payment_Order_confirm',
    );
});

Scenario('Test item No.23+24 Fill password + check for omitted password', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    const passwordSelector = '//*[@data-testid="marginPaymentConfirm_password_id"]';
    const passwordOmittedSelector = '//*[@data-testid="marginPaymentConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginPaymentConfirm_confirmButton_id"]';
    I.fillField(passwordSelector, stockMarginPaymentOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmittedSelector);
    I.assertContain(
        await I.grabCssPropertyFrom(confirmButtonSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not qual',
    );
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Test_item_No.23_23_Fill_password_and_check_for_omitted_password_to_See_order_confirmation_button_state',
    );
});

// Not possible
Scenario.skip('Test item No.25-2 Order Confirmation - browser back', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    const passwordSelector = '//*[@data-testid="marginPaymentConfirm_password_id"]';
    const passwordOmittedSelector = '//*[@data-testid="marginPaymentConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginPaymentConfirm_confirmButton_id"]';
    I.fillField(passwordSelector, stockMarginPaymentOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmittedSelector);
    // If browser is backed up during loading: Transition to Order Inquiry - Domestic Stocks - Credit - List Screen
    await I.executeScript(() => {
        setTimeout(() => {
            window.history.back();
        }, 500);
    });
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor();
    await stockMarginPaymentOrder.compareUrl('/mobile/order-inquiry/margin');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Test_item_No.25-2_Transition_to_order_inquiry_list_screen',
    );
});

Scenario('Test item No.25-3 Order Confirmation', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    const passwordSelector = '//*[@data-testid="marginPaymentConfirm_password_id"]';
    const passwordOmittedSelector = '//*[@data-testid="marginPaymentConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginPaymentConfirm_confirmButton_id"]';
    I.fillField(passwordSelector, stockMarginPaymentOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmittedSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/complete');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm('Test_item_No.25-3_Transition_to_order_completed');
});

Scenario('Test item No.26 Modify order details', async ({ I, stockMarginPaymentOrder }) => {
    const priceInputSelector =
        '//*[@data-testid="marginPaymentUturn_executionMethod_id"]//input[@data-testid="groupInputNumber_input_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginPaymentUturn_orderConfirmButton_id"]';
    const correctButtonSelector = '//*[@data-testid="marginPaymentConfirm_correctButton_id"]';
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    await I.waitFor('mediumWait');
    I.waitForElement(correctButtonSelector, 5);
    await I.scrollToElement(correctButtonSelector);
    await I.clickFixed(correctButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm('Test_item_No.26_Transition_to_order_entry');
    // I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
    await I.waitFor();
    await stockMarginPaymentOrder.goToMarginPaymentUturnOrderInput();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user33 });
    I.refreshPage();
    await I.waitFor('mediumWait');
    await I.scrollToElement(priceInputSelector);
    I.fillField(priceInputSelector, stockMarginPaymentOrder.inputValues.price);
    I.blur(priceInputSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    I.waitForElement(correctButtonSelector, 5);
    await I.clickFixed(correctButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/uturn');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm('Test_item_No.26_Transition_to_Uturn_order_entry');
});

Scenario('Test item No.27 Cancel order', async ({ I, stockMarginPaymentOrder }) => {
    const cancelOrderButtonSelector = '//*[@data-testid="marginPaymentConfirm_cancelButton_id"]';
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    await I.clickFixed(cancelOrderButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginPaymentOrder.compareUrl('/mobile/search');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Test_item_No.27_Tap_Cancel_order_to_transition_to_general_search_page',
    );
});

Scenario('Test item No.28 Caution', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
    const cautionSelector = '//*[@data-testid="marginPaymentConfirm_caution_id"]//div[1]';
    await I.clickFixed(cautionSelector);
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Test_item_No.28_Opening_the_caution_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm('Test_item_No.28_Closing_the_caution_accordion');
});

Scenario('Test item No.30 Trailing Stop Orders', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderInput();
    const autoTradingTab = '//*[@data-testid="marginPaymentInput_executionMethod_id"]//button[3]';
    const methodDropdown = '//button[p[contains(text(), "逆指値")]]';
    const trailingStopOrderMethod = '//p[contains(text(), "トレーリングストップ")]';
    const quantityInput = '//input[@placeholder="数量を入力"]';
    const trailingWidth = '//input[@placeholder="トレール幅を入力"]';
    await I.scrollToElement(autoTradingTab);
    await I.clickFixed(autoTradingTab);
    await I.waitFor();
    await I.clickFixed(methodDropdown);
    await I.waitFor('shortWait');
    await I.clickFixed(trailingStopOrderMethod);
    I.fillField(quantityInput, stockMarginPaymentOrder.inputValues.quantity);
    I.fillField(trailingWidth, stockMarginPaymentOrder.inputValues.trailWith);
    await I.scrollToElement(stockMarginPaymentOrder.locator.marginPaymentOrderConfirmButton);
    await I.clickFixed(stockMarginPaymentOrder.locator.marginPaymentOrderConfirmButton);
    await I.waitFor('mediumWait');
    const trailingStopAccordionSelector = '//*[@data-testid="marginPaymentConfirm_trailing_id"]//div[1]';
    await I.clickFixed(trailingStopAccordionSelector);
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Test_item_No.30_Opening_the_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(trailingStopAccordionSelector);
    await I.waitFor();
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm('Test_item_No.30_Closing_the_accordion');
});

Scenario('Test item No.31 ± Limit orders', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderInput();
    const autoTradingTab = '//*[@data-testid="marginPaymentInput_executionMethod_id"]//button[3]';
    const methodDropdown = '//button[p[contains(text(), "逆指値")]]';
    const adjustLimitOrderMethod = '//p[contains(text(), "±指値")]';
    const quantityInput = '//input[@placeholder="数量を入力"]';
    const conditionPrice = '//input[@placeholder="条件価格を入力"]';
    await I.scrollToElement(autoTradingTab);
    await I.clickFixed(autoTradingTab);
    await I.waitFor();
    await I.clickFixed(methodDropdown);
    await I.waitFor('shortWait');
    await I.clickFixed(adjustLimitOrderMethod);
    I.fillField(quantityInput, stockMarginPaymentOrder.inputValues.quantity);
    I.fillField(conditionPrice, '0.1');
    await I.scrollToElement(stockMarginPaymentOrder.locator.marginPaymentOrderConfirmButton);
    await I.clickFixed(stockMarginPaymentOrder.locator.marginPaymentOrderConfirmButton);
    await I.waitFor('mediumWait');
    const limitAccordionSelector = '//*[@data-testid="marginPaymentConfirm_limit_id"]//div[1]';
    await I.clickFixed(limitAccordionSelector);
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm(
        'Test_item_No.31_Opening_the_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(limitAccordionSelector);
    await I.waitFor();
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderConfirm('Test_item_No.31_Closing_the_accordion');
});
