import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';

Feature('InvestmentProducts - DomesticStockMarginTradingPaymentOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginPaymentOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user33 });
    await stockMarginPaymentOrder.goToMarginPaymentOrderInput();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading Payment Order input', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Display_Domestic_Stock_Margin_Trading_Payment_Order_input',
    );
});

Scenario('Test item No.2 Trade Restrictions and Trade Caution Information', async ({ I, stockMarginPaymentOrder }) => {
    const closeButtonSelector = '//*[@data-testid="common_rightSlide_close_id"]';
    await I.clickFixed(stockMarginPaymentOrder.locator.marginPaymentCautionInfo);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    I.seeElement(closeButtonSelector);
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.2_Tap_to_display_the_Trade_Caution_Information_modal',
    );
});

Scenario('Test item No.7 Order deadline', async ({ I, stockMarginPaymentOrder }) => {
    const orderDeadlineSelector = '//*[@data-testid="marginPaymentInput_orderPeriod_id"]';
    const secondItemSelector = `${orderDeadlineSelector}//label[2]`;
    const secondItemTextSelector = `${secondItemSelector}//p`;
    const thirdItemSelector = `${orderDeadlineSelector}//label[3]`;
    const thirdItemTextSelector = `${thirdItemSelector}//p`;
    const modalSelector = '#bottom-sheet-container';
    await I.clickFixed(secondItemSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(secondItemTextSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.7_Tap_the_radio_button_to_activate_it_and_update_the_date_label',
    );
    await I.clickFixed(thirdItemSelector);
    await I.waitFor('mediumWait');
    I.assertEqual(
        await I.grabCssPropertyFrom(thirdItemTextSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    I.seeElement(modalSelector);
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.7_Tap_Specify_period_to_Display_the_date_picker_for_specifying_the_order_deadline',
    );
});

Scenario('Test item No.10 Execution method - Select execution method', async ({ I, stockMarginPaymentOrder }) => {
    const executionMethodSelector = '//*[@data-testid="marginPaymentInput_executionMethod_id"]';
    const limitOrderSelector = `${executionMethodSelector}//button[1]`;
    const marketOrderSelector = `${executionMethodSelector}//button[2]`;
    const automatedTradingSelector = `${executionMethodSelector}//button[3]`;
    await I.swipeDirection('up');
    await I.waitFor('shortWait');
    await I.clickFixed(marketOrderSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(marketOrderSelector, 'background-color'),
        'rgba(239, 239, 239, 1)',
        'Background color is not equal',
    );
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.10_Select_the_market_order_element',
    );
    await I.clickFixed(limitOrderSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(limitOrderSelector, 'background-color'),
        'rgba(239, 239, 239, 1)',
        'Background color is not equal',
    );
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.10_Select_the_limit_order_element',
    );
    await I.clickFixed(automatedTradingSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(automatedTradingSelector, 'background-color'),
        'rgba(239, 239, 239, 1)',
        'Background color is not equal',
    );
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.10_Select_the_automated_trading_element',
    );
});

Scenario('Test item No.11 Execution method', async ({ I, stockMarginPaymentOrder }) => {
    const executionMethodSelector = '//*[@data-testid="marginPaymentInput_executionMethod_id"]';
    const limitOrderSelector = `${executionMethodSelector}//button[1]`;
    await I.swipeDirection('up', 0.5);
    await I.waitFor('shortWait');
    I.assertEqual(
        await I.grabCssPropertyFrom(limitOrderSelector, 'background-color'),
        'rgba(239, 239, 239, 1)',
        'Background color is not equal',
    );
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.11_See_Common_UI_Order_Entry');
});

Scenario('Test item No.16 Lump sum/details', async ({ I, stockMarginPaymentOrder }) => {
    const batchDetailSelector = '//*[@data-testid="marginPaymentInput_batchDetail_id"]';
    const lumpSumSelector = `${batchDetailSelector}//label[1]`;
    const detailsSelector = `${batchDetailSelector}//label[2]`;
    const detailsContainerSelector = '//*[@data-testid="marginPaymentInput_specifyDetails_id"]';
    const lumpSumTextSelector = `${lumpSumSelector}//p`;
    await I.clickFixed(lumpSumSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(lumpSumTextSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.16_Select_lump_sum');
    await I.clickFixed(detailsSelector);
    await I.waitFor();
    I.see('明細指定', 'body');
    I.seeElement(detailsContainerSelector);
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.16_Select_details');
});

Scenario('Test item No.17 Selection', async ({ I, stockMarginPaymentOrder }) => {
    const batchDetailSelector = '//*[@data-testid="marginPaymentInput_batchDetail_id"]';
    const detailsSelector = `${batchDetailSelector}//label[2]`;
    const detailsContainerSelector = '//*[@data-testid="marginPaymentInput_specifyDetails_id"]';
    const detailsQuantitySelector = `${detailsContainerSelector}//input[contains(@placeholder, "数量を入力")]`;
    const detailsConfirmButtonSelector = '//*[@data-testid="marginPaymentInput_confirm_id"]';
    const selectionItemSelector = '//*[@data-testid="marginPaymentInput_select_id"]';
    await I.clickFixed(detailsSelector);
    await I.waitFor();
    I.waitForElement(detailsContainerSelector, 2);
    I.fillField(detailsQuantitySelector, '100');
    I.blur(detailsQuantitySelector);
    await I.clickFixed(detailsConfirmButtonSelector);
    await I.waitFor();
    I.waitForElement(selectionItemSelector, 2);
    await I.clickFixed(selectionItemSelector);
    await I.waitFor();
    I.see('明細指定', 'body');
    I.seeElement(detailsContainerSelector);
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.17_Display_position_selection_modal');
});

Scenario('Test item No.18 Repayment order', async ({ I, stockMarginPaymentOrder }) => {
    const repaymentOrderSelector = '//*[@data-testid="marginPaymentInput_repaymentOrder_id"]';
    await I.swipeDirection('up', 0.8);
    await I.clickFixed(repaymentOrderSelector);
    await I.waitFor('shortWait');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.18_Show_drop_down_menu');
});

Scenario('Test item No.12 Go to the order confirmation screen', async ({ I, stockMarginPaymentOrder }) => {
    const executionMethodSelector = '//*[@data-testid="marginPaymentInput_executionMethodContent_id"]';
    const quantitySelector = `${executionMethodSelector}//input[contains(@placeholder, "数量を入力")]`;
    const priceSelector = `${executionMethodSelector}//input[contains(@placeholder, "価格を入力")]`;
    const orderConfirmButtonSelector = '//*[@data-testid="marginPaymentInput_orderConfirmButton_id"]';
    const specialConditionWrapperSelector = '//div[p[contains(text(), "特殊条件")]]';
    const specialConditionSelector = `${specialConditionWrapperSelector}/button`;
    const relayConditionSelector = `${specialConditionWrapperSelector}//button[contains(@value, "SPECIAL_CONDITION_RELAY")]`;
    I.fillField(quantitySelector, '100');
    I.fillField(priceSelector, '1,585');
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    I.see('信用返済', 'body');
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/confirm');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.12_Transition_to_order_confirm');
    await commonUi.header.clickBack();
    await I.waitFor();
    await I.swipeDirection('up', 0.3);
    await I.clickFixed(specialConditionSelector);
    await I.clickFixed(relayConditionSelector);
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor();
    I.see('リレー選択信用取引', 'body');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.12_Display_relay_selection_modal');
});

Scenario('Test item No.23 Select the trading market', async ({ I, stockMarginPaymentOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user82 });
    await stockMarginPaymentOrder.goToMarginPaymentOrderInput({
        accountType: "ACCOUNT_TYPE_TOKUTEI",
        symbol: "2801",
        exchange: "EXCHANGE_TSE_MS",
        marginTradeType: "MARGIN_TRADE_TYPE_GENERAL",
        buySell: "BUY_SELL_BUY"
    });
    const positionMarketSelection = '//*[@data-testid="marginPaymentInput_positionMarketSelection_id"]';
    const tokyoOption = `${positionMarketSelection}//label[p[contains(text(), "東京")]]`;
    await I.scrollToElement(positionMarketSelection);
    await I.clickFixed(tokyoOption);
    await I.waitFor();
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.23_Reinitialize_the_UI_and_re_execute_the_display_process');
});

Scenario('Test item No.20 Detail specification', async ({ I, stockMarginPaymentOrder }) => {
    const detailSecificationItemSelector = '//*[@data-testid="marginPaymentInput_batchDetail_id"]//label[2]';
    const specifyDetailsSelector = '//*[@data-testid="marginPaymentInput_specifyDetails_id"]';
    const quantitySelector = `${specifyDetailsSelector}//div[input[contains(@placeholder, "数量を入力")]]`;
    const minusQuantityButtonSelector = `${quantitySelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusQuantityButtonSelector = `${quantitySelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.clickFixed(detailSecificationItemSelector);
    await I.waitFor();
    for (let i = 0; i < 3; i++) {
        await I.clickFixed(plusQuantityButtonSelector);
    }
    await I.clickFixed(minusQuantityButtonSelector);
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.20_Numeric_stepper_reference');
});

Scenario('Test item No.21 Cancel Detail specification', async ({ I, stockMarginPaymentOrder }) => {
    const detailSecificationItemSelector = '//*[@data-testid="marginPaymentInput_batchDetail_id"]//label[2]';
    const specifyDetailsSelector = '//*[@data-testid="marginPaymentInput_specifyDetails_id"]';
    const cancelSelector = '//*[@data-testid="marginPaymentInput_cancel_id"]';
    await I.clickFixed(detailSecificationItemSelector);
    await I.waitFor();
    I.waitForElement(specifyDetailsSelector, 2);
    await I.clickFixed(cancelSelector);
    await I.waitFor();
    I.see('信用返済', 'body');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput('Test_item_No.21_Close_modal_Detail_specification');
});

Scenario('Test item No.22 Confirm Detail specification', async ({ I, stockMarginPaymentOrder }) => {
    const detailSecificationItemSelector = '//*[@data-testid="marginPaymentInput_batchDetail_id"]//label[2]';
    const specifyDetailsSelector = '//*[@data-testid="marginPaymentInput_specifyDetails_id"]';
    const confirmSelector = '//*[@data-testid="marginPaymentInput_confirm_id"]';
    const quantitySelector = `${specifyDetailsSelector}//input[contains(@placeholder, "数量を入力")]`;
    await I.clickFixed(detailSecificationItemSelector);
    await I.waitFor();
    I.waitForElement(specifyDetailsSelector, 2);
    I.fillField(quantitySelector, '100');
    I.blur(quantitySelector);
    await I.clickFixed(confirmSelector);
    await I.waitFor();
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderInput(
        'Test_item_No.22_The_contents_specified_in_the_details_specification_are_reflected_on_the_main_screen',
    );
});
