import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import hamburgerMenu from '../pages/hamburgerMenu';
import search from '../pages/search/index';

Feature('Symbol_ProductSearch - USStockSearchTOP');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user19 });
    await hamburgerMenu.goToMenu();
    await hamburgerMenu.clickItem(hamburgerMenu.locators.usStock);
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('test usstock search top page', async ({ I }) => {
    await commonUi.header.verifyTitle('米国株');
    await I.saveScreenshot('488.usstock_search_top_page.png');
});

// 1.キーワード入力
Scenario('test usstock search top page [1.キーワード入力]', async ({ I }) => {
    const inputLocator = search.usStockTop.locators.input;

    await search.common.search(inputLocator, 'ケロッグ');
    await I.saveScreenshot('488.usstock_search_top_page_1_search_suggestion.png');
});

// 2.テーマ
Scenario('test usstock search top page [2.テーマ]', async ({ I }) => {
    await search.usStockTop.theme();
    await I.saveScreenshot('488.usstock_search_top_page_2_click_theme.png');
});

// 3.ランキング
Scenario('test usstock search top page [3.ランキング]', async ({ I }) => {
    await search.usStockTop.ranking();
    await I.saveScreenshot('488.usstock_search_top_page_3_click_ranking.png');
});

// 4.米国株検索
Scenario('test usstock search top page [4.米国株検索]', async ({ I }) => {
    await search.usStockTop.usStockSearch();
    await I.saveScreenshot('488.usstock_search_top_page_4_click_usstock_search.png');
});

// 5.残高照会
Scenario('test usstock search top page [5.残高照会]', async ({ I }) => {
    await search.usStockTop.positionInquiry();
    await I.saveScreenshot('488.usstock_search_top_page_5_click_position_inquiry.png');
});

// 6.注文照会
Scenario('test usstock search top page [6.注文照会]', async ({ I }) => {
    await search.usStockTop.orderStatus();
    await I.saveScreenshot('488.usstock_search_top_page_6_click_order_status.png');
});

// 7.取引履歴
Scenario('test usstock search top page [7.取引履歴]', async ({ I }) => {
    await search.usStockTop.tradingHistory();
    await I.saveScreenshot('488.usstock_search_top_page_7_click_trading_history.png');
});

// 8.買付出金可能額
Scenario('test usstock search top page [8.買付出金可能額]', async ({ I }) => {
    await search.usStockTop.availableAmount();
    await I.saveScreenshot('488.usstock_search_top_page_8_click_available_amount.png');
});

// 9.お預り金振替
Scenario('test usstock search top page [9.お預り金振替]', async ({ I }) => {
    await search.usStockTop.depositTransfer();
    await I.saveScreenshot('488.usstock_search_top_page_9_click_deposit_transfer.png');
});

// 10.外貨お預り金振替
Scenario('test usstock search top page [10.外貨お預り金振替]', async ({ I }) => {
    await search.usStockTop.foreignCurrencyDepositTransfer();
    await I.saveScreenshot('488.usstock_search_top_page_10_click_foreign_currency_deposit_transfer.png');
});

// 11.米国株式決済方法設定
Scenario('test usstock search top page [11.米国株式決済方法設定]', async ({ I }) => {
    await search.usStockTop.usStockPaymentMethodSetting();
    await I.saveScreenshot('488.usstock_search_top_page_11_click_us_stock_payment_method_setting.png');
});

// 12.カブボードフラッシュ
Scenario('test usstock search top page [12.カブボードフラッシュ]', async ({ I }) => {
    await search.usStockTop.kabuBoardFlash();
    await I.saveScreenshot('488.usstock_search_top_page_12_click_kabu_board_flash.png');
});

// 13.手数料
Scenario('test usstock search top page [13.手数料]', async ({ I }) => {
    await search.usStockTop.commission();
    await I.saveScreenshot('488.usstock_search_top_page_13_click_commission.png');
});

// 14.取引ルール
Scenario('test usstock search top page [14.取引ルール]', async ({ I }) => {
    await search.usStockTop.tradingRules();
    await I.saveScreenshot('488.usstock_search_top_page_14_click_trading_rules.png');
});

// 15.キャンセル
Scenario('test usstock search top page [15.キャンセル]', async ({ I }) => {
    const inputLocator = search.usStockTop.locators.input;

    await search.common.search(inputLocator, 'test');
    await I.saveScreenshot('488.usstock_search_top_page_15_search_suggestion.png');
    await search.suggestion.cancel();
    await I.saveScreenshot('488.usstock_search_top_page_15_search_cancel.png');
});

// 17.検索候補(米国株式)アイテム
Scenario('test usstock search top page [17.検索候補(米国株式)アイテム]', async ({ I }) => {
    const keyword = 'ケロッグ';
    const inputLocator = search.usStockTop.locators.input;
    await search.common.search(inputLocator, keyword);
    await I.saveScreenshot('488.usstock_search_top_page_17_search_suggestion.png');
    I.click(locate('p').withText(keyword));
    I.waitInUrl('/mobile/info/usstock/summary', 5);
    await I.saveScreenshot('488.usstock_search_top_page_17_display_summary.png');
});
