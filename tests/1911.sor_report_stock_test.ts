Feature('SORReport - Stock');
import { COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user67 });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('go to SOR Report Stock page', async ({ I }) => {
    I.amOnPage(PAGE_URL.sorReportStock);;
    I.see(
        `SOR価格改善
レポート`,
        '//*[@data-testid="common_header_title_id"]',
    );
});

Scenario('Item 1: Future', async ({ I }) => {
    // Click on the Future tab
    await I.clickFixed('//*[@data-testid="sorReportStock_future_id"]');
    await I.waitFor();
    // Check if the URL contains "/sor-report/future"
    I.seeInCurrentUrl('/mobile/sor-report/future');

    // Click on the back button to return
    await I.clickFixed('//*[@data-testid="sorReportFuture_stock_id"]');
    await I.waitFor();
    // Verify we're back at the stock page
    I.seeInCurrentUrl('/mobile/sor-report/stock');
});

Scenario('Item 5: Display Period', async ({ I }) => {
    // Define the base locator for display period labels
    let displayPeriodLabelsLocator = '//*[@data-testid="sorReportStock_displayPeriod_id"]';

    // Wait for display period labels to be visible
    I.waitForElement(displayPeriodLabelsLocator);
    displayPeriodLabelsLocator = `${displayPeriodLabelsLocator}/label`;
    // Get the count of labels
    const count = await I.grabNumberOfVisibleElements(displayPeriodLabelsLocator);
    I.say(`Found ${count} display period labels`);

    // Test each label except the last one
    for (let i = 1; i < count; i++) {
        const labelLocator = `${displayPeriodLabelsLocator}[${i}]/p`;

        // Click on the label
        await I.clickFixed(labelLocator);
        await I.waitFor(); // Wait for visual change

        // Check opacity is 0.7
        const color = await I.grabCssPropertyFrom(labelLocator, 'color');
        I.assertEqual(color, 'rgba(255, 86, 0, 1)', `Label ${i} should have color rgba(255, 86, 0, 1) when selected`);

        await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_5_Display_Period_Label_${i}.png`);
    }

    // Test the last label which should open a bottom sheet
    const lastLabelLocator = `(${displayPeriodLabelsLocator})[${count}]`;

    // Click on the last label
    await I.clickFixed(lastLabelLocator);
    await I.waitFor(); // Wait for bottom sheet to appear

    // Check if bottom sheet container exists
    const bottomSheetContentLocator = '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[1]/p';
    I.seeElement(bottomSheetContentLocator);

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_5_Display_Period_Bottom_Sheet.png`);

    // Click cancel button
    await I.clickFixed('//button[@aria-label="cancel-btn"]');

    // Verify bottom sheet is closed
    I.dontSeeElement(bottomSheetContentLocator);

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_5_Display_Period_After_Cancel.png`);
});

Scenario('Item 22: Page No', async ({ I }) => {
    // Define the page number locator
    const pageNumberLocator = '//*[@data-testid="sorReportStock_pageNumber_id"]';

    // Check if the page number locator is displayed
    I.waitForElement(pageNumberLocator);
    I.seeElement(pageNumberLocator);
    I.say('Page number buttons are displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_22_Page_No_Before_Click.png`);

    // Define the second button locator
    const secondButtonLocator = `${pageNumberLocator}/button[2]`;

    // Click on the second button
    await I.clickFixed(secondButtonLocator);
    await I.waitFor(); // Wait for visual change

    // Check the color of the second button
    const buttonColor = await I.grabCssPropertyFrom(secondButtonLocator, 'color');
    I.say(`Button 2 color: ${buttonColor}`);
    I.assertEqual(buttonColor, 'rgba(255, 86, 0, 1)', 'Button 2 should have color rgba(255, 86, 0, 1) when selected');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_22_Page_No_After_Click.png`);
});

Scenario('Item 21: Previous Page', async ({ I }) => {
    // Define the locators
    const prevPageLocator = '//*[@data-testid="sorReportStock_prevPage_id"]';
    const pageNumbersLocator = '//*[@data-testid="sorReportStock_pageNumber_id"]';
    const firstPageButtonLocator = `${pageNumbersLocator}/button[1]`;

    // Ensure we're on page 2 first by clicking the second page button
    const secondPageButtonLocator = `${pageNumbersLocator}/button[2]`;
    await I.clickFixed(secondPageButtonLocator);
    await I.waitFor(); // Wait for visual change

    // Take screenshot before clicking previous page button
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_21_Previous_Page_Before_Click.png`);

    // Click on the previous page button
    await I.clickFixed(prevPageLocator);
    await I.waitFor(); // Wait for visual change

    // Check the color of the first page button
    const buttonColor = await I.grabCssPropertyFrom(firstPageButtonLocator, 'color');
    I.say(`First page button color: ${buttonColor}`);
    I.assertEqual(
        buttonColor,
        'rgba(255, 86, 0, 1)',
        'First page button should have color rgba(255, 86, 0, 1) when selected',
    );

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_21_Previous_Page_After_Click.png`);
});

Scenario('Item 23: Next Page', async ({ I }) => {
    // Define the locators
    const nextPageLocator = '//*[@data-testid="sorReportStock_nextPage_id"]';
    const pageNumbersLocator = '//*[@data-testid="sorReportStock_pageNumber_id"]';
    const secondPageButtonLocator = `${pageNumbersLocator}/button[2]`;

    // Ensure we're on page 1 first by clicking the first page button
    const firstPageButtonLocator = `${pageNumbersLocator}/button[1]`;
    await I.clickFixed(firstPageButtonLocator);
    await I.waitFor(); // Wait for visual change

    // Take screenshot before clicking next page button
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_22_Next_Page_Before_Click.png`);

    // Click on the next page button
    await I.clickFixed(nextPageLocator);
    await I.waitFor(); // Wait for visual change

    // Check the color of the second page button
    const buttonColor = await I.grabCssPropertyFrom(secondPageButtonLocator, 'color');
    I.say(`Second page button color: ${buttonColor}`);
    I.assertEqual(
        buttonColor,
        'rgba(255, 86, 0, 1)',
        'Second page button should have color rgba(255, 86, 0, 1) when selected',
    );

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_22_Next_Page_After_Click.png`);
});

Scenario('Item 25: Previous Month', async ({ I }) => {
    // Define the locators
    const displayPeriodLocator = '//*[@data-testid="sorReportStock_displayPeriod_id"]';
    const fourthLabelLocator = `${displayPeriodLocator}/label[4]`;
    const prevMonthLocator = '//*[@data-testid="sorReportStock_prevMonth_id"]';
    const currentMonthHeaderLocator =
        '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[2]/div[1]/div[2]/p';

    // Click on the fourth label to open the date picker modal
    await I.clickFixed(fourthLabelLocator);
    await I.waitFor(); // Wait for modal to appear

    // Check if the Previous Month and Next Month buttons are displayed
    I.seeElement(prevMonthLocator);
    I.say('Previous Month buttons are displayed');

    // Get the current date and format it as {year}年{month}月
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
    const currentMonthText = `${currentYear}年${currentMonth}月`;

    // Verify the current month is displayed correctly
    const displayedMonthText = await I.grabTextFrom(currentMonthHeaderLocator);
    I.assertEqual(displayedMonthText, currentMonthText, 'Current month is not displayed correctly');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_25_Previous_Month_Before_Click.png`);

    // Click on the Previous Month button
    await I.clickFixed(prevMonthLocator);
    await I.waitFor(); // Wait for month to change

    // Calculate the expected previous month
    let prevMonth = currentMonth - 1;
    let prevYear = currentYear;
    if (prevMonth === 0) {
        prevMonth = 12;
        prevYear -= 1;
    }
    const expectedPrevMonthText = `${prevYear}年${prevMonth}月`;

    // Verify the previous month is displayed correctly
    const displayedPrevMonthText = await I.grabTextFrom(currentMonthHeaderLocator);
    I.assertEqual(displayedPrevMonthText, expectedPrevMonthText, 'Previous month is not displayed correctly');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_25_Previous_Month_After_Click.png`);
});

Scenario('Item 27: Next Month', async ({ I }) => {
    // Define the locators
    const nextMonthLocator = '//*[@data-testid="sorReportStock_nextMonth_id"]';
    const currentMonthHeaderLocator =
        '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[2]/div[1]/div[2]/p';

    // Check if the Next Month button is displayed
    I.seeElement(nextMonthLocator);
    I.say('Next Month button is displayed');

    // Get the current date and format it as {year}年{month}月
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
    const currentMonthText = `${currentYear}年${currentMonth}月`;

    // Click on the Next Month button
    await I.clickFixed(nextMonthLocator);
    await I.waitFor(); // Wait for month to change

    // Verify the next month is displayed correctly
    const displayedNextMonthText = await I.grabTextFrom(currentMonthHeaderLocator);
    I.assertEqual(displayedNextMonthText, currentMonthText, 'Next month is not displayed correctly');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_27_Next_Month_After_Click.png`);
});

Scenario('Item 28: Calendar', async ({ I }) => {
    // Define the locators
    const prevMonthLocator = '//*[@data-testid="sorReportStock_prevMonth_id"]';
    const calendarLocator = '//*[@data-testid="sorReportStock_calendar_id"]';

    // Click on the Previous Month button to navigate to previous month
    await I.clickFixed(prevMonthLocator);
    await I.waitFor(); // Wait for month to change

    // Check if the calendar is displayed
    I.seeElement(calendarLocator);
    I.say('Calendar is displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_28_Calendar_Before_Click.png`);

    // Click on the first day of the second week (index 1, first day index 0)
    const secondWeekFirstDaySelector = `.DayPicker-Body .DayPicker-Week:nth-child(3) .DayPicker-Day:first-child`;
    await I.clickFixed(secondWeekFirstDaySelector);
    await I.waitFor(); // Wait for visual change
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_28_Calendar_After_First_Click.png`);
    const firstDayColor = await I.grabCssPropertyFrom(`${secondWeekFirstDaySelector} div`, 'color');
    I.assertEqual(
        firstDayColor,
        'rgba(255, 86, 0, 1)',
        'First day of the second week should have color rgba(255, 86, 0, 1) when selected',
    );
    // Click on the last day of the last week
    const lastWeekLastDaySelector = `.DayPicker-Body .DayPicker-Week:nth-child(3) .DayPicker-Day:last-child`;
    await I.clickFixed(lastWeekLastDaySelector);
    await I.waitFor(); // Wait for visual change
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_28_Calendar_After_Last_Click.png`);
    const lastDayColor = await I.grabCssPropertyFrom(`${lastWeekLastDaySelector} div div`, 'background-color');
    I.assertEqual(
        lastDayColor,
        'rgba(255, 86, 0, 1)',
        'Last day of the last week should have color rgba(255, 86, 0, 1) when selected',
    );
});

Scenario('Item 29: Reset', async ({ I }) => {
    // Define the locators
    const prevMonthLocator = '//*[@data-testid="sorReportStock_prevMonth_id"]';
    const resetLocator = '//*[@data-testid="sorReportStock_reset_id"]';
    const currentMonthHeaderLocator =
        '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[2]/div[1]/div[2]/p';

    // Change the month by clicking on the Previous Month button
    await I.clickFixed(prevMonthLocator);
    await I.waitFor(); // Wait for month to change

    // Check if the reset button is displayed
    I.seeElement(resetLocator);
    I.say('Reset button is displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_29_Reset_Before_Click.png`);

    // Get the current date as reference for reset
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
    const expectedCurrentMonthText = `${currentYear}年${currentMonth}月`;

    // Click on the Reset button
    await I.clickFixed(resetLocator);
    await I.waitFor(); // Wait for reset to apply

    // Verify the month has been reset to current month
    const displayedMonthText = await I.grabTextFrom(currentMonthHeaderLocator);
    I.assertEqual(displayedMonthText, expectedCurrentMonthText, 'Month should be reset to current month');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_29_Reset_After_Click.png`);
});

Scenario('Item 30: Confirm', async ({ I }) => {
    // Define the locators
    const confirmLocator = '//*[@data-testid="sorReportStock_confirm_id"]';
    const bottomSheetLocator = '//*[@id="bottom-sheet-container"]';

    // Check if the confirm button is displayed
    I.seeElement(confirmLocator);
    I.say('Confirm button is displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_30_Confirm_Before_Click.png`);

    // Verify bottom sheet is visible before confirming
    I.seeElement(bottomSheetLocator);

    // Click on the Confirm button
    await I.clickFixed(confirmLocator);
    await I.waitFor(); // Wait for action to complete

    // Verify the bottom sheet is closed after confirmation
    I.dontSeeElement(bottomSheetLocator);

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportStock}_Item_30_Confirm_After_Click.png`);
});
