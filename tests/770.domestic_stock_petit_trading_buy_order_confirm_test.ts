import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockPetitTradingBuyOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs, stockPetitOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user29 });
    await stockPetitOrder.goToPetitBuyOrderConfirm();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Petit Trading Buy Order confirm', async ({ I, stockPetitOrder }) => {
    await stockPetitOrder.compareUrl('/mobile/trade/petit/buy/confirm');
    stockPetitOrder.takeScreenshot.petitBuyOrderConfirm('Display_Domestic_Stock_Petit_Trading_Buy_Order_confirm');
});

Scenario('Test item No.19+20 Fill password + check for omitted password', async ({ I, stockPetitOrder }) => {
    const passwordSelector = '//*[@data-testid="tradePetitBuyConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitBuyConfirm_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitBuyConfirm_orderConfirm_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(orderConfirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitBuyOrderConfirm(
        'Test_item_No.19+20_Fill_password_and_tap_check_for_omitted_password_to_see_order_confirm_button_state',
    );
});

Scenario('Test item No.26 Check for entered password', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user79 });
    await stockPetitOrder.goToPetitBuyOrderConfirm();
    const inputPasswordCheckSelector = '//*[@data-testid="tradePetitBuyConfirm_checkInputPassword_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitBuyConfirm_orderConfirm_id"]';
    await I.clickFixed(inputPasswordCheckSelector);
    await I.scrollToElement(orderConfirmButton);
    I.assertEqual(
        await I.grabCssPropertyFrom(orderConfirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitBuyOrderConfirm('Test_item_No.26_Check_state_of_Order_Confirmation');
});

Scenario('Test item No.21-3 Order Confirmation', async ({ I, stockPetitOrder }) => {
    const passwordSelector = '//*[@data-testid="tradePetitBuyConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitBuyConfirm_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitBuyConfirm_orderConfirm_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor('mediumWait');
    await stockPetitOrder.compareUrl('/mobile/trade/petit/buy/complete');
    stockPetitOrder.takeScreenshot.petitBuyOrderConfirm(
        'Test_item_No.21-3_Tap_Order_Confirmation_to_transition_to_complete',
    );
});

Scenario('Test item No.22 Order correction', async ({ I, stockPetitOrder }) => {
    const buyQuantitySelector = '//*[@data-testid="tradePetitBuy_quantity_id"]';
    const orderModifySelector = '//*[@data-testid="tradePetitBuyConfirm_orderModify_id"]';
    await I.clickFixed(orderModifySelector);
    await I.waitFor('mediumWait');
    I.waitForElement(buyQuantitySelector, 2);
    await stockPetitOrder.compareUrl('/mobile/trade/petit/buy');
    stockPetitOrder.takeScreenshot.petitBuyOrderConfirm(
        'Test_item_No.22_Tap_Order_correction_to_transition_to_domestic_stock_trading_buy_order_entry',
    );
});

Scenario('Test item No.23 Cancel order', async ({ I, stockPetitOrder }) => {
    const orderCancelSelector = '//*[@data-testid="tradePetitBuyConfirm_orderCancel_id"]';
    const searchPageSelector = '#searchPage';
    await I.clickFixed(orderCancelSelector);
    await I.waitFor('mediumWait');
    I.waitForElement(searchPageSelector, 2);
    I.see('銘柄検索', 'body');
    await stockPetitOrder.compareUrl('/mobile/search');
    stockPetitOrder.takeScreenshot.petitBuyOrderConfirm(
        'Test_item_No.23_Tap_Cancel_order_to_transition_to_search_stock_page',
    );
});
