import { COOKIE_KEY, USER_ID } from "../const/constant";
import pdfView from "../pages/common-ui/pdfView";
import common from "../pages/search/common";

Feature('Reserve - CustomerReservePetitReserveOrderConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user49 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Savings Application Confirmation', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderConfirmPage();
});
Scenario('Test Item 14: Check Password Tap Opens a text input form with alphanumeric characters', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user48 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveOrderConfirmPage();
    await I.waitFor();
    const applyButton = '//button[text()="このまま申込む"]';
    await I.waitForElement(applyButton, 5);
    
    await I.clickFixed(applyButton);
    await I.waitFor();
    const passwordInput = '//*[@data-testid="reserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.scrollAndFill(passwordInput, '123@abc');
    await I.waitFor('shortWait');
    // click img alt="eye" to see password
    const eyeIcon = '//img[@alt="eye"]';
    await I.waitForElement(eyeIcon, 1);
    I.clickFixed(eyeIcon);
    await I.waitFor('shortWait');
    I.saveScreenshot('1384.Test_item_No.14_Password_Tap_Opens_a_text_input_form_with_alphanumeric_characters_and_see_password.png');
});
Scenario('Test Item 15: Check Password omission check: Switch ON/OFF', async ({ I }) => {

    const passwordOmissionCheck = '//*[@data-testid="reserveOrderConfirm_passwordOmissionCheck_id"]';
    await I.waitForElement(passwordOmissionCheck, 1);
    // ON
    I.clickFixed(passwordOmissionCheck);
    I.saveScreenshot('1384.Test_item_No.15_Password_omission_check_ON.png');
    // OFF
    I.clickFixed(passwordOmissionCheck);
    I.saveScreenshot('1384.Test_item_No.15_Password_omission_check_OFF.png');
});
Scenario('Test Item 17: Check Password input check: Switch between ON and OFF', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user49 });
    await I.waitFor()
    await accumulation.goToReserveOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="reserveOrderConfirm_checkInputPassword_id"]';
    await I.waitForElement(passwordInputCheck, 3);
    // ON
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1384.Test_item_No.17_Password_input_check_ON.png');
    // OFF
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1384.Test_item_No.17_Password_input_check_OFF.png');
});
//TODO: 18.1: Skip bacause cannot check maintenance
Scenario.skip('Test Item 18.2: Check browser back when loading is in progress', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    const passwordInput = '//*[@data-testid="reserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.scrollAndFill(passwordInput, '111111');
    const orderButton = '//*[@data-testid="reserveOrderConfirm_order_id"]';
    await I.waitForElement(orderButton, 3);
    I.scrollAndClick(orderButton);
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    I.saveScreenshot('1384.Test_item_No.18.2_After_browser_back_redirected_to_reserve_plan.png');
});
Scenario('Test Item 18.3: Check Transition to "Savings - Petit Stocks - Savings Application Completion', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user48 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveOrderConfirmPage();
    await I.waitFor();
    const applyButton = '//button[text()="このまま申込む"]';
    await I.waitForElement(applyButton, 5);
    
    await I.clickFixed(applyButton);
    await I.waitFor();
    const passwordInput = '//*[@data-testid="reserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.scrollAndFill(passwordInput, '111111');

    const orderButton = '//*[@data-testid="reserveOrderConfirm_order_id"]';
    await I.waitForElement(orderButton, 3);
    I.scrollAndClick(orderButton);
    I.saveScreenshot('1384.Test_item_No.18.3_Savings_application_completion_screen.png');

});
Scenario('Test Item 22: Check Click Open the following URL in a new tab', async ({ I, accumulation }) => {
    //step 1: Go to Reserve Order Input Page
    await accumulation.goToReserveOrderInputPage();
    const accountTypeLabels = '//*[@data-testid="reserveOrderInput_accountType_id"]';
    await I.waitForElement(accountTypeLabels, 1);
    I.scrollToElement(accountTypeLabels);
    //step 2: Select Account Type
    const identificationLabel = `${accountTypeLabels}//label[1]`;
    await I.waitFor('shortWait');
    I.clickFixed(identificationLabel);
    await I.waitFor('shortWait');
    // get all labels in payment type radio group
    const paymentTypeLabels = '//*[@data-testid="reserveOrderInput_paymentType_id"]//div[@role="radiogroup"]//label[4]';
    await I.waitForElement(paymentTypeLabels, 1);
    I.scrollAndClick(paymentTypeLabels);
    //step 3: Increase amount
    const plusButton = '//p[contains(text(), "毎月の指定金額")]/parent::div//button[@data-testid="groupInputNumber_plus_id"]';
    await I.waitForElement(plusButton, 3);
    I.scrollAndClick(plusButton);
    //Step 4: Check Insider confirmation panel Toggle selection
    const button = '//div[@data-testid="reserveOrderInput_notInsiderTradeConfirm_id"]//button';
    await I.waitForElement(button, 3);
    I.scrollAndClick(button);

    // step 5: click go to confirm page
    const confirmButton = '//*[@data-testid="reserveOrderInput_confirmOrderButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollAndClick(confirmButton);
    //step 6: Click Open the following URL in a new tab
    const hereLink = '//p[@data-testid="reserveOrderConfirm_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 1);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');

});
Scenario('Test Item 23: Check Terms and Conditions for Regular Fractional Share Accumulation Transactions ', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderConfirmPage();
    const petitReserveTradingTerms = '//*[@data-testid="reserveOrderConfirm_petitReserveTradingTerms_id"]';
    await I.waitForElement(petitReserveTradingTerms, 3);
    I.scrollAndClick(petitReserveTradingTerms);
    // Back to app
    await pdfView.closePDFView();
    await I.waitFor('shortWait');
    I.saveScreenshot('1384.Test_item_No.23_Back_to_order_confirmation_screen.png');
});
Scenario('Test Item 24: Check NISA Account Fractional Unit Share Regular Accumulation Transaction Terms and Conditions', async ({ I, accumulation }) => {
    const nisaTradingTermsLink = '//*[@data-testid="reserveOrderConfirm_nisaTradingTermsLink_id"]';
    await I.waitForElement(nisaTradingTermsLink, 3);
    I.scrollAndClick(nisaTradingTermsLink);
    // Back to app
    await pdfView.closePDFView();
    await I.waitFor('shortWait');
    I.saveScreenshot('1384.Test_item_No.24_Back_to_order_confirmation_screen.png');
});
