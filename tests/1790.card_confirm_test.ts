Feature('Settings_Entry - CardConfirmPage');
import { COOKIE_KEY, USER_ID } from '../const/constant';
import SettingCard from '../pages/settingCard';

const locators = SettingCard.locators;

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user63 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Go to card confirm page', async () => {
    await SettingCard.goToPageCardConfirm();
    SettingCard.takeScreenshot('1790_card_confirm_page.png');
});

Scenario('Test item 2 Here Link', async () => {
    await SettingCard.clickExternalUrl(locators.confirmHereLink, locators.kddifsUrl);
    SettingCard.takeScreenshot('1790_Test_Item_2_Here_Link.png');
});

Scenario('Test item 3 Password', () => {
    SettingCard.fillInput(locators.confirmPasswordInput, '111111');
    SettingCard.takeScreenshot('1790_Test_Item_3_Password.png');
});

Scenario('Test item 4 Register', async ({ I }) => {
    await SettingCard.clickItem(locators.confirmRegisterBtn);
    I.seeElement(locators.confirmCompleteMessage);
    SettingCard.takeScreenshot('1790_Test_Item_4_Register.png');
});

Scenario('Test item 5 Registration Completed', async ({ I }) => {
    await SettingCard.clickItem(locators.confirmCompleteMessage);
    I.amOnPage(locators.infoFundSearch);
    SettingCard.takeScreenshot('1790_Test_Item_5_Registration_Completed.png');
});
