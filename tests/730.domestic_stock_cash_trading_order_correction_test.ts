import { COOKIE_KEY, PAGE_URL, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import orderInquiry from '../pages/order-inquiry';
import tradeStock from '../pages/trade-stock';

Feature('InvestmentProducts - DomesticStockCashTradingOrderCorrection');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user27 });

    await I.amOnPage(PAGE_URL.orderInquiryStock);
    await I.waitFor(); // wait for page loaded
    await orderInquiry.stockList.clickItem(0);
    await I.waitFor();
    await orderInquiry.stockDetail.orderCorrection();
    await <PERSON>.waitFor(); // wait for page loaded
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('domestic stock cash trading order correction [レイアウト]', async ({ I }) => {
    I.waitInUrl('/mobile/trade/stock/correction', 5);
    await commonUi.header.verifyTitle('現物訂正');
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_layout.png');
});

// 3.訂正内容 - 削減数量
Scenario('domestic stock cash trading order correction [3.訂正内容 - 削減数量]', async ({ I }) => {
    const replaceContent = locate(tradeStock.correction.locators.replaceContent);
    const input = locate(tradeStock.correction.locators.replaceContentInput);
    const minusButton = locate(tradeStock.correction.locators.replaceContentMinus);
    const plusButton = locate(tradeStock.correction.locators.replaceContentPlus);
    const pulldownButton = locate('button').inside(replaceContent).withText('変更しない');

    // verify input
    I.waitForVisible(input);
    await I.clickFixed(input);
    await I.fillField(input, '200');
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_input.png');

    // verify minus button
    I.waitForVisible(minusButton);
    await I.clickFixed(minusButton);
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_minus_button.png');

    // verify plus button
    I.waitForVisible(plusButton);
    await I.clickFixed(plusButton);
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_plus_button.png');

    // verify pulldown
    I.waitForVisible(pulldownButton);
    await I.scrollToElement(pulldownButton.toXPath());
    await I.clickFixed(pulldownButton);
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_pulldown.png');

    // select limit order
    await I.clickFixed(locate('p').withText('指値').inside(replaceContent));
    await I.waitFor();
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_limit_order.png');

    // trigger price board input
    await I.clickFixed(locate('p').withText('板入力').inside(replaceContent));
    await I.waitFor('mediumWait');
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_trigger_board_order.png');
    await I.click('~Check');
    await I.waitFor();

    // trigger price chart input
    await I.clickFixed(locate('p').withText('チャート入力').inside(replaceContent));
    await I.waitFor('mediumWait');
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_3_trigger_chart_order.png');
});

// 4.訂正を行う
Scenario('domestic stock cash trading order correction [4.訂正を行う]', async ({ I }) => {
    await tradeStock.correction.doReplace();
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_4_do_replace.png');
});

// 23.ご注意
Scenario('domestic stock cash trading order correction [23.ご注意]', async ({ I }) => {
    await tradeStock.correction.verifyCaution();
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_23_caution.png');
});

// 26.リレー先注文番号
Scenario('domestic stock cash trading order correction [26.リレー先注文番号]', async ({ I }) => {
    await tradeStock.correction.verifyRelayTargetOrderID();
    I.saveScreenshot('730.domestic_stock_cash_trading_order_correction_26_relay_target_order_id.png');
});
