import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingNewOrderCompleted');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
    await stockMarginOrder.goToMarginNewOrderCompleted();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading New Order completed', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.compareUrl('/mobile/trade/margin/new/complete');
    stockMarginOrder.takeScreenshot.marginNewOrderCompleted(
        'Display_Domestic_Stock_Margin_Trading_New_Order_completed',
    );
});

Scenario('Test item No.1 Order number', async ({ I, stockMarginOrder }) => {
    const orderNumberSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    const orderInquiryDetailSelector = '#order-inquiry-detail';
    await I.clickFixed(orderNumberSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquiryDetailSelector);
    await stockMarginOrder.compareUrl('/mobile/order-inquiry/margin/detail');
    stockMarginOrder.takeScreenshot.marginNewOrderCompleted(
        'Test_item_No.1_Tap_Order_number_to_transition_to_details_page',
    );
});

Scenario('Test item No.2 Order Inquiry', async ({ I, stockMarginOrder }) => {
    const orderInquiryButtonSelector = '//*[@data-testid="complete_orderStatusButton_id"]';
    const orderInquirySelector = '#order-inquiry';
    await I.clickFixed(orderInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquirySelector);
    await stockMarginOrder.compareUrl('/mobile/order-inquiry/margin');
    stockMarginOrder.takeScreenshot.marginNewOrderCompleted(
        'Test_item_No.2_Tap_Order_Inquiry_to_transition_to_order_list_page',
    );
});

Scenario('Test item No.3 Balance inquiry', async ({ I, stockMarginOrder }) => {
    const balanceInquiryButtonSelector = '//*[@data-testid="complete_positionInquiryButton_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(balanceInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(positionInquirySelector);
    await stockMarginOrder.compareUrl('/mobile/position-inquiry/margin');
    stockMarginOrder.takeScreenshot.marginNewOrderCompleted(
        'Test_item_No.3_Tap_Balance_inquiry_to_transition_to_balance_list_page',
    );
});

Scenario('Test Back to browser', async ({ I, stockMarginOrder }) => {
    await I.performBrowserBack();
    await I.waitFor();
    I.seeElement('#searchPage');
    await stockMarginOrder.compareUrl('/mobile/search');
    stockMarginOrder.takeScreenshot.marginNewOrderCompleted(
        'Test_Back_to_browser_to_transition_to_general_search_page',
    );
});
