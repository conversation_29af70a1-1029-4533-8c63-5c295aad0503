Feature('Settings_Entry - CardCommonRegisterPage');
import { COOKIE_KEY, USER_ID } from '../const/constant';
import SettingCard from '../pages/settingCard';

const locators = SettingCard.locators;

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user64 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Go to card common register page', async () => {
    await SettingCard.goToPageCardCommonRegister();
    await SettingCard.takeScreenshot('1796_card_common_register_page.png');
});

Scenario('Test item 1 Terms', async () => {
    await SettingCard.clickPdfUrl(locators.termRegister);
    await SettingCard.takeScreenshot('1796_Test_Item_1_Terms.png');
});

Scenario('Test item 2 Term HTML', async ({ I }) => {
    await SettingCard.takeScreenshot('1796_Test_Item_2_Term_HTML_1.png');

    // Swipe up html
    await I.swipeUpFixed(locators.termHTMLRegister);
    await I.waitFor();
    await SettingCard.takeScreenshot('1796_Test_Item_2_Term_HTML_2.png');

    // Swipe down html
    await I.swipeDownFixed(locators.termHTMLRegister);
    await I.waitFor();
    await SettingCard.takeScreenshot('1796_Test_Item_2_Term_HTML_3.png');
});

Scenario('Test item 3 Agreement document', async () => {
    await SettingCard.clickPdfUrl(locators.agreeDocumentRegister);
    await SettingCard.takeScreenshot('1796_Test_Item_3_Agreement_document.png');
});

Scenario('Test item 4 Agreement Document HTML', async ({ I }) => {
    await I.swipeUpFixed(locators.termHTMLRegister);
    await SettingCard.takeScreenshot('1796_Test_Item_4_Agreement_Document_HTML_1.png');

    // Swipe up html
    await I.scrollToElement(locators.agreeDocumentHTMLRegister);
    await I.swipeUpFixed(locators.agreeDocumentHTMLRegister);
    await I.waitFor();
    await SettingCard.takeScreenshot('1796_Test_Item_4_Agreement_Document_HTML_2.png');

    // Swipe down html
    await I.swipeDownFixed(locators.agreeDocumentHTMLRegister);
    await I.waitFor();
    await SettingCard.takeScreenshot('1796_Test_Item_4_Agreement_Document_HTML_3.png');
});

Scenario('Test item 5 Agreement Check', async ({ I }) => {
    await I.scrollToElement(locators.agreeCheckRegister);
    await SettingCard.clickItem(locators.agreeCheckRegister);
    await SettingCard.takeScreenshot('1796_Test_Item_5_Agreement_Check_1.png');
    await SettingCard.clickItem(locators.agreeCheckRegister);
    await SettingCard.takeScreenshot('1796_Test_Item_5_Agreement_Check_2.png');
});

Scenario('Test item 6 Next', async ({ I }) => {
    await I.scrollToElement(locators.agreeCheckRegister);
    await SettingCard.clickItem(locators.agreeCheckRegister);
    await SettingCard.clickItem(locators.registerNextBtn);
    I.seeElement(locators.cardNumber);
    await SettingCard.takeScreenshot('1796_Test_Item_6_Next.png');
});

Scenario('Test item 7 Card number', async () => {
    await SettingCard.fillInput(locators.cardNumber, '1111111111');
    await SettingCard.takeScreenshot('1796_Test_Item_7_Card_number.png');
});

Scenario('Test item 8 Expiry date MM', async () => {
    await SettingCard.fillInput(locators.expiryDateMM, '12');
    await SettingCard.takeScreenshot('1796_Test_Item_8_Expiry_date_MM.png');
});

Scenario('Test item 9 Expiry date YY', async () => {
    await SettingCard.fillInput(locators.expiryDateYY, '26');
    await SettingCard.takeScreenshot('1796_Test_Item_9_Expiry_date_YY.png');
});

Scenario('Test item 10 Security code', async () => {
    await SettingCard.fillInput(locators.securityCode, '111111');
    await SettingCard.takeScreenshot('1796_Test_Item_10_Security_code.png');
});

Scenario('Test item 11 Password', async () => {
    await SettingCard.fillInput(locators.inputPasswordRegister, '111111');
    await SettingCard.takeScreenshot('1796_Test_Item_11_Password.png');
});

Scenario('Test item 12 Register', async () => {
    await SettingCard.clickItem(locators.registerBtn);
    await SettingCard.takeScreenshot('1796_Test_Item_12_Registerd.png');
});
