import { COMMON_BACK_BUTTON, COOKIE_KEY, USER_ID } from '../const/constant';

Feature('DistributionHistory - Page');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await <PERSON><PERSON>waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user45 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario(
    'Test Item 1: Check UI of distribution history page & jumto dividend history page',
    async ({ I, dividendHistory }) => {
        await dividendHistory.goToDistributionHistoryPage();
        I.seeElement(dividendHistory.locators.dividendTabButton);
        I.clickFixed(dividendHistory.locators.dividendTabButton);
        await <PERSON><PERSON>waitFor('shortWait');
        I.seeInCurrentUrl(dividendHistory.urls.base);
        I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.1_jumto_distribution_history_list.png`);
    },
);

Scenario('Test Item 2: Show this screen view modal', async ({ I, dividendHistory }) => {
    // switch to dividend history page
    I.seeElement(dividendHistory.locators.distributionTabButton);
    I.clickFixed(dividendHistory.locators.distributionTabButton);
    // find the view this screen icon & click it
    const locator = dividendHistory.locators.viewThisScreenOpenIcon('distributionHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.waitForElement(dividendHistory.locators.viewScreenModal);
    I.see('分配金履歴の見方');
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.2_show_this_screen_view_modal.png`);
    I.clickFixed(dividendHistory.locators.viewScreenModalCloseButton);
});

Scenario('Test Item 3: Open screen display settings', async ({ I, dividendHistory }) => {
    await dividendHistory.goToDistributionHistoryPage();
    await dividendHistory.openDisplaySetting('distributionHistoryList');
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.3_open_screen_display_setting.png`);
    dividendHistory.closeDisplaySetting();
});

Scenario('Test Item 8: Jumto distribution detail', async ({ I, dividendHistory }) => {
    I.seeElement(dividendHistory.locators.distributionDetailId);
    I.click(dividendHistory.locators.distributionDetailId);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.8_jumto_distribution_detail.png`);
    I.waitFor('shortWait');
    I.clickFixed(COMMON_BACK_BUTTON);
});

Scenario('Test Item 16: Scroll to the top of the screen', async ({ I, dividendHistory }) => {
    await I.swipeElementDirection('up', dividendHistory.locators.swipeToElement('distribution'));
    await I.waitFor();
    I.seeElement(dividendHistory.locators.scrollToTopButton);
    I.click(dividendHistory.locators.scrollToTopButton);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.16_scroll_to_the_top_of_the_screen.png`);
});

Scenario('Test Item 17: Switch ON/OFF product type', async ({ I, dividendHistory }) => {
    await dividendHistory.openDisplaySetting('distributionHistoryList');
    const productTypes = await dividendHistory.getLabelValues('distributionHistoryList_productType_id');
    await dividendHistory.toggleChecker(productTypes);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.17_switch_on_off_product_type.png`);
});

Scenario('Test Item 18: Switch ON/OFF distribution type', async ({ I, dividendHistory }) => {
    const distributionTypes = await dividendHistory.getLabelValues('distributionHistoryList_distributionType_id');
    await dividendHistory.toggleChecker(distributionTypes);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.18_switch_on_off_distribution_type.png`);
});

Scenario('Test Item 19: toggle show display order drop-down menu', async ({ I, dividendHistory }) => {
    const locator = dividendHistory.locators.displayOrderButton('distributionHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.19_toggle_show_display_order.png`);
    const secondOption = `${locator}//p[2]`;
    I.seeElement(secondOption);
    I.clickFixed(secondOption);
    I.say('Select the second value option');
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.19_select_the_second_value_option.png`);
});

Scenario('Test Item 20: Switch ON/OFF display account filter', async ({ I, dividendHistory }) => {
    const accountFilers = [
        'ACCOUNT_TYPE_TOKUTEI',
        'ACCOUNT_TYPE_IPPAN',
        'ACCOUNT_TYPE_SOGONISA_GLOW',
        'ACCOUNT_TYPE_SOGONISA_PERIODIC',
        'ACCOUNT_TYPE_NISA',
        'ACCOUNT_TYPE_TSUMITATE_NISA',
    ];
    await dividendHistory.toggleChecker(accountFilers);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.20_switch_on_off_display_account_filter.png`);
});

Scenario('Test Item 21: Clear items 17,18,19,20,23 to default', async ({ I, dividendHistory }) => {
    const locator = dividendHistory.locators.displaySettingClearButton('distributionHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.say("Click 'Clear' button to reset items 17,18,19,20,23 to default");
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.21_clear_items_17_18_19_20_237_to_default.png`);
});

Scenario('Test Item 22: Close the display settings modal', async ({ I, dividendHistory }) => {
    const locator = dividendHistory.locators.displaySettingConfirmButton('distributionHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.say("Click 'Confirm' button to close the display settings modal");
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.22_close_the_display_settings_modal.png`);
});

Scenario('Test Item 20: toggle show display period drop-down menu', async ({ I, dividendHistory }) => {
    await dividendHistory.openDisplaySetting('distributionHistoryList');
    const locator = dividendHistory.locators.displayPeriodButton('distributionHistoryList');
    I.seeElement(locator);
    I.clickFixed(locator);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.20_toggle_show_display_period.png`);
    const secondOption = `${locator}//p[2]`;
    I.seeElement(secondOption);
    I.clickFixed(secondOption);
    I.say('Select the second value option');
    const confirmLocator = dividendHistory.locators.displaySettingConfirmButton('distributionHistoryList');
    I.seeElement(confirmLocator);
    I.clickFixed(confirmLocator);
    I.saveScreenshot(`${dividendHistory.screenshotDisPrefix}.20_select_the_second_value_option.png`);
});
