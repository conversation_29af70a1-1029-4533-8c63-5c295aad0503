import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioStockPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to stock page
    await portfolio.top.goToPage();
    await portfolio.top.stock();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});


Scenario('test portfolio stock page', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.verifyProductName(stock.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_layout.png`);
});

Scenario('test portfolio stock page [3.銘柄カード]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_3_symbol_card.png`);
});

Scenario('test portfolio stock page [13.買付]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    await stock.buy();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_13_buy.png`);
});

Scenario('test portfolio stock page [14.売却]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    await stock.sell();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_14_sell.png`);
});

Scenario('test portfolio stock page [15.プチ買]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    await stock.petitBuy();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_15_petit_buy.png`);
});

Scenario('test portfolio stock page [16.プチ売]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    await stock.petitSell();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_16_petit_sell.png`);
});

Scenario('test portfolio stock page [17.銘柄情報を見る]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    await stock.seeInfo();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_17_see_info.png`);
});

Scenario('test portfolio stock page [18.残高照会]', async ({ I }) => {
    const { common, stock } = portfolio;

    await common.clickSymbolCard(stock.productType, '1305');
    await stock.positionInquiry();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioStockPage}_18_position_inquiry.png`);
});
