import { COOKIE_KEY, LAST_EXTERNAL_URL, USER_ID } from '../const/constant';

Feature('Notify - NotifyDetailsIndividualPage');
// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyDetailsIndividual.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

Scenario('test notify details individual page', async ({ I }) => {
    I.waitForElement('//*[@data-testid="common_noti_id"]');
    I.click('//*[@data-testid="common_noti_id"]');
    await I.waitFor('mediumWait');

    const notifyIndividualTab = '//*[@data-testid="noticeTab_individualNoti_id"]';
    I.waitForElement(notifyIndividualTab);
    I.click(notifyIndividualTab);
    await I.waitFor();
    I.saveScreenshot('251.notify_details_individual_individualListTab.png');

    const notifyListDetail = '//*[@data-testid="notifyIndividualList_notiItem_id_0"]';
    I.seeElement(notifyListDetail);
    I.click(notifyListDetail);
    await I.waitFor('mediumWait');
    I.saveScreenshot('251.notify_details_individual_notifyListDetail.png');
});

Scenario('test Item 5 Confirm Button page', async ({ I }) => {
    await I.saveScreenshot('251.notify_details_individual_confirmButton.png');
    const confirmButton = '//*[@data-testid="notifyDetailsIndividual_confirmationDeadline_id"]';
    I.seeElement(confirmButton);
    I.click(confirmButton);
    await I.waitFor();
});

Scenario('test Item 13 title setting url page', async ({ I }) => {
    const titleSettingUrl = '//*[@data-testid="notifyDetailsIndividual_linkTitle_id"]';
    I.seeElement(titleSettingUrl);
    I.tapLocationOfElement(titleSettingUrl);
    await I.waitFor('extraLongWait');
    I.activateApp();
    await I.waitFor();
    const externalUrl = await I.getLocalStorage(LAST_EXTERNAL_URL);
    I.assertContain(
        externalUrl,
        'https://translate.google.com/?sl=ja&tl=vi&op=translate',
        'externalUrl is not equal to https://translate.google.com/?sl=ja&tl=vi&op=translate',
    );
    I.saveScreenshot('251.notify_details_individual_titleSettingUrl.png');
});

Scenario('test Item 11 previous noti, Item 12 next noti page', async ({ I }) => {
    const nextNoti = '//*[@data-testid="notifyDetailsIndividual_nextNoti_id"]';
    const previousNoti = '//*[@data-testid="notifyDetailsIndividual_previousNoti_id"]';
    I.seeElement(nextNoti);
    I.dontSeeElement(previousNoti);
    I.click(nextNoti);
    await I.waitFor();
    I.saveScreenshot('251.notify_details_individual_nextNoti.png');
    I.seeElement(previousNoti);
    I.click(previousNoti);
    await I.waitFor();
    I.dontSeeElement(previousNoti);
    I.saveScreenshot('251.notify_details_individual_previousNoti.png');
});
