import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('InvestmentProducts - DomesticStockDetailBasic');

Before(async ({ I, loginAndSwitchToWebAs, stockDetailBasicPage }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Detail Basic page', async ({ stockDetailBasicPage }) => {
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/basic');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Display_Domestic_Stock_Detail_Basic_page');
});

Scenario('Test item No.2 Previous', async ({ I, stockDetailBasicPage }) => {
    const previousButtonSelector = '//div[contains(@class, "slick-prev")]';
    await I.clickFixed(previousButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.newsContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/news');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.2_Click_previous_go_to_stock_news_page');
});

Scenario('Test item No.3 News', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.newsTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.newsContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/news');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.3_Click_News_tab_go_to_stock_news_page');
});

Scenario('Test item No.5 Next', async ({ I, stockDetailBasicPage }) => {
    const nextButtonSelector = '//div[contains(@class, "slick-next")]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.detailInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.5_Click_next_go_to_stock_detail_page');
});

Scenario('Test item No.4 Details', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.detailInformationTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.detailInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.4_Click_Details_tab_go_to_stock_detail_page');
});

Scenario('Test item No.10 Market name', async ({ I, stockDetailBasicPage }) => {
    const marketNameSelector = '//*[@data-testid="common_stockInfoExchangeName_id"]';
    await I.clickFixed(marketNameSelector);
    await I.waitFor();
    I.seeElement('#bottom-sheet-container');
    I.seeElement('//*[@data-testid="common_exchangeListCancel_id"]');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.10_Show_market_selection_modal');
});

Scenario('Test item No.17 Trading Caution', async ({ I, stockDetailBasicPage }) => {
    const tradingCautionSelector = '//*[@data-testid="stockDetailBasic_tradeCaution_id"]';
    await I.clickFixed(tradingCautionSelector);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    I.seeElement('//*[@data-testid="common_rightSlide_close_id"]');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.10_Show_trading_caution_information_modal');
});

Scenario('Test item No.32 Trade', async ({ I, stockDetailBasicPage }) => {
    const tradeButtonSelector = '//*[@data-testid="stockInfo_tradeButton_id"]';
    await I.clickFixed(tradeButtonSelector);
    await I.waitFor();
    I.seeElement('#bottom-sheet-container');
    I.see('取引方法', '#bottom-sheet-container');
    I.seeElement('//*[@data-testid="tradingMethod_close_id"]');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.32_Display_stock_trading_method_selection');
});

Scenario('Test item No.33 Favorites', async ({ I, stockDetailBasicPage }) => {
    const favoriteButtonSelector = '//*[@data-testid="stockInfo_favoriteButton_id"]';
    const commonRightSlideSelector = '//*[@data-testid="common_rightSlide_favorite_id"]';
    await I.clickFixed(favoriteButtonSelector);
    await I.waitFor();
    I.seeElement(commonRightSlideSelector);
    I.see('お気に入り追加', commonRightSlideSelector);
    I.seeElement('//*[@data-testid="favoriteRegisterModal_listAdd_id"]');
    I.seeElement('//*[@data-testid="favoriteRegisterModal_confirm_id"]');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.33_Show_favorite_registration_modal');
});

Scenario('Test item No.34 Market List', async ({ I, stockDetailBasicPage }) => {
    const marketNameSelector = '//*[@data-testid="common_stockInfoExchangeName_id"]';
    const marketItem = '//*[@data-testid="common_exchangeListItem_id_1"]';
    await I.clickFixed(marketNameSelector);
    await I.waitFor();
    await I.clickFixed(marketItem);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.34_Select_market_item_in_market_list');
});

Scenario('Test item No.35 Close the Market selection modal', async ({ I, stockDetailBasicPage }) => {
    const marketNameSelector = '//*[@data-testid="common_stockInfoExchangeName_id"]';
    const closeButton = '//*[@data-testid="common_exchangeListCancel_id"]';
    await I.clickFixed(marketNameSelector);
    await I.waitFor();
    await I.clickFixed(closeButton);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.35_Close_the_Market_selection_modal');
});

Scenario('Test item No.40 See all', async ({ I, stockDetailBasicPage }) => {
    const viewAllSelector = '//*[@data-testid="stockDetailBasic_seeAll_id"]';
    const bottomSheetSelector = '#bottom-sheet-container';
    await I.clickFixed(viewAllSelector);
    await I.waitFor();
    I.seeElement(bottomSheetSelector);
    I.see('関連テーマ', bottomSheetSelector);
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.40_Show_related_themes_modal');
});

Scenario('Test item No.42 Close related themes modal', async ({ I, stockDetailBasicPage }) => {
    const viewAllSelector = '//*[@data-testid="stockDetailBasic_seeAll_id"]';
    const bottomSheetSelector = '#bottom-sheet-container';
    const closeButtonSelector = '//*[@data-testid="stockDetailBasic_close_id"]';
    await I.clickFixed(viewAllSelector);
    await I.waitFor();
    I.waitForElement(bottomSheetSelector);
    await I.clickFixed(closeButtonSelector);
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.42_Close_related_themes_modal');
});

Scenario('Test item No.43 List of related themes', async ({ I, stockDetailBasicPage }) => {
    const viewAllSelector = '//*[@data-testid="stockDetailBasic_seeAll_id"]';
    const themeListSelector = '//*[@data-testid="stockDetailBasic_themeList_id"]';
    const themeItemSelector = `${themeListSelector}/label[2]`;
    await I.clickFixed(viewAllSelector);
    await I.waitFor();
    I.waitForElement(themeListSelector);
    await I.clickFixed(themeItemSelector);
    await I.waitFor('mediumWait');
    I.see('テーマ詳細', 'body');
    await stockDetailBasicPage.compareUrl('/mobile/search/theme/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.43_Select_related_theme_item_go_to_domestic_stock_theme_detail_page');
});

Scenario('Test item No.41 Related Theme Items', async ({ I, stockDetailBasicPage }) => {
    const themeGroupSelector = '//*[@data-testid="stockDetailBasic_themeGroupItem_id"]';
    const themeItemSelector = `${themeGroupSelector}/label[2]`;
    await I.clickFixed(themeItemSelector);
    await I.waitFor('mediumWait');
    I.see('テーマ詳細', 'body');
    await stockDetailBasicPage.compareUrl('/mobile/search/theme/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.41_Select_related_theme_item_go_to_domestic_stock_theme_detail_page');
});

Scenario('Test item No.41-1 Related Theme Items - Swipe left and right', async ({ I, stockDetailBasicPage }) => {
    const themeGroupSelector = '//*[@data-testid="stockDetailBasic_themeGroupItem_id"]';
    const themeItemSelector = `${themeGroupSelector}/label[2]`;
    await I.scrollToElement(themeGroupSelector);
    await I.swipeLeftFixed(themeItemSelector);
    await stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.41-1_Select_related_theme_item_Swipe_left');
    await I.waitFor();
    await I.swipeRightFixed(themeItemSelector);
    await stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.41-1_Select_related_theme_item_Swipe_right');
});

Scenario('Test item No.45 Short selling restrictions in effect', async () => {
    const shortSellingRestrictions = '//*[@data-testid="common_stockInfoShortSaleRestricted_id"]';
    // Open the following in a new tab https://www.jpx.co.jp/markets/equities/ss-reg/index.html
    await common.clickCardItem(shortSellingRestrictions, 'https://www.jpx.co.jp/markets/equities/ss-reg/index.html', 'external');
});

Scenario('Test item No.51 Analyst forecasts', async ({ I, stockDetailBasicPage }) => {
    const analystForecastsSelector = '//*[@data-testid="stockDetailBasic_analystForecastDetail_id"]';
    await I.clickFixed(analystForecastsSelector);
    await I.waitFor('mediumWait');
    I.seeElement(stockDetailBasicPage.locator.financialInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/financial');
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.51_View_stock_details_and_financial_results');
});

Scenario('Test item No.53 Timeline row', async ({ I, stockDetailBasicPage }) => {
    // In specs this is item no.52
    const timeLineRowSelector = '//div[p[contains(text(), "時系列（株価表示）")]]';
    await I.clickFixed(timeLineRowSelector);
    await I.waitFor('shortWait');
    await stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.53_Open_the_accordion');
    await I.clickFixed(timeLineRowSelector);
    await I.waitFor('shortWait');
    await stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.53_Close_the_accordion');
});

Scenario('Test item No.6 Scroll Basic Information', async ({ I, stockDetailBasicPage }) => {
    const basicInfoAreaSelector = '//*[@data-testid="stockDetailBasic_basicInfo_id"]';
    await I.swipeUpFixed(basicInfoAreaSelector);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailBasic('Test_item_No.6_Scroll_Basic_Information');
});
