import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import hamburgerMenu from '../pages/hamburgerMenu';
import search from '../pages/search/index';

Feature('Symbol_ProductSearch - USStockSearchResult');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user19 });

    // Go to menu
    const inputLocator = search.usStockTop.locators.input;
    await hamburgerMenu.goToMenu();
    await hamburgerMenu.clickItem(hamburgerMenu.locators.usStock);
    await search.common.searchResult(inputLocator, 'ケロッグ');
});

After(async ({ I }) => {
    console.debug('after');
    await I<PERSON>closeBrowser();
    await I.switchToNative();
});

// レイアウト
Scenario('test usstock search result page', async ({ I }) => {
    I.waitInUrl('/mobile/search-usstock/result', 5);
    await commonUi.header.verifyTitle('米国株');
    I.saveScreenshot('505.usstock_search_result_page.png');
});

// 1.キーワード入力
Scenario('test usstock search result page [1.キーワード入力]', async ({ I }) => {
    await search.usStockResult.clickInput();
    I.saveScreenshot('505.usstock_search_result_page_1_click_input.png');
});

// 2.検索結果銘柄リスト
Scenario('test usstock search result page [2.検索結果銘柄リスト]', async ({ I }) => {
    const listLocator = search.usStockResult.locators.searchResultSymbolList;
    await I.swipeElementDirection('up', listLocator);
    I.saveScreenshot('505.usstock_search_result_page_2_swipe_up.png');
    await I.waitFor();
    await I.swipeElementDirection('down', listLocator);
    I.saveScreenshot('505.usstock_search_result_page_2_swipe_down.png');
});

// 3.検索結果銘柄アイテム(米国株)
Scenario('test usstock search result page [3.検索結果銘柄アイテム(米国株)]', async ({ I }) => {
    await search.usStockResult.clickSymbolItem(0);
    I.saveScreenshot('505.usstock_search_result_page_3_click_symbol_item.png');
});

// 4.検索結果銘柄アイテム(米国株)-未契約
Scenario('test usstock search result page [4.検索結果銘柄アイテム(米国株)-未契約]', async ({ I }) => {
    const inputLocator = search.usStockTop.locators.input;

    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user20 });
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    await search.common.searchResult(inputLocator, 'ケロッグ');
    I.saveScreenshot('505.usstock_search_result_page_4_search_item.png');
    await search.usStockResult.clickUncontractedSymbolItem(0);
    I.saveScreenshot('505.usstock_search_result_page_4_click_uncontracted_symbol_item.png');
});

// 5.続きを読み込む
Scenario('test usstock search result page [5.続きを読み込む]', async ({ I }) => {
    const loadMoreLocator = search.usStockResult.locators.readMoreButton;

    await search.common.scrollToLoadMore(loadMoreLocator);
    I.saveScreenshot('505.usstock_search_result_page_5_scroll_bottom_list.png');
    await search.common.performLoadMore(loadMoreLocator);
    I.saveScreenshot('505.usstock_search_result_page_5_click_read_more.png');
});

// 6.上に戻る
Scenario('test usstock search result page [6.上に戻る]', async ({ I }) => {
    const listLocator = search.usStockResult.locators.searchResultSymbolList;
    await I.swipeElementDirection('up', listLocator);
    I.saveScreenshot('505.usstock_search_result_page_6_swipe_up.png');
    await I.waitFor();
    await commonUi.other.clickScrollUp();
    I.saveScreenshot('505.usstock_search_result_page_6_click_scroll_up.png');
});
