Feature('Settings_Entry - ChangeLoginPasswordPage');
import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import ChangeLoginPassword from '../pages/changeLoginPassword';
import common from '../pages/search/common';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await <PERSON>.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('go to change login password page', async () => {
    await ChangeLoginPassword.navigateToPage(ChangeLoginPassword.locators.pageDescription, ChangeLoginPassword.locators.url);
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeLoginPassword}_page.png`);
});

Scenario('Test Item 1: Current Password', async () => {
    await ChangeLoginPassword.fillPasswordField(ChangeLoginPassword.locators.currentPasswordInput, '123456');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeLoginPassword}_current_password_input.png`);
});

Scenario('Test Item 2: New Password', async () => {
    await ChangeLoginPassword.fillPasswordField(ChangeLoginPassword.locators.newPasswordInput, '654321');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeLoginPassword}_new_password_input.png`);
});

Scenario('Test Item 3: New Password Confirmation', async () => {
    await ChangeLoginPassword.fillPasswordField(ChangeLoginPassword.locators.newPasswordConfirmInput, '654321');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.changeLoginPassword}_new_password_confirm_input.png`);
});

Scenario('Test Item 4: Complete Password Change Form', async ({ I }) => {
    await ChangeLoginPassword.submitForm(
        ChangeLoginPassword.locators.descriptionSelector,
        ChangeLoginPassword.locators.submitButton,
        ChangeLoginPassword.locators.modalSelector,
        false,
        `${SCREENSHOT_PREFIX.changeLoginPassword}_complete_password_change_form.png`
    );
    await common.checkAndLoginAgain();
});
