import { COOKIE_KEY, USER_ID } from '../const/constant';
import electronicDelivery from '../pages/electronicDelivery';
import common from '../pages/search/common';

Feature('Settings_Entry - EasyElectronicDelivery');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await <PERSON>.closeBrowser();
});

Scenario('Test Item 0: Check UI Electronic Delivery Pre Contract Completion', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    I.saveScreenshot(
        `${electronicDelivery.prefix.complete}.0_Check_UI_Electronic_Delivery_Pre_Contract_Completion.png`,
    );
});

// 3.その他約諾書リンクアイテム-遷移ボタン
// '+ B.その他約諾書一覧取得API.documentIds = 2040 の場合:
// 以下のURLを別タブで表示する。
// {member-site-url}/Members/personal/shinyouyakudaku/SHY01101.asp
// + B.その他約諾書一覧取得API.documentIds = 2800 の場合:
// 以下のURLを別タブで表示する
// {member-site-url}/Members/personal/tfxYakudaku/tfx01101.asp
// + B.その他約諾書一覧取得API.documentIds = 2900 の場合:
// 以下のURLを別タブで表示する
// {member-site-url}/Members/personal/tcfdYakudaku/tcfd01101.asp
Scenario('Test Item 3: Other agreement link item-transition button', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // documentIds = 2040
    await common.clickCardItem(electronicDelivery.locators.documentButtonId(0), '/Members/personal/shinyouyakudaku/SHY01101.asp', 'external');
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();

    // documentIds = 2800
    await common.clickCardItem(electronicDelivery.locators.documentButtonId(2), '/Members/personal/tfxYakudaku/tfx01101.asp', 'external');
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();

    // documentIds = 2900
    await common.clickCardItem(electronicDelivery.locators.documentButtonId(3), '/Members/personal/tcfdYakudaku/tcfd01101.asp', 'external');
    await I.waitFor();
});

// 4.手数料コース・変更 -> 下記URLに遷移する
Scenario('Test Item 4: Fee Course/Change -> Move to the URL below', async () => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iPhone/Personal/CommissionCourseApply/Apply
    await common.clickCardItem(electronicDelivery.locators.feeCourseChangeHref, '/ap/iPhone/Personal/CommissionCourseApply/Apply', 'kcMemberSite');
});

// 5.手数料の詳細ついて -> 下記URLを別タブで表示する
Scenario('Test Item 5: Regarding fee details -> Display the URL below in a separate tab', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    await common.clickCardItem(electronicDelivery.locators.detailOfFeeHref, 'https://kabu.com/sp/cost/default.html#anc01', 'external');
});

// 7.信用取引 -> 下記URLに遷移する
Scenario('Test Item 7: Margin trading -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationMargin/ExaminationInput
    await electronicDelivery.openAccountLink(0, '/ap/iphone/Personal/WebExaminationMargin/ExaminationInput');
});

// 8.信用取引開設 -> 下記URLに遷移する
Scenario('Test Item 8: Opening margin trading -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationMargin/ExaminationInput
    await electronicDelivery.openCreditAccount(0, '/ap/iphone/Personal/WebExaminationMargin/ExaminationInput');
});

// 9.三菱UFJ eスマート証券 FX -> 下記URLに遷移する
Scenario('Test Item 9: Mitsubishi UFJ e-Smart Securities FX -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationFX/ExaminationInput
    await electronicDelivery.openAccountLink(1, '/ap/iphone/Personal/WebExaminationFX/ExaminationInput');
});

// 10.三菱UFJ eスマート証券 FX開設 -> 下記URLに遷移する
Scenario('Test Item 10: Mitsubishi UFJ e-Smart Securities FX Opening -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationFX/ExaminationInput
    await electronicDelivery.openCreditAccount(1, '/ap/iphone/Personal/WebExaminationFX/ExaminationInput');
});

// 11.取引所CFD(株365) -> 下記URLに遷移する
Scenario('Test Item 11: Exchange CFD (Stock 365) -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationCFD/ExaminationInput
    await electronicDelivery.openAccountLink(2, '/ap/iphone/Personal/WebExaminationCFD/ExaminationInput');
});

// 12.取引所CFD(株365)開設 -> 下記URLに遷移する
Scenario('Test Item 12: Opening the Exchange CFD (Stock 365) -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationCFD/ExaminationInput
    await electronicDelivery.openCreditAccount(2, '/ap/iphone/Personal/WebExaminationCFD/ExaminationInput');
});

// // 13.先物・オプション取引 -> 下記URLに遷移する
Scenario('Test Item 13: Futures and Options Trading -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationDeriv/ExaminationInput
    await electronicDelivery.openAccountLink(3, '/ap/iphone/Personal/WebExaminationDeriv/ExaminationInput');
});

// 14.先物・オプション取引開設  -> 下記URLに遷移する
Scenario('Test Item 14: Opening Futures and Options Trading -> Go to the URL below', async ({ I }) => {
    await electronicDelivery.goToCompletionPage();
    // {member-site-url}/ap/iphone/Personal/WebExaminationDeriv/ExaminationInput
    await electronicDelivery.openCreditAccount(3, '/ap/iphone/Personal/WebExaminationDeriv/ExaminationInput');
});
