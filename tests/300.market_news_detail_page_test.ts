import { COOKIE_KEY, USER_ID } from '../const/constant';
import newsDetail from '../pages/newsDetail';
import newsList from '../pages/newsList';

Feature('Market - NewsDetailPage');

Before(async ({ I }) => {
  console.debug('before');
  await I.activateApp();
  await I.waitFor('mediumWait');
  await I.switchToWeb();
  await I.waitFor('mediumWait');
  <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user12 });
});

After(async ({ I }) => {
  console.debug('after');
  await I.closeBrowser();
  await I.switchToNative();
});

Scenario('Test item No.0 Check news detail display', async ({ I }) => {
  // Direct to news detail page
  const newsTitle = await newsDetail.navigateToNewsDetail();
  // Check the elements displayed on the news detail page
  I.waitForElement(newsDetail.locator.newsDetailContainer);
  I.waitForElement(newsDetail.locator.newsDetailTitle);
  I.waitForElement(newsDetail.locator.backButton);
  // Verify the news detail
  await newsDetail.verifyNewsDetailPage(newsTitle);
  // Take screenshot of the news detail page
  I.saveScreenshot('300.Test_item_No.1_news_detail_display.png');
});
Scenario('Test item No.0.1 Check scrolling news detail content', async ({ I }) => {
  // Direct to news detail page
  await newsDetail.navigateToNewsDetail();
  // Take screenshot before scrolling
  I.saveScreenshot('300.Test_item_No.0.1_before_scroll.png');
  // Scroll news content
  await newsDetail.scrollNewsContent();
  // Take screenshot after scrolling
  I.saveScreenshot('300.Test_item_No.0.1_after_scroll.png');
});

Scenario('Test item No.6 Check switching from news tab to indicator tab', async ({ I }) => {
  // Switch to indicatorListTab/rankingTab/newsTab
  await newsList.clickMarketTabGroup();
  I.saveScreenshot('300.Test_item_No.6_Check_switching_from_news_tab_to_indicator_tab.png');
});
Scenario('Test item No.5: Check Related stock list', async ({ I }) => {
  // Direct to news detail page
  const newsTitle = await newsDetail.navigateToNewsDetail();
  // Check the elements displayed on the news detail page
  I.waitForElement(newsDetail.locator.newsDetailContainer);
  I.waitForElement(newsDetail.locator.newsDetailTitle);
  I.waitForElement(newsDetail.locator.backButton);
  // Verify the news detail
  await newsDetail.verifyNewsDetailPage(newsTitle);
  // Take screenshot of the news detail page
  const marketNewsDetailPage = '//*[@data-testid="marketNewsDetailPage_relatedSymbolItem_index"]';
  I.waitForElement(marketNewsDetailPage, 2);
  I.scrollAndClick(marketNewsDetailPage);
  I.saveScreenshot('300.Test_item_No.5_Check_related_stock_list.png');
});
