import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';
import domesticStockCashtrading from '../pages/domesticStockCashtrading';

Feature('InvestmentProducts - DomesticStockCashtrading');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor('mediumWait');

    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user25 });
    await <PERSON>.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Go to the stock uturn buy page', async ({ I }) => {
    await domesticStockCashtrading.goToStockUturnBuyPage();
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.0_Go_to_the_stock_uturn_buy_page.png`);
});

// 3.取引制限・取引注意情報 -> 共通UI-取引注意情報モーダルを表示する
Scenario('Item 3. Display the common UI - Trading Caution Information modal', async ({ I }) => {
    const closePopupButton = '//div[p[contains(text(), "取引制限・取引注意情報")]]//*[@data-testid="common_rightSlide_close_id"]';
    await I.scrollAndClick('//*[@data-testid="sellUturn_tradeRestricitonTradeCautionInformation_id"]');
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.3_Display_the_common_UI_-_Trading_Caution_Information_modal.png`,
    );
    await I.clickFixed(closePopupButton);
    await I.waitFor();
});

// 9.執行方法選択 -> タップした要素を選択状態とする
Scenario('Item 9. Execution method selection: Set the tapped element to the selected state', async ({ I }) => {
    await I.scrollAndClick(domesticStockCashtrading.locators.sellUturnExecutionMethodSelection(2));
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.9_select_自動売買_tap.png`);
});

// 10.執行方法設定パネル -> 共通UI-共通注文入力参照
Scenario('Item 10. Execution method setting panel -> Refer to common UI - Common order input', async ({ I }) => {
    await I.scrollAndClick(domesticStockCashtrading.locators.sellUturnExecutionMethodSelection(1));
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.10_select_指値_tap.png`);
    await I.scrollToElement(locate(domesticStockCashtrading.locators.plusButton).toXPath());
    await I.clickFixed(domesticStockCashtrading.locators.plusButton);
    await I.clickFixed(domesticStockCashtrading.locators.plusButton);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.10_refer_to_common_ui_common_order_input.png`,
    );
});

//11.注文確認画面へ -> 国内株式現物取引-売注文-注文確認に遷移する
Scenario(
    'Item 11. Order confirmation screen -> Go to Domestic Stock Cash Trading - Sell Order - Order Confirmation',
    async ({ I }) => {
        await I.scrollAndClick('//*[@data-testid="sellUturn_orderConfirmButton_id"]');
        I.see('現物売', COMMON_HEADER_TITLE);
        I.seeInCurrentUrl(domesticStockCashtrading.urls.confirm);
        await I.saveScreenshot(
            `${domesticStockCashtrading.screenshotPrefix.uturn}.11_go_to_domestic_stock_cash_trading_sell_order_order_confirmation.png`,
        );

        // ブラウザバック -> 銘柄/商品検索_総合検索画面TOPに遷移するようブラウザのヒストリを更新する
        await I.scrollAndClick('//*[@data-testid="common_back_id"]');
        I.performBrowserBack();
        await I.saveScreenshot(
            `${domesticStockCashtrading.screenshotPrefix.uturn}.11_browser_back_to_the_top_search_page.png`,
        );
    },
);
