import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginDeliveryOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginDelivery }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginDelivery.goToMarginDeliveryOrderInput();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Delivery Order Input', async ({ I, stockMarginDelivery }) => {
    await stockMarginDelivery.compareUrl(stockMarginDelivery.urls.marginDeliveryOrderInput);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput(
        'Display_Domestic_Stock_Margin_Trading_Delivery_Order_Input',
    );
});

Scenario('Test item No.6 Trade Restrictions and Trade Caution Information', async ({ I, stockMarginDelivery }) => {
    await I.clickFixed(stockMarginDelivery.locator.marginDeliveryCautionInfo);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.6_Open_Trade_Caution_Information_modal');
});

Scenario('Test item No.3 Reception status', async ({ I, stockMarginDelivery }) => {
    const receptionStatusSelector = '//*[@data-testid="marginDeliveryInput_recieveStatus_id"]';
    const bottomSheetContainerSelector = '#bottom-sheet-container';
    await I.clickFixed(receptionStatusSelector);
    await I.waitFor();
    I.see('品受・品渡のご注文受付時間について', bottomSheetContainerSelector);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput(
        'Test_item_No.3_Display_the_item_reception_delivery_modal',
    );
});

Scenario('Test item No.9 Deliver this position', async ({ I, stockMarginDelivery }) => {
    const deliveryThisPositionSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const deliveryThisPositionRadioSelector = `${deliveryThisPositionSelector}/div[1]/div[1]`;
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    await I.clickFixed(deliveryThisPositionSelector);
    await I.waitFor();
    I.seeElement(quantityInputSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisPositionRadioSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    await stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput(
        'Test_item_No.9_Change_selected_status_and_show_quantity',
    );
    await I.clickFixed(deliveryThisPositionSelector);
    await I.waitFor();
    I.dontSeeElement(quantityInputSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisPositionRadioSelector, 'background-color'),
        'rgba(212, 212, 212, 1)',
        'Background color is not equal',
    );
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput(
        'Test_item_No.9_Change_unselected_status_and_hide_quantity',
    );
});

Scenario('Test item No.10 Details display', async ({ I, stockMarginDelivery }) => {
    const detailDisplaySelector = '//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    await I.clickFixed(detailDisplaySelector);
    await I.waitFor();
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.10_Switch_the_details_display');
});

Scenario('Test item No.21 Next', async ({ I, stockMarginDelivery }) => {
    const marginListItemSelector =
        '//*[@id="MarginListInfo0"]//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const positionListItemSelector =
        '//*[@id="PositionListInfo0"]//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const nextButtonSelector = '//*[@data-testid="marginDeliveryInput_deliveryButton_id"]';
    await I.clickFixed(marginListItemSelector);
    await I.waitFor();
    I.fillField(quantityInputSelector, stockMarginDelivery.inputValues.quantity);
    I.blur(quantityInputSelector);
    await I.swipeDirection('up');
    await I.clickFixed(positionListItemSelector);
    await I.clickFixed(nextButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginDelivery.compareUrl('/mobile/trade/margin/delivery/confirm');
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.21_Tap_next_to_Go_to_Confirm_Delivery');
});

Scenario('Test item No.22 Quantity', async ({ I, stockMarginDelivery }) => {
    const marginListItemSelector =
        '//*[@id="MarginListInfo0"]//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const minusButtonSelector = '//*[@data-testid="groupInputNumber_minus_id"]';
    const plusButtonSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    await I.clickFixed(marginListItemSelector);
    await I.waitFor();
    I.seeElement(quantityInputSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(minusButtonSelector);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.22_See_Numeric_Stepper');
});

Scenario('Test item No.26 Deliver this position', async ({ I, stockMarginDelivery }) => {
    const detailDisplaySelector = '//*[@id="MarginListInfo0"]//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const deliveryThisDepositSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisDeposit_id"]';
    const deliveryThisDepositIconSelector = '[data-testid="marginDeliveryInput_deliveryThisDeposit_id"] button svg';
    await I.clickFixed(detailDisplaySelector);
    await I.waitFor();
    await I.clickFixed(deliveryThisDepositSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisDepositIconSelector, 'color'),
        'rgba(255, 86, 0, 1)',
        'Color is not equal',
    );
    I.seeElement(quantityInputSelector);
    await stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput(
        'Test_item_No.26_Change_selected_status_and_show_quantity',
    );
    await I.clickFixed(deliveryThisDepositSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisDepositIconSelector, 'color'),
        'rgba(137, 137, 139, 1)',
        'Color is not equal',
    );
    I.dontSeeElement(quantityInputSelector);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput(
        'Test_item_No.26_Change_unselected_status_and_hide_quantity',
    );
});

Scenario('Test item No.31 Quantity', async ({ I, stockMarginDelivery }) => {
    const detailDisplaySelector = '//*[@id="MarginListInfo0"]//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const deliveryThisDepositSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisDeposit_id"]';
    const minusButtonSelector = '//*[@data-testid="groupInputNumber_minus_id"]';
    const plusButtonSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    await I.clickFixed(detailDisplaySelector);
    await I.waitFor();
    await I.clickFixed(deliveryThisDepositSelector);
    await I.waitFor();
    I.seeElement(quantityInputSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(minusButtonSelector);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.31_See_Numeric_Stepper');
});

Scenario('Test item No.35 Hand over this item', async ({ I, stockMarginDelivery }) => {
    const deliveryThisPositionSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const deliveryThisPositionIconSelector = `${deliveryThisPositionSelector}/div[1]/div[1]`;
    await I.clickFixed(deliveryThisPositionSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisPositionIconSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    await stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.35_Change_selected_status');
    await I.clickFixed(deliveryThisPositionSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisPositionIconSelector, 'background-color'),
        'rgba(212, 212, 212, 1)',
        'Background color is not equal',
    );
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.35_Change_unselected_status');
});

Scenario('Test item No.36 Details display', async ({ I, stockMarginDelivery }) => {
    const detailDisplaySelector =
        '//*[@id="PositionListInfo0"]//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    await I.clickFixed(detailDisplaySelector);
    await I.waitFor();
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderInput('Test_item_No.36_Switch_the_details_display');
});
