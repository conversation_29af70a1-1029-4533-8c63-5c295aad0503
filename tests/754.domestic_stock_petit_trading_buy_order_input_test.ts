import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('InvestmentProducts - DomesticStockPetitTradingBuyOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockPetitOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user29 });
    await stockPetitOrder.goToPetitBuyOrderInput();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Petit Trading Buy Order input', async ({ I, stockPetitOrder }) => {
    I.seeElement(stockPetitOrder.locator.cautionInfoPetitBuy);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Display_Domestic_Stock_Petit_Trading_Buy_Order_input');
});

Scenario('Test item No.3 Trade Restrictions and Trade Caution Information', async ({ I, stockPetitOrder }) => {
    const closeButtonSelector = '//*[@data-testid="common_rightSlide_close_id"]';
    await I.clickFixed(stockPetitOrder.locator.cautionInfoPetitBuy);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    I.seeElement(closeButtonSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput(
        'Test_item_No.3_Tap_to_display_the_Trade_Caution_Information_modal',
    );
});

Scenario('Test item No.7 Account Category', async ({ I, stockPetitOrder }) => {
    const accountTypeSelector = '//*[@data-testid="tradePetitBuy_accountType_id"]';
    const secondTypeItemSelector = `${accountTypeSelector}//p[contains(text(), "一般")]`;
    await I.clickFixed(secondTypeItemSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(secondTypeItemSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.7_Select_account_category_item');
});

Scenario('Test item No.14 Quantity', async ({ I, stockPetitOrder }) => {
    const minusButtonSelector = '//*[@data-testid="groupInputNumber_minus_id"]';
    const quantitySelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const plusButtonSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(minusButtonSelector);
    I.seeElement(quantitySelector);
    I.seeElement(minusButtonSelector);
    I.seeElement(plusButtonSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.14_Quantity_See_Numeric_Stepper');
});

Scenario('Test item No.18 Unitization', async ({ I, stockPetitOrder }) => {
    const unitizationSelector = '//*[@data-testid="tradePetitBuy_unitization_id"]';
    await I.clickFixed(unitizationSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.18_Click_Unitization_to_set_calculated_quantity');
});

Scenario('Test item No.22 Go to the order confirmation screen', async ({ I, stockPetitOrder }) => {
    const confirmButtonSelector = '//*[@data-testid="tradePetitBuy_orderConfirm_id"]';
    const checkButtonSelector = '//button[@aria-label="Check"]';
    const unitizationSelector = '//*[@data-testid="tradePetitBuy_unitization_id"]';
    await I.scrollToElement(confirmButtonSelector);
    await I.clickFixed(unitizationSelector);
    if ((await I.grabNumberOfVisibleElements(checkButtonSelector)) > 0) {
        await I.clickFixed(checkButtonSelector);
    }
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement('//*[@data-testid="tradePetitBuyConfirm_orderConfirm_id"]');
    await stockPetitOrder.compareUrl('/mobile/trade/petit/buy/confirm');
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.22_Go_to_the_order_confirmation_screen');
});

Scenario('Test item No.24 Agree to the Terms of Use', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user30 });
    await stockPetitOrder.goToPetitBuyOrderInput();
    const agreeTerms = '//div[button[@data-testid="tradePetitBuy_orderConfirm_id"]]//span[contains(text(), "利用規約の同意")]';
    // Go to the following URL: {member-site-url}/ap/iphone/personal/pontapointdoui/agree
    await common.clickCardItem(agreeTerms, '/ap/iphone/personal/pontapointdoui/agree', 'kcMemberSite')
});

Scenario('Test item No.25 auID Linking', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user31 });
    await stockPetitOrder.goToPetitBuyOrderInput();
    const auIdLinking = '//div[button[@data-testid="tradePetitBuy_orderConfirm_id"]]//span[contains(text(), "auID連携")]';
    // Go to the following URL: {member-site-url}/ap/iphone/personal/pontapointdoui/agree
    await common.clickCardItem(auIdLinking, '/ap/iphone/personal/pontapointdoui/agree', 'kcMemberSite')
});

Scenario('Test item No.27 Points', async ({ I, stockPetitOrder }) => {
    const pontaPointSelector = '//*[@data-testid="tradePetitBuy_usePontaPoint_id"]';
    const pointInputSelector = '//*[@data-testid="tradePetitBuy_pointInput_id"]';
    const minusButtonSelector = `${pointInputSelector}//*[@data-testid="groupInputNumber_minus_id"]`;
    const plusButtonSelector = `${pointInputSelector}//*[@data-testid="groupInputNumber_plus_id"]`;
    await I.scrollToElement(pontaPointSelector);
    await I.clickFixed(pontaPointSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.clickFixed(minusButtonSelector);
    I.seeElement(pointInputSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.27_Points_See_Numeric_Stepper');
});

Scenario('Test item No.28 Use all', async ({ I, stockPetitOrder }) => {
    const pontaPointSelector = '//*[@data-testid="tradePetitBuy_usePontaPoint_id"]';
    const useAllSelector = '//*[@data-testid="tradePetitBuy_useAll_id"]';
    await I.scrollToElement(pontaPointSelector);
    await I.clickFixed(pontaPointSelector);
    I.seeElement(useAllSelector);
    await I.clickFixed(useAllSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.28_Click_Use_all_to_set_owned_points');
});

Scenario('Test item No.29 Deposit request', async ({ }) => {
    const depositRequest = '//*[@data-testid="tradePetitBuy_depositRequest_id"]';
    // Transition to Deposit request
    await common.clickCardItem(depositRequest, '/mobile/cashflow/depositrequest', 'external');
});

Scenario('Test item No.36 What is the binding amount when placing an order?', async ({ I, stockPetitOrder }) => {
    const aboutAmountSelector = '//*[@data-testid="common_aboutRestraintAmount_id"]';
    const dialogSelector = '//section[@role="dialog"]';
    await I.clickFixed(aboutAmountSelector);
    I.seeElement(dialogSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput('Test_item_No.36_Display_explanation_using_tooltip_UI');
});

Scenario('Test item No.37 Fees link', async ({ I }) => {
    const aboutAmountSelector = '//*[@data-testid="common_aboutRestraintAmount_id"]';
    const commissionLink = '//span[@data-testid="common_commissionLink_id"]//span[contains(text(), "手数料")]';
    await I.clickFixed(aboutAmountSelector);
    await I.waitFor('shortWait');
    // Transition to "Petit Stocks® (fractional shares) Fees" in a separate tab
    await common.clickCardItem(commissionLink, 'https://kabu.com/item/petit/cost.html', 'external');
});

Scenario('Test item No.19 Use Ponta points', async ({ I, stockPetitOrder }) => {
    const pontaPointSelector = '//*[@data-testid="tradePetitBuy_usePontaPoint_id"]';
    const pointInputSelector = '//*[@data-testid="tradePetitBuy_pointInput_id"]';
    const useAllSelector = '//*[@data-testid="tradePetitBuy_useAll_id"]';
    await I.scrollToElement(pontaPointSelector);
    await I.clickFixed(pontaPointSelector);
    await I.waitFor();
    I.seeElement(pointInputSelector);
    I.seeElement(useAllSelector);
    await stockPetitOrder.takeScreenshot.petitBuyOrderInput(
        'Test_item_No.19_Click_Use_Ponta_points_to_show_input_and_all_usage',
    );
    await I.clickFixed(pontaPointSelector);
    await I.waitFor();
    I.dontSeeElement(pointInputSelector);
    I.dontSeeElement(useAllSelector);
    stockPetitOrder.takeScreenshot.petitBuyOrderInput(
        'Test_item_No.19_Click_Use_Ponta_points_to_hide_input_and_all_usage',
    );
});
