import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingPaymentUturnOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginPaymentOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await stockMarginPaymentOrder.goToMarginPaymentUturnOrderInput();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user33 });
    I.refreshPage();
    await I.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario(
    'Test Display Domestic Stock Margin Trading Payment Uturn Order input',
    async ({ stockMarginPaymentOrder }) => {
        await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/uturn');
        stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
            'Display_Domestic_Stock_Margin_Trading_Payment_Uturn_Order_input',
        );
    },
);

Scenario('Test item No.10 Execution method', async ({ I, stockMarginPaymentOrder }) => {
    const executionMethodSelector = '//*[@data-testid="marginPaymentUturn_executionMethod_id"]';
    const limitOrderSelector = `${executionMethodSelector}//button[contains(text(), "指値")]`;
    await I.swipeDirection('up', 0.5);
    await I.waitFor('shortWait');
    I.assertEqual(
        await I.grabCssPropertyFrom(limitOrderSelector, 'background-color'),
        'rgba(239, 239, 239, 1)',
        'Background color is not equal',
    );
    stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput('Test_item_No.10_See_Common_UI_Order_Entry');
});

Scenario('Test item No.11 Go to the order confirmation screen', async ({ I, stockMarginPaymentOrder }) => {
    const executionMethodSelector = '//*[@data-testid="marginPaymentUturn_executionMethod_id"]';
    const priceSelector = `${executionMethodSelector}//input[contains(@placeholder, "価格を入力")]`;
    const orderConfirmButtonSelector = '//*[@data-testid="marginPaymentUturn_orderConfirmButton_id"]';
    await I.scrollToElement(priceSelector);
    await I.waitFor();
    I.fillField(priceSelector, '1,130.5');
    await I.waitFor();
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    I.see('信用返済', 'body');
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/confirm');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
        'Test_item_No.11_Transition_to_order_confirm',
    );
});

Scenario('Test item No.12 Caution', async ({ I, stockMarginPaymentOrder }) => {
    const cautionSelector = '//*[@data-testid="marginPaymentUturn_caution_id"]/div';
    await I.clickFixed(cautionSelector);
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
        'Test_item_No.12_Opening_the_caution_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
        'Test_item_No.12_Closing_the_caution_accordion',
    );
});

Scenario('Test item No.13 Screen display settings', async ({ I, stockMarginPaymentOrder }) => {
    const screenDisplaySettingsSelector = '//*[@data-testid="marginPaymentUturn_screenDisplaySetting_id"]//div[1]';
    await I.clickFixed(screenDisplaySettingsSelector);
    await I.waitFor('mediumWait');
    await stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
        'Test_item_No.13_Closing_the_screen_display_settings_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(screenDisplaySettingsSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
        'Test_item_No.13_Opening_the_screen_display_settings_accordion',
    );
});

Scenario('Test Back to browser', async ({ I, stockMarginPaymentOrder }) => {
    await I.performBrowserBack();
    await I.waitFor();
    I.seeElement('#searchPage');
    await stockMarginPaymentOrder.compareUrl('/mobile/search');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentUturnOrderInput(
        'Test_Back_to_browser_to_transition_to_general_search_page',
    );
});
