import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Feature('Market - BenefitSearchResult');

Before(async ({ I, loginAndSwitchToWebAs, market }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user14 });
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.waitFor();
    await market.goToShareholderBenefitsSearch();
    await market.clickBenefitSearchButton();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Market Benefit Search Result page', async ({ I }) => {
    I.seeElement('//*[@data-testid="benefitSearchResult_saveCondition_id"]');
    I.seeElement('//*[@data-testid="benefitSearchResult_searchAgain_id"]');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Display_Benefit_Search_Result_page.png`);
});

Scenario('Test item No.3 Swipe up and down list of preferred stocks', async ({ I }) => {
    const benefitResultListSelector = '//*[@data-testid="benefitSearchResult_benefitList_id_0"]/..';
    await I.swipeUpFixed(benefitResultListSelector);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Test_item_No.2_Swipe_up_list_of_preferred_stocks.png`);
    await I.swipeDownFixed(benefitResultListSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Test_item_No.3_Swipe_down_list_of_preferred_stocks.png`);
});

Scenario('Test item No.4 Benefit stock details', async ({ I }) => {
    const firstStockItemSelector = '//*[@data-testid="benefitSearchResult_benefitList_id_0"]';
    await I.clickFixed(firstStockItemSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="benefitDetail_symbolInfo_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/benefit/detail', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Test_item_No.4_Benefit_stock_details.png`);
});

Scenario('Test item No.13 Favorites', async ({ I }) => {
    const favoriteButton = '//*[@data-testid="benefitList_benefitFavorite_id"]';
    const favoriteModal = '//*[@data-testid="common_rightSlide_favorite_id"]';
    const addButton = '//*[@data-testid="favoriteRegisterModal_listAdd_id"]';
    const confirmButton = '//*[@data-testid="favoriteRegisterModal_confirm_id"]';
    await I.clickFixed(favoriteButton);
    await I.waitFor();
    I.seeElement(favoriteModal);
    I.seeElement(addButton);
    I.seeElement(confirmButton);
    I.see('お気に入り追加', favoriteModal);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Test_item_No.13_Show_favorite_registration_modal.png`);
});

Scenario('Test item No.14 Save search conditions', async ({ I }) => {
    const saveConditionsButton = '//*[@data-testid="benefitSearchResult_saveCondition_id"]';
    await I.clickFixed(saveConditionsButton);
    await I.waitFor();
    I.see('検索条件を保存しました。', 'body');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Test_item_No.14_Display_search_condition_save_notification_toast.png`);
});

Scenario('Test item No.15 Search again', async ({ I }) => {
    const saveAgainButton = '//*[@data-testid="benefitSearchResult_searchAgain_id"]';
    await I.clickFixed(saveAgainButton);
    await I.waitFor();
    I.seeElement('//*[@data-testid="benefitSearch_searchBox_id"]');
    I.seeElement('//*[@data-testid="benefitSearch_search_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/benefit/search', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearchResult}_Test_item_No.15_Search_again_go_to_benefit_search.png`);
});
