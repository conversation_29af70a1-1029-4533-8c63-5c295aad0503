import { COOKIE_KEY, USER_ID } from "../const/constant";
import indicator from "../pages/indicator";

Feature('Market - ChartPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr/vn/Customer/InvestmentResult/InvestmentProfitLoss.html
Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user42 });
   
    await indicator.goToIndicator();
    await indicator.clickChart(); 
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await <PERSON>.switchToNative();
});

Scenario('Test item No.1 Line chart display', async ({ I }) => {
    // Verify line chart is displayed with correct design
    I.waitForElement('//*[@data-testid="commonChart_chartContainer_id"]', 3);
    I.saveScreenshot('276.Test_item_No.1_Line_chart_display.png')
});
Scenario('Test item No.2 Candlestick chart display', async ({ I }) => {
    await indicator.clickCandlestickChart();
    I.saveScreenshot('276.Test_item_No.2_Candlestick_chart_display.png');

    
   
});
Scenario('Test item No.3 Trend selection display', async ({ I }) => {
    // Click on Trend button
    await indicator.clickTrendButton();
    
    // Verify trend selection is displayed with correct design
    I.seeElement('//*[@data-testid="commonChart_trendSelection_id"]');
    I.saveScreenshot('276.Test_item_No.3_Trend_selection_display.png');
    
    // Verify deselection works
    await indicator.clickTrendButton();
    I.dontSeeElement('//*[@data-testid="commonChart_trendSelection_id"]');
});
Scenario('Test item No.4 Oscillator selection display', async ({ I }) => {
    // Click on Oscillator button
    await indicator.clickOscillatorButton();
    
    // Verify oscillator selection is displayed with correct design
    I.seeElement('//*[@data-testid="commonChart_oscillatorSelection_id"]');
    I.saveScreenshot('276.Test_item_No.4_Oscillator_selection_display.png');
    
    // Verify deselection works
    await indicator.clickOscillatorButton();
    I.dontSeeElement('//*[@data-testid="commonChart_oscillatorSelection_id"]');
});
Scenario('Test item No.5 EVERChart URL opening', async ({ I }) => {
    // Click on EVERChart button
    await indicator.clickEverChartButton();
    await I.waitFor('longWait');
    
    // Return to the app after EVERChart opens
    I.activateApp();
    I.saveScreenshot('276.Test_item_No.5_EVERChart_URL_opening.png');
});
Scenario('Test item No.6 Chart value reading line display', async ({ I }) => {
    // Click on EVERChart button
    // Verify line chart is displayed with correct design
    I.waitForElement('//*[@data-testid="commonChart_chartContainer_id"]', 3);
    I.saveScreenshot('276.Test_item_No.6_Chart_value_reading_line.png');
});
Scenario('Test item No.9 Display period selection', async ({ I }) => {
    // Test different display periods using text
    const periodTexts = ['1日', '1週', '1ヶ月', '6ヶ月', '1年', '5年'];
    
    for (const periodText of periodTexts) {
        await indicator.selectDisplayPeriodByText(periodText);
        I.saveScreenshot(`276.Test_item_No.9_Display_period_${periodText}.png`);
    }
});
Scenario('Test item No.10 Trend selection functionality', async ({ I }) => {
    // Click on Trend button to show trend selection
    await indicator.clickTrendButton();
    
    // Verify trend selection is displayed
    I.seeElement('//*[@data-testid="commonChart_trendSelection_id"]');
    
    // Test different trend options
    const trendOptions = [
        { label: '前日終値ライン', value: 'prevClose' },
        { label: '単純移動平均線（5,25,75）', value: 'sma' },
        { label: 'ボリンジャーバンド', value: 'bollingerBand' },
        { label: 'HLバンド(5)', value: 'priceFlucationRange' },
        { label: 'パラボリック', value: 'parabolic' }
    ];
    
    // Select and test each trend option
    for (const option of trendOptions) {
        await indicator.selectTrendOption(option.value);
        I.saveScreenshot(`276.Test_item_No.10_Trend_selection_${option.value}.png`);
    }
});
Scenario('Test item No.11 Oscillator selection functionality', async ({ I }) => {
    // Click on Oscillator button to show oscillator selection
    await indicator.clickOscillatorButton();
    
    // Select and test each oscillator option
    const oscillatorOptions = [
        { label: 'MACD', value: 'macd' },
        { label: 'モメンタム', value: 'momentum' },
        { label: 'RSI', value: 'rsi' },
        { label: 'CCI', value: 'cci' },
        { label: 'DMI', value: 'dmi' },
        { label: 'ウィリアムズ%R ', value: 'williamR' }
    ];

    // Select and test each oscillator option
    for (const option of oscillatorOptions) {
        await indicator.selectOscillatorOption(option.value);
        I.saveScreenshot(`276.Test_item_No.11_Oscillator_selection_${option.value}.png`);
    }
});
Scenario('Test item No.15 Tooltip display', async ({ I }) => {
    await indicator.clickToolTip();    
    I.seeElement('//section[contains(@class, "chakra-popover__content")]');
    I.saveScreenshot('276.Test_item_No.15_Tooltip_display.png');
});