Feature('Settings_Entry - AppSettingPage');
import { COOKIE_KEY, USER_ID } from '../const/constant';
import AppSetting from '../pages/appSetting';
import common from '../pages/search/common';

const locators = AppSetting.locators;

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user59 }, { name: COOKIE_KEY.siteId, value: '1' }]);
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Go to app setting page', () => {
    AppSetting.goToPage();
    AppSetting.takeScreenshot('1701_app_setting_page.png');
});

Scenario('Test item 1 Mail Notification', ({ I }) => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Mail Notification Settings modal
    I.seeElement(locators.executionAndExpiration);
    I.waitForText('メール通知設定', 3, 'body');
    AppSetting.takeScreenshot('1701_Test_Item_1_Mail_Notification.png');
    AppSetting.closeRightModal();
});

Scenario('Test item 2 Push Notification Setting', ({ I }) => {
    AppSetting.clickItem(locators.pushNotificationSettings);
    // Display Push Notification setting modal
    I.waitForText('プッシュ通知設定', 3, 'body');
    I.waitForText('・配信ON/OFF設定の反映は1時間程度掛かる場合があります。', 3, 'body');
    AppSetting.takeScreenshot('1701_Test_Item_2_Push_Notification_Setting.png');
    AppSetting.closeRightModal();
});

Scenario('Test item 3 orderPriorityMethod Setting', ({ I }) => {
    AppSetting.clickItem(locators.priorityOrderingMethodSettings);
    // Display orderPrioritySetting modal
    I.waitForText('優先発注方法設定(株式)', 3, 'body');
    I.seeElement(locators.priorityOrderMethod);
    AppSetting.takeScreenshot('1701_Test_Item_3_orderPriorityMethod_Setting.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 4 EasyScreen Setting', ({ I }) => {
    AppSetting.clickItem(locators.simpleScreenSettings);
    // Display EasyScreenSetting modal
    I.waitForText('売買画面表示設定', 3, 'body');
    I.seeElement("//button[@type='button'][text()='自動売買あり']");
    AppSetting.takeScreenshot('1701_Test_Item_4_EasyScreen_Setting.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 5 Update auto Interval Setting', ({ I }) => {
    AppSetting.clickItem(locators.automaticUpdateInterval);
    // Display automatic Update Interval Setting modal
    I.waitForText('自動更新間隔', 3, 'body');
    I.seeElement(locators.automaticUpdateIntervalSelection);
    AppSetting.takeScreenshot('1701_Test_Item_5_Update_auto_Interval_Setting.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 52 Order Priority Method Setting', ({ I }) => {
    AppSetting.clickItem(locators.priorityOrderMethodSettingFutures);
    // Display Order Priority Method Setting modal
    I.waitForText('優先発注方法設定(先物)', 3, 'body');
    I.seeElement(locators.priorityOrderMethodFuture);
    AppSetting.takeScreenshot('1701_Test_Item_52_Order_Priority_Method_Setting.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 38 Contact Settings', async ({ I }) => {
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user70 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await AppSetting.goToPage();
    await AppSetting.clickItem(locators.emailNotificationSettings);
    // Go to the following URL: {member-site-url}/ap/iPhone/personal/contact/List
    const contactSettings = '//*[@data-testid="appSetting_contactSetting_id"]//span[contains(text(), "こちら")]';
    await common.clickCardItem(contactSettings, '/ap/iPhone/personal/contact/List', 'kcMemberSite');
});

Scenario('Test item 11 Execution Invalid Noti', async () => {
    await AppSetting.goToPage();
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Execution・Invalid Noti area
    AppSetting.clickItem(locators.executionAndExpiration);
    AppSetting.takeScreenshot('1701_Test_Item_11_Execution_Invalid_Noti_1.png');

    // Click title link
    await AppSetting.clickExternalUrl("//a[text()='株式約定通知']", locators.notificationDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_11_Execution_Invalid_Noti_2.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.stockExecutionNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_11_Execution_Invalid_Noti_3.png');
    await AppSetting.clickCheckbox(locators.stockExecutionNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_11_Execution_Invalid_Noti_4.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 12 Kabucall related', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Kabucall related area
    AppSetting.clickItem(locators.kabuRelated);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_1.png');

    // Click Kabucall Noti - title
    await AppSetting.clickExternalUrl("//a[text()='カブコール通知']", locators.kabucallUrl);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_2.png');

    // Click Investment trust Kabucall expire Noti - Title
    await AppSetting.clickExternalUrl("//a[text()='投信カブコール期限切れ通知']", locators.kabucallToushinUrl);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_3.png');

    // Click Symbol Report Noti - Title
    await AppSetting.clickExternalUrl("//a[text()='銘柄レポート通知']", locators.meigaraReportUrl);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_4.png');

    // Click Deposit asset Noti - Title
    await AppSetting.clickExternalUrl("//a[text()='預り資産目標達成通知']", locators.assetDepositDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_5.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.kabuNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_6.png');
    await AppSetting.clickCheckbox(locators.kabuNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_12_Kabucall_related_7.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 46 KabuCall Notification - KabuCall Settings', async ({ I }) => {
    await AppSetting.goToPage();
    await AppSetting.clickItem(locators.emailNotificationSettings);
    await AppSetting.clickItem(locators.kabuRelated);
    const kabuCallSettings = `${locators.kabuRelated}//span[contains(text(), "こちら")]`;
    // Display the following URL in a separate tab {member-site-url}/ap/PC/Notify/KabuCall/Stock/Input
    await common.clickCardItem(kabuCallSettings, '/ap/PC/Notify/KabuCall/Stock/Input', 'external');
    await I.switchToWeb();
    await I.waitFor();
    await AppSetting.closeBottomSheet();
});

Scenario('Test item 47 Stock report notification - stock specification', async ({ I }) => {
    await AppSetting.goToPage();
    await AppSetting.clickItem(locators.emailNotificationSettings);
    await AppSetting.clickItem(locators.kabuRelated);
    const stockSpecification = `//p[@data-testid="settingAppCell_stockReportNoti_id"]//span[contains(text(), "こちら")]`;
    // Go to the following URL: {member-site-url}/Members/TradeTool/KabuNews/Notify/KabuNewsSet1.asp
    await common.clickCardItem(stockSpecification, '/Members/TradeTool/KabuNews/Notify/KabuNewsSet1.asp', 'external');
    await I.switchToWeb();
    await I.waitFor();
    await AppSetting.closeBottomSheet();
});

Scenario('Test item 48 Notification of reaching the target for deposited assets - KabuCall settings', async ({ I }) => {
    await AppSetting.goToPage();
    await AppSetting.clickItem(locators.emailNotificationSettings);
    await AppSetting.clickItem(locators.kabuRelated);
    const depositedAssets = `//p[@data-testid="settingAppCell_assetCustodyTargetAchievementNoti_id"]//span[contains(text(), "こちら")]`;
    // Go to the following URL: {member-site-url}/Members/TradeTool/KabuAsset/KA01101.asp
    await common.clickCardItem(depositedAssets, '/Members/TradeTool/KabuAsset/KA01101.asp', 'external');
    await I.switchToWeb();
    await I.waitFor();
    await AppSetting.closeBottomSheet();
});

Scenario('Test item 13 Future Option related', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Future Option related area
    AppSetting.clickItem(locators.futureOptionRelated);
    AppSetting.takeScreenshot('1701_Test_Item_13_Future_Option_related_1.png');

    // Click title link
    await AppSetting.clickExternalUrl("//a[text()='日経平均・TOPIX変動通知']", locators.notificationDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_13_Future_Option_related_2.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.futureOPAlertNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_13_Future_Option_related_3.png');
    await AppSetting.clickCheckbox(locators.futureOPAlertNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_13_Future_Option_related_4.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 14 Deposit and withdrawal related', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Deposit and withdrawal related area
    AppSetting.clickItem(locators.depositAndWithdrawRelated);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_1.png');

    // Click Rental fee payment link
    await AppSetting.clickExternalUrl("//a[text()='貸借料支払通知']", locators.stockLendingDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_2.png');

    // Click Dividend payment Noti link
    await AppSetting.clickExternalUrl("//a[text()='配当金入金通知']", locators.haitouUketoriDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_3.png');

    // Click Deposit Noti link
    await AppSetting.clickExternalUrl("//a[text()='入金通知']", locators.paymentDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_4.png');

    // Click Withdrawal Noti link
    await AppSetting.clickExternalUrl("//a[text()='出金通知']", locators.cashoutDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_5.png');

    // Click Withdrawal Noti link
    await AppSetting.clickExternalUrl("//a[text()='自動引落エラー通知']", locators.paymentDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_6.png');

    // Click Real-time account transfer Noti link
    await AppSetting.clickExternalUrl(
        "//a[text()='リアルタイム口座振替エラー通知']",
        locators.realtimeTransferDefaultUrl,
    );
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_7.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.rentalFeePaymentNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_8.png');
    await AppSetting.clickCheckbox(locators.rentalFeePaymentNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_14_Deposit_withdrawal_related_9.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 15 Reserve related', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Reserve related area
    AppSetting.clickItem(locators.fundingRelated);
    AppSetting.takeScreenshot('1701_Test_Item_15_Reserve_related_1.png');

    // Click Investment trust buy Noti link
    await AppSetting.clickExternalUrl("//a[text()='プレミアム積立®(投信)買付通知']", locators.kabuFundUrl);
    AppSetting.takeScreenshot('1701_Test_Item_15_Reserve_related_2.png');

    // Click Petit buy Noti link
    await AppSetting.clickExternalUrl("//a[text()='プレミアム積立®(プチ株®)買付通知']", locators.tsumitateDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_15_Reserve_related_3.png');

    // Click Foreign currency par valueMMF buy Noti link
    await AppSetting.clickExternalUrl("//a[text()='積立(外貨建MMF)買付通知']", locators.multiCurMmfDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_15_Reserve_related_4.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.premiumReserveInvestmentTrustPurchaseNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_15_Reserve_related_5.png');
    await AppSetting.clickCheckbox(locators.premiumReserveInvestmentTrustPurchaseNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_15_Reserve_related_6.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 16 Credit related', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Credit related area
    AppSetting.clickItem(locators.bondRelated);
    AppSetting.takeScreenshot('1701_Test_Item_16_Credit_related_1.png');

    // Click Investment trust buy Noti link
    await AppSetting.clickExternalUrl("//a[text()='債券利払・償還通知']", locators.bondDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_16_Credit_related_2.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.bondInterestPaymentRedemptionNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_16_Credit_related_3.png');
    await AppSetting.clickCheckbox(locators.bondInterestPaymentRedemptionNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_16_Credit_related_4.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 17 Other transactions services', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    // Display Other transactions services area
    AppSetting.clickItem(locators.otherTransactionAndService);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_1.png');

    // Click Trading caution issues Noti
    await AppSetting.clickExternalUrl("//a[text()='取引注意銘柄通知']", locators.notificationDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_2.png');

    // Click New Public stock/Public offering
    await AppSetting.clickExternalUrl("//a[text()='新規公開株/公募・売出(らくらくBB)通知']", locators.ipoPoDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_3.png');

    // Click Electronic delivery Noti
    await AppSetting.clickExternalUrl("//a[text()='電子交付通知']", locators.rakurakuDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_4.png');

    // Click Investment Trust Management Report Noti
    await AppSetting.clickExternalUrl("//a[text()='投信運用報告書通知']", locators.rakurakuDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_5.png');

    // Click Warehousing procedure completion Noti
    await AppSetting.clickExternalUrl("//a[text()='入庫手続き完了通知']", locators.stockTransferDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_6.png');

    // Click General margin lottery result Noti
    await AppSetting.clickExternalUrl("//a[text()='一般信用売抽選結果通知']", locators.shinyoDefaultUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_7.png');

    // Click IR news
    await AppSetting.clickExternalUrl("//a[text()='IR情報配信']", locators.irServiceUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_8.png');

    // Click New report Noti
    await AppSetting.clickExternalUrl("//a[text()='新着レポート一覧通知']", locators.meigaraReportUrl);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_9.png');

    // Toggle checkbox
    await AppSetting.clickCheckbox(locators.transactionCautionStockNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_10.png');
    await AppSetting.clickCheckbox(locators.transactionCautionStockNotiCheckbox);
    AppSetting.takeScreenshot('1701_Test_Item_17_Other_transactions_services_11.png');
    AppSetting.closeBottomSheet();
});

Scenario('Test item 9 Button Confirm Mail Notification', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    AppSetting.takeScreenshot('1701_Test_Item_9_Button_Confirm_Mail_Notification_1.png');
    AppSetting.clickItem(locators.confirmBtn);
    AppSetting.takeScreenshot('1701_Test_Item_9_Button_Confirm_Mail_Notification_2.png');
});

Scenario('Test item 10 Button Cancel Mail Notification', async () => {
    AppSetting.clickItem(locators.emailNotificationSettings);
    AppSetting.takeScreenshot('1701_Test_Item_10_Button_Cancel_Mail_Notification_1.png');
    AppSetting.clickItem(locators.cancelBtn);
    AppSetting.takeScreenshot('1701_Test_Item_10_Button_Cancel_Mail_Notification_2.png');
});

Scenario('Test item 26 Priority Order Method', async () => {
    await AppSetting.clickItem(locators.priorityOrderingMethodSettings);
    await AppSetting.clickItem(locators.priorityOrderMarket);
    AppSetting.takeScreenshot('1701_Test_Item_26_Priority_Order_Method_1.png');
    await AppSetting.clickItem(locators.priorityOrderMethod);
    AppSetting.takeScreenshot('1701_Test_Item_26_Priority_Order_Method_2.png');
});

Scenario('Test item 28 Button Confirm Priority Order', async () => {
    AppSetting.takeScreenshot('1701_Test_Item_28_Button_Confirm_Priority_Order_1.png');
    AppSetting.clickItem(locators.confirmBtn);
    AppSetting.takeScreenshot('1701_Test_Item_28_Button_Confirm_Priority_Order_2.png');
});

Scenario('Test item 29 Button Cancel Priority Order', async () => {
    await AppSetting.clickItem(locators.priorityOrderingMethodSettings);
    AppSetting.takeScreenshot('1701_Test_Item_29_Button_Cancel_Priority_Order_1.png');
    AppSetting.clickItem(locators.cancelBtn);
    AppSetting.takeScreenshot('1701_Test_Item_29_Button_Cancel_Priority_Order_2.png');
});

Scenario('Test item 32 Button Confirm Easy Setting', async () => {
    await AppSetting.clickItem(locators.simpleScreenSettings);
    AppSetting.takeScreenshot('1701_Test_Item_32_Button_Confirm_Easy_Setting_1.png');
    AppSetting.clickItem(locators.confirmBtn);
    AppSetting.takeScreenshot('1701_Test_Item_32_Button_Confirm_Easy_Setting_2.png');
});

Scenario('Test item 33 Button Cancel Easy Setting', async () => {
    await AppSetting.clickItem(locators.simpleScreenSettings);
    AppSetting.takeScreenshot('1701_Test_Item_33_Button_Cancel_Easy_Setting_1.png');
    AppSetting.clickItem(locators.cancelBtn);
    AppSetting.takeScreenshot('1701_Test_Item_33_Button_Cancel_Easy_Setting_2.png');
});

Scenario('Test item 34 Automatic Update Interval Selection', async ({ I }) => {
    await AppSetting.clickItem(locators.automaticUpdateInterval);
    const listSelectionItem = await I.grabNumberOfVisibleElements(`${locators.automaticUpdateIntervalSelection}/label`);
    for (let i = 1; i <= listSelectionItem; i++) {
        AppSetting.clickItem(`${locators.automaticUpdateIntervalSelection}/label[${i}]`);
        AppSetting.takeScreenshot(`1701_Test_Item_34_Automatic_Update_Interval_Selection_${i}.png`);
    }
});

Scenario('Test item 35 Button Confirm Automatic Update Interval Setting', async () => {
    AppSetting.takeScreenshot('1701_Test_Item_35_Button_Confirm_Automatic_Update_Interval_Setting_1.png');
    AppSetting.clickItem(locators.confirmBtn);
    AppSetting.takeScreenshot('1701_Test_Item_35_Button_Confirm_Automatic_Update_Interval_Setting_2.png');
});

Scenario('Test item 36 Button Cancel Automatic Update Interval Setting', async () => {
    await AppSetting.clickItem(locators.automaticUpdateInterval);
    AppSetting.takeScreenshot('1701_Test_Item_36_Button_Cancel_Automatic_Update_Interval_Setting_1.png');
    AppSetting.clickItem(locators.cancelBtn);
    AppSetting.takeScreenshot('1701_Test_Item_36_Button_Cancel_Automatic_Update_Interval_Setting_2.png');
});

Scenario('Test item 53 Priority Order Method Future', async () => {
    await AppSetting.clickItem(locators.priorityOrderMethodSettingFutures);
    await AppSetting.clickItem(locators.priorityOrderMarketFuture);
    AppSetting.takeScreenshot('1701_Test_Item_53_Priority_Order_Method_Future_1.png');
    await AppSetting.clickItem(locators.priorityOrderMethodFuture);
    AppSetting.takeScreenshot('1701_Test_Item_53_Priority_Order_Method_Future_2.png');
});

Scenario('Test item 54 Button Confirm Priority Order Future', async () => {
    AppSetting.takeScreenshot('1701_Test_Item_54_Button_Confirm_Priority_Order_Future_1.png');
    AppSetting.clickItem(locators.confirmBtn);
    AppSetting.takeScreenshot('1701_Test_Item_54_Button_Confirm_Priority_Order_Future_2.png');
});

Scenario('Test item 55 Button Cancel Priority Order Future', async () => {
    await AppSetting.clickItem(locators.priorityOrderMethodSettingFutures);
    AppSetting.takeScreenshot('1701_Test_Item_55_Button_Cancel_Priority_Order_Future_1.png');
    AppSetting.clickItem(locators.cancelBtn);
    AppSetting.takeScreenshot('1701_Test_Item_55_Button_Cancel_Priority_Order_Future_2.png');
});

Scenario('Test item 50 Settings can be found here', async ({ I }) => {
    await AppSetting.goToPage();
    const marketRelated = '//*[@data-testid="settingAppCell_marketRelated_accordion_id"]';
    const settingKabucomId = '//*[@data-testid="settingAppCell_kabucom_id"]';
    await AppSetting.clickItem(locators.pushNotificationSettings);
    await AppSetting.clickItem(marketRelated);
    // Display the following URL in a new tab: {member-site-url}/ap/PC/Notify/KabuCall/Stock/Input
    await common.clickCardItem(settingKabucomId, '/ap/PC/Notify/KabuCall/Stock/Input', 'external');
    await I.switchToWeb();
    await I.waitFor();
    await AppSetting.closeRightModal();
});

Scenario('Test Push notification settings screen - each notification setting', async ({ I }) => {
    await AppSetting.goToPage();
    const favoriteRegistrationHoldingStockToggle = '//*[@data-testid="settingAppCell_favoriteRegistrationHoldingStock_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const transactionRelatedToggle = '//*[@data-testid="settingAppCell_transactionRelated_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const noticeAccordionToggle = '//*[@data-testid="settingAppCell_notice_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const marketRelatedToggle = '//*[@data-testid="settingAppCell_marketRelated_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const systemRelatedToggle = '//*[@data-testid="settingAppCell_systemRelated_accordion_id"]//label[input[contains(@type, "checkbox")]]';

    await AppSetting.clickItem(locators.pushNotificationSettings);
    // favoriteRegistrationHoldingStock
    await AppSetting.clickItem(favoriteRegistrationHoldingStockToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_favorite_registration_holding_stock_toggle_the_button_off.png');
    await AppSetting.clickItem(favoriteRegistrationHoldingStockToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_favorite_registration_holding_stock_toggle_the_button_on.png');
    // transactionRelated
    await AppSetting.clickItem(transactionRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_transaction_related_toggle_the_button_off.png');
    await AppSetting.clickItem(transactionRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_transaction_related_toggle_the_button_on.png');
    // noticeAccordion
    await AppSetting.clickItem(noticeAccordionToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_notice_toggle_the_button_off.png');
    await AppSetting.clickItem(noticeAccordionToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_notice_toggle_the_button_on.png');
    // marketRelated
    await AppSetting.clickItem(marketRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_market_related_toggle_the_button_off.png');
    await AppSetting.clickItem(marketRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_market_related_toggle_the_button_on.png');
    // systemRelated
    await AppSetting.clickItem(systemRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_system_related_toggle_the_button_on.png');
    await AppSetting.clickItem(systemRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_system_related_toggle_the_button_off.png');
    await AppSetting.closeRightModal();
});

Scenario('Test Push notification settings screen - bulk notification settings', async ({ I }) => {
    await AppSetting.goToPage();
    // Expand/Collapse area
    const favoriteRegistrationHoldingStock = '//*[@data-testid="settingAppCell_favoriteRegistrationHoldingStock_accordion_id"]';
    const transactionRelated = '//*[@data-testid="settingAppCell_transactionRelated_accordion_id"]';
    const noticeAccordion = '//*[@data-testid="settingAppCell_notice_accordion_id"]';
    const marketRelated = '//*[@data-testid="settingAppCell_marketRelated_accordion_id"]';
    const systemRelated = '//*[@data-testid="settingAppCell_systemRelated_accordion_id"]';
    // Toggle
    const favoriteRegistrationHoldingStockToggle = '//*[@data-testid="settingAppCell_favoriteRegistrationHoldingStock_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const transactionRelatedToggle = '//*[@data-testid="settingAppCell_transactionRelated_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const noticeAccordionToggle = '//*[@data-testid="settingAppCell_notice_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const marketRelatedToggle = '//*[@data-testid="settingAppCell_marketRelated_accordion_id"]//label[input[contains(@type, "checkbox")]]';
    const systemRelatedToggle = '//*[@data-testid="settingAppCell_systemRelated_accordion_id"]//label[input[contains(@type, "checkbox")]]';

    await AppSetting.clickItem(locators.pushNotificationSettings);
    // favoriteRegistrationHoldingStock
    await I.scrollToElement(favoriteRegistrationHoldingStock);
    await AppSetting.clickItem(favoriteRegistrationHoldingStock);
    await AppSetting.clickItem(favoriteRegistrationHoldingStockToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_favorite_registration_holding_stock_toggle_the_button_off.png');
    await AppSetting.clickItem(favoriteRegistrationHoldingStockToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_favorite_registration_holding_stock_toggle_the_button_on.png');
    // transactionRelated
    await I.scrollToElement(transactionRelated);
    await AppSetting.clickItem(transactionRelated);
    await AppSetting.clickItem(transactionRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_transaction_related_toggle_the_button_off.png');
    await AppSetting.clickItem(transactionRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_transaction_related_toggle_the_button_on.png');
    // noticeAccordion
    await I.scrollToElement(noticeAccordion);
    await AppSetting.clickItem(noticeAccordion);
    await AppSetting.clickItem(noticeAccordionToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_notice_toggle_the_button_off.png');
    await AppSetting.clickItem(noticeAccordionToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_notice_toggle_the_button_on.png');
    // marketRelated
    await I.scrollToElement(marketRelated);
    await AppSetting.clickItem(marketRelated);
    await AppSetting.clickItem(marketRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_market_related_toggle_the_button_off.png');
    await AppSetting.clickItem(marketRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_market_related_toggle_the_button_on.png');
    // systemRelated
    await I.scrollToElement(systemRelated);
    await AppSetting.clickItem(systemRelated);
    await AppSetting.clickItem(systemRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_system_related_toggle_the_button_on.png');
    await AppSetting.clickItem(systemRelatedToggle);
    await AppSetting.takeScreenshot('1701_Test_Push_notification_settings_screen_bulk_system_related_toggle_the_button_off.png');
    await AppSetting.closeRightModal();
});

Scenario('Test item 18 Button Confirm Push Noti Setting', async () => {
    await AppSetting.clickItem(locators.pushNotificationSettings);
    AppSetting.takeScreenshot('1701_Test_Item_18 Button_Confirm_Push_Noti_Setting_1.png');
    AppSetting.clickItem(locators.confirmBtn);
    AppSetting.takeScreenshot('1701_Test_Item_18 Button_Confirm_Push_Noti_Setting_2.png');
});

Scenario('Test item 19 Button Cancel Push Noti Setting', async () => {
    await AppSetting.clickItem(locators.pushNotificationSettings);
    AppSetting.takeScreenshot('1701_Test_Item_18 Button_Cancel_Push_Noti_Setting_1.png');
    AppSetting.clickItem(locators.cancelBtn);
    AppSetting.takeScreenshot('1701_Test_Item_18 Button_Cancel_Push_Noti_Setting_2.png');
});
