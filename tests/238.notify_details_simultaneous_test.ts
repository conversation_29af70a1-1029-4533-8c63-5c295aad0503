import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Notify - NotifyDetailsSimultaneousPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyDetailsSimultaneous.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await <PERSON><PERSON>switchToNative();
});

Scenario('test notify details simultaneous page', async ({ I }) => {
    I.waitForElement('//*[@data-testid="common_noti_id"]');
    I.click('//*[@data-testid="common_noti_id"]');
    await I.waitFor('mediumWait');
    I.saveScreenshot('238.notify_details_simultaneous_notifyTab.png');

    const newInformationTab = '//*[@data-testid="noticeTab_newInformation_id"]';
    I.waitForElement(newInformationTab);
    I.click(newInformationTab);
    await I.waitFor();
    I.saveScreenshot('238.notify_details_simultaneous_newInformationTab.png');

    const notiItem = '//*[@data-testid="notifyWholeList_notiItem_id_0"]';
    I.waitForElement(notiItem);
    I.tapLocationOfElement(notiItem);
    await I.waitFor();
    I.saveScreenshot('238.notify_details_simultaneous_notiItem.png');
    const currentUrl = await I.grabCurrentUrl();
    I.say(currentUrl);
    I.assertContain(currentUrl, 'notice/general/detail');
});

Scenario('test Item 7 Link title page', async ({ I }) => {
    await I.saveScreenshot('238.notify_details_simultaneous_notiItem7.png');
    const url = '/ap2/iphone/Personal/ISAMoushikomi/Confirm?req=1';
    const aElement = '//*[@id="__next"]/div/div[2]/div[1]//a';
    const aUrl = await I.grabAttributeFrom(aElement, 'href');
    I.assertContain(aUrl, url, 'aUrl is not equal to url');
});

Scenario('test Item 5 prevNoti, 6 nextNoti page', async ({ I }) => {
    I.saveScreenshot('238.notify_details_simultaneous_notiItem56.png');
    const nextNoti = '//*[@data-testid="notifyDetailsSimultaneous_nextNoti_id"]';
    const prevNoti = '//*[@data-testid="notifyDetailsSimultaneous_previousNoti_id"]';
    I.waitForElement(nextNoti);
    I.dontSeeElement(prevNoti);
    I.click(nextNoti);
    await I.waitFor();
    I.saveScreenshot('238.notify_details_simultaneous_notiItem6.png');
    I.waitForElement(prevNoti);
    I.click(prevNoti);
    await I.waitFor();
    I.dontSeeElement(prevNoti);
});
