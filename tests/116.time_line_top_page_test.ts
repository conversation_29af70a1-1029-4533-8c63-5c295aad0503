import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Timeline - TimeLineTopPage');

Before(async ({ I, loginAndSwitchToWebAs, timeLine }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user72 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    // Navigate to Time Line Top page
    await timeLine.goToTimeLineTop();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Time Line Top page', async ({ I }) => {
    await I.saveScreenshot('116.time_line_top_page_Test_Display_Timeline_Top_page.png');
});

Scenario('Test item No.1 Setting', async ({ I }) => {
    // Screenshot initial layout
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.1_Initial_Layout.png');

    // Open modal
    const settingBtn = '//*[@data-testid="timeLineTop_setting_id"]';
    const closeModalBtn = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(settingBtn);
    await I.clickFixed(settingBtn);
    await I.waitFor();
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.1_Setting_Modal.png');
    I.seeElement(closeModalBtn);
    I.waitForText('タイムライン配信設定', 3, 'body');

    // Close modal
    I.waitForVisible(closeModalBtn, 3);
    await I.clickFixed(closeModalBtn);
    await I.waitFor();
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.1_After_Close_Modal.png');
    I.dontSeeElement(closeModalBtn);
    I.waitForText('タイムライン', 3, 'body');
    I.seeElement(settingBtn);
});

Scenario('Test item No.7 Timeline Card', async ({ I }) => {
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.7_Timeline_Cards.png');
    const cardItem = '//*[@data-testid="timeLineTop_timelineCard_id_0"]';
    I.waitForElement(cardItem);
    await I.clickFixed(cardItem);
    await I.waitFor();

    // Compare current url
    const currentUrl = await I.grabCurrentUrl();
    await I.waitFor('mediumWait');
    I.assertContain(currentUrl, '/mobile/timeline/detail', 'URL is not matching');
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.7_Timeline_Detail.png');

    await I.waitFor();
    const backHeader = '//*[@data-testid="common_back_id"]';
    I.waitForElement(backHeader);
    await I.clickFixed(backHeader);
    await I.waitFor();
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.7_Back_To_Timeline.png');
    I.waitForText('タイムライン', 3, 'body');
    I.seeElement('//*[@data-testid="timeLineTop_setting_id"]');
});

Scenario('Test item No.5 Scroll to top', async ({ I }) => {
    const timeLineList = '//*[@data-testid="timeLineTop_timelineList_id"]';
    I.waitForElement(timeLineList);
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.5_Before_Scroll.png');
    I.swipeUpFixed(timeLineList);
    await I.waitFor('longWait');
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.5_After_Scroll_Down.png');
    I.waitForElement('//button[@aria-label="scroll-to-top-btn"]');
    await I.clickFixed('//button[@aria-label="scroll-to-top-btn"]');
    await I.waitFor();
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.5_After_Scroll_Top.png');
});
Scenario('Test item No.6 Load more Timeline List lazy load', async ({ I, timeLine }) => {
    await timeLine.goToTimeLineTop();
    await I.waitFor('mediumWait');
    const timeLineList = '//*[@data-testid="timeLineTop_timelineList_id"]';
    I.waitForElement(timeLineList);
    // Screenshot before scrolling
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.6_Initial_List.png');
    // Get initial items count
    const initialItems = await I.grabNumberOfVisibleElements(`${timeLineList}//*[@data-testid="timeLineTop_timelineCard_id"]`);
    console.log('Initial items:', initialItems);
    // Scroll multiple times to ensure trigger load more
    for (let i = 0; i < 3; i++) {
        I.swipeUpFixed(timeLineList);
        await I.waitFor();
        // Screenshot after each scroll
        await I.saveScreenshot(`116.time_line_top_page_Test_item_No.6_After_Scroll_${i + 1}.png`);
    }
    // Wait for API load to complete
    await I.waitFor('longWait');
    // Check items count after scroll
    const afterScrollItems = await I.grabNumberOfVisibleElements(`${timeLineList}//*[@data-testid="timeLineTop_timelineCard_id"]`);
    // Screenshot after loading is complete
    await I.saveScreenshot('116.time_line_top_page_Test_item_No.6_After_Load_Complete.png');
    if (afterScrollItems > initialItems) {
        const lastItem = `${timeLineList}//*[@data-testid="timeLineTop_timelineCard_id_${afterScrollItems - 1}"]`;
        I.waitForElement(lastItem);
        await I.clickFixed(lastItem);
        await I.waitFor();
        // Screenshot of the last item's detail page
        await I.saveScreenshot('116.time_line_top_page_Test_item_No.6_Last_Item_Detail.png');
        const currentUrl = await I.grabCurrentUrl();
        I.assertContain(currentUrl, '/mobile/timeline/detail', 'URL is not matching');
    } else {
        // If there are no new items -> click first item to verify still clickable
        const firstItem = `${timeLineList}//*[@data-testid="timeLineTop_timelineCard_id_0"]`;
        I.waitForElement(firstItem);
        await I.clickFixed(firstItem);
        await I.waitFor();
        // Screenshot of the first item's detail page
        await I.saveScreenshot('116.time_line_top_page_Test_item_No.6_First_Item_Detail.png');
        const currentUrl = await I.grabCurrentUrl();
        I.assertContain(currentUrl, '/mobile/timeline/detail', 'URL is not matching');
    }
});
