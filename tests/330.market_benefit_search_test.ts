import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Feature('Market - BenefitSearch');

Before(async ({ I, loginAndSwitchToWebAs, market }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user14 });
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.waitFor();
    await market.goToShareholderBenefitsSearch();
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Market Benefit Search page', async ({ I }) => {
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Display_Benefit_Search_page.png`);
});

Scenario('Test item No.2 Tap search box', async ({ I }) => {
    await I.clickFixed('//*[@data-testid="benefitSearch_searchBox_id"]/input');
    await I.waitFor();
    I.assertTrue(await I.isKeyboardShown(), 'The keyboard must be shown');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.2_Tap_search_box.png`);
});

Scenario('Test item No.2-1 Close input keyboard', async ({ I }) => {
    await I.clickFixed('//*[@data-testid="benefitSearch_searchBox_id"]/input');
    await I.waitFor();
    I.assertTrue(await I.isKeyboardShown(), 'The keyboard must be shown');
    await I.clickFixed('//*[@data-testid="benefitSearch_benefitCategory_id"]');
    await I.waitFor();
    I.assertFalse(await I.isKeyboardShown(), 'The keyboard must be hide');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.2-1_Close_input_keyboard.png`);
});

Scenario('Test item No.3 Special Offers Category', async ({ I }) => {
    const benefitCategoryAllSelector = '//*[@data-testid="benefitSearch_benefitCategoryAll_id"]';
    await I.clickFixed('//*[@data-testid="benefitSearch_benefitCategory_id"]');
    await I.waitFor();
    I.seeElement(benefitCategoryAllSelector);
    I.see('すべて', benefitCategoryAllSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.3_Display_Special_Offers_Category_panel.png`);
});

Scenario('Test item No.4 Entitlement Month', async ({ I }) => {
    const specialBenefitMonthAllSelector = '//*[@data-testid="benefitSearch_specialBenefitMonthAll_id"]';
    await I.clickFixed('//*[@data-testid="benefitSearch_specialMonthAccordion_id"]');
    await I.waitFor();
    I.seeElement(specialBenefitMonthAllSelector);
    I.see('すべて', specialBenefitMonthAllSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.4_Display_Entitlement_Month_panel.png`);
});

Scenario('Test item No.5 Required investment amount - Lower limit', async ({ I, market }) => {
    const lowerLimitSelector = '//*[@data-testid="benefitSearch_lowerLimit_id"]';
    const dropdownSelector = '//*[@data-testid="common_MenuList_id"][(contains(@style, "visibility: visible"))]';
    const lowerLimitItemSelector = `${dropdownSelector}//*[@data-testid="common_MenuItem_id"]`;
    I.waitForElement(lowerLimitSelector);
    await market.handleDropdownInteraction(lowerLimitSelector, dropdownSelector, lowerLimitItemSelector, [], `${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.5_Required_investment_amount_Lower_limit`);
});

Scenario('Test item No.6 Required investment amount - upper limit', async ({ I, market }) => {
    const upperLimitSelector = '//*[@data-testid="benefitSearch_upperLimit_id"]';
    const dropdownSelector = '//*[@data-testid="common_MenuList_id"][(contains(@style, "visibility: visible"))]';
    const upperLimitItemSelector = `${dropdownSelector}//*[@data-testid="common_MenuItem_id"]`;
    I.waitForElement(upperLimitSelector);
    await market.handleDropdownInteraction(upperLimitSelector, dropdownSelector, upperLimitItemSelector, [], `${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.6_Required_investment_amount_upper_limit`);
});

Scenario('Test item No.7 Short selling possible', async ({ I }) => {
    const shortOption = '//*[@data-testid="benefitSearch_availableSale_id_MARGIN_TRADE_TYPE_COMPANY_SHORT"]';
    const longOption = '//*[@data-testid="benefitSearch_availableSale_id_MARGIN_TRADE_TYPE_COMPANY_LONG"]';
    const noneOption = '//*[@data-testid="benefitSearch_availableSale_id_MARGIN_TRADE_TYPE_NONE"]';
    I.waitForElement(shortOption);
    await I.clickFixed(shortOption);
    await I.waitFor();
    const backgroundShortOption = await I.grabCssPropertyFrom(`${shortOption}/div/div`, 'background-color');
    I.assertEqual(backgroundShortOption, 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.7_Short_selling_possible_selected_short_option.png`);
    I.waitForElement(longOption);
    await I.clickFixed(longOption);
    const backgroundLongOption = await I.grabCssPropertyFrom(`${longOption}/div/div`, 'background-color');
    I.assertEqual(backgroundLongOption, 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.7_Short_selling_possible_selected_long_option.png`);
    I.waitForElement(noneOption);
    await I.clickFixed(noneOption);
    const backgroundNoneOption = await I.grabCssPropertyFrom(`${noneOption}/div/div`, 'background-color');
    I.assertEqual(backgroundNoneOption, 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.7_Short_selling_possible_selected_none_option.png`);
});

Scenario('Test item No.8 Estimated Dividend Yield', async ({ market }) => {
    const dividendYieldSelector = '//*[@data-testid="benefitSearch_dividendYield_id"]';
    const dropdownSelector = '//*[@data-testid="common_MenuList_id"][(contains(@style, "visibility: visible"))]';
    const dividendYieldItemSelector = `${dropdownSelector}//*[@data-testid="common_MenuItem_id"]`;
    await market.handleDropdownInteraction(dividendYieldSelector, dropdownSelector, dividendYieldItemSelector, [], `${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.8_Estimated_Dividend_Yield`);
});

Scenario('Test item No.9 Load saved search conditions', async ({ I }) => {
    const searchButtonSelector = '//*[@data-testid="benefitSearch_search_id"]';
    const saveConditionButtonSelector = '//*[@data-testid="benefitSearchResult_saveCondition_id"]';
    const searchAgainButtonSelector = '//*[@data-testid="benefitSearchResult_searchAgain_id"]';
    const loadConditionButtonSelector = '//*[@data-testid="benefitSearch_loadCondition_id"]';
    const benefitCategorySelector = '//*[@data-testid="benefitSearch_benefitCategory_id"]';
    const specialMonthAccordionSelector = '//*[@data-testid="benefitSearch_specialMonthAccordion_id"]';
    const arrowUpSelector = '//*[@data-testid="common_dropDown_arrow_id_up"]';
    await I.clickFixed(searchButtonSelector);
    await I.waitFor();
    I.waitForElement(saveConditionButtonSelector);
    await I.clickFixed(saveConditionButtonSelector);
    await I.waitFor();
    await I.clickFixed(searchAgainButtonSelector);
    await I.waitFor();
    I.waitForElement(loadConditionButtonSelector);
    await I.clickFixed(loadConditionButtonSelector);
    // Display loading notification toast
    I.see('保存した検索条件を読み込みました。', 'body');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.9_Load_saved_search_conditions_Display_loading_notification_toast.png`);
    await I.swipeDownFixed('body');
    // Open preferential category accordion
    I.seeElement(`${benefitCategorySelector}${arrowUpSelector}`);
    I.see('すべて', benefitCategorySelector);
    // Open entitlement month accordion
    I.seeElement(`${specialMonthAccordionSelector}${arrowUpSelector}`);
    I.see('すべて', specialMonthAccordionSelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.9_Load_saved_search_conditions_Open_preferential_category_and_entitlement_month_accordion.png`);
});

Scenario('Test item No.10 Search', async ({ I }) => {
    const searchButtonSelector = '//*[@data-testid="benefitSearch_search_id"]';
    const saveConditionButtonSelector = '//*[@data-testid="benefitSearchResult_saveCondition_id"]';
    const searchAgainButtonSelector = '//*[@data-testid="benefitSearchResult_searchAgain_id"]';
    await I.clickFixed(searchButtonSelector);
    await I.waitFor();
    I.seeElement(saveConditionButtonSelector);
    I.seeElement(searchAgainButtonSelector);
    I.assertContain(await I.grabCurrentUrl(), '/mobile/benefit/search/result', 'URL does not contain expected path');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.10_Search_go_to_benefit_search_result.png`);
});

Scenario('Test item No.11 Clear search conditions', async ({ I }) => {
    const loadConditionButtonSelector = '//*[@data-testid="benefitSearch_loadCondition_id"]';
    const clearSearchButtonSelector = '//*[@data-testid="benefitSearch_clearSearch_id"]';
    const benefitCategorySelector = '//*[@data-testid="benefitSearch_benefitCategory_id"]';
    const specialMonthAccordionSelector = '//*[@data-testid="benefitSearch_specialMonthAccordion_id"]';
    const arrowDownSelector = '//*[@data-testid="common_dropDown_arrow_id_down"]';
    I.waitForElement(loadConditionButtonSelector);
    await I.clickFixed(loadConditionButtonSelector);
    await I.waitFor();
    I.waitForElement(clearSearchButtonSelector);
    await I.clickFixed(clearSearchButtonSelector);
    await I.swipeDownFixed('body');
    // Close preferential category accordion
    I.dontSee('すべて', benefitCategorySelector);
    // Close entitlement month accordion
    I.seeElement(`${specialMonthAccordionSelector}${arrowDownSelector}`);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.11_Clear_search_conditions.png`);
});

Scenario('Test item No.14 Preferential Categories - All', async ({ I }) => {
    const benefitCategorySelector = '//*[@data-testid="benefitSearch_benefitCategory_id"]';
    const benefitCategoryAllSelector = '//*[@data-testid="benefitSearch_benefitCategoryAll_id"]';
    I.waitForElement(benefitCategorySelector);
    await I.clickFixed(benefitCategorySelector);
    I.waitForElement(benefitCategoryAllSelector);
    await I.clickFixed(benefitCategoryAllSelector);
    const categoryAllBackground = await I.grabCssPropertyFrom(benefitCategoryAllSelector, 'background-color');
    I.assertEqual(categoryAllBackground, 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.14_Select_Preferential_Categories_All.png`);
});

Scenario('Test item No.16 Special Offers Category - Page Control', async ({ I }) => {
    const benefitCategorySelector = '//*[@data-testid="benefitSearch_benefitCategory_id"]';
    const commonSwiperBullets = '//*[@data-testid="common_swiper_bullets_id"]';
    const firstSwiperButton = `//*[@data-testid="common_swiper_bullet_id_0"]`;
    const secondSwiperButton = `//*[@data-testid="common_swiper_bullet_id_1"]`;
    const thirdSwiperButton = `//*[@data-testid="common_swiper_bullet_id_2"]`;
    I.waitForElement(benefitCategorySelector);
    await I.clickFixed(benefitCategorySelector);
    I.waitForElement(commonSwiperBullets);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(firstSwiperButton, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.16_Special_Offers_Category_Page_control_1.png`);
    I.click(secondSwiperButton);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(secondSwiperButton, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.16_Special_Offers_Category_Page_control_2.png`);
    I.click(thirdSwiperButton);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(thirdSwiperButton, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.16_Special_Offers_Category_Page_control_3.png`);
});

Scenario('Test item No.18 Entitlement Month - All', async ({ I }) => {
    const specialMonthAccordionSelector = '//*[@data-testid="benefitSearch_specialMonthAccordion_id"]';
    const specialMonthAllSelector = '//*[@data-testid="benefitSearch_specialBenefitMonthAll_id"]';
    I.waitForElement(specialMonthAccordionSelector);
    await I.clickFixed(specialMonthAccordionSelector);
    I.waitForElement(specialMonthAllSelector);
    await I.clickFixed(specialMonthAllSelector);
    const specialMonthAllBackground = await I.grabCssPropertyFrom(specialMonthAllSelector, 'background-color');
    I.assertEqual(specialMonthAllBackground, 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.14_Select_Entitlement_Month_All.png`);
});

Scenario('Test item No.20 Entitlement Month - Page Control', async ({ I }) => {
    const specialMonthAccordionSelector = '//*[@data-testid="benefitSearch_specialMonthAccordion_id"]';
    I.waitForElement(specialMonthAccordionSelector);
    await I.clickFixed(specialMonthAccordionSelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.20_Entitlement_Month_Page_control_1.png`);
});

Scenario('Test item No.15 Preferential category - Other than all', async ({ I }) => {
    const benefitCategorySelector = '//*[@data-testid="benefitSearch_benefitCategory_id"]';
    const firstOptioCategorySelector = '//div[contains(text(), "飲食券・飲食 割引券")]';
    I.waitForElement(benefitCategorySelector);
    await I.clickFixed(benefitCategorySelector);
    await I.waitFor();
    await I.clickFixed(firstOptioCategorySelector);
    I.assertEqual(await I.grabCssPropertyFrom(firstOptioCategorySelector, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.15_Preferential_category_select_other_than_all.png`);
    await I.clickFixed(firstOptioCategorySelector);
    I.assertEqual(await I.grabCssPropertyFrom(firstOptioCategorySelector, 'background-color'), 'rgba(244, 244, 244, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.15_Preferential_category_unselect_other_than_all.png`);
});

Scenario('Test item No.15-1 Swipe left or right Preferential category', async ({ I }) => {
    const benefitCategorySelector = '//*[@data-testid="benefitSearch_benefitCategory_id"]';
    I.waitForElement(benefitCategorySelector);
    await I.clickFixed(benefitCategorySelector);
    await I.waitFor();
    await I.swipeLeftFixed(benefitCategorySelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.15-1_Swipe_left_Preferential_category.png`);
    await I.swipeRightFixed(benefitCategorySelector);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.15-1_Swipe_right_Preferential_category.png`);
});

Scenario('Test item No.19 Entitlement month - Other than all', async ({ I }) => {
    const specialMonthAccordionSelector = '//*[@data-testid="benefitSearch_specialMonthAccordion_id"]';
    const firstSpecialMonthAccordionSelector = '//div[contains(text(), "1月")]';
    I.waitForElement(specialMonthAccordionSelector);
    await I.clickFixed(specialMonthAccordionSelector);
    await I.waitFor();
    await I.clickFixed(firstSpecialMonthAccordionSelector);
    I.assertEqual(await I.grabCssPropertyFrom(firstSpecialMonthAccordionSelector, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.15_Entitlement_month_select_other_than_all.png`);
    await I.clickFixed(firstSpecialMonthAccordionSelector);
    I.assertEqual(await I.grabCssPropertyFrom(firstSpecialMonthAccordionSelector, 'background-color'), 'rgba(244, 244, 244, 1)', 'Background color is not equal');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitSearch}_Test_item_No.15_Entitlement_month_unselect_other_than_all.png`);
});