import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Market - IndicatorListPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user11 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Market Indicator List page', async ({ I, market }) => {
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    I.saveScreenshot('261.market_indicator_list_Test_Display_Market_Indicator_List_page.png');
});

Scenario('Test item No.1 Market tab info menu', async ({ I, market }) => {
    await market.clickMarketTabGroup();
    await <PERSON><PERSON>waitFor();
});

Scenario('Test item No.3 Edit button', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    const completeBtn = '//*[@data-testid="marketIndicatorList_complete_id"]';
    await market.clickEditButton();
    I.see("完了", completeBtn);
    I.saveScreenshot('261.market_indicator_list_Test_item_No.3_Edit_button_edit_mode.png');
});


Scenario('Test item No.4 Sorting', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    await I.waitFor('mediumWait');
    const sortButtonSelector = '//*[@data-testid="marketIndicatorList_sortSwitching_id"]';
    const allItemsSelector = '//*[@data-testid[starts-with(., "marketIndicatorList_listItem_id_")]]';

    const getFilteredDataTexts = async () => {
        const dataTexts = await I.grabTextFromAll({ xpath: `${allItemsSelector}//p` });
        return dataTexts
            .filter((value) => value.includes('%'))
            .map((item) => item.replace(/[%]/g, ''));
    };

    const verifySorting = (actual: string[], expected: string[], screenshotName: string) => {
        actual.forEach((item, index) => {
            I.assertEqual(item, expected[index], 'Item should match');
        });
        I.saveScreenshot(screenshotName);
    };

    const initialRatioDataTexts = await getFilteredDataTexts();
    const ascendingRatioDataTexts = [...initialRatioDataTexts].sort((a, b) => (isNaN(+a) ? 0 : 1) - (isNaN(+b) ? 0 : 1) || (+a - +b));
    const descendingRatioDataTexts = [...ascendingRatioDataTexts].reverse();

    I.waitForElement(sortButtonSelector);

    // Test ascending order
    I.click(sortButtonSelector);
    await I.waitFor();
    const dataTextsAfterSortAscending = await getFilteredDataTexts();
    verifySorting(dataTextsAfterSortAscending, ascendingRatioDataTexts, '261.market_indicator_list_Test_item_No.4_Sorting_ascending.png');

    // Test descending order
    I.click(sortButtonSelector);
    await I.waitFor();
    const dataTextsAfterSortDescending = await getFilteredDataTexts();
    verifySorting(dataTextsAfterSortDescending, descendingRatioDataTexts, '261.market_indicator_list_Test_item_No.4_Sorting_descending.png');
});

Scenario('Test item No.6 Scroll to top', async ({ I }) => {
    const indicatorList = '//*[@id="market-main-indicator"]';
    const scrollToTopButton = '//button[@aria-label="scroll-to-top-btn"]';

    const performScrollAndScreenshot = async (action: () => void, screenshotName: string) => {
        action();
        await I.waitFor();
        I.saveScreenshot(screenshotName);
    };

    I.waitForElement(indicatorList);
    await performScrollAndScreenshot(() => I.swipeUpFixed(indicatorList), '261.market_indicator_list_Test_item_No.6_Scroll_to_top.png');
    I.waitForElement(scrollToTopButton);
    I.click(scrollToTopButton);
    await performScrollAndScreenshot(() => {}, '261.market_indicator_list_Test_item_No.6_Scroll_to_top_after_click.png');
});
Scenario('Test item No.7 Complete button', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    const editBtn = '//*[@data-testid="marketIndicatorList_edit_id"]';
    await market.clickEditButton();
    await I.saveScreenshot('261.market_indicator_list_Test_item_No.7_Edit_button_edit_mode.png');
    await I.waitFor();
    await market.clickCompleteButton();
    I.see('編集', editBtn);
    I.saveScreenshot('261.market_indicator_list_Test_item_No.7_Complete_button_complete_mode.png');

});
Scenario('Test item No.8 Toggle check boxes', async ({ I, market }) => {
    const checkboxItem = '[data-testid="marketIndicatorList_listItem_id_PRODUCT_TYPE_INDEX_0"] button';
    const checkIcon = `${checkboxItem} svg path`;

    const toggleCheckboxAndVerify = async (expectedColor: string, screenshotName: string) => {
        I.click(checkboxItem);
        await I.waitFor();
        const iconColor = await I.grabCssPropertyFrom(checkIcon, 'fill');
        I.assertContain(iconColor, expectedColor);
        I.saveScreenshot(screenshotName);
    };

    await market.clickEditButton();
    await I.waitFor();
    I.waitForElement(checkboxItem);

    // Verify uncheck state
    await toggleCheckboxAndVerify('rgb(137, 137, 139)', '261.market_indicator_list_Test_item_No.8_Toggle_check_boxes_uncheck.png');

    // Verify checked state
    await toggleCheckboxAndVerify('rgb(255, 86, 0)', '261.market_indicator_list_Test_item_No.8_Toggle_check_boxes_checked.png');
});

// Test case for Domestic Index (国内指数)
Scenario('Test Item No.20 navigation to indicator detail when tapping a Domestic Index item', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    await I.waitFor('mediumWait');
    const domesticIndexItemSelector = '(//*[@data-testid[starts-with(., "marketIndicatorList_listItem_id_PRODUCT_TYPE_INDEX")]])[1]';
    const detailDomesticIndexItemSelector = '//*[@data-testid="cellStockIndicator_brand_id"]';

    await market.verifyNavigationToDetail(domesticIndexItemSelector, detailDomesticIndexItemSelector, '261.market_indicator_list_Test_item_No.20_Domestic_Indices_detail.png');
});

Scenario('Test Item No.11 navigation to indicator detail when tapping a Foreign Currency item', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    await I.waitFor('mediumWait');
    const foreignCurrencyItemSelector = '(//*[@data-testid[starts-with(., "marketIndicatorList_listItem_id_PRODUCT_TYPE_FOREX")]])[1]';
    const detailForeignCurrencyItemSelector = '//*[@data-testid="cellStockIndicator_brand_id"]';

    await market.verifyNavigationToDetail(foreignCurrencyItemSelector, detailForeignCurrencyItemSelector, '261.market_indicator_list_Test_item_No.11_Foreign_Currency_detail.png');
});

Scenario('Test item No.29 when click to US Index item', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    await I.waitFor('mediumWait');

    // Find  US Index item
    const usIndexItemSelector = '(//*[@data-testid[starts-with(., "marketIndicatorList_listItem_id_PRODUCT_TYPE_US")]])[1]';
    I.waitForElement(usIndexItemSelector);
    const getFilteredDataTexts = async () => {
        const dataTexts = await I.grabTextFromAll({ xpath: `${usIndexItemSelector}//p` });
        return dataTexts
            .map((item) => item.replace(/[%]/g, ''));
    };      
    // Check p content
    const pContent = await getFilteredDataTexts();
    const isNumber = !isNaN(parseFloat(pContent[2]));
    // If p content is not a number, click to item and redirect to agreement page
    if (!isNumber) {
        // Click to item when not agree
        I.click(usIndexItemSelector);
        await I.waitFor('mediumWait');
        await I.switchToNative();
        // await I.waitFor('mediumWait');
        await I.activateApp();
        // Verify URL redirect
        // const currentUrl = await I.grabCurrentUrl();
        // I.assertContain(currentUrl, '/members/personal/dkstatus/dk01101.asp#tradeservice');
    }
    else {
        // Verify display detail
        const detailUSIndexItemSelector = '//*[@data-testid="cellStockIndicator_brand_id"]';
        await market.verifyNavigationToDetail(usIndexItemSelector, detailUSIndexItemSelector, '261.market_indicator_list_Test_item_No.29_US_Index_detail.png');
    }
});

Scenario('Test item No.2 Indicator list info swipe', async ({ I, market }) => {
    await market.goToMarketIndicatorList();
    const indicatorSwipeTarget = '//*[@id="market-main-indicator"]';
    const rankingSwipeTarget = '//*[@id="market-ranking"]';
    const newsSwipeTarget = '//*[@id="market-news"]';

    const performSwipeAndScreenshot = async (target: string, swipeAction: () => void, screenshotPrefix: string) => {
        I.waitForElement(target);
        swipeAction();
        await I.waitFor('shortWait');
        I.saveScreenshot(`${screenshotPrefix}_scroll_down.png`);
        swipeAction();
        await I.waitFor('shortWait');
        I.saveScreenshot(`${screenshotPrefix}_scroll_up.png`);
    };

    const swipeUpOrDown = (target: string, isSwipeDown = false) => {
        if (isSwipeDown) {
            I.swipeDownFixed(target);
        } else {
            I.swipeUpFixed(target);
        }
    };

    const swipeLeftOrRight = async (target: string, isSwipeRight = false) => {
        if (isSwipeRight) {
            await I.swipeRightWithOffsetX(target);
        } else {
            await I.swipeLeftWithOffsetX(target);
        }
    };

    // Show Market Indicator
    await performSwipeAndScreenshot(indicatorSwipeTarget, () => swipeUpOrDown(indicatorSwipeTarget), '261.market_indicator_list_Test_item_No.2_Indicator');

    // Show Market Ranking
    await swipeLeftOrRight(indicatorSwipeTarget);
    await I.waitFor('shortWait');
    market.displayRankingTab();
    await performSwipeAndScreenshot(rankingSwipeTarget, () => swipeUpOrDown(rankingSwipeTarget), '261.market_indicator_list_Test_item_No.2_Ranking');

    // Show Market News
    await swipeLeftOrRight(rankingSwipeTarget);
    await I.waitFor('shortWait');
    market.displayNewsTab();
    await performSwipeAndScreenshot(newsSwipeTarget, () => swipeUpOrDown(newsSwipeTarget), '261.market_indicator_list_Test_item_No.2_News');

    // Show Market Indicator List
    await swipeLeftOrRight(newsSwipeTarget, true);
    await I.waitFor('shortWait');
    market.displayRankingTab();
    await swipeLeftOrRight(rankingSwipeTarget, true);
});

Scenario('Test item No.9 Drag and drop index detail', async ({ I, market }) => {
    // Go to Market Indicator List page
    await market.goToMarketIndicatorList();
    await I.waitFor('mediumWait');

    // Click to Edit button
    await market.clickEditButton();
    await I.waitFor('mediumWait');
    I.saveScreenshot('261.market_indicator_list_Test_item_No.9_Before_edit_mode.png');

    // Get list container
    const listContainer = '//*[@data-testid="marketIndicatorList_indicatorList_id"]';
    I.waitForElement(listContainer);

    // Get item selectors
    const getItemSelectors = (index: number) => ({
        item: `${listContainer}//div[@data-testid="marketIndicatorList_listItem_id_PRODUCT_TYPE_INDEX_${index}"]`,
        dragButton: `${listContainer}//div[@data-testid="marketIndicatorList_listItem_id_PRODUCT_TYPE_INDEX_${index}"]//div[contains(@role, "button")]`
    });

    // Get source and target selectors
    const sourceSelectors = getItemSelectors(0);
    const targetSelectors = getItemSelectors(1);

    if (!sourceSelectors.item || !targetSelectors.item) return;

    I.waitForElement(sourceSelectors.dragButton);
    I.waitForElement(targetSelectors.dragButton);

    // Get item text
    const getItemText = async (selector: string) => await I.grabTextFrom(`${selector}//p`);

    const sourceText = await getItemText(sourceSelectors.item);
    const targetText = await getItemText(targetSelectors.item);

    // Perform drag and drop item
    const performDragAndDrop = async (source: string, target: string) => {
        await I.executeScript((sourceSelector, targetSelector) => {
            const getElement = (selector: string) =>
                document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;

            const sourceButton = getElement(sourceSelector) as HTMLElement;
            const targetButton = getElement(targetSelector) as HTMLElement;

            const sourceRect = sourceButton.getBoundingClientRect();
            const targetRect = targetButton.getBoundingClientRect();

            const createMouseEvent = (type: string, x: number, y: number) =>
                new MouseEvent(type, { bubbles: true, cancelable: true, view: window, clientX: x, clientY: y });

            sourceButton.dispatchEvent(createMouseEvent('mousedown', sourceRect.x, sourceRect.y));

            const steps = 20;
            const xStep = (targetRect.x - sourceRect.x) / steps;
            const yStep = (targetRect.y - sourceRect.y) / steps;

            let currentStep = 0;
            const moveInterval = setInterval(() => {
                if (currentStep <= steps) {
                    document.dispatchEvent(createMouseEvent('mousemove', sourceRect.x + xStep * currentStep, sourceRect.y + yStep * currentStep));
                    currentStep++;
                } else {
                    clearInterval(moveInterval);
                    targetButton.dispatchEvent(createMouseEvent('mouseup', targetRect.x, targetRect.y));
                }
            }, 50);
        }, source, target);
    };

    // Perform drag and drop item   
    await performDragAndDrop(sourceSelectors.dragButton, targetSelectors.dragButton);

    await I.waitFor('longWait');

    const newSourceText = await getItemText(sourceSelectors.item);
    const newTargetText = await getItemText(targetSelectors.item);
    // If drag and drop success, the source text and target text will be swapped
    if (newSourceText === targetText && newTargetText === sourceText) {
        I.saveScreenshot('261.market_indicator_list_Test_item_No.9_Drag_and_drop_success.png');
    }

    // Click to Complete button
    await market.clickCompleteButton();
});

