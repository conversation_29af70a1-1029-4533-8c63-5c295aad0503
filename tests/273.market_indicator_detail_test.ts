import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Market - IndicatorDetailPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr/vn/Customer/InvestmentResult/InvestmentProfitLoss.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user11 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});


Scenario('Test Market Indicator Detail page', async ({ I, market }) => {
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    // Navigate to Market Indicator Detail page
    await market.goToMarketIndicatorDetail();
    await I.saveScreenshot('273.market_indicator_detail_test_Test_Market_Indicator_Detail_page.png');
});

Scenario('Test item No.12 Market tab info menu', async ({ I, market }) => {
    market.clickMarketTabGroup();
    await I.waitFor();
    await I.saveScreenshot('273.market_indicator_detail_test_Test_item_No.12_Market_tab_info_menu.png');
});

Scenario('Test item No.6 Favorite button', async ({ I }) => {
    const favoriteBtn1 = '//div[@id="market-main-indicator"]/div/div[3]/div/div/div[1]';
    const favoriteBtn2 = '//*[@data-testid="marketIndicatorDetail_favorite_id"]';
    I.waitForElement(favoriteBtn1);
    I.scrollAndClick(favoriteBtn1);
    await I.waitFor();
    I.waitForElement(favoriteBtn2);
    I.scrollAndClick(favoriteBtn2);
    await I.waitFor();
    I.seeElement('//*[@data-testid="common_rightSlide_favorite_id"]');
    I.seeElement('//*[@data-testid="favoriteRegisterModal_listAdd_id"]');
    I.seeElement('//*[@data-testid="favoriteRegisterModal_confirm_id"]');
    await I.saveScreenshot('273.market_indicator_detail_test_Test_item_No.6_Favorite_button.png');
    const closeModal = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(closeModal);
    I.click(closeModal);
});
