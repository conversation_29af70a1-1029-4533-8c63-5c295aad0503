import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReservePetitCancelReserveOrderConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Petit Cancel Reserve Order Confirm', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    I.saveScreenshot('1432.Test_item_No.0_Check_UI_Reserve_Petit_Cancel_Reserve_Order_Confirm.png');
});
Scenario('Test Item 11-12: Check password Tap Opens a text input form with alphanumeric characters and password omission check', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user47 });
    await I.waitFor()
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    await I.waitFor()
    const passwordInput = '//*[@data-testid="cancelReserveOrder_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    await I.scrollAndFill(passwordInput, '123@abc');
    await I.waitFor('shortWait');
    // click img alt="eye" to see password
    const eyeIcon = '//img[@alt="eye"]';
    await I.waitForElement(eyeIcon, 1);
    I.clickFixed(eyeIcon);
    await I.waitFor('shortWait');
    I.saveScreenshot('1432.Test_item_No.11_Password_Tap_Opens_a_text_input_form_with_alphanumeric_characters_and_see_password.png');
    const passwordInputOmissionCheck = '//*[@data-testid="cancelReserveOrder_passwordInputOmission_id"]';
    await I.waitForElement(passwordInputOmissionCheck, 1);
    // ON
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1432.Test_item_No.12_Password_omission_check_ON.png');
    // OFF
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1432.Test_item_No.12_Password_omission_check_OFF.png');
    
});

Scenario('Test Item 14: Check Password input check: Switch between ON and OFF', async ({ I, accumulation }) => { 
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="cancelReserveOrder_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 1);
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1432.Test_item_No.14_Password_input_check_1.png');
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1432.Test_item_No.14_Password_input_check_2.png');
});
Scenario.skip('Test Item 16.2: Browser back during loading redirects to reserve plan', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    const passwordInput = '//*[@data-testid="cancelReserveOrder_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    const confirmButton = '//*[@data-testid="cancelReserveOrder_confirm_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);

    I.executeScript(() => window.history.back());

    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1432.Test_item_No.16.2_redirect_to_reserve_plan.png');
});

Scenario('Test Item 16.3: Loading redirects to cancel completion page', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    await I.waitFor()
    const passwordInputCheck = '//*[@data-testid="cancelReserveOrder_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 1);
    I.clickFixed(passwordInputCheck);
    await I.waitFor('shortWait');
    const confirmButton = '//*[@data-testid="cancelReserveOrder_confirm_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);
    I.seeElement('//p[contains(text(), "積立中止が完了しました。")]');
    I.saveScreenshot('1432.Test_item_No.16.3_cancel_completion_page.png');
});

Scenario('Test Item 17: Check Terms and Conditions for Regular Fractional Share Accumulation Transactions ', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    const caution = '//*[@data-testid="cancelReserveOrder_caution_id"]';
    await I.waitForElement(caution, 1);
    await I.scrollAndClick(caution);
    const chakraCollapse = '//*[@data-testid="cancelReserveOrder_caution_id"]//div[@class="chakra-collapse"]';
    await I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1432.Test_item_No.17_Check_Terms_and_Conditions_for_Regular_Fractional_Share_Accumulation_Transactions.png');
    I.waitFor('shortWait');
    const arrowUp = '//*[@data-testid="cancelReserveOrder_caution_id"]//*[@data-testid="common_dropDown_arrow_id_up"]';
    await I.waitForElement(arrowUp, 1);
    I.clickFixed(arrowUp);
    I.saveScreenshot('1432.Test_item_No.17_Check_Terms_and_Conditions_for_Regular_Fractional_Share_Accumulation_Transactions_closed.png');
});

Scenario('Test Item 20: Check Click Open the following URL in a new tab', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user51 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReservePetitCancelReserveOrderConfirmPage();
    const hereLink = '//p[@data-testid="cancelReserveOrder_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 1);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
});