import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Reserve - CustomerReservePetitChangeReserveOrderComplete');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 }); //09109903
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Petit ChangeReserveOrder Complete', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderCompletePage();
});
Scenario('Test Item 1: Tap to transition to the savings plan', async ({ I, accumulation }) => {
    const reservePlan = '//*[@data-testid="reserveCommon_reservePlan_id"]';
    await I.waitForElement(reservePlan, 3);
    I.scrollAndClick(reservePlan);
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1428.Test_item_No.1_Tap_to_transition_to_the_savings_plan.png');
})
Scenario('Test Item 2: Tap to transition to the savings calendar', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderCompletePage();
   
    const reserveCalendar = '//*[@data-testid="reserveCommon_reserveCalendar_id"]';
    await I.waitForElement(reserveCalendar, 3);
    I.scrollAndClick(reserveCalendar);
    I.see('積立カレンダー', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1428.Test_item_No.2_Tap_to_transition_to_the_savings_calendar.png');
})
Scenario('Test Browser back', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitChangeOrderCompletePage();
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1428.Test_item_Browser_back.png');
})