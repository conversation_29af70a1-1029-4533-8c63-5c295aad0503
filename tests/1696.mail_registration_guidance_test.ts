import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Settings_Entry - MailRegistrationGuidance');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.userNone }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Mail Registration Guidance', async ({ I }) => {
    const url = '/mobile/setting/mail-registration/guidance';
    I.amOnPage(url);
    I.waitForText('メールアドレス\n登録方法のご案内', 5, 'p');
    I.seeInCurrentUrl(url);
    await I.saveScreenshot('1696.Test_item_No.0_Check_UI_Mail_Registration_Guidance.png');
});

// 1.セキュリティ設定画面へ -> セキュリティ設定-TOPに遷移する
Scenario('Test Item 1: Go to Security Settings Screen -> Security Settings - TOP', async ({ I }) => {
    await I.seeAndClickElement('$mailRegistrationGuidance_gotoSecurityScreen_id', { waitFor: 'mediumWait' });
    I.waitForText('セキュリティ設定', 5, 'p');
    I.seeInCurrentUrl('/mobile/setting/security/top');
    I.saveScreenshot('1696.Test_item_No.1_Go_to_Security_Settings_Screen-Security_Settings-TOP.png');
    await I.waitFor();
    await I.seeAndClickElement('$common_back_id');
});

// 2.後で手続きをする(トップページへ) -> 資産状況_資産ポートフォリオTOPへ遷移する
Scenario('Test Item 2: Do Later (Go to Top Page) -> Asset Status_Asset Portfolio TOP', async ({ I }) => {
    await I.seeAndClickElement('$mailRegistrationGuidance_comeToTop_id', { waitFor: 'mediumWait' });
    I.seeInCurrentUrl('/mobile/mypage/portfolio');
    await I.saveScreenshot('1696.Test_item_No.2_Go_to_Portfolio_Top_Page.png');
    await I.waitFor('mediumWait');
    await I.performBrowserBack();
});

// 3.アドレスの変更方法 -> 別タブで下記のURLに遷移する
Scenario('Test Item 3: How to change the address -> Move to the URL below in a different tab', async ({ I }) => {
    await common.clickCardItem('$mailRegistrationGuidance_addressChangeMethod_id', 'https://faq.kabu.com/s/article/k007478', 'external');
});

// 4.認証をしなかったらどうなりますか -> 別タブで下記のURLに遷移する
Scenario(
    `Test Item 4: What happens if you don't authenticate -> Move to the URL below in a different tab`,
    async ({ I }) => {
        await common.clickCardItem('$mailRegistrationGuidance_whatHappenDontAuthenticate_id', 'https://faq.kabu.com/s/article/k007479', 'external');
    },
);
