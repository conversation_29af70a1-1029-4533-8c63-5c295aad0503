Feature('Settings_Entry - CardCommonUnRegisterPage');
import { COOKIE_KEY, USER_ID } from '../const/constant';
import SettingCard from '../pages/settingCard';

const locators = SettingCard.locators;

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitUntilHaveWebVewContext(20000, 2000);
    await I.switchToWeb();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user64 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Go to card common unregister page', async () => {
    await SettingCard.goToPageCardCommonUnRegister();
    await SettingCard.takeScreenshot('1810_card_common_unregister_page.png');
});

Scenario('Test item 1 Change credit card', async () => {
    await SettingCard.clickItem(locators.creditCardChange);
    await SettingCard.takeScreenshot('1810_Test_Item_1_Change_credit_card_1.png');
    await SettingCard.clickItem(locators.creditCardChange);
    await SettingCard.takeScreenshot('1810_Test_Item_1_Change_credit_card_2.png');
});

// This item is label
Scenario('Test item 2 Card type', async ({ I }) => {
    await I.scrollToElement(locators.cardType);
    await SettingCard.takeScreenshot('1810_Test_Item_2_Card_type.png');
});

Scenario('Test item 4 Password', async ({ I }) => {
    await SettingCard.goToPageCardCommonUnRegister();
    await I.scrollToElement(locators.unRegisterPassword);
    await SettingCard.fillInput(locators.unRegisterPassword, '111111');
    await SettingCard.takeScreenshot('1810_Test_Item_4_Password.png');
});

Scenario('Test item 5 UnRegister', async () => {
    await SettingCard.clickItem(locators.unRegisterBtn);
    await SettingCard.takeScreenshot('1810_Test_Item_5_UnRegister.png');
});

Scenario('Test item 7 Click Here', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user65 });
    await SettingCard.clickItem(locators.unRegisterClickHere);
    await I.amOnPage(locators.cardSelect);
    await SettingCard.takeScreenshot('1810_Test_Item_7_Click_Here.png');
});

Scenario('Test item 6 Reserve Plan', async ({ I }) => {
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user66 });
    await I.waitFor();
    await SettingCard.goToPageCardCommonUnRegister();
    await SettingCard.clickItem(locators.reservePlanLink);
    await I.amOnPage(locators.reservePlan);
    await SettingCard.takeScreenshot('1810_Test_Item_6_Reserve_Plan.png');
});
