import { COMMON_HEADER_TITLE, COOKIE_KEY, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE, USER_ID } from '../const/constant';
import domesticStockCashtrading from '../pages/domesticStockCashtrading';

Feature('InvestmentProducts - DomesticStockCashtrading');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor('mediumWait');

    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user26 });
    await I.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('Item 0. Access to the trade stock sell page', async ({ I }) => {
    I.setSessionStorage(SESSION_STORAGE_KEY.tradeStockSell, SESSION_STORAGE_VALUE.tradeStockSell());
    I.amOnPage(domesticStockCashtrading.urls.sell);
    await I.waitFor('mediumWait');
    I.see('現物売', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(domesticStockCashtrading.urls.sell);
    await I.seeAndClickElement(domesticStockCashtrading.locators.executionMethodOptionItem(2));
    await domesticStockCashtrading.seeAndFillText('groupNumberInput', '1000');
    await domesticStockCashtrading.goToConfirmPage();
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.confirm}.0_access_to_the_order_confirm_page.png`,
    );
});

// 22.パスワード -> 国内株式現物取引-買注文-注文確認参照
Scenario('Item 22. Fill password input', async ({ I }) => {
    await domesticStockCashtrading.setConfirmSessionData({ isOmmitPassword: false });
    await domesticStockCashtrading.swipeToLocator('passwordInput');
    await domesticStockCashtrading.seeAndFillText('passwordInput', '111111');
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.confirm}.22_fill_password_input.png`);
});

// 23.パスワード省略チェック -> 国内株式現物取引-買注文-注文確認参照
Scenario('Item 23. Omit password from your next order.', async ({ I }) => {
    await I.seeAndClickElement(domesticStockCashtrading.locators.passwordOmissionCheck);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.confirm}.23_omit_password_from_your_next_order.png`,
    );
});

// 29.パスワード入力チェック -> 国内株式現物取引-買注文-注文確認参照
Scenario('Item 29. Check input password.', async ({ I }) => {
    await domesticStockCashtrading.setConfirmSessionData({ isOmmitPassword: true });
    await domesticStockCashtrading.swipeToLocator('checkInputPassword');
    await I.seeAndClickElement(domesticStockCashtrading.locators.checkInputPassword);
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.confirm}.29_Check_input_password.png`);
});

// 24.注文確定
Scenario('Item 24. Go to the sell complete page.', async ({ I }) => {
    // isOpe=trueの場合は、opeエラーダイアログを表示し、後続処理を行わない
    // ローディング中にブラウザバックされた場合: 注文照会-国内株式-単元株-一覧画面に遷移
    // ->  temporarily skip these 2 cases as they cannot be reproduced yet

    // 国内株式現物取引-売注文-注文完了に遷移する
    await I.seeAndClickElement(domesticStockCashtrading.locators.sellConfirmButton);
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.confirm}.24_go_to_the_sell_complete_page.png`);
});

// 25.注文訂正 -> 上記以外の場合：国内株式現物取引-売注文-注文入力に遷移する
Scenario('Item 25. Go to the uturn order input', async ({ I }) => {
    I.amOnPage(domesticStockCashtrading.urls.confirm);
    I.see('現物売', COMMON_HEADER_TITLE);
    I.seeElement(domesticStockCashtrading.locators.orderModifyButton);
    await domesticStockCashtrading.swipeToLocator('orderModifyButton');
    await I.seeAndClickElement(domesticStockCashtrading.locators.orderModifyButton);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.confirm}.25_go_to_the_uturn_order_input_page.png`,
    );
});

// 26.注文キャンセル -> 銘柄/商品検索_総合検索画面TOPに遷移する
Scenario('Item 26. Go to the general search page TOP', async ({ I }) => {
    I.amOnPage(domesticStockCashtrading.urls.confirm);
    I.see('現物売', COMMON_HEADER_TITLE);
    I.seeElement(domesticStockCashtrading.locators.cancelOrderButton);
    await domesticStockCashtrading.swipeToLocator('cancelOrderButton');
    await I.seeAndClickElement(domesticStockCashtrading.locators.cancelOrderButton);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.confirm}.26_go_to_the_general_search_page_top.png`,
    );
});
