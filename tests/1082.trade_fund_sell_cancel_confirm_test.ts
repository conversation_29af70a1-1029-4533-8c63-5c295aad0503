import { COOKIE_KEY, USER_ID } from '../const/constant';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundSellInput');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user38 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await I.waitFor();
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test trade fund cancel confirm', async ({ I }) => {
    await tradeFund.sellInput.goToTradeFundSellInputPage();
    await tradeFund.sellInput.fillGroupInputNumber();
    await tradeFund.sellInput.goToTradeFundConfirmPage();
    await tradeFund.sellInput.goToTradeFundCompletePage();
    await tradeFund.sellInput.goToTradeFundCancelConfirmPage();
    I.saveScreenshot(
        `1082.${tradeFund.sellInput.screenShotPrefix.fundCancelCofirm}.0_Go_to_trade_fund_cancel_confirm.png`,
    );
});

// 13.パスワード -> 国内株式現物取引-買注文-注文確認参照
Scenario('Test No.13 Password -> Domestic Stock Pure Trading-Buy Order-See Order Confirmation', async ({ I }) => {
    await I.waitFor();
    const passwordInput = await I.grabNumberOfVisibleElements(tradeFund.sellInput.locators.cancelConfirmPassword);
    if (passwordInput > 0) {
        I.seeElement(tradeFund.sellInput.locators.cancelConfirmPassword);
        I.fillField(tradeFund.sellInput.locators.cancelConfirmPassword, '123');
        I.saveScreenshot(
            `1082.${tradeFund.sellInput.screenShotPrefix.fundCancelCofirm}.13_Password-Domestic_Stock_Pure_Trading-Buy_Order-See_Order_Confirmation.png`,
        );
        await I.waitFor();
    } else {
        I.say('Password is not found');
    }
});

// 14.パスワード省略チェック -> 国内株式現物取引-買注文-注文確認参照
Scenario(
    'Test No.14 Check password omitted -> Domestic stock trading - Buy order - See Order confirmati',
    async ({ I }) => {
        const passwordOmission = await I.grabNumberOfVisibleElements(
            tradeFund.sellInput.locators.cancelConfirmPasswordOmissionCheck,
        );
        if (passwordOmission > 0) {
            I.seeElement(tradeFund.sellInput.locators.cancelConfirmPasswordOmissionCheck);
            await I.clickFixed(tradeFund.sellInput.locators.cancelConfirmPasswordOmissionCheck);
            I.saveScreenshot(
                `1082.${tradeFund.sellInput.screenShotPrefix.fundCancelCofirm}.14_Check_password_omitted-Domestic_stock_trading-Buy_order-See_Order_confirmation.png`,
            );
            await I.waitFor();
        } else {
            I.say('Password omission check is not found');
        }
    },
);

// 16.パスワード入力チェック -> 国内株式現物取引-買注文-注文確認参照
Scenario(
    'Test No.16 Check password input -> Domestic stock trading-Buy order-See Order confirmation',
    async ({ I }) => {
        const passwordInputCheck = await I.grabNumberOfVisibleElements(
            tradeFund.sellInput.locators.cancelConfirmPasswordInputCheck,
        );
        if (passwordInputCheck > 0) {
            I.seeElement(tradeFund.sellInput.locators.cancelConfirmPasswordInputCheck);
            await I.clickFixed(tradeFund.sellInput.locators.cancelConfirmPasswordInputCheck);
            I.saveScreenshot(
                `1082.${tradeFund.sellInput.screenShotPrefix.fundCancelCofirm}.16_Check_password_input-Domestic_stock_trading-Buy_order-See_Order_confirmation.png`,
            );
        } else {
            I.say('No password input check element found');
        }
        await I.waitFor();
    },
);

// 17.取消を行う
Scenario('Test No.17 Cancel', async ({ I }) => {
    // isOpe=trueの場合は、opeエラーダイアログを表示し、後続処理を行わない
    // ローディング中にブラウザバックされた場合: 注文照会-投資信託-一覧に遷移
    // ->  temporarily skip these 2 cases as they cannot be reproduced yet

    // 注文照会-投資信託-一覧に遷移する
    I.seeElement(tradeFund.sellInput.locators.cancelOrderButton);
    await I.clickFixed(tradeFund.sellInput.locators.cancelOrderButton);
    I.waitForText('注文約定照会\n投資信託', 5, 'p');
    I.seeInCurrentUrl(tradeFund.sellInput.urls.orderInquiryFund);
    I.saveScreenshot(
        `1082.${tradeFund.sellInput.screenShotPrefix.fundCancelCofirm}.17_Go_to_Order_Inquiry-Investment_Trust-List.png`,
    );
});
