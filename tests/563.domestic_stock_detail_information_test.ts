import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockDetailInformation');

Before(async ({ I, loginAndSwitchToWebAs, stockDetailBasicPage }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToDetailInformationTab();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Detail Information page', async ({ I, stockDetailBasicPage }) => {
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Display_Domestic_Stock_Detail_Information_page');
});

Scenario('Test item No.2 Previous', async ({ I, stockDetailBasicPage }) => {
    const previousButtonSelector = '//div[contains(@class, "slick-prev")]';
    await I.clickFixed(previousButtonSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="stockDetailBasic_basicInfo_id"]');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/basic');
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.2_Click_previous_go_to_basic_tab');
});

Scenario('Test item No.3 Basic information', async ({ I, stockDetailBasicPage }) => {
    const basicTabSelector = '//div[@aria-hidden="false"][.//p[contains(text(), "基本情報")]]'
    await I.clickFixed(basicTabSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="stockDetailBasic_basicInfo_id"]');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/basic');
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.3_Click_basic_tab');
});

Scenario('Test item No.5 Next', async ({ I, stockDetailBasicPage }) => {
    const nextButtonSelector = '//div[contains(@class, "slick-next")]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.boardInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/board');
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.5_Click_next_go_to_stock_detail_quotes_tab');
});

Scenario('Test item No.4 Depth of Market', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.detailQuoteTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.boardInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/board');
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.4_Click_Depth_of_Market_tab_go_to_stock_detail_quotes_tab');
});

Scenario('Test item No.8 Transaction Caution', async ({ I, stockDetailBasicPage }) => {
    const transactionCautionSelector = '//*[@data-testid="stockInfoDetail_tradeCaution_id"]';
    await I.clickFixed(transactionCautionSelector);
    await I.waitFor();
    I.seeElement('//*[@data-testid="common_rightSlide_close_id"]');
    I.see('取引制限・取引注意情報', 'body');
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.8_Display_transaction_caution_information_modal');
});

Scenario('Test item No.53 Investment indicators?', async ({ I, stockDetailBasicPage }) => {
    const investmentIndicatorsSelector = '//*[@data-testid="stockInfoDetail_investmentIndex_id"]';
    await I.clickFixed(investmentIndicatorsSelector);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.53_Show_tooltip_Investment_indicators');
});

Scenario('Test item No.60 Dividend information?', async ({ I, stockDetailBasicPage }) => {
    const dividendInfoSelector = '//*[@data-testid="stockInfoDetail_dividendInfo_id"]';
    await I.clickFixed(dividendInfoSelector);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.60_Show_tooltip_Dividend_information');
});

Scenario('Test item No.6 Scroll Detailed information', async ({ I, stockDetailBasicPage }) => {
    await I.swipeUpFixed(stockDetailBasicPage.locator.detailInfoContainer);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailInformation('Test_item_No.6_Scroll_Detailed_information');
});