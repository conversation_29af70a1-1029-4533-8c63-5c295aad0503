import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockCashTradingBuyOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs, stockCashOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user25 });
    await stockCashOrder.goToCashBuyOrderConfirm();
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Display Domestic Stock Cash Trading Buy Order Confirm', async ({ stockCashOrder }) => {
    stockCashOrder.compareUrl(stockCashOrder.urls.cashBuyConfirm);
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Display_Domestic_Stock_Cash_Trading_Buy_Order_Confirm');
});

Scenario('Test item No.23 Password', async ({ I, stockCashOrder }) => {
    const passwordSelector = '//*[@data-testid="buyConfirm_password_id"]';
    const passwordCheckSelector = '//*[@data-testid="buyConfirm_passwordOmissionCheck_id"]';
    await I.scrollToElement(passwordSelector);
    await I.clickFixed(passwordSelector);
    await I.waitFor();
    I.assertEqual(await I.isKeyboardShown(), true, 'The keyboard should be shown');
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.23_Open_a_text_input_form_with_alphanumeric_characters');
    await I.clickFixed(passwordCheckSelector);
});

Scenario('Test item No.24 Check for omitted passwords', async ({ I, stockCashOrder }) => {
    const passwordCheckSelector = '//*[@data-testid="buyConfirm_passwordOmissionCheck_id"]';
    const passwordCheckIconSelector = '[data-testid="buyConfirm_passwordOmissionCheck_id"] button svg';
    await I.swipeDirection('up');
    await I.waitFor();
    await I.scrollToElement(passwordCheckSelector);
    await I.clickFixed(passwordCheckSelector);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(passwordCheckIconSelector, 'color'), 'rgba(255, 86, 0, 1)', 'Color is not equal');
    await stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.24_Toggle_the_check_ON');

    await I.clickFixed(passwordCheckSelector);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(passwordCheckIconSelector, 'color'), 'rgba(137, 137, 139, 1)', 'Color is not equal');
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.24_Toggle_the_check_OFF');
});

Scenario('Test item No.25 Order Confirmation', async ({ I, stockCashOrder }) => {
    const quantityInputSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//input[@placeholder="数量を入力"]';
    const priceInputSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//input[@placeholder="価格を入力"]';
    const specialConditionSelector = '//div[p[contains(text(), "特殊条件")]]/button[1]';
    const uturnConditionSelector = '//div[p[contains(text(), "特殊条件")]]//button[contains(@value, "SPECIAL_CONDITION_U_TURN")]';
    const confirmButtonSelector = '//*[@data-testid="buyInput_OrderConfirmButton_id"]';
    // For all other cases: Transition to domestic stock spot trading - buy order - order completion
    await stockCashOrder.goToCashBuyOrderCompleted();
    await stockCashOrder.compareUrl(stockCashOrder.urls.cashBuyCompleted);
    await stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.25_Transition_to_order_completion');
    // For U-turn orders: Transition to domestic stock spot trading - sell order - U-turn order input screen
    await stockCashOrder.goToCashBuyInput();
    I.fillField(quantityInputSelector, stockCashOrder.inputValues.quantity);
    I.fillField(priceInputSelector, stockCashOrder.inputValues.price);
    I.blur(priceInputSelector);
    await I.clickFixed(specialConditionSelector);
    await I.waitFor();
    await I.clickFixed(uturnConditionSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/trade/stock/buy/confirm');
    await I.swipeDirection('up');
    const numberVisibleBuyConfirmSoftCheck = await I.grabNumberOfVisibleElements(stockCashOrder.locator.buyConfirmSoftCheck);
    if (numberVisibleBuyConfirmSoftCheck) {
        await I.clickFixed(stockCashOrder.locator.buyConfirmSoftCheck);
    }
    const numberVisibleBuyConfirmPasswordInput = await I.grabNumberOfVisibleElements(stockCashOrder.locator.buyConfirmPasswordInput);
    if (numberVisibleBuyConfirmPasswordInput) {
        I.fillField(stockCashOrder.locator.buyConfirmPasswordInput, stockCashOrder.inputValues.password);
    }
    const numberVisibleBuyConfirmPasswordCheck = await I.grabNumberOfVisibleElements(stockCashOrder.locator.buyConfirmPasswordCheck);
    if (numberVisibleBuyConfirmPasswordCheck) {
        await I.clickFixed(stockCashOrder.locator.buyConfirmPasswordCheck);
    }
    await I.clickFixed(stockCashOrder.locator.buyConfirmConfirmButton);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/trade/stock/uturn');
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.25_Transition_to_Uturn_order_entry');
});

Scenario('Test item No.26 Order correction', async ({ I, stockCashOrder }) => {
    const orderCorrectButtonSelector = '//*[@data-testid="buyConfirm_orderModify_id"]';
    await I.swipeDirection('up', 0.3);
    await I.clickFixed(orderCorrectButtonSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl(stockCashOrder.urls.cashBuyInput);
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.26_Go_to_order_entry_screen');
});

Scenario('Test item No.27 Cancel order', async ({ I, stockCashOrder }) => {
    const orderCancelButtonSelector = '//*[@data-testid="buyConfirm_orderCancel_id"]';
    await I.swipeDirection('up', 0.3);
    await I.clickFixed(orderCancelButtonSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/search');
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.27_Go_to_general_search_page');
});

Scenario('Test item No.28 Caution', async ({ I, stockCashOrder }) => {
    const cautionSelector = '//*[@data-testid="buyConfirm_caution_id"]/div[1]';
    await I.swipeDirection('up', 0.2);
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    await stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.28_Opening_the_accordion');
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.28_Closing_the_accordion');
});

Scenario('Test item No.30 Password input check', async ({ I, stockCashOrder }) => {
    const passwordCheckSelector = '//*[@data-testid="buyConfirm_passwordOmissionCheck_id"]';
    const passwordCheckIconSelector = '[data-testid="buyConfirm_passwordOmissionCheck_id"] button svg';
    await I.swipeDirection('up');
    await I.clickFixed(passwordCheckSelector);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(passwordCheckIconSelector, 'color'), 'rgba(255, 86, 0, 1)', 'Color is not equal');
    await stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.30_Toggle_check_on');
    await I.clickFixed(passwordCheckSelector);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(passwordCheckIconSelector, 'color'), 'rgba(137, 137, 139, 1)', 'Color is not equal');
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.30_Toggle_check_off');
});

Scenario('Test item No.31 Trailing Stop Orders', async ({ I, stockCashOrder }) => {
    await stockCashOrder.goToCashBuyInput();
    const automatedTradingTab = '//*[@data-testid="buyInput_executionMethodSelection_id"]//button[contains(text(), "自動売買")]';
    const orderMethodDropdown = '//button[p[contains(text(), "逆指値")]]';
    const trailingStopMethod = '//p[contains(text(), "トレーリングストップ")]';
    const quantityInputSelector =
        '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "数量を入力")]';
    const trailWidthSelector =
        '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "トレール幅を入力")]';
    const trailingStopOrderAccordion = '//*[@data-testid="buyConfirm_trailingStopOrders_id"]//div[1]';
    await I.scrollToElement(automatedTradingTab);
    await I.clickFixed(automatedTradingTab);
    await I.waitFor();
    await I.clickFixed(orderMethodDropdown);
    await I.waitFor('shortWait');
    await I.clickFixed(trailingStopMethod);
    await I.waitFor('shortWait');
    I.fillField(quantityInputSelector, stockCashOrder.inputValues.quantity);
    await I.waitFor('shortWait');
    I.fillField(trailWidthSelector, stockCashOrder.inputValues.trailWidth);
    await I.waitFor('shortWait');
    await I.scrollToElement(stockCashOrder.locator.buyInputConfirmButton);
    await I.clickFixed(stockCashOrder.locator.buyInputConfirmButton);
    await I.waitFor('mediumWait');
    await I.clickFixed(trailingStopOrderAccordion);
    await I.waitFor();
    await I.swipeDirection('up');
    await stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.28_Opening_the_accordion');
    await I.clickFixed(trailingStopOrderAccordion);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.28_Closing_the_accordion');
});

Scenario('Test item No.32 ± Limit orders', async ({ I, stockCashOrder }) => {
    await stockCashOrder.goToCashBuyInput();
    const automatedTradingTab = '//*[@data-testid="buyInput_executionMethodSelection_id"]//button[contains(text(), "自動売買")]';
    const orderMethodDropdown = '//button[p[contains(text(), "逆指値")]]';
    const adjustLimitMethod = '//p[contains(text(), "±指値")]';
    const quantityInputSelector =
        '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "数量を入力")]';
    const conditionPriceInputSelector =
        '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "条件価格を入力")]';
    const limitOrderAccordion = '//*[@data-testid="buyConfirm_limitOrders_id"]//div[1]';
    await I.scrollToElement(automatedTradingTab);
    await I.clickFixed(automatedTradingTab);
    await I.waitFor();
    await I.clickFixed(orderMethodDropdown);
    await I.waitFor('shortWait');
    await I.clickFixed(adjustLimitMethod);
    await I.waitFor('shortWait');
    I.fillField(quantityInputSelector, stockCashOrder.inputValues.quantity);
    await I.waitFor('shortWait');
    I.fillField(conditionPriceInputSelector, stockCashOrder.inputValues.conditionPrice);
    await I.waitFor('shortWait');
    await I.scrollToElement(stockCashOrder.locator.buyInputConfirmButton);
    await I.clickFixed(stockCashOrder.locator.buyInputConfirmButton);
    await I.waitFor('mediumWait');
    await I.clickFixed(limitOrderAccordion);
    await I.waitFor();
    await I.swipeDirection('up');
    await stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.28_Opening_the_accordion');
    await I.clickFixed(limitOrderAccordion);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyOrderConfirm('Test_item_No.28_Closing_the_accordion');
});
