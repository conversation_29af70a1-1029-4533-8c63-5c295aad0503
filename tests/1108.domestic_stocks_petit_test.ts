import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('OrderInquiry - DomesticPetitStocks');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await <PERSON>.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user39 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 1: Check UI of Domestic Petit Stocks page', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionPetitStocksPage();
    I.saveScreenshot('1108.Test_item_No.1_UI_of_Domestic_Petit_Stocks_page.png');
});
Scenario('Test Item 3: Check button filter ', async ({ I }) => {
    const settingButton = '//*[@data-testid="petitStockList_filter_id"]';
    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1108.Test_item_No.3_show_filler_modal.png');
   
});
Scenario('Test Item 16: Display pull-down menu excluding duplicates', async ({ I }) => {
    const settingButton = '//*[@data-testid="petitStockList_filter_id"]';
    
    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 全銘柄
    const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';
    
    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1108.Test_item_No.16_display_pull-down_menu_excluding_duplicates.png');
    //click button have p text 三菱ＵＦＪ 8306
    const mitsubishiUfj8306Button = '//p[contains(text(), "三菱ＵＦＪ 8306")]';
    
    I.clickFixed(mitsubishiUfj8306Button);
})
Scenario('Test Item 17: Display Order Display the drop-down menu', async ({ I }) => {

    const settingButton = '//*[@data-testid="petitStockList_filter_id"]';
    
    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 状態更新順
    const statusUpdateButton = '//p[contains(text(), "状態更新順")]';
    
    I.clickFixed(statusUpdateButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1108.Test_item_No.17_display_order_display_the_drop-down_menu.png');
    //click button have p text 注文日時順
    const orderDateButton = '//p[contains(text(), "注文日時順")]';
    
    I.clickFixed(orderDateButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1108.Test_item_No.17_display_order_display_the_drop-down_menu_order_date.png');
})
Scenario('Test Item 18: Set initial display sticks and order', async ({ I }) => {
    const clearButton = '//*[@data-testid="filter_clear_id"]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1108.Test_item_No.18_set_initial_display_sticks_and_order.png');
})
Scenario('Test Item 19: Filter and sort based on entered parameters', async ({ I }) => {
    const settingButton = '//*[@data-testid="petitStockList_filter_id"]';
    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 全銘柄
    const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';
    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');

    //click button have p text 三菱ＵＦＪ 8306
    const mitsubishiUfj8306Button = '//p[contains(text(), "三菱ＵＦＪ 8306")]';
    
    I.clickFixed(mitsubishiUfj8306Button);
    await I.waitFor('shortWait');
    //click button have p text 状態更新順
    const statusUpdateButton = '//*[@data-testid="filter_displayOrder_id"]';
    I.clickFixed(statusUpdateButton);
    await I.waitFor('shortWait');
    //click button have p text 注文日時順
    const orderDateButton = '//p[contains(text(), "注文日時順")]';
    
    I.clickFixed(orderDateButton);
    await I.waitFor('shortWait');
    const submitButton = '//*[@data-testid="filter_confirm_id"]';
    
    I.clickFixed(submitButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1108.Test_item_No.19_filter_and_sort_based_on_entered_parameters.png');

})
Scenario('Test Item 20: Close fillter and sort modal', async ({ I }) => {
    const settingButton = '//*[@data-testid="petitStockList_filter_id"]';
    
    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.20_show_setting_and_sort_modal.png');
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';
    
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.21_close_setting_and_sort_modal.png');
});
Scenario('Test Item 4: Details list Scroll Set the relevant area as the scroll range', async ({ I }) => {
    // order-inquiry--tabpanel-3
    const petitTabpanel = '//div[@id="order-inquiry--tabpanel-3"]';
    I.waitForElement(petitTabpanel, 2);
    I.scrollToElement(petitTabpanel);
    I.saveScreenshot('1108.Test_item_No.4_Details_list_Scroll_Set_the_relevant_area_as_the_scroll_range.png');
});
// Petit Stocks detail
Scenario('Check UI of Petit Stocks detail page', async ({ I }) => {
   // Ensure that the current tab is 現物
   const petitStocksButton = '//button[.//p[contains(text(), "プチ株")]]';
    
   await I.scrollAndClick(petitStocksButton);
   await I.waitFor();
   await I.clickFixed('//*[@data-testid="petitStockList_detailList_id"]//div[1]');
   I.saveScreenshot('1087.Test_item_go_to_stock_detail_no_orders.png');
});
Scenario('Test Item 33: Cancel order Tap Domestic Stocks Small Stock Trading - Cancel - Go to Cancel', async ({ I, orderStatus }) => {
    const modifyOrderButton = '//button[contains(text(), "注文を取消")]';
    I.waitForElement(modifyOrderButton, 2);
    I.scrollAndClick(modifyOrderButton);
    I.saveScreenshot('1108.Test_item_No.33_transition_to_domestic_stock_spot_trading_modify_cancel_order_modify.png');
    await orderStatus.backToOrderInquiry();
});
Scenario('Test Item 38: Order execution inquiry Tap Order inquiry - Domestic Stocks - Small Stocks - Go to List', async ({ I, orderStatus }) => {
    const cancelOrderButton = '//button[contains(text(), "注文約定照会")]';
    I.waitForElement(cancelOrderButton, 2);
    I.scrollAndClick(cancelOrderButton);
    I.saveScreenshot('1108.Test_item_No.38_transition_to_domestic_stock_spot_trading_modify_cancel_order_cancel.png');
    await orderStatus.backToOrderInquiry();
});
