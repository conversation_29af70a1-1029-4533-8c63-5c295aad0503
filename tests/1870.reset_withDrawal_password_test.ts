Feature('Settings_Entry - ResetWithdrawalPasswordPage');
import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import ChangeLoginPassword from '../pages/changeLoginPassword';
import common from '../pages/search/common';

const resetWithdrawalPasswordLocators = {
    resetWithDrawalUrl: '/mobile/setting/password/reset-withdrawal-password',
    link: '//*[@data-testid="resetWithdrawalStep1_hereLink_id"]/span[2]',
    pageDescription: '//*[@id="__next"]/div/div[2]/div/div[2]/div[1]',
    telephoneNumberInput: '//*[@data-testid="resetWithdrawalStep1_input_0_id"]',
    step1Input: '//*[@data-testid="resetWithdrawalStep1_input_1_id"]',
    issueOneTimeButton: '//*[@data-testid="resetWithdrawalStep1_issueOneTimeCode_id"]',
    step2Input: '//*[@data-testid="resetWithdrawalStep2_oneTimeCode_id"]/div/input',
    step2WithdrawalPasswordInput: '//*[@data-testid="resetWithdrawalStep2_newWithdrawalPassword_id"]/div/input',
    step2WithdrawalPasswordConfirmInput:
        '//*[@data-testid="resetWithdrawalStep2_newWithdrawalPasswordConfirm_id"]/div/input',
    step2IssueOneTimeButton: '//*[@data-testid="resetWithdrawalStep2_setting_id"]',
    returnToTopButton: '//*[@data-testid="resetWithdrawalStep3_returnToTop_id"]',
};

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});
// Switch to step 1,2,3 (1870, 1871, 1872)
Scenario('go to change withdrawal password page', async ({ I }) => {
    // Display STEP1
    await ChangeLoginPassword.navigateToPage(
        resetWithdrawalPasswordLocators.pageDescription,
        resetWithdrawalPasswordLocators.resetWithDrawalUrl,
    );
    
    await ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_page_step1.png`);
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.telephoneNumberInput, '123456');
    await I.scrollToElement(resetWithdrawalPasswordLocators.issueOneTimeButton);
    await I.clickFixed(resetWithdrawalPasswordLocators.issueOneTimeButton);
    await I.waitFor('mediumWait');
    // Display STEP2
    await I.swipeDirection('down');
    await ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_page_step2.png`);
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.step2Input, '123456');
    await I.waitFor('shortWait');
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.step2WithdrawalPasswordInput, '123456');
    await I.waitFor('shortWait');
    await ChangeLoginPassword.fillPasswordField(
        resetWithdrawalPasswordLocators.step2WithdrawalPasswordConfirmInput,
        '123456',
    );
    await I.waitFor('shortWait');
    await I.blur(resetWithdrawalPasswordLocators.step2WithdrawalPasswordConfirmInput);
    await I.scrollToElement(resetWithdrawalPasswordLocators.step2IssueOneTimeButton);
    await I.clickFixed(resetWithdrawalPasswordLocators.step2IssueOneTimeButton);
    await I.waitFor('mediumWait');
    // Display STEP3
    await ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_page_step3.png`);
});

Scenario('Test Item 1: Tap here', async () => {
    const clickHereLink = '//*[@data-testid="resetWithdrawalStep1_hereLink_id"]//span[contains(text(), "こちら")]';
    await ChangeLoginPassword.navigateToPage(
        resetWithdrawalPasswordLocators.pageDescription,
        resetWithdrawalPasswordLocators.resetWithDrawalUrl,
    );
    // Go to the following URL: {member-site-url}/ap/iPhone/personal/contact/List
    await common.clickCardItem(clickHereLink, '/ap/iPhone/personal/contact/List', 'kcMemberSite');
});

Scenario('Test Item 3: Telephone number', async () => {
    await ChangeLoginPassword.navigateToPage(
        resetWithdrawalPasswordLocators.pageDescription,
        resetWithdrawalPasswordLocators.resetWithDrawalUrl,
    );
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.telephoneNumberInput, '123456');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_telephone_number_input.png`);
});

Scenario('Test Item 4: Email address', async () => {
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.step1Input, '123456');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_email_address_input.png`);
});

Scenario('Test Item 5: Complete Password Change Form', ({ I }) => {
    I.click(resetWithdrawalPasswordLocators.issueOneTimeButton);
    ChangeLoginPassword.takeScreenshot(
        `${SCREENSHOT_PREFIX.resetWithdrawalPassword}_complete_password_change_form.png`,
    );
});

Scenario('Test Item 10: One-time code', async () => {
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.step2Input, '123456');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_one_time_code.png`);
});

Scenario('Test Item 11: New Withdrawal Password', async () => {
    await ChangeLoginPassword.fillPasswordField(resetWithdrawalPasswordLocators.step2WithdrawalPasswordInput, '123456');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_new_withdrawal_password.png`);
});

Scenario('Test Item 12: New Withdrawal Password (for confirmation)', async () => {
    await ChangeLoginPassword.fillPasswordField(
        resetWithdrawalPasswordLocators.step2WithdrawalPasswordConfirmInput,
        '123456',
    );
    ChangeLoginPassword.takeScreenshot(
        `${SCREENSHOT_PREFIX.resetWithdrawalPassword}_confirm_new_withdrawal_password.png`,
    );
});

Scenario('Test Item 13: Setting', async ({ I }) => {
    await I.waitFor();
    I.blur(resetWithdrawalPasswordLocators.step2WithdrawalPasswordConfirmInput);
    await I.waitFor();
    await I.clickFixed(resetWithdrawalPasswordLocators.step2IssueOneTimeButton);
    await I.waitFor('mediumWait');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_step2_setting.png`);
});

Scenario('Test Item 14: Return to top', async ({ I }) => {
    await I.scrollAndClick(resetWithdrawalPasswordLocators.returnToTopButton);
    await I.waitFor('mediumWait');
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, '/mobile/mypage/portfolio');
    ChangeLoginPassword.takeScreenshot(`${SCREENSHOT_PREFIX.resetWithdrawalPassword}_return_to_top.png`);
});
