import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingNewOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading New Order confirm', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderConfirm();
    await stockMarginOrder.compareUrl('/mobile/trade/margin/new/confirm');
    I.seeElement(stockMarginOrder.locator.marginNewOrderConfirmButton);
    stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Display_Domestic_Stock_Margin_Trading_New_Order_confirm');
});

Scenario('Test item No.20 Soft limit violation - confirmation check', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderConfirm();
    await stockMarginOrder.setSessionData('trade/margin/new/confirm', { isOmmitPassword: true });
    await I.scrollToElement('//button[@data-testid="marginNew_orderConfirmButton_id"]');
    const buttonLocator = '//button[@aria-label="Check"]';
    const svgLocator = 'button[aria-label="Check"] svg';
    I.assertEqual(
        await I.grabCssPropertyFrom(svgLocator, 'color'),
        'rgba(137, 137, 139, 1)',
        'Background color is not equal',
    );
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm(
        'Test_item_No.20_Soft_Limit_violation_unselected_state',
    );
    await I.waitFor();
    await I.clickFixed(buttonLocator);
    I.assertEqual(
        await I.grabCssPropertyFrom(svgLocator, 'color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.20_Soft_Limit_violation_selected_state');
});

Scenario('Test item No.21+22 Password + Check for omitted password', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderConfirm();
    await stockMarginOrder.setSessionData('trade/margin/new/confirm', { isOmmitPassword: true });
    const softLimitConfirmWrapperSelector = '//*[@data-testid="marginNew_softlimitConflit_id"]';
    const softLimitConfirmButtonSelector = `${softLimitConfirmWrapperSelector}//button`;
    const passwordInputSelector = '//*[@data-testid="marginNew_passwordInput_id"]';
    const ommitPasswordSelector = '//*[@data-testid="marginNew_passwordInputCheckBox_id"]';
    const orderConfirmButtonSelector = '//*[@data-testid="marginNew_orderConfirmButton_id"]';
    await I.scrollToElement(orderConfirmButtonSelector);
    if ((await I.grabNumberOfVisibleElements(softLimitConfirmButtonSelector)) > 0) {
        await I.clickFixed(softLimitConfirmButtonSelector);
    }
    I.fillField(passwordInputSelector, stockMarginOrder.inputValues.password);
    await I.clickFixed(ommitPasswordSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(orderConfirmButtonSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.21_22_Check_order_confirm_button_state');
});

Scenario('Test item No.23-3 Order Confirmation - For U-turn orders and otherwise', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewUturnOrderConfirm();
    const softLimitConfirmWrapperSelector = '//*[@data-testid="marginNew_softlimitConflit_id"]';
    const softLimitConfirmButtonSelector = `${softLimitConfirmWrapperSelector}//button`;
    const passwordInputSelector = '//*[@data-testid="marginNew_passwordInput_id"]';
    const ommitPasswordSelector = '//*[@data-testid="marginNew_passwordInputCheckBox_id"]';
    const orderConfirmButtonSelector = '//*[@data-testid="marginNew_orderConfirmButton_id"]';
    if ((await I.grabNumberOfVisibleElements(softLimitConfirmButtonSelector)) > 0) {
        await I.clickFixed(softLimitConfirmButtonSelector);
    }
    I.fillField(passwordInputSelector, stockMarginOrder.inputValues.password);
    await I.clickFixed(ommitPasswordSelector);
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginOrder.compareUrl('/mobile/trade/margin/repayment/uturn');
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.23-3_Order_confirm_for_U-turn_orders');
    await stockMarginOrder.goToMarginNewOrderConfirm();
    if ((await I.grabNumberOfVisibleElements(softLimitConfirmButtonSelector)) > 0) {
        await I.clickFixed(softLimitConfirmButtonSelector);
    }
    I.fillField(passwordInputSelector, stockMarginOrder.inputValues.password);
    await I.clickFixed(ommitPasswordSelector);
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginOrder.compareUrl('/mobile/trade/margin/new/complete');
    stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.23-3_Order_confirm_for_otherwise');
});

Scenario('Test item No.24 Modify the order details', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderConfirm();
    const modifyOrderSelector = '//*[@data-testid="marginNew_modifyOrderContent_id"]';
    await I.clickFixed(modifyOrderSelector);
    await I.waitFor('mediumWait');
    await stockMarginOrder.compareUrl('/mobile/trade/margin/new');
    stockMarginOrder.takeScreenshot.marginNewOrderConfirm(
        'Test_item_No.24_Tap_modify_the_order_details_to_transition_to_new_order_entry',
    );
});

Scenario('Test item No.25 Cancel order', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderConfirm();
    const cancelOrderSelector = '//*[@data-testid="marginNew_cancelOrderButton_id"]';
    await I.clickFixed(cancelOrderSelector);
    await I.waitFor('mediumWait');
    await stockMarginOrder.compareUrl('/mobile/search');
    stockMarginOrder.takeScreenshot.marginNewOrderConfirm(
        'Test_item_No.25_Tap_cancel_order_to_transition_to_general_search_page',
    );
});

Scenario('Test item No.26 Caution', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderConfirm();
    const cautionSelector = '//*[@data-testid="marginNew_caution_id"]';
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.26_Opening_the_caution_accordion');
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.26_Closing_the_caution_accordion');
});

Scenario('Test item No.29 Trailing Stop Orders', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderInput();
    const autoTradingTab = '//button[contains(text(), "自動売買")]';
    const methodDropdown = '//button[p[contains(text(), "逆指値")]]';
    const trailingStopOrderMethod = '//p[contains(text(), "トレーリングストップ")]';
    const quantityInput = '//input[@placeholder="数量を入力"]';
    const trailInput = '//input[@placeholder="トレール幅を入力"]';
    await I.scrollToElement(autoTradingTab);
    await I.clickFixed(autoTradingTab);
    await I.waitFor();
    await I.scrollToElement(methodDropdown);
    await I.clickFixed(methodDropdown);
    await I.waitFor('shortWait');
    await I.clickFixed(trailingStopOrderMethod);
    I.fillField(quantityInput, stockMarginOrder.inputValues.quantity);
    I.fillField(trailInput, stockMarginOrder.inputValues.trailWidth);
    await I.scrollToElement(stockMarginOrder.locator.marginNewOrderConfirmButton);
    await I.clickFixed(stockMarginOrder.locator.marginNewOrderConfirmButton);
    await I.waitFor('mediumWait');
    const trailingAccordionSelector = '//*[@data-testid="marginNew_cautionaryMessageConfirm_trailing_id"]//div[1]';
    await I.clickFixed(trailingAccordionSelector);
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm(
        'Test_item_No.29_Opening_the_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(trailingAccordionSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.29_Closing_the_accordion');
});

Scenario('Test item No.30 ± Limit orders', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.goToMarginNewOrderInput();
    const autoTradingTab = '//button[contains(text(), "自動売買")]';
    const methodDropdown = '//button[p[contains(text(), "逆指値")]]';
    const adjustLimitOrderMethod = '//p[contains(text(), "±指値")]';
    const quantityInput = '//input[@placeholder="数量を入力"]';
    const conditionPrice = '//input[@placeholder="条件価格を入力"]';
    await I.scrollToElement(autoTradingTab);
    await I.clickFixed(autoTradingTab);
    await I.waitFor();
    await I.clickFixed(methodDropdown);
    await I.waitFor('shortWait');
    await I.clickFixed(adjustLimitOrderMethod);
    I.fillField(quantityInput, stockMarginOrder.inputValues.quantity);
    I.fillField(conditionPrice, '0.1');
    await I.scrollToElement(stockMarginOrder.locator.marginNewOrderConfirmButton);
    await I.clickFixed(stockMarginOrder.locator.marginNewOrderConfirmButton);
    await I.waitFor('mediumWait');
    const limitAccordionSelector = '//*[@data-testid="marginNew_cautionaryMessageConfirm_limit_id"]//div[1]';
    await I.clickFixed(limitAccordionSelector);
    await I.waitFor('mediumWait');
    await I.swipeDirection('up');
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm(
        'Test_item_No.30_Opening_the_accordion',
    );
    await I.waitFor('shortWait');
    await I.clickFixed(limitAccordionSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderConfirm('Test_item_No.30_Closing_the_accordion');
});
