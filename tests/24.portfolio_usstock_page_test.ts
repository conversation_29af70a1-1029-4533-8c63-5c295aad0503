import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioUSStockPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to usStock page
    await portfolio.top.goToPage();
    await portfolio.top.usStock();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio usstock page', async ({ I }) => {
    const { common, usStock } = portfolio;
    await common.verifyProductName(usStock.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_layout.png`);
});

Scenario('test portfolio usstock page [3.銘柄カード]', async ({ I }) => {
    const { common, usStock } = portfolio;

    await common.clickSymbolCard(usStock.productType, 'MCD');
    await I.waitFor('mediumWait');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_3_symbol_card.png`);
});

Scenario('test portfolio usstock page [13.買付]', async ({ I }) => {
    const { common, usStock } = portfolio;

    await common.clickSymbolCard(usStock.productType, 'MCD');
    await usStock.buy('MCD');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_13_buy.png`);
});

Scenario('test portfolio usstock page [14.売却]', async ({ I }) => {
    const { common, usStock } = portfolio;

    await common.clickSymbolCard(usStock.productType, 'MCD');
    await usStock.sell('MCD');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_14_sell.png`);
});

Scenario('test portfolio usstock page [15.銘柄情報を見る]', async ({ I }) => {
    const { common, usStock } = portfolio;

    await common.clickSymbolCard(usStock.productType, 'MCD');
    await usStock.seeInfo();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_15_see_info.png`);
});

Scenario('test portfolio usstock page [16.残高照会]', async ({ I }) => {
    const { common, usStock } = portfolio;

    await common.clickSymbolCard(usStock.productType, 'MCD');
    await usStock.positionInquiry();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_16_position_inquiry.png`);
});

Scenario('test portfolio usstock page [17.電子契約]', async ({ I }) => {
    const { top, usStock } = portfolio;
    // Go to usStock page
    await I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user3 });
    await top.goToPage();
    await I.waitFor();
    await top.usStock();
    await I.waitFor('mediumWait');

    await usStock.verifyUnusablePage();
    await usStock.clickEasyElectricContract();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioUsStockPage}_17_easy_electric_contract.png`);
});
