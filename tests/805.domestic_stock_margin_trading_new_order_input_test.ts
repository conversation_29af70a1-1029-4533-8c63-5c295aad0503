import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('InvestmentProducts - DomesticStockMarginTradingNewOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
    await stockMarginOrder.goToMarginNewOrderInput();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading New Order input', async ({ I, stockMarginOrder }) => {
    await stockMarginOrder.compareUrl('/mobile/trade/margin/new');
    I.seeElement(stockMarginOrder.locator.marginNewDepositRequest);
    stockMarginOrder.takeScreenshot.marginNewOrderInput('Display_Domestic_Stock_Margin_Trading_New_Order_input');
});

Scenario('Test item No.2 Trade Restrictions and Trade Caution Information', async ({ I, stockMarginOrder }) => {
    const closeButtonSelector = '//*[@data-testid="common_rightSlide_close_id"]';
    await I.clickFixed(stockMarginOrder.locator.marginNewCautionInfo);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    I.seeElement(closeButtonSelector);
    stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.2_Tap_to_display_the_Trade_Caution_Information_modal',
    );
});

Scenario('Test item No.5 Market', async ({ I, stockMarginOrder }) => {
    const marketSelector = '//*[@data-testid="marginNew_exchange_id"]';
    const dropdownSelector = '//*[@data-testid="common_MenuList_id"][(contains(@style, "visibility: visible"))]';
    const secondMarketItemSelector = `${dropdownSelector}//button[2]`;
    await I.clickFixed(marketSelector);
    await stockMarginOrder.takeScreenshot.marginNewOrderInput('Test_item_No.5_Display_pulldown_menu');
    await I.clickFixed(secondMarketItemSelector);
    await I.waitFor();
    stockMarginOrder.takeScreenshot.marginNewOrderInput('Test_item_No.5_Process_market_change_on_screen');
});

Scenario('Test item No.6 Account classification', async ({ I, stockMarginOrder }) => {
    const accountTypeSelector = '//*[@data-testid="marginNew_accountType_id"]';
    const secondTypeItemSelector = `${accountTypeSelector}//label[2]`;
    const secondTypeItemTextSelector = `${secondTypeItemSelector}//p`;
    await I.clickFixed(secondTypeItemSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(secondTypeItemTextSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginNewOrderInput('Test_item_No.6_Tap_the_account_type_item_to_make_it_selected');
});

Scenario('Test item No.7 Trading category', async ({ I, stockMarginOrder }) => {
    const tradingCategorySelector = '//*[@data-testid="marginNew_tradingType_id"]';
    const thirdTypeItemSelector = `${tradingCategorySelector}//label[3]`;
    const thirdTradingCategoryItemSelector = `${thirdTypeItemSelector}//p`;
    await I.clickFixed(thirdTypeItemSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(thirdTradingCategoryItemSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.7_Update_the_display_of_the_buying_and_selling_category',
    );
});

Scenario('Test item No.8 Buy/Sell Category', async ({ I, stockMarginOrder }) => {
    const buySellCategorySelector = '//*[@data-testid="marginNew_sellBuyType_id"]';
    const secondTypeItemSelector = `${buySellCategorySelector}//label[2]`;
    const secondBuySellCategoryItemSelector = `${secondTypeItemSelector}//p`;
    await I.clickFixed(secondTypeItemSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(secondBuySellCategoryItemSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.8_Tap_the_buy_sell_type_item_to_make_it_selected',
    );
});

Scenario('Test item No.11 Order deadline', async ({ I, stockMarginOrder }) => {
    const orderDeadlineSelector = '//div[p[contains(text(), "注文期限")]]';
    const secondItemSelector = `${orderDeadlineSelector}//label[2]`;
    const secondItemTextSelector = `${secondItemSelector}//p`;
    const thirdItemSelector = `${orderDeadlineSelector}//label[3]`;
    const thirdItemTextSelector = `${thirdItemSelector}//p`;
    const modalSelector = '#bottom-sheet-container';
    await I.clickFixed(secondItemSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(secondItemTextSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    await stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.11_Tap_the_radio_button_to_activate_it_and_update_the_date_label',
    );
    await I.clickFixed(thirdItemSelector);
    await I.waitFor();
    I.assertEqual(
        await I.grabCssPropertyFrom(thirdItemTextSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    I.seeElement(modalSelector);
    I.see('注文期限の日付をご選択ください。', modalSelector);
    stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.11_Tap_Specify_period_to_Display_the_date_picker_for_specifying_the_order_deadline',
    );
});

Scenario('Test item No.13 Go to order confirmation screen', async ({ I, stockMarginOrder }) => {
    const orderConfirmButtonSelector = '//*[@data-testid="marginNew_orderConfirmButton_id"]';
    const quantityInputSelector =
        '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "数量を入力")]';
    const priceInputSelector =
        '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "価格を入力")]';
    const specialConditionSelector = '//button[.//span[contains(text(), "条件なし")]]';
    const secondSelectedSpecialConditionSelector = '//button[.//span[contains(text(), "リレー注文")]]';
    const conditionReplayItemSelector = '//button[contains(@value, "SPECIAL_CONDITION_RELAY")]';
    const conditionNoneItemSelector = '//button[contains(@value, "SPECIAL_CONDITION_NONE")]';
    const symbolFilterReplaySelector = '//*[@data-testid="marginRelay_symbolFilter_id"]';
    const closeButtonSelector = '//*[@data-testid="common_rightSlide_close_id"][contains(@aria-label, "btn-close")]';
    I.fillField(quantityInputSelector, stockMarginOrder.inputValues.quantity);
    I.fillField(priceInputSelector, '1,430.5');
    await I.clickFixed(specialConditionSelector);
    await I.clickFixed(conditionReplayItemSelector);
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor();
    I.see('リレー選択信用取引', 'body');
    I.waitForElement(symbolFilterReplaySelector, 2);
    await stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.13_Tap_Special_condition_Relay_order_to_Display_relay_selection_modal',
    );
    await I.clickFixed(closeButtonSelector);
    await I.waitFor();
    await I.clickFixed(secondSelectedSpecialConditionSelector);
    await I.clickFixed(conditionNoneItemSelector);
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor();
    await stockMarginOrder.compareUrl('/mobile/trade/margin/new/confirm');
    stockMarginOrder.takeScreenshot.marginNewOrderInput('Test_item_No.13_Go_to_order_confirmation_screen');
});

Scenario('Test item No.21 Deposit request', async ({ }) => {
    const depositRequest = '//*[@data-testid="marginNew_depositRequest_id"]';
    // Transition to Deposit request
    await common.clickCardItem(depositRequest, '/mobile/cashflow/depositrequest', 'external');
});

Scenario('Test item No.14 Caution', async ({ I, stockMarginOrder }) => {
    const cautionSelector = '//*[@data-testid="marginNew_caution_id"]';
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderInput('Test_item_No.14_Opening_the_caution_accordion');
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderInput('Test_item_No.14_Closing_the_caution_accordion');
});

Scenario('Test item No.15 Setting the preferred ordering method', async ({ I, stockMarginOrder }) => {
    const orderingMethodSettingsSelector = '//*[@data-testid="marginNew_priorityOrderMethod_id"]';
    await I.clickFixed(orderingMethodSettingsSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.15_Closing_Setting_the_preferred_ordering_method_accordion',
    );
    await I.clickFixed(orderingMethodSettingsSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.15_Opening_Setting_the_preferred_ordering_method_accordion',
    );
});

Scenario('Test item No.16 Screen display settings', async ({ I, stockMarginOrder }) => {
    const screenDisplaySettingsSelector = '//*[@data-testid="marginNew_screenDisplaySetting_id"]';
    await I.clickFixed(screenDisplaySettingsSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.16_Closing_Screen_display_settings_accordion',
    );
    await I.clickFixed(screenDisplaySettingsSelector);
    await I.waitFor();
    await stockMarginOrder.takeScreenshot.marginNewOrderInput(
        'Test_item_No.16_Opening_Screen_display_settings_accordion',
    );
});
