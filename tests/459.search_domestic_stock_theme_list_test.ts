import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Feature('Symbol_ProductSearch - SearchDomesticStockThemeList');

Before(async ({ I, loginAndSwitchToWebAs, search }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user18 });
    await search.goToDomesticStockThemeList();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Theme list page', async ({ I }) => {
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search/theme', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeList}_Display_Domestic_Stock_Theme_list_page.png`);
});

Scenario('Test item No.1 Swipe up or down Themes list', async ({ I }) => {
    const themeListSelector = '//*[@data-testid="themeTop_themeList_id"]';
    await I.swipeUpFixed(themeListSelector);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeList}_Test_item_No.1_Swipe_up_themes_list.png`);
    await I.swipeDownFixed(themeListSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeList}_Test_item_No.1_Swipe_down_themes_list.png`);
});

Scenario('Test item No.2 Individual Themes', async ({ I, search }) => {
    await search.goToDomesticStockThemeDetail();
    I.see('テーマ詳細', 'body');
    I.seeElement('//*[@data-testid="themeDetail_themeList_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search/theme/detail', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchDomesticStockThemeList}_Test_item_No.2_Go_to_theme_details_page.png`);
});

