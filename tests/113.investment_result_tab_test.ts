import { COL<PERSON>, COOKIE_KEY, PAGE_URL, USER_ID } from "../const/constant";

Feature('AssetStatus - InvestmentResultTabPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/InvestmentResult/Tab.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user4 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test investment result page', async ({ I }) => {
    await I.amOnPage(PAGE_URL.mypagePortfolio);
    await I.waitFor('mediumWait');
    const processingButton = locate('button').withChild(locate('div').withChild(locate('p').withText('投資成績')));
    I.click(processingButton);
    await I.waitFor();
});

Scenario('test Item 1 balance tab page', async ({ I }) => {
    const balanceTab = '//*[@data-testid="investmentResult_balance_id"]';
    I.waitForElement(balanceTab);
    I.click(balanceTab);
    const backgroundColor = await I.grabCssPropertyFrom(balanceTab, 'background-color');
    I.assertEqual(backgroundColor, COLOR.mainColor, `backgroundColor is not ${COLOR.mainColor}`);
    await I.waitFor('mediumWait');
    I.waitForElement('//*[@data-testid="assetBalance_legend_id"]', 4);
    I.saveScreenshot('113.investment_result_tab_balanceTab.png');
});

Scenario('test Item 2 investment profit loss tab page', async ({ I }) => {
    const investmentProfitLossTab = '//*[@data-testid="investmentResult_investmentProfitLoss_id"]';
    I.waitForElement(investmentProfitLossTab);
    I.click(investmentProfitLossTab);
    const backgroundColor = await I.grabCssPropertyFrom(investmentProfitLossTab, 'background-color');
    I.assertEqual(backgroundColor, COLOR.mainColor, `backgroundColor is not ${COLOR.mainColor}`);
    await I.waitFor();
    I.seeElement('//*[@id="mypage-inves"]/div/div[2]/div[1]/div[1]/p[1]');
    I.saveScreenshot('113.investment_result_tab_investmentProfitLossTab.png');
});

Scenario('test Item 3 asset performance tab page', async ({ I }) => {
    const assetPerformanceTab = '//*[@data-testid="investmentResult_assetPerformance_id"]';
    I.waitForElement(assetPerformanceTab);
    I.click(assetPerformanceTab);
    const backgroundColor = await I.grabCssPropertyFrom(assetPerformanceTab, 'background-color');
    I.assertEqual(backgroundColor, COLOR.mainColor, `backgroundColor is not ${COLOR.mainColor}`);
    await I.waitFor();
    I.seeElement('//*[@data-testid="assetTransition_legend_id"]');
    I.saveScreenshot('113.investment_result_tab_assetPerformanceTab.png');
});
