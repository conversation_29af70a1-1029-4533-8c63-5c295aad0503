import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Feature('Market - FavoriteRegisterModal');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user11 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Favorite Register Modal', async ({ I, market }) => {
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    // Navigate to Market Indicator Detail page
    await market.goToMarketIndicatorDetail();
    // Open modal
    await market.openFavoriteModal();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_Favorite_Register_Modal.png`);
});

Scenario('Test item No.1 close', async ({ I }) => {
    const closeBtn = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(closeBtn);
    I.clickFixed(closeBtn);
    await I.waitFor();
    I.dontSeeElement('//*[@data-testid="common_rightSlide_favorite_id"]');
    I.dontSeeElement('//*[@data-testid="favoriteRegisterModal_confirm_id"]');
    I.waitForText('マーケット', 3, 'body');
    I.seeElement('//*[@data-testid="commonChart_howToViewChart_id"]');
    I.seeElement('//*[@data-testid="marketIndicatorDetail_favorite_id"]');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_item_No.1_Close_Favorite_Register_Modal.png`);
});

Scenario('Test item No.3 Favorite Selection', async ({ I, market }) => {
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    // Navigate to Market Indicator Detail page
    await market.goToMarketIndicatorDetail();
    // Open modal
    await market.openFavoriteModal();
    // Toggle checkbox
    const checkboxItem = '[data-testid="favoriteRegisterModal_favoriteSelection_id_0"]';
    I.waitForElement(checkboxItem);
    I.clickFixed(checkboxItem);
    await I.waitFor();
    const checkIcon = `${checkboxItem} button svg`;
    const checkOnColor = await I.grabCssPropertyFrom(checkIcon, 'color');
    I.assertContain(checkOnColor, 'rgba(255, 86, 0, 1)');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_item_No.3_Favorite_Selection_ON.png`);
    await I.waitFor('mediumWait');
    I.clickFixed(checkboxItem);
    await I.waitFor();
    const checkOffColor = await I.grabCssPropertyFrom(checkIcon, 'color');
    I.assertContain(checkOffColor, 'rgba(137, 137, 139, 1)');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_item_No.3_Favorite_Selection_OFF.png`);
});

Scenario('Test item No.7 Kabu Board Seletion', async ({ I }) => {
    await I.swipeUpFixed('body');
    // Toggle checkbox
    const checkboxItem = '[data-testid="favoriteRegisterModal_kabuSelection_id_0"]';
    I.waitForElement(checkboxItem);
    I.clickFixed(checkboxItem);
    await I.waitFor();
    const checkIcon = `${checkboxItem} button svg`;
    const checkOnColor = await I.grabCssPropertyFrom(checkIcon, 'color');
    I.assertContain(checkOnColor, 'rgba(255, 86, 0, 1)');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_item_No.3_Kabu_Board_Selection_ON.png`);
    await I.waitFor('mediumWait');
    I.clickFixed(checkboxItem);
    await I.waitFor();
    const checkOffColor = await I.grabCssPropertyFrom(checkIcon, 'color');
    I.assertContain(checkOffColor, 'rgba(137, 137, 139, 1)');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_item_No.3_Kabu_Board_Selection_OFF.png`);
});

Scenario('Test item No.9 List Add button', async ({ I }) => {
    const listAddBtn = '//*[@data-testid="favoriteRegisterModal_listAdd_id"]';
    const cancelBtn = '//*[@data-testid="favoritesList_cancelButton_id"]';
    const closeBtn = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(listAddBtn);
    I.clickFixed(listAddBtn);
    await I.waitFor();
    I.seeElement('//*[@data-testid="favoritesList_favoriteListName_id"]');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.favoriteRegisterModal}_Test_item_No.9_List_Add_button.png`);
    await I.clickFixed(cancelBtn);
    await I.waitFor();
    await I.clickFixed(closeBtn);
});
