import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveHistory');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user46 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI of Customer Reserve History Page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveHistoryPage();
    I.saveScreenshot('1330.Test_item_No.0_UI_of_Customer_Reserve_History_Page.png');
});
Scenario('Test Item 1: Check Go To Reserve Plan Page', async ({ I }) => {
    // Go to reserve plan page
    await I.click('//*[@data-testid="reservePlan_reservePlan_tab"]');
    I.waitFor('shortWait');
    I.saveScreenshot('1298.Test_item_No.1_UI_of_Reserve_Plan_Page.png');
});
Scenario('Test Item 2: Check Go To Reserve Calendar Page', async ({ I }) => {
    // Go to reserve calendar page
    await I.click('//*[@data-testid="reservePlan_reserveCalendar_tab"]');
    I.waitFor('shortWait');
    I.saveScreenshot('1330.Test_item_No.2_UI_of_Reserve_Calendar_Page.png');
    // Go back to reserve calendar page to continue test
    await I.click('//*[@data-testid="reservePlan_reserveCalendar_tab"]');
});
Scenario('Test Item 10: Check Next Page Navigation', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveHistoryPage();
    I.waitFor();
    const nextPageButton = '//*[@data-testid="reserveHistory_next_id"]';
    I.saveScreenshot('1330.Test_item_No.10_Go_To_Next_Page_before_click.png');
    I.clickFixed(nextPageButton);
    I.saveScreenshot('1330.Test_item_No.10_Go_To_Next_Page_after_click.png');
});
Scenario('Test Item 8: Check Previous Page Navigation', async ({ I, accumulation }) => {

    const previousPageButton = '//*[@data-testid="reserveHistory_prev_id"]';
    I.saveScreenshot('1330.Test_item_No.8_Go_To_Previous_Page_before_click.png');
    I.clickFixed(previousPageButton);
    I.saveScreenshot('1330.Test_item_No.8_Go_To_Previous_Page_after_click.png');
});
Scenario('Test Item 9: Check Page Number Navigation', async ({ I }) => {
    const enabledButton = '//div[@data-testid="reserveHistory_page_id"]//button[not(@disabled)]';

    try {
        if (await I.grabNumberOfVisibleElements(enabledButton) > 0) {
            I.clickFixed(`(${enabledButton})[1]`);
            I.waitFor('shortWait');
            I.saveScreenshot('1330.Test_item_No.9_click_enabled_pagination_button.png');
        } else {
            console.log('No enabled pagination buttons found');
            I.saveScreenshot('1330.Test_item_No.9_no_enabled_buttons.png');
        }
    } catch (e) {
        console.log('Error when clicking pagination button:', e);
        I.saveScreenshot('1330.Test_item_No.9_error.png');
    }
});

Scenario('Test Item 19: Check navigation to investment trust transaction history detail', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveHistoryPage();
    const tradingNisaHistoryButton = '//table[.//span[contains(text(), "投信")]][.//td[contains(text(), "NISA")]][.//*[@data-testid="reserveHistory_tradingHistory_id"]]//*[@data-testid="reserveHistory_tradingHistory_id"]';
    await I.scrollToElement(tradingNisaHistoryButton);
    I.waitFor('shortWait');
    // Check if current URL contains either NISA or normal transaction history path
    // ・A.tsumitateHistoryList.executionAccountType=NISA口座 or 積立NISA口座 or NISA(成長投資枠) or NISA(つみたて投資枠)の場合は下記のURLに遷移 {member-site-url}/iPhone/Account/TradeRirekiNISA/TradeShousaiToshin.asp?YakujoNo=A.tsumitateHistoryList.executionId
    await common.clickCardItem(tradingNisaHistoryButton, '/account/traderirekiNISA/TradeShousaiToshin.asp', 'external', true);

    // ・If not, go to the URL below {member-site-url}/iPhone/Account/TradeRireki/TradeShousaiToshin.asp?YakujoNo=A.tsumitateHistoryList.executionId
    await I.switchToWeb();
    await I.waitFor();
    const tradingNormalHistoryButton = '//table[.//span[contains(text(), "投信")]][not(.//td[contains(text(), "NISA")])][.//*[@data-testid="reserveHistory_tradingHistory_id"]]//*[@data-testid="reserveHistory_tradingHistory_id"]';
    await common.clickCardItem(tradingNormalHistoryButton, '/account/traderireki/TradeShousaiToshin.asp', 'external', true);
});

Scenario('Test Item 21: Check filter modal', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveHistoryPage();
    // Click on filter button
    const filterButton = '//*[@data-testid="reserveHistory_filterSetting_id"]';
    I.clickFixed(filterButton);
    I.waitFor('shortWait');
    // Verify the filter modal
    I.saveScreenshot('1330.Test_item_No.21_Filter_Modal.png');

});
Scenario('Test Item 22: Check display symbol dropdown menu', async ({ I }) => {
    // Click on display symbol dropdown menu
    const displaySymbolDropdown = '//*[@data-testid="reserveHistory_displayOrder_id"]';
    I.clickFixed(displaySymbolDropdown);
    I.waitFor('shortWait');
    // Verify the display symbol dropdown menu
    I.saveScreenshot('1330.Test_item_No.22_Display_Symbol_Dropdown_Menu.png');

});
Scenario('Test Item 23: Check Switch product category ON/OFF', async ({ I }) => {
    const firstLabel = '//label[@value="TSUMITATE_PRODUCT_TYPE_FUND"]';
    try {
        for (let i = 1; i <= 3; i++) {
            I.waitForElement(firstLabel, 5);
            I.clickFixed(firstLabel);
        }
        I.saveScreenshot(`1298.Test_item_No.47_Switch_Product_Category.png`);

    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1298.Test_item_No.47_error.png');
    }
});
Scenario('Test Item 24: Check Switch payment method ON/OFF', async ({ I }) => {
    const firstLabel = '//label[@value="PAYMENT_TYPE_AZUKARI"]';

    try {
        for (let i = 1; i <= 3; i++) {
            I.waitForElement(firstLabel, 5);
            I.clickFixed(firstLabel);
        }
        I.saveScreenshot(`1330.Test_item_No.24_Switch_Payment_Method.png`);
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1330.Test_item_No.24_error.png');
    }
});
Scenario('Test Item 25: Check Switch display account ON/OFF', async ({ I }) => {
    const firstLabel = '//label[@value="ACCOUNT_TYPE_TOKUTEI"]';

    try {
        for (let i = 1; i <= 3; i++) {
            I.waitForElement(firstLabel, 5);
            I.clickFixed(firstLabel);
        }
        I.saveScreenshot(`1330.Test_item_No.25_Switch_Display_Account.png`);
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1330.Test_item_No.25_error.png');
    }
});
Scenario('Test Item 26: Check initial display', async ({ I }) => {
    const clearButton = '//button[contains(text(), "クリア")]';
    I.clickFixed(clearButton);
    I.waitFor('shortWait');
    I.saveScreenshot('1330.Test_item_No.26_Initial_Display.png');
});
Scenario('Test Item 27: Check confirm modal', async ({ I }) => {
    const confirmButton = '//button[contains(text(), "確定する")]';
    I.clickFixed(confirmButton);
    I.waitFor('shortWait');
    I.saveScreenshot('1330.Test_item_No.27_Confirm_Modal.png');
});
Scenario('Test Item 30: Check point usage status', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveHistoryPage();
    await I.waitFor();
    const pointUsageStatusButton = '//*[@data-testid="reserveHistory_pointUsageStatus_id"]';
    I.waitForElement(pointUsageStatusButton, 2);
    await I.scrollToElement(pointUsageStatusButton);
    await common.clickCardItem(pointUsageStatusButton, '/ap/iPhone/Personal/UsePoint/List', 'kcMemberSite');
});
Scenario('Test Item 31: Check scroll top in reserve history', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveHistoryPage();
    I.waitFor();
    const lastTable = '(//table[@role="table" and contains(@class, "chakra-table")])[last()]';
    I.waitForElement(lastTable, 2);
    I.scrollToElement(lastTable);
    const scrollToTopButton = '//*[@id="scrollButton"]';
    I.saveScreenshot('1330.Test_item_No.31_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    I.waitFor('shortWait');
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1330.Test_item_No.31_scrollToTop_dont_see_button.png');
});
