import { event } from 'codeceptjs';
import { COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('InvestmentProducts - DomesticStockCashTradingBuyInput');

let userId = USER_ID.user25;
const scenarioItemsWithCondition = ['Test item No.13-28 Execution method setting panel - Automated trading - Execution content - Execution conditions'];
event.dispatcher.on(event.test.before, (test) => {
    userId = scenarioItemsWithCondition.includes(test.title) ? USER_ID.user23 : USER_ID.user25;
});

Before(async ({ I, loginAndSwitchToWebAs, stockCashOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: userId });
    await stockCashOrder.goToCashBuyInput();
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Display Domestic Stock Cash Trading Buy Input', async ({ stockCashOrder }) => {
    await stockCashOrder.compareUrl(stockCashOrder.urls.cashBuyInput);
    stockCashOrder.takeScreenshot.cashBuyInput('Display_Domestic_Stock_Cash_Trading_Buy_input');
});

Scenario('Test item No.3 Trade Restrictions and Trade Caution Information', async ({ I, stockCashOrder }) => {
    await I.clickFixed(stockCashOrder.locator.buyInputCautionInfo);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.3_Show_Common_UI_Trade_Caution_Information_Modal');
});

Scenario('Test item No.6 Market', async ({ I, stockCashOrder }) => {
    const marketWrapperSelector = '//div[button[@data-testid="buyInput_exchange_id"]]';
    const marketSelector = '//*[@data-testid="buyInput_exchange_id"]';
    const secondMarketOptionSelector = `${marketWrapperSelector}//*[@data-testid="common_MenuList_id"]//button[2]`
    await I.clickFixed(marketSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.6_Show_pull_down_menu');
    await I.clickFixed(secondMarketOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.6_Execute_process_market_change_on_screen"');
});

Scenario('Test item No.7 Account category', async ({ I, stockCashOrder }) => {
    const accountTypeWrapperSelector = '//*[@data-testid="buyInput_accountType_id"]';
    const secondAccountTypeOptionSelector = `${accountTypeWrapperSelector}/label[2]`;
    const secondAccountTypeIconOptionSelector = `${secondAccountTypeOptionSelector}/p`;
    await I.clickFixed(secondAccountTypeOptionSelector);
    I.assertEqual(await I.grabCssPropertyFrom(secondAccountTypeIconOptionSelector, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.7_Select_the_item_you_tapped');
});

Scenario('Test item No.8 Order deadline', async ({ I, stockCashOrder }) => {
    const orderDeadlineWrapperSelector = '//*[@data-testid="buyInput_orderPeriod_id"]';
    const secondOrderDeadlineOptionSelector = `${orderDeadlineWrapperSelector}/label[2]`;
    const secondOrderDeadlineOptionIconSelector = `${orderDeadlineWrapperSelector}/label[2]/p`;
    const specifyPeriodIconOptionSelector = `${orderDeadlineWrapperSelector}/label[p[contains(text(), "期間指定")]]`;
    const bottomSheetSelector = '#bottom-sheet-container';
    await I.clickFixed(secondOrderDeadlineOptionSelector);
    I.assertEqual(await I.grabCssPropertyFrom(secondOrderDeadlineOptionIconSelector, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.8_Select_the_item_you_tapped');
    await I.clickFixed(specifyPeriodIconOptionSelector);
    await I.waitFor();
    I.seeElement(bottomSheetSelector);
    I.see('注文期限の日付をご選択ください。', 'body');
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.8_When_you_tap_Specify_period_Overlay_the_common_UI_Order_deadline_date_picker');
});

Scenario('Test item No.9 Order deadline date', async ({ I, stockCashOrder }) => {
    const orderDeadlineWrapperSelector = '//*[@data-testid="buyInput_orderPeriod_id"]';
    const orderDeadlineDateSelector = '//*[@data-testid="buyInput_orderExpireDate_id"]/div[1]/div[1]';
    const specifyPeriodIconOptionSelector = `${orderDeadlineWrapperSelector}/label[p[contains(text(), "期間指定")]]`;
    const bottomSheetSelector = '#bottom-sheet-container';
    const closeButton = '//button[@aria-label="closeIcon"]';
    await I.clickFixed(specifyPeriodIconOptionSelector);
    await I.waitFor();
    I.waitForElement(bottomSheetSelector, 2);
    await I.clickFixed(closeButton);
    await I.waitFor();
    await I.clickFixed(orderDeadlineDateSelector);
    await I.waitFor();
    I.seeElement(bottomSheetSelector);
    I.see('注文期限の日付をご選択ください。', 'body');
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.8_When_you_tap_Specify_period_Overlay_the_common_UI_Order_deadline_date_picker');
});

Scenario('Test item No.12 Select execution method', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const successMethodSelector = `${executionMethodWrapperSelector}/button[2]`;
    await I.scrollToElement(successMethodSelector);
    await I.clickFixed(successMethodSelector);
    I.assertEqual(await I.grabCssPropertyFrom(successMethodSelector, 'background-color'), 'rgba(239, 239, 239, 1)', 'Background color is not equal');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.12_Select_the_item_you_tapped');
});

Scenario('Test item No.13-1 Execution method setting panel - quantity', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const quantityWraperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "数量")]]/form/div[1]`;
    const minusButtonSelector = `${quantityWraperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusButtonSelector = `${quantityWraperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.scrollToElement(plusButtonSelector);
    await I.clickFixed(plusButtonSelector);
    await I.waitFor();
    await I.clickFixed(plusButtonSelector);
    await I.waitFor();
    await I.clickFixed(minusButtonSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-1_Common_UI_Perform_processing_as_described_in_the_numerical_stepper');
});

Scenario('Test item No.13-2 Execution method setting panel - Automated trading execution method', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const dropdownSelector = `${executionMethodSettingPanelSelector}//button[p[contains(text(), "逆指値")]]`;
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.clickFixed(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(dropdownSelector);
    await I.waitFor();
    I.see('逆指値', 'body');
    I.see('W指値', 'body');
    I.see('±指値', 'body');
    I.see('トレーリングストップ', 'body');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-2_Show_the_following_drop_down_menus');
});

Scenario('Test item No.13-3 Execution method setting panel - Limit order setting - Price', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const priceWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "価格")]]/form/div[1]`;
    const minusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.scrollToElement(plusPriceButtonSelector);
    await I.clickFixed(plusPriceButtonSelector);
    await I.waitFor();
    await I.clickFixed(plusPriceButtonSelector);
    await I.waitFor();
    await I.clickFixed(minusPriceButtonSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-3_Follow_the_price_input_item_specifications');
});

Scenario('Test item No.13-4 Execution method setting panel - Limit order setting - Depth of market input', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const depthOfMarketInputSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "板入力")]]`;
    const headerBoardInput = '//*[@id="headerBoardInput"]';
    const boardInputPrice = '//*[@id="ask_10"]';
    await I.scrollToElement(depthOfMarketInputSelector);
    await I.clickFixed(depthOfMarketInputSelector);
    await I.waitFor();
    await I.scrollToElement(headerBoardInput);
    I.seeElement(headerBoardInput);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-4_Show_depth_of_market_input_modal');
    await I.clickFixed(boardInputPrice);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-4_Set_the_price_selected_in_the_depth_of_market_input_modal_as_the_price');
});

Scenario('Test item No.13-5 Execution method setting panel - Limit order setting - Chart input', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const limitOrderTabSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]/button[1]';
    const chartInputButtonSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "チャート入力")]]`;
    const chartInputContainerSelector = '.chart-input';
    await I.scrollToElement(chartInputButtonSelector);
    await I.clickFixed(chartInputButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(chartInputContainerSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-5_Displays_the_chart_input_modal');
    await I.clickFixed(chartInputContainerSelector);
    await I.waitFor();
    await I.scrollToElement(limitOrderTabSelector);
    await I.clickFixed(limitOrderTabSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-5_The_selected_price_is_rounded_and_set_as_the_price_when_the_stepper_input_is_confirmed');
});

Scenario('Test item No.13-6 Execution method setting panel - Execution conditions', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const executionConditionWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "執行条件")]]`;
    const conditionDropdownSelector = `${executionConditionWrapperSelector}/button[1]`;
    const secondConditionSelector = `${executionConditionWrapperSelector}//*[@data-testid="common_MenuList_id"]//button[2]`;
    await I.waitFor();
    await I.scrollToElement(conditionDropdownSelector);
    await I.clickFixed(conditionDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-6_Show_pull_down_menu');
    await I.scrollToElement(secondConditionSelector);
    await I.clickFixed(secondConditionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-6_No_special_processing_will_be_performed_if_the_item_is_changed');
});

Scenario('Test item No.13-7 Execution method setting panel - Special conditions', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const specialConditionWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "特殊条件")]]`;
    const conditionDropdownSelector = `${specialConditionWrapperSelector}/button[1]`;
    const relayConditionSelector = `${specialConditionWrapperSelector}//*[@data-testid="common_MenuList_id"]//button[contains(@value, "SPECIAL_CONDITION_RELAY")]`;
    await I.scrollToElement(conditionDropdownSelector);
    await I.clickFixed(conditionDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-7_Show_pull_down_menu');
    await I.clickFixed(relayConditionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-7_No_special_processing_will_be_performed_if_the_item_is_changed');
});

Scenario('Test item No.13-8 Execution method setting panel - Stop loss - Indicator', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const stopLossIndicatorSelector = `${executionMethodSettingPanelSelector}//button[p[contains(text(), "逆指値")]]`;
    const otherItemSelector = `${executionMethodSettingPanelSelector}//div[contains(@class, "select-collapse")]//p[contains(text(), "W指値")]`;
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(stopLossIndicatorSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-8_Show_pull_down_menu');
    await I.clickFixed(otherItemSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-8_Update_the_part_that_updates_the_display_status_by_referring_to_the_relevant_item');
});

Scenario('Test item No.13-9 Execution method setting panel - Stop limit - Price', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const stopLimitPriceInputWrapperSelector = `${executionMethodSettingPanelSelector}//div[input[contains(@placeholder, "価格を入力")]]`;
    const minusPriceSelector = `${stopLimitPriceInputWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusPriceSelector = `${stopLimitPriceInputWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    const priceIndicatorSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "が")]]/button[1]`;
    const nk225ItemSelector = '//button[@data-testid="common_MenuItem_id"][contains(@value, "NK225")]';
    const topixItemSelector = '//button[@data-testid="common_MenuItem_id"][contains(@value, "TOPIX")]';
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(automatedTradingMethodSelector);
    await I.waitFor();
    await I.scrollToElement(minusPriceSelector);
    // Indicator is stock price: Follow the input item specifications for stock price
    await I.clickFixed(minusPriceSelector);
    await I.clickFixed(plusPriceSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-9_Indicator_is_stock_price');

    // Other than the above: Follow the input item specifications for NK225 and TOPIX
    await I.clickFixed(priceIndicatorSelector);
    await I.waitFor();
    // NK225
    await I.clickFixed(nk225ItemSelector);
    await I.waitFor();
    await I.clickFixed(minusPriceSelector);
    await I.clickFixed(plusPriceSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-9_Indicator_is_NK225');

    await I.clickFixed(priceIndicatorSelector);
    await I.waitFor();
    // TOPIX
    await I.clickFixed(topixItemSelector);
    await I.waitFor();
    await I.clickFixed(minusPriceSelector);
    await I.clickFixed(plusPriceSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-9_Indicator_is_TOPIX');
});

Scenario('Test item No.13-10 Execution method setting panel - Stop limit - Depth input', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const depthInputSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "板入力")]]/..`;
    const ask10PriceSelector = '//*[@id="ask_10"]';
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(automatedTradingMethodSelector);
    await I.waitFor();
    await I.scrollToElement(depthInputSelector);
    await I.clickFixed(depthInputSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-10_Show_depth_input_modal');
    await I.clickFixed(ask10PriceSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-10_Set_price_to_price_selected_in_depth_input_modal');
});

Scenario('Test item No.13-11 Execution method setting panel - Stop limit - Chart input', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const chartInputSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "チャート入力")]]/..`;
    const chartInputContainerSelector = '.chart-input';
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(automatedTradingMethodSelector);
    await I.waitFor();
    await I.scrollToElement(chartInputSelector);
    await I.clickFixed(chartInputSelector);
    await I.waitFor('mediumWait');
    I.seeElement('.chart-input');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-11_Display_chart_input_modal');
    await I.clickFixed(chartInputContainerSelector);
    await I.waitFor();
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.clickFixed(automatedTradingMethodSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-11_The_selected_price_will_be_rounded_and_set_to_the_price_when_the_stepper_input_is_confirmed');
});

Scenario('Test item No.13-12 Execution method setting panel - Stop limit - Above or Below', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const aboveOrBelowWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "になったら")]]`;
    const aboveOrBelowDropdownSelector = `${aboveOrBelowWrapperSelector}/button[1]`;
    const belowOptionSelector = `${aboveOrBelowWrapperSelector}//button[contains(@value, UNDER_OVER_UNDER)]`;
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.waitFor();
    await I.clickFixed(automatedTradingMethodSelector);
    await I.waitFor();
    await I.scrollToElement(aboveOrBelowDropdownSelector);
    await I.clickFixed(aboveOrBelowDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-12_Show_pull_down_menu');
    await I.clickFixed(belowOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-12_No_special_processing_will_be_performed_if_the_item_is_changed');
});

Scenario('Test item No.13-13 Execution method setting panel - W limit order - Trigger 1 - Execution method', async ({ I, stockCashOrder }) => {
    await stockCashOrder.selectWLimitOrderMethod();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-13_Select_the_element_you_tap');
});

Scenario('Test item No.13-14 Execution method setting panel - W limit order - Trigger 1 - Price', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const priceWrapperSelector = `${executionMethodSettingPanelSelector}//div[input[contains(@placeholder, "価格を入力")]]`;
    const minusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await stockCashOrder.selectWLimitOrderMethod();
    await I.clickFixed(plusPriceButtonSelector);
    await I.clickFixed(plusPriceButtonSelector);
    await I.clickFixed(minusPriceButtonSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-14_Follow_the_price_input_item_specifications');
});

Scenario('Test item No.13-15 Execution method setting panel - W limit order - Trigger 1 - Depth input', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const depthInputSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "値幅：")]]//div[p[contains(text(), "板入力")]]/..`;
    const ask10Price = '//*[@id="ask_10"]';
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(depthInputSelector);
    await I.clickFixed(depthInputSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-15_Show_depth_input_modal');
    await I.scrollToElement(ask10Price);
    await I.clickFixed(ask10Price);
    await I.waitFor();
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.clickFixed(automatedTradingMethodSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-15_Set_price_to_price_selected_in_depth_input_modal');
});

Scenario('Test item No.13-16 Execution method setting panel - W limit - Trigger 1 - Chart input', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const chartInputSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "値幅：")]]//div[p[contains(text(), "チャート入力")]]/..`;
    const chartInputContainerSelector = '.chart-input';
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(chartInputSelector);
    await I.clickFixed(chartInputSelector);
    await I.waitFor('mediumWait');
    I.seeElement(chartInputContainerSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-16_Display_chart_input_modal');
    await I.clickFixed(chartInputContainerSelector);
    await I.waitFor();
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.clickFixed(automatedTradingMethodSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-16_The_selected_price_will_be_rounded_and_set_to_the_price_when_the_stepper_input_is_confirmed');
});

Scenario('Test item No.13-17 Execution method setting panel - W limit - Trigger 1 - Morning/Afternoon', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const failureBtn = `${executionMethodSettingPanelSelector}//p[contains(text(), "不成")]`;
    const morningOption = `${executionMethodSettingPanelSelector}//p[contains(text(), "前場")]`;
    const afternoonOption = `${executionMethodSettingPanelSelector}//p[contains(text(), "後場")]`;
    await stockCashOrder.selectWLimitOrderMethod();
    await I.clickFixed(failureBtn);
    await I.waitFor();
    await I.scrollToElement(morningOption);
    await I.clickFixed(morningOption);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-17_Select_morning');
    await I.clickFixed(afternoonOption);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-17_Select_afternoon');
});

Scenario('Test item No.13-18 Execution method setting panel - W limit - Trigger 2 - Indicator', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const priceIndicatorSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "が")]]/button[1]`;
    const nk225ItemSelector = '//button[@data-testid="common_MenuItem_id"][contains(@value, "NK225")]';
    const topixItemSelector = '//button[@data-testid="common_MenuItem_id"][contains(@value, "TOPIX")]';
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(priceIndicatorSelector);
    await I.clickFixed(priceIndicatorSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-18_Show_pull_down_menu');
    // Select NK225
    await I.clickFixed(nk225ItemSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-18_Update_the_part_that_updates_the_display_status_by_referring_to_the_NK225_item_in_question');
    // Select TOPIX
    await I.clickFixed(priceIndicatorSelector);
    await I.clickFixed(topixItemSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-18_Update_the_part_that_updates_the_display_status_by_referring_to_the_TOPIX_item_in_question');
});

Scenario('Test item No.13-19 Execution method setting panel - W limit - Trigger 2 - Price', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const wLimitPriceInputWrapperSelector = `(${executionMethodSettingPanelSelector}//div[input[contains(@placeholder, "価格を入力")]])[2]`;
    const minusPriceSelector = `${wLimitPriceInputWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusPriceSelector = `${wLimitPriceInputWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    const priceIndicatorSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "が")]]/button[1]`;
    const nk225ItemSelector = '//button[@data-testid="common_MenuItem_id"][contains(@value, "NK225")]';
    const topixItemSelector = '//button[@data-testid="common_MenuItem_id"][contains(@value, "TOPIX")]';
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(minusPriceSelector);
    // Indicator is stock price: Follow the input item specifications for stock price
    await I.clickFixed(minusPriceSelector);
    await I.clickFixed(plusPriceSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-19_Indicator_is_stock_price');

    // Other than the above: Follow the input item specifications for NK225 and TOPIX
    await I.clickFixed(priceIndicatorSelector);
    await I.waitFor();
    // NK225
    await I.clickFixed(nk225ItemSelector);
    await I.waitFor();
    await I.clickFixed(minusPriceSelector);
    await I.clickFixed(plusPriceSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-19_Indicator_is_NK225');

    await I.clickFixed(priceIndicatorSelector);
    await I.waitFor();
    // TOPIX
    await I.clickFixed(topixItemSelector);
    await I.waitFor();
    await I.clickFixed(minusPriceSelector);
    await I.clickFixed(plusPriceSelector);
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-19_Indicator_is_TOPIX');
});

Scenario('Test item No.13-20 Execution method setting panel - W limit - Trigger 2 - Depth input', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const depthInputSelector = `(${executionMethodSettingPanelSelector}//div[p[contains(text(), "価格")]])[2]//div[p[contains(text(), "板入力")]]/..`;
    const ask10Price = '//*[@id="ask_10"]';
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(depthInputSelector);
    await I.waitFor();
    await I.clickFixed(depthInputSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-20_Show_depth_input_modal');
    await I.clickFixed(ask10Price);
    await I.waitFor();
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.clickFixed(automatedTradingMethodSelector);
    await I.swipeDirection('up', 0.8);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-20_Set_price_to_price_selected_in_depth_input_modal');
});

Scenario('Test item No.13-21 Execution method setting panel - W limit - Trigger 2 - Chart input', async ({ I, stockCashOrder }) => {
    const executionMethodWrapperSelector = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    const automatedTradingMethodSelector = `${executionMethodWrapperSelector}/button[3]`;
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const chartInputSelector = `(${executionMethodSettingPanelSelector}//div[p[contains(text(), "価格")]])[2]//div[p[contains(text(), "チャート入力")]]/..`;
    const chartInputContainerSelector = '.chart-input';
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(chartInputSelector);
    await I.waitFor();
    await I.clickFixed(chartInputSelector);
    await I.waitFor('mediumWait');
    I.seeElement('.chart-input');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-21_Display_chart_input_modal');
    await I.clickFixed(chartInputContainerSelector);
    await I.waitFor();
    await I.scrollToElement(automatedTradingMethodSelector);
    await I.clickFixed(automatedTradingMethodSelector);
    await I.swipeDirection('up', 0.8);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-21_The_selected_price_will_be_rounded_and_set_to_the_price_when_the_stepper_input_is_confirmed');
});

Scenario('Test item No.13-22 Execution method setting panel - W limit - Trigger 2 - Above or Below', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const aboveOrBelowWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "になったら")]]`;
    const aboveOrBelowDropdownSelector = `${aboveOrBelowWrapperSelector}/button[1]`;
    const belowOptionSelector = `${aboveOrBelowWrapperSelector}//button[contains(@value, UNDER_OVER_UNDER)]`;
    await stockCashOrder.selectWLimitOrderMethod();
    await I.scrollToElement(aboveOrBelowDropdownSelector);
    await I.clickFixed(aboveOrBelowDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-22_Show_pull_down_menu');
    await I.clickFixed(belowOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-22_No_special_processing_will_be_performed_if_the_item_is_changed');
});

Scenario('Test item No.13-23 Execution method setting panel - Trailing stop - Price', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const priceWrapperSelector = `${executionMethodSettingPanelSelector}//div[input[contains(@placeholder, "トレール幅を入力")]]`;
    const minusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await stockCashOrder.selectTrailingStopOrderMethod();
    await I.clickFixed(plusPriceButtonSelector);
    await I.clickFixed(plusPriceButtonSelector);
    await I.clickFixed(minusPriceButtonSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-23_Common_order_input_Trailing_stop_Price_content_is_processed_in_the_Common_UI_Numeric_stepper');
});

Scenario('Test item No.13-24 Execution method setting panel - ± limit price - execution method - opening price and closing price', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const openClosePriceWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "株価が")]]`;
    const openClosePriceDropdownSelector = `${openClosePriceWrapperSelector}//button[1]`;
    const closingOptionSelector = `${openClosePriceWrapperSelector}//button[span[contains(text(), "終値")]]`;
    await stockCashOrder.selectAdjustedLimitOrderMethod();
    await I.scrollToElement(openClosePriceDropdownSelector);
    await I.clickFixed(openClosePriceDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-24_Show_pull_down_menu');
    await I.clickFixed(closingOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-24_Reflect_the_selected_contents_on_the_opening_price_and_closing_price');
});

Scenario('Test item No.13-25 Execution method setting panel - ± limit price - Execution method - plus minus', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const openClosePriceWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "株価が")]]`;
    const plusPriceDropdownSelector = `${openClosePriceWrapperSelector}/button[2]`;
    const minusOptionSelector = `${openClosePriceWrapperSelector}//button[span[contains(text(), "-")]]`;
    await stockCashOrder.selectAdjustedLimitOrderMethod();
    await I.scrollToElement(plusPriceDropdownSelector);
    await I.clickFixed(plusPriceDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-25_Show_pull_down_menu');
    await I.clickFixed(minusOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-25_Reflect_the_selected_content_on_plus_minus');
});

Scenario('Test item No.13-26 Execution method setting panel - ± limit price - price', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const priceWrapperSelector = `${executionMethodSettingPanelSelector}//div[input[contains(@placeholder, "条件価格を入力")]]`;
    const minusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusPriceButtonSelector = `${priceWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await stockCashOrder.selectAdjustedLimitOrderMethod();
    await I.scrollToElement(plusPriceButtonSelector);
    await I.clickFixed(plusPriceButtonSelector);
    await I.clickFixed(plusPriceButtonSelector);
    await I.clickFixed(minusPriceButtonSelector);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-26_Common_order_input_Adjusted_Price_content_is_processed_according_to_the_common_UI_numerical_stepper');
});

Scenario('Test item No.13-27 Execution method settings panel - Automated trading - Execution content - Execution method', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const executionMethodWrapperSelector = `${executionMethodSettingPanelSelector}//div[label[.//div[contains(text(), "成行")]]]`;
    const secondOptionSelector = `${executionMethodWrapperSelector}//label[2]`;
    const secondOptionIconSelector = `${secondOptionSelector}/div/div/div`;
    await stockCashOrder.selectAutomatedTradingTab();
    await I.scrollToElement(secondOptionSelector);
    await I.clickFixed(secondOptionSelector);
    I.assertEqual(await I.grabCssPropertyFrom(secondOptionIconSelector, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-27_Select_the_element_you_tap');
});

Scenario(scenarioItemsWithCondition[0], async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const executionMethodWrapperSelector = `${executionMethodSettingPanelSelector}//div[label[.//div[contains(text(), "成行")]]]`;
    const secondOptionSelector = `${executionMethodWrapperSelector}//label[2]`;
    const executionConditionSelector = `//div[${executionMethodWrapperSelector}]//button[.//span[contains(text(), "条件なし")]]`;
    const secondExecutionConditionSelector = `//div[${executionMethodWrapperSelector}]//div[@data-testid="common_MenuList_id"]//button[contains(@value, "unsuccessful")]`;
    await stockCashOrder.selectAutomatedTradingTab();
    await I.scrollToElement(secondOptionSelector);
    await I.clickFixed(secondOptionSelector);
    await I.waitFor();
    await I.clickFixed(executionConditionSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-28_Show_pull_down_menu');
    await I.waitFor();
    await I.clickFixed(secondExecutionConditionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-28_No_special_processing_will_be_performed_if_the_item_is_changed');
});

Scenario('Test item No.13-29 Execution method setting panel - Automated trading - Execution content - Opening and closing prices', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const executionMethodWrapperSelector = `${executionMethodSettingPanelSelector}//div[contains(@role, "radiogroup")][.//div[contains(text(), "成行")]]`;
    const secondOptionSelector = `${executionMethodWrapperSelector}//label[2]`;
    const openCloseDropdownSelector = `(${executionMethodSettingPanelSelector}//button[.//div[span[contains(text(), "始値")]]])[2]`;
    const closeOptionSelector = `(${executionMethodSettingPanelSelector}//button[contains(@value, "closePrice")])[2]`;
    await stockCashOrder.selectAdjustedLimitOrderMethod();
    await I.scrollToElement(secondOptionSelector);
    await I.clickFixed(secondOptionSelector);
    await I.waitFor();
    await I.clickFixed(openCloseDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-29_Show_pull_down_menu');
    await I.clickFixed(closeOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-29_Reflect_the_selected_content_on_the_opening_and_closing_prices');
});

Scenario('Test item No.13-30 Execution method setting panel - Automated trading - Execution content - Plus/minus', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const executionMethodWrapperSelector = `${executionMethodSettingPanelSelector}//div[contains(@role, "radiogroup")][.//div[contains(text(), "成行")]]`;
    const secondOptionSelector = `${executionMethodWrapperSelector}//label[2]`;
    const plusMinusDropdownSelector = `(${executionMethodSettingPanelSelector}//button[.//div[span[contains(text(), "+")]]])[2]`;
    const minusOptionSelector = `(${executionMethodSettingPanelSelector}//button[contains(@value, "PLUS_MINUS_MINUS")])[2]`;
    await stockCashOrder.selectAdjustedLimitOrderMethod();
    await I.scrollToElement(secondOptionSelector);
    await I.clickFixed(secondOptionSelector);
    await I.waitFor();
    await I.clickFixed(plusMinusDropdownSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-30_Show_pull_down_menu');
    await I.clickFixed(minusOptionSelector);
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.13-30_Reflect_the_selected_content_in_plus_minus');
});

Scenario('Test item No.17 Screen display settings', async ({ I, stockCashOrder }) => {
    const screenDisplaySettingSelector = '//*[@data-testid="buyInput_screenDisplaySetting_id"]/div[1]';
    await I.clickFixed(screenDisplaySettingSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.17_Close_screen_display_settings');
    await I.clickFixed(screenDisplaySettingSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.17_Open_screen_display_settings');
});

Scenario('Test item No.16 Preferred ordering method settings', async ({ I, stockCashOrder }) => {
    const preferredOrderingMethodSettingSelector = '//*[@data-testid="buyInput_priorityOrderSetting_id"]/div[1]';
    await I.clickFixed(preferredOrderingMethodSettingSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.16_Close_Preferred_ordering_method_settings');
    await I.clickFixed(preferredOrderingMethodSettingSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.16_Open_Preferred_ordering_method_settings');
});

Scenario('Test item No.15 Caution', async ({ I, stockCashOrder }) => {
    const cautionSelector = '//*[@data-testid="buyInput_caution_id"]/div[1]';
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.15_Open_caution');
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    await I.waitFor();
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.15_Close_caution');
});

Scenario('Test item No.14 Go to the order confirmation screen', async ({ I, stockCashOrder }) => {
    const executionMethodSettingPanelSelector = '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]';
    const quantitySelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "数量")]]/form/div[1]/input[@data-testid="groupInputNumber_input_id"]`;
    const priceSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "価格")]]/form/div[1]/input[@data-testid="groupInputNumber_input_id"]`;
    const orderConfirmButtonSelector = '//*[@data-testid="buyInput_OrderConfirmButton_id"]';
    const specialConditionWrapperSelector = `${executionMethodSettingPanelSelector}//div[p[contains(text(), "特殊条件")]]`;
    const conditionDropdownSelector = `${specialConditionWrapperSelector}/button[1]`;
    const noConditionSelector = `${specialConditionWrapperSelector}//*[@data-testid="common_MenuList_id"]//button[contains(@value, "SPECIAL_CONDITION_NONE")]`;
    const relayConditionSelector = `${specialConditionWrapperSelector}//*[@data-testid="common_MenuList_id"]//button[contains(@value, "SPECIAL_CONDITION_RELAY")]`;
    const relayCloseButton = '//div[p[contains(text(), "リレー選択現物取引")]]/button[@data-testid="common_rightSlide_close_id"]';
    await I.scrollToElement(quantitySelector);
    I.fillField(quantitySelector, stockCashOrder.inputValues.quantity);
    I.fillField(priceSelector, stockCashOrder.inputValues.price);
    await I.scrollToElement(conditionDropdownSelector);
    // Select relay condition
    await I.clickFixed(conditionDropdownSelector);
    await I.waitFor();
    await I.clickFixed(relayConditionSelector);
    await I.waitFor();
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    I.see('リレー選択現物取引', 'body');
    await stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.14_Display_relay_order_selection');
    await I.clickFixed(relayCloseButton);
    await I.waitFor()
    // Other than the above
    await I.clickFixed(conditionDropdownSelector);
    await I.waitFor();
    await I.clickFixed(noConditionSelector);
    await I.waitFor();
    await I.clickFixed(orderConfirmButtonSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl(stockCashOrder.urls.cashBuyConfirm);
    stockCashOrder.takeScreenshot.cashBuyInput('Test_item_No.14_Go_to_order_confirmation_screen');
});

Scenario('Test item No.19 Deposit request', async ({ }) => {
    const depositRequest = '//*[@data-testid="buyInput_depositRequest_id"]';
    // Transition to Deposit request
    await common.clickCardItem(depositRequest, '/mobile/cashflow/depositrequest', 'external');
});
