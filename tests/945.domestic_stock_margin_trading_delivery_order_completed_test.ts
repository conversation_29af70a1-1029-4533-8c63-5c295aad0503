import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingDeliveryOrderCompleted');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginDelivery }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginDelivery.goToMarginDeliveryOrderComplete();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading Delivery Order completed', async ({ I, stockMarginDelivery }) => {
    await stockMarginDelivery.compareUrl(stockMarginDelivery.urls.marginDeliveryOrderCompleted);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderCompleted(
        'Display_Domestic_Stock_Margin_Trading_Delivery_Order_completed',
    );
});

Scenario('Test item No.1 Order number', async ({ I, stockMarginDelivery }) => {
    const orderNumberSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    await I.clickFixed(orderNumberSelector);
    await I.waitFor('mediumWait');
    await stockMarginDelivery.compareUrl('/mobile/order-inquiry/margin/detail-receipt-delivery');
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderCompleted(
        'Test_item_No.1_Tap_Order_number_to_transition_to_receipt_and_delivery_screen',
    );
});

Scenario('Test item No.2 Order Inquiry', async ({ I, stockMarginDelivery }) => {
    const orderInquiryButtonSelector = '//*[@data-testid="complete_orderStatusButton_id"]';
    const orderInquirySelector = '#order-inquiry';
    await I.clickFixed(orderInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquirySelector);
    await stockMarginDelivery.compareUrl('/mobile/order-inquiry/margin');
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderCompleted(
        'Test_item_No.2_Tap_Order_Inquiry_to_transition_to_order_list_page',
    );
});

Scenario('Test item No.3 Balance inquiry', async ({ I, stockMarginDelivery }) => {
    const balanceInquiryButtonSelector = '//*[@data-testid="complete_positionInquiryButton_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(balanceInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(positionInquirySelector);
    await stockMarginDelivery.compareUrl('/mobile/position-inquiry/margin');
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderCompleted(
        'Test_item_No.3_Tap_Balance_inquiry_to_transition_to_balance_list_page',
    );
});

Scenario('Test Back to browser', async ({ I, stockMarginDelivery }) => {
    await I.performBrowserBack();
    await I.waitFor();
    I.seeElement('#searchPage');
    await stockMarginDelivery.compareUrl('/mobile/search');
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderCompleted(
        'Back_to_browser_to_transition_to_general_search_page',
    );
});
