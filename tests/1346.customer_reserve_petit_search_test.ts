import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Reserve - CustomerReservePetitSearch');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await <PERSON>.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user48 });
    I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI of Customer Reserve Petit Search Page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePetitSearchPage();
    I.saveScreenshot('1346.Test_item_No.0_UI_of_Customer_Reserve_Petit_Search_Page.png');
});
Scenario('Test Item 1: Check keyword input', async ({ I }) => {
    // Open input keyboard
    const searchInput = '//*[@data-testid="petitSearch_cancel_id"]//input'
    await I.waitForElement(searchInput, 1);
    I.clickFixed(searchInput);
    await I.waitFor('shortWait');
    I.saveScreenshot('1346.Test_item_No.1_Open_Keyboard.png');

    // Input keyword and confirm
    I.fillField(searchInput, 'test');
    I.saveScreenshot('1346.Test_item_No.1_Input_Keyword.png');

    // Check keyboard closes when tapping outside
    I.clickFixed(searchInput);
    await I.waitFor('shortWait');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
    await I.waitFor('shortWait');
    I.saveScreenshot('1346.Test_item_No.1_Close_Keyboard_By_Tap_Outside.png');
});
Scenario('Test Item 2: Check open/close accordion when clicking on search condition', async ({ I }) => {
    // Wait for the dropdown arrow to appear
    const dropdownArrow = '//*[@data-testid="petitSearch_searchConditions_id"]';
    I.waitForElement(dropdownArrow);
    // Take screenshot before close accordion
    I.saveScreenshot('1346.Test_item_No.2_Before_Close_Accordion.png');
    // Click on the arrow to close accordion
    I.clickFixed(dropdownArrow);
    // Take screenshot after closing accordion
    I.saveScreenshot('1346.Test_item_No.2_After_Close_Accordion.png');
    // Click again to open accordion
    I.clickFixed(dropdownArrow);
    // Take screenshot after opening accordion
    I.saveScreenshot('1346.Test_item_No.2_After_Open_Accordion.png');
});
Scenario('Test Item 3: Check search condition selection Select the item you tap', async ({ I }) => {
    // Find unchecked labels (those without data-checked attribute)
    const uncheckedLabels = '//div[@data-testid="petitSearch_searchConditionSelect_id"]//label[.//div[not(@data-checked)]]';
    // Take screenshot before selection
    I.saveScreenshot('1346.Test_item_No.3_Before_Select_Search_Condition.png');
    try {
        // Get count of unchecked labels
        const uncheckedCount = await I.grabNumberOfVisibleElements(uncheckedLabels);
        if (uncheckedCount > 0) {
            // Click the first unchecked label
            I.clickFixed(`(${uncheckedLabels})[1]`);
            await I.waitFor('shortWait');
        }
        // Take screenshot after selection
        I.saveScreenshot('1346.Test_item_No.3_After_Select_Search_Condition.png');

    } catch (e) {
        console.log('Error when selecting search condition:', e);
        I.saveScreenshot('1346.Test_item_No.3_Select_Search_Condition_Error.png');
    }
});
Scenario('Test Item 4: Check search condition selection', async ({ I }) => {
    // Click on the search button
    const searchButton = '//*[@data-testid="petitSearch_searchButton_id"]';
    I.waitForElement(searchButton);
    I.clickFixed(searchButton);
    const reservePetitDetail = '//*[@data-testid="petitSearch_reservePetitDetail_id"]';
    I.waitForElement(reservePetitDetail, 2);
    I.scrollToElement(reservePetitDetail);
    I.saveScreenshot('1346.Test_item_No.4_Search.png');
});
Scenario('Test Item 5: Check cancel search keyword', async ({ I }) => {
    // Open input keyboard
    const searchInput = '//*[@data-testid="petitSearch_cancel_id"]//input'
    const cancelButton = '//*[@data-testid="search_cancel_id"]'
    await I.waitForElement(searchInput, 1);
    I.clickFixed(searchInput);
    await I.waitFor('shortWait');

    // Input keyword and confirm
    I.fillField(searchInput, 'testitem 5');
    I.saveScreenshot('1346.Test_item_No.5_Input_Keyword_Before_Cancel.png');

    I.clickFixed(cancelButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1346.Test_item_No.5_Cancel_Search_Keyword.png');
});
Scenario('Test Item 7: Check sort order selection', async ({ I, accumulation }) => {
    await accumulation.searchReservePetit('testitem 7');

    const sortOrderDropdown = '//*[@data-testid="petitSearch_sortOrder_id"]';
    I.waitForElement(sortOrderDropdown, 2);
    I.scrollToElement(sortOrderDropdown);
    // sreenshot at default option (By name)
    I.saveScreenshot('1346.Test_item_No.7_Default_Option_By_Name.png');
    // Click on the dropdown to open the menu
    I.clickFixed(sortOrderDropdown);
    // Click on the option (By code)
    const byCodeOption = '//button[@value="SEARCH_TSUMITATE_PETIT_ORDER_SYMBOL_CODE"]';
    I.clickFixed(byCodeOption);
    I.saveScreenshot('1346.Test_item_No.7_Option_By_Code.png');
    // Click on the dropdown to open the menu
    I.clickFixed(sortOrderDropdown);
    // Click on the option (By industry)
    const byIndustryOption = '//button[@value="SEARCH_TSUMITATE_PETIT_ORDER_CATEGORY_NAME"]';
    I.clickFixed(byIndustryOption);
    I.saveScreenshot('1346.Test_item_No.7_Option_By_Industry.png');
    // Click on the dropdown to open the menu
    I.clickFixed(sortOrderDropdown);
    // Click on the option (By preferred market)
    const byPreferredMarketOption = '//button[@value="SEARCH_TSUMITATE_PETIT_ORDER_EXCHANGE"]';
    I.clickFixed(byPreferredMarketOption);
    I.saveScreenshot('1346.Test_item_No.7_Option_By_Preferred_Market.png');
    // Click on the dropdown to open the menu
    I.clickFixed(sortOrderDropdown);
    // Click on the option (By trading unit)
    const byTradingUnitOption = '//button[@value="SEARCH_TSUMITATE_PETIT_ORDER_TRADE_UNIT"]';
    I.clickFixed(byTradingUnitOption);
    I.saveScreenshot('1346.Test_item_No.7_Option_By_Trading_Unit.png');

});
Scenario('Test Item 9: Check misplay modal for details of small stock investment', async ({ I, accumulation }) => {
    await accumulation.searchReservePetit('testitem 9');
    const reservePetitDetail = '//*[@data-testid="petitSearch_reservePetitDetail_id"]';
    I.waitForElement(reservePetitDetail, 2);
    I.scrollToElement(reservePetitDetail);
    const firstDetailItem = `${reservePetitDetail}/div[1]`;
    I.clickFixed(firstDetailItem);

    await I.waitFor('shortWait');
    I.saveScreenshot('1346.Test_item_No.9_Mispaly_Modal_For_Details_Of_Small_Stock_Investment.png');
    //close 
    const cancelButton = '//*[@data-testid="petitReverseSearch_cancel_id"]';
    I.clickFixed(cancelButton);

});
Scenario('Test Item 14: Check Back to top Scroll to the top of the screen', async ({ I }) => {
   const petitSearch = '//*[@data-testid="petitSearch_caution_id"]';
    I.waitForElement(petitSearch, 1);
    I.scrollToElement(petitSearch);
    await I.waitFor('shortWait');
    const scrollToTopButton = '//*[@id="scrollButton"]';
    I.saveScreenshot('1346.Test_item_No.14_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');
    I.dontSeeElement(scrollToTopButton);
    I.saveScreenshot('1346.Test_item_No.14_scrollToTop_dont_see_button.png');
});
Scenario('Test Item 15: Load more (current page +1) and refresh to add the result card', async ({ I, accumulation }) => {
    await accumulation.searchReservePetit('testitem 15');
    const loadMoreButton = '//*[@data-testid="petitSearch_loadMore_id"]';
    I.waitForElement(loadMoreButton, 1);
    I.scrollToElement(loadMoreButton);
    I.saveScreenshot('1346.Test_item_No.15_Load_More.png');
    I.clickFixed(loadMoreButton);
    I.saveScreenshot('1346.Test_item_No.15_Load_More_After.png');
});
Scenario('Test Item 16: Check Savings - Petit Stocks -Transition to Savings Application Input', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePetitSearchPage();
    await accumulation.searchReservePetit('testitem 16');
    const reservePetitDetail = '//*[@data-testid="petitSearch_reservePetitDetail_id"]';
    I.waitForElement(reservePetitDetail, 1);
    I.scrollToElement(reservePetitDetail);
    const firstDetailItem = `${reservePetitDetail}/div[1]`;
    I.clickFixed(firstDetailItem);
    await I.waitFor('shortWait');
    const petitReverseSearch_reserve_id = '//*[@data-testid="petitReverseSearch_reserve_id"]';
    I.clickFixed(petitReverseSearch_reserve_id);
    await I.waitFor('shortWait');
    I.saveScreenshot('1346.Test_item_No.16_Transition_To_Savings_Application_Input.png');
    // back to petit search page
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);

});
//TODO: miss data-testid, use name
Scenario('Test Item 17: Check Individual Stock Information Domestic Stock Investment Information - Stock Details_Transition to Chart Display', async ({ I, accumulation }) => {
    await accumulation.searchReservePetit('testitem 17');
    const reservePetitDetail = '//*[@data-testid="petitSearch_reservePetitDetail_id"]';
    I.waitForElement(reservePetitDetail, 1);
    I.scrollToElement(reservePetitDetail);
    const firstDetailItem = `${reservePetitDetail}/div[1]`;
    I.clickFixed(firstDetailItem);
    await I.waitFor('shortWait');
    // const petitReverseSearch_reserve_id = '//*[@data-testid="petitReverseSearch_reserve_id"]';
    const individualStockInformationButton = '//button[contains(text(), "個別銘柄情報")]';

    I.clickFixed(individualStockInformationButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1346.Test_item_No.17_Transition_To_Chart_Display.png');
    // back to petit search page
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);

});
Scenario('Test Item 18: Caution statement Opening and closing the accordion', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePetitSearchPage();
    const dropDownArrow = '//div[@data-testid="petitSearch_caution_id"]//img[@data-testid="common_dropDown_arrow_id_down"]';
    const dropDownArrowUp = '//div[@data-testid="petitSearch_caution_id"]//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.waitForElement(dropDownArrow, 2);
    I.scrollAndClick(dropDownArrow);
    const chakraCollapse = '//*[@data-testid="petitSearch_caution_id"]//*[@class="chakra-collapse"]';
    I.waitForElement(chakraCollapse, 2);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1346.Test_item_No.18_Caution_Statement_Opening_The_Accordion.png');
    I.waitFor()
    I.clickFixed(dropDownArrowUp);
    I.saveScreenshot('1346.Test_item_No.18_Caution_Statement_Closing_The_Accordion.png');
});
