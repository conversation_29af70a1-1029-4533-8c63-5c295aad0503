import { COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';
import transferCash from '../pages/transferCash';

Feature('Deposits_Withdrawals - TransferCashInput');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user102 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await <PERSON>.closeBrowser();
});

Scenario('Test Item 0: Transfer Cash Input', async ({ I }) => {
    await transferCash.goToCashInputPage();
    I.saveScreenshot(`${transferCash.prefix.input}.0_Transfer_Cash_Input.png`);
});

Scenario('Test Item 2: Deposit transfer inquiry', async ({ I }) => {
    const cashInquiry = '//*[@data-testid="transferCashInput_cashInquiry_id"]/span[contains(text(), "こちら")]';
    await transferCash.goToCashInputPage();
    // Go to the deposit transfer inquiry screen {member-site-url}/ap/iPhone/CashFlow/Transfer/Cash/Inquiry
    await common.clickCardItem(cashInquiry, '/ap/iPhone/CashFlow/Transfer/Cash/Inquiry', 'kcMemberSite');
});

// 8.（預り金▶保証金・証拠金）
// （保証金・証拠金▶預り金）
// タブを切り替える
Scenario('Test Item 8: Switch tabs', async ({ I }) => {
    await transferCash.goToCashInputPage();
    const count = await I.grabNumberOfVisibleElements(transferCash.locators.tabButton);
    if (count > 0) {
        for (let i = 1; i <= count; i++) {
            await I.seeAndClickElement(`${transferCash.locators.tabButton}[${i}]`, { waitFor: 'mediumWait' });
            await I.saveScreenshot(`${transferCash.prefix.input}.8_switch_tab_${i}.png`);
        }
    } else {
        I.say('No tab buttons found, skipping switch tabs test');
    }
});

// 9.振替金額 -> 共通UI-金額テキストフィールド参照
Scenario('Test Item 9: Transfer Amount -> Common UI - See Amount Text Field', async ({ I }) => {
    await I.scrollAndClick(transferCash.locators.groupInputNumber);
    await I.waitFor();
    I.fillField(transferCash.locators.groupInputNumber, '1000');
    await I.saveScreenshot(`${transferCash.prefix.input}.9_Transfer_Amount-Common_UI-See_Amount_Text_Field.png`);
    await I.waitFor('shortWait');
    I.blur(transferCash.locators.groupInputNumber);
});

// 10.確認画面 -> 預り金振替確認画面に遷移する
Scenario('Test Item 10: Confirmation screen -> Transition to deposit transfer confirmation screen', async ({ I }) => {
    const transferCashInput_confirmScreen_id = '//*[@data-testid="transferCashInput_confirmScreen_id"]';
    I.waitForElement(transferCashInput_confirmScreen_id, 2);
    I.scrollAndClick(transferCashInput_confirmScreen_id);
    await I.saveScreenshot(
        `${transferCash.prefix.input}.10_Confirmation_screen-Transition_to_deposit_transfer_confirmation_screen.png`,
    );
    //back to transfer cash input page
    await I.backToPreviousScreen();
});

// 12.くりっく365TFX取引画面 -> 与信取引系リンクの2.くりっく365タップと同様
Scenario('Test Item 12: Check Go to the following URL', async ({ I }) => {
    await transferCash.goToCashInputPage();
    const click365TFX = '//*[@data-testid="transferCashInput_click365TFX_id"]';
    const urlLink = '//a[contains(text(), "こちら")]';
    I.waitForElement(click365TFX, 2);
    I.scrollAndClick(click365TFX);
    await I.waitFor();
    // Open in a new tab https://kabu.com/item/fx/click365/apply.html
    await common.clickCardItem(urlLink, 'https://kabu.com/item/fx/click365/apply.html', 'external');
    await I.switchToWeb();
    await I.waitFor();
    //click 閉じる to close modal
    const closeModal = '//button[text()="閉じる"]';
    I.waitForElement(closeModal, 2);
    I.scrollAndClick(closeModal);
    await I.waitFor();
}); 
// 14.取引所CFD（株365）取引画面 -> 与信取引系リンクの3.取引所CFDタップと同様
Scenario('Test Item 14: Check Go to the following URL', async ({ I }) => {
    await transferCash.goToCashInputPage();
    const transactionScreen = '//*[@data-testid="transferCashInput_transactionScreen_id"]';
    const urlLink = '//a[contains(@class, "sanitize-html-a")]';
    I.waitForElement(transactionScreen, 2);
    I.scrollAndClick(transactionScreen);
    await I.waitFor();
    // Check open in a new tab /Members/personal/tcfdyakudaku/tcfd01101.asp
    const url = await I.grabAttributeFrom(urlLink, 'href');
    I.assertContain(url, '/Members/personal/tcfdyakudaku/tcfd01101.asp');
    //click 閉じる to close modal
    const closeModal = '//button[text()="閉じる"]';
    I.waitForElement(closeModal, 2);
    I.scrollAndClick(closeModal);
    await I.waitFor();
});

// 15.注意事項 -> アコーディオンを開閉
Scenario('Test Item 15: Notes -> Open/close the accordion', async ({ I }) => {
    await I.scrollAndClick(transferCash.locators.noteCautionary);
    await I.saveScreenshot(`${transferCash.prefix.input}.15_Notes_Open_the_accordion.png`);
    await I.waitFor();
    await I.seeAndClickElement(transferCash.locators.noteCautionary);
    await I.saveScreenshot(`${transferCash.prefix.input}.15_Notes_Close_the_accordion.png`);
    await I.waitFor();
});

// 102.保証金・証拠金-選択 -> ラジオボタンを選択状況として変更する
Scenario('Test Item 102: Deposit/Mail-Select -> Change the radio button as selection status', async ({ I }) => {
    await I.scrollToElement(`${transferCash.locators.tabButton}[1]`);
    await I.seeAndClickElement(`${transferCash.locators.tabButton}[1]`);
    await I.waitFor();
    await I.scrollToElement(transferCash.locators.securitySelectRadioItem);
    await I.seeAndClickElement(transferCash.locators.securitySelectRadioItem);
    await I.saveScreenshot(
        `${transferCash.prefix.input}.102_Deposits-Margin_and_Collateral-Select_second_radio_option.png`,
    );
});

// 201.保証金・証拠金-選択  -> ラジオボタンを選択状況として変更する
Scenario('Test Item 201: Deposit/Mail-Select -> Change the radio button as selection status', async ({ I }) => {
    await I.waitFor();
    await I.seeAndClickElement(`${transferCash.locators.tabButton}[2]`);
    await I.seeAndClickElement(transferCash.locators.marginSelectRadioItem);
    await I.saveScreenshot(
        `${transferCash.prefix.input}.102_Margin_and_Collateral-Deposits-Select_second_radio_option.png`,
    );
});
