import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('PositionInquiry - PositionInquiryDomesticStockUnitStockDetail');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user42 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Go to Stock Detail', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryDomesticStockUnitStocksPage();

    const listStock = '//*[@data-testid="stockList_positionDetail_id"]';
    I.waitForElement(listStock, 5);
    const firstStock = '//*[@data-testid="stockList_positionDetail_id"]/div[1]';
    I.clickFixed(firstStock);
    await I.waitFor('shortWait');
    I.saveScreenshot('1169.Test_item_No.25_go_to_stock_detail.png');
})
Scenario('Test Item 2: Tap the stock to move to Domestic Stock Investment Information - Stock Details_Chart Display', async ({ I }) => {
    const stockDetailListSymbol = '//*[@data-testid="stockDetailList_symbol_id"]';
    I.waitForElement(stockDetailListSymbol, 1);
    I.clickFixed(stockDetailListSymbol);
    await I.waitFor('shortWait');
    I.saveScreenshot('1169.Test_item_No.2_tap_the_stock_to_move_to_Domestic_Stock_Investment_Information_Stock_Details_Chart_Display.png');
     //back button
     const backButton = '//*[@data-testid="common_back_id"]';
     I.clickFixed(backButton);
     await I.waitFor();
     I.saveScreenshot('1169.Test_item_No.2_tap_the_stock_to_move_to_Domestic_Stock_Investment_Information_Stock_Details_Chart_Display_after_back_button.png');
})

// 19.現物買		国内株式現物取引-買注文-注文入力に遷移する data-testid="stockDetailList_cashBuy_id"
// 20.現物売		国内株式現物取引-売注文-注文入力に遷移する data-testid="stockDetailList_cashSell_id"
// 21.プチ買		国内株式プチ株取引-買注文-注文入力に遷移する data-testid="stockDetailList_petitBuy_id"
// 22.プチ売		国内株式プチ株取引-売注文-注文入力に遷移する data-testid="stockDetailList_petitSell_id"

Scenario('Test Item 19: Domestic Stock Spot Trading - Buy Order - Order Input Transition', async ({ I }) => {
    const cashBuyButton = '//*[@data-testid="stockDetailList_cashBuy_id"]';
    I.waitForElement(cashBuyButton, 1);
    I.scrollAndClick(cashBuyButton);
    I.saveScreenshot('1169.Test_item_No.19_Domestic_Stock_Spot_Trading_Buy_Order_Order_Input_Transition.png');

    //back button
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
    // screenshot after back button
    I.saveScreenshot('1169.Test_item_No.19_Domestic_Stock_Spot_Trading_Buy_Order_Order_Input_Transition_after_back_button.png');
});

Scenario('Test Item 20: Domestic Stock Spot Trading - Sell Order - Order Input Transition', async ({ I }) => {
    const cashSellButton = '//*[@data-testid="stockDetailList_cashSell_id"]';
    I.waitForElement(cashSellButton, 1);
    I.scrollAndClick(cashSellButton);
    I.saveScreenshot('1169.Test_item_No.20_Domestic_Stock_Spot_Trading_Sell_Order_Order_Input_Transition.png');

    //back button
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
    // screenshot after back button
    I.saveScreenshot('1169.Test_item_No.20_Domestic_Stock_Spot_Trading_Sell_Order_Order_Input_Transition_after_back_button.png');
});

Scenario('Test Item 21: Domestic Stock Petit Stock Trading - Buy Order - Order Input Transition', async ({ I }) => {
    const petitBuyButton = '//*[@data-testid="stockDetailList_petitBuy_id"]';
    I.waitForElement(petitBuyButton, 1);
    I.scrollAndClick(petitBuyButton);
    I.saveScreenshot('1169.Test_item_No.21_Domestic_Stock_Petit_Stock_Trading_Buy_Order_Order_Input_Transition.png');

    //back button
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
    // screenshot after back button
    I.saveScreenshot('1169.Test_item_No.21_Domestic_Stock_Petit_Stock_Trading_Buy_Order_Order_Input_Transition_after_back_button.png');
});

Scenario('Test Item 22: Domestic Stock Petit Stock Trading - Sell Order - Order Input Transition', async ({ I }) => {
    const petitSellButton = '//*[@data-testid="stockDetailList_petitSell_id"]';
    I.waitForElement(petitSellButton, 1);
    I.scrollAndClick(petitSellButton);
    I.saveScreenshot('1169.Test_item_No.22_Domestic_Stock_Petit_Stock_Trading_Sell_Order_Order_Input_Transition.png');

    //back button
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
    // screenshot after back button
    I.saveScreenshot('1169.Test_item_No.22_Domestic_Stock_Petit_Stock_Trading_Sell_Order_Order_Input_Transition_after_back_button.png');
});

Scenario('Test Item 25: Check Move to Details Balance Inquiry-Domestic Stocks-Unit Stocks-Details', async ({ I }) => {
    const detailButton = '//*[@data-testid="stockDetailList_detail_id"]';
    I.waitForElement(detailButton, 5);
    I.clickFixed(detailButton);
    await I.waitFor('longWait');
    I.saveScreenshot('1156.Test_item_No.25_move_to_Details_Balance_Inquiry_Domestic_Stocks_Unit_Stocks_Details.png');
    //back button
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
    // screenshot after back button
    I.saveScreenshot('1156.Test_item_No.25_move_to_Details_Balance_Inquiry_Domestic_Stocks_Unit_Stocks_Details_after_back_button.png');
})
Scenario('Test Item 26:Check Display the actual help modal', async ({ I }) => {
    const moreInfoButton = '//*[@data-testid="stockDetailList_moreInfo_id"]';
    I.clickFixed(moreInfoButton);
    await I.waitFor('longWait');
    I.saveScreenshot('1156.Test_item_No.26_check_Display_the_actual_help_modal.png');
    const closeButton = '//button[@aria-label="cancel-btn"]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1156.Test_item_No.26_close_actual_help_modal.png');
    
})
Scenario('Check Display processing Layout', async ({ I }) => {
    const detailButton = '//*[@data-testid="stockDetailList_detail_id"]';
    I.waitForElement(detailButton, 5);
    I.clickFixed(detailButton);
    await I.waitFor();
    I.see('現物株式', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1156.Test_item_check_Display_processing_Layout.png');
})
Scenario('Test Item 18: Check Display processing Layout', async ({ I }) => {
    const favoriteListContainer = '//p[contains(text(), "ご注意(必ずお読みください)")]';
    I.waitForElement(favoriteListContainer, 2);
    I.scrollToElement(favoriteListContainer);
    I.saveScreenshot('1156.Test_item_No.18_check_Display_processing_Layout.png');
    I.clickFixed(favoriteListContainer);
    I.saveScreenshot('1156.Test_item_No.18_check_Display_processing_Layout_close_the_accordion.png');
})


