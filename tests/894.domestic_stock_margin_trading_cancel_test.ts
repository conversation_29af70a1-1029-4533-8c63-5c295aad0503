import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginCancel');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginCancel }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCancel.goToMarginCancel();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Cancel', async ({ I, stockMarginCancel }) => {
    I.assertContain(
        await I.grabCurrentUrl(),
        stockMarginCancel.urls.marginCancelOrder,
        'URL does not contain expected path',
    );
    stockMarginCancel.takeScreenshot.marginCancel('Display_Domestic_Stock_Margin_Trading_Cancel');
});

Scenario('Test item No.12+13 Fill password and check for omitted password', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const passwordSelector = '//*[@data-testid="marginCancel_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCancel_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginCancel_confirmCancel_id"]';
    I.fillField(passwordSelector, stockMarginCancel.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(confirmButtonSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginCancel.takeScreenshot.marginCancel('Test_item_No.12_13_See_order_confirmation_button_state');
});

Scenario('Test item No.14 Confirm cancellation', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const passwordSelector = '//*[@data-testid="marginCancel_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCancel_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginCancel_confirmCancel_id"]';
    I.fillField(passwordSelector, stockMarginCancel.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(
        await I.grabCurrentUrl(),
        '/mobile/order-inquiry/margin/detail',
        'URL does not contain expected path',
    );
    stockMarginCancel.takeScreenshot.marginCancel('Test_item_No.14_Tap_confirm_button_to_transition_to_details_screen');
});

Scenario('Test item No.15 Return to the order inquiry screen', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const backToOrderStatusButtonSelector = '//*[@data-testid="marginCancel_backToOrderStatus_id"]';
    await I.clickFixed(backToOrderStatusButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(
        await I.grabCurrentUrl(),
        '/mobile/order-inquiry/margin/detail',
        'URL does not contain expected path',
    );
    stockMarginCancel.takeScreenshot.marginCancel(
        'Test_item_No.15_Tap_Return_to_the_order_inquiry_button_to_transition_to_details_screen',
    );
});
