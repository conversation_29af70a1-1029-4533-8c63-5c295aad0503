import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from '../pages/search/common';
Feature('OrderInquiry - DomesticStocksCredit');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user39 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});
//1119
Scenario('Test Item 1: Check UI of Domestic Credit Stocks page', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    I.saveScreenshot('1119.Test_item_No.1_UI_of_Domestic_Credit_Stocks_page.png');
});
Scenario('Test Item 2: Check button filter ', async ({ I }) => {
    const settingButton = '//*[@data-testid="marginList_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.2_show_filler_modal.png');
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.2_close_filler_modal.png');

});
Scenario('Test Item 3: Go to Order inquiry-Domestic stock-Credit-Details screen', async ({ I, orderStatus }) => {
    // Ensure that the current tab is 現物
    const creditStocksButton = '//button[.//p[contains(text(), "信用")]]';

    I.clickFixed(creditStocksButton);
    await I.waitFor();

    try {
        // Find the first order based on the actual HTML structure
        const orderSelectors = [
            '//div[@id="order-inquiry--tabpanel-1"]//div[contains(@class, "css-1wnrurq")][1]',
            '//div[@id="order-inquiry--tabpanel-1"]//div[contains(@class, "css-10knfks")][1]',
            '//div[@id="order-inquiry--tabpanel-1"]//div[contains(@class, "chakra-stack")]/div[1]'
        ];

        let orderFound = false;
        for (const selector of orderSelectors) {
            if (await I.grabNumberOfVisibleElements(selector) > 0) {
                // Take a screenshot before clicking
                I.saveScreenshot('1087.Test_item_No.5_before_click_order.png');
                // Click the order
                I.clickFixed(selector);
                await I.waitFor();
                orderFound = true;
                break;
            }
        }
        if (!orderFound) {
            throw new Error('No order items found');
        }
        // Confirm that you have navigated to the detail page
        I.see('品受・品渡');
        I.saveScreenshot('1119.Test_item_go_to_stock_detail.png');
        await orderStatus.backToOrderInquiry();
    } catch (e) {
        console.log('No orders found to check details:', e);
        I.saveScreenshot('1119.Test_item_go_to_stock_detail_no_orders.png');
    }
})
Scenario('Test Item 17: Check scroll top', async ({ I }) => {
    const lastItem = '//*[@data-testid="marginList_detailList_id"]/div[last()]';
    const scrollToTopButton = '//*[@id="scrollButton"]';

    // Scroll to the last item
    await I.scrollToElement(lastItem);
    await I.waitFor('shortWait');

    // Test scroll to top button
    I.saveScreenshot('1119.Test_item_No.17_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');

    // Dont see scroll to top button
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1119.Test_item_No.17_scrollToTop_dont_see_button.png');
});
Scenario('Test Item 10: Go to the Order Inquiry - Domestic Stocks - Credit - Details - Receipt and Delivery screen', async ({ I }) => {
    const firstOrderInList = '//*[@data-testid="marginList_detailList_id"]/div[1]';
    I.clickFixed(firstOrderInList);

    await I.waitFor();
    I.saveScreenshot('1119.Test_item_No.10_go_to_the_Order_Inquiry_Domestic_Stocks_Credit_Details_Receipt_and_Delivery_screen.png');

    //back 
    await I.backToPreviousScreen();
});




Scenario('Test Item 22: Display pull-down menu excluding duplicates', async ({ I }) => {
    const settingButton = '//*[@data-testid="marginList_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');


    const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';
    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.22_display_pull-down_menu_excluding_duplicates.png');
    //click button have p text 三菱ＵＦＪ 8306
    const a1StringB1101Button = '//p[contains(text(), "三菱ＵＦＪ 8306")]';

    I.clickFixed(a1StringB1101Button);
})
Scenario('Test Item 24: Display Order Display the drop-down menu', async ({ I }) => {
    const statusUpdateButton = '//*[@data-testid="filter_displayOrder_id"]';

    I.clickFixed(statusUpdateButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.24_display_order_display_the_drop-down_menu.png');
    //click button have p text 注文日時順
    const orderDateButton =  '//p[contains(text(), "注文日時順")]';

    I.clickFixed(orderDateButton);
 
})
Scenario('Test Item 25: Set initial display sticks and order', async ({ I }) => {
    const clearButton = '//button[contains(text(), "クリア")]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.25_set_initial_display_sticks_and_order.png');
    //close modal
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
})
Scenario('Test Item 26: Filter and sort based on entered parameters', async ({ I }) => {
    const settingButton = '//*[@data-testid="marginList_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');


    const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';
    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.22_display_pull-down_menu_excluding_duplicates.png');
    //click button have p text 三菱ＵＦＪ 8306
    const a1StringB1101Button = '//p[contains(text(), "三菱ＵＦＪ 8306")]';
    I.clickFixed(a1StringB1101Button);

    const statusUpdateButton = '//*[@data-testid="filter_displayOrder_id"]';

    I.clickFixed(statusUpdateButton);
    await I.waitFor('shortWait');
    //click button have p text 注文日時順
    const orderDateButton =  '//p[contains(text(), "注文日時順")]';

    I.clickFixed(orderDateButton);

    const submitButton = '//button[@data-testid="filter_confirm_id"]';
    I.clickFixed(submitButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1119.Test_item_No.26_filter_and_sort_based_on_entered_parameters.png');

})
Scenario('Test Item 15: Details list Scroll Set the relevant area as the scroll range', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    // order-inquiry--tabpanel-1
    const creditTabpanel = '//div[@id="order-inquiry--tabpanel-1"]';
    I.scrollToElement(creditTabpanel);
    I.saveScreenshot('1119.Test_item_No.15_Details_list_Scroll_Set_the_relevant_area_as_the_scroll_range.png');
});


// //1129
Scenario('Go to the Order Inquiry - Domestic Stocks - Credit - Details - Receipt and Delivery screen', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="marginList_orderDetail_9_id"]');
    await I.waitFor();
    I.see('信用取引', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1119.Test_item_No.10_go_to_the_Order_Inquiry_Domestic_Stocks_Credit_Details_Receipt_and_Delivery_screen.png');

});
Scenario('Test Item 39: Check tab Relay order number - Go to Order Inquiry - Domestic Stocks - Credit - Details', async ({ I }) => {
    
    const relayOrderLink = '//tbody[@data-testid="correction_relayTargetOrderID_id"]//a';
    I.waitForElement(relayOrderLink, 2);
    I.clickFixed(relayOrderLink);
    await I.waitFor();
    
    I.saveScreenshot('1119.Test_item_No.39_check_tab_relay_order_number.png');
    
    // Back to prev page
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
});
Scenario('Test Item 32: Check tab Condition - Go to Order Inquiry - Domestic Stocks - Credit - Details', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="marginList_orderDetail_9_id"]');
    await I.waitFor();
    I.see('信用取引', '//*[@data-testid="common_header_title_id"]');
    const conditionTab = '//*[@data-testid="stockDetail_condition_0_id"]';
    I.waitForElement(conditionTab, 2);
    I.clickFixed(conditionTab);
    await I.waitFor();
    I.saveScreenshot('1119.Test_item_No.32_check_tab_condition.png');

    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
});
Scenario('Test Item 35: Modify order details. Go to Domestic stock margin trading - Modify/cancel order - Enter modifications.', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="marginList_orderDetail_9_id"]');
    await I.waitFor();
    I.see('信用取引', '//*[@data-testid="common_header_title_id"]');
    const modifyOrderButton = '//button[@data-testid="stockDetail_correctOrder_id"]';
    I.waitForElement(modifyOrderButton, 2);
    I.scrollAndClick(modifyOrderButton);
    I.saveScreenshot('1119.Test_item_No.35_modify_order_details_go_to_domestic_stock_margin_trading_modify_cancel_order_enter_modifications.png');
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
});
Scenario('Test Item 36: Cancel order Domestic stock margin trading - Order modification/cancellation - Cancel.', async ({ I }) => {
    const cancelOrderButton = '//button[@data-testid="stockDetail_cancelOrder_id"]';
    I.waitForElement(cancelOrderButton, 2);
    I.scrollAndClick(cancelOrderButton);
    I.saveScreenshot('1119.Test_item_No.36_cancel_order_domestic_stock_margin_trading_order_modification_cancellation_cancel.png');
    // Back to Domestic stock margin trading by button
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor();
});
Scenario('Test Item 43: Order Execution Inquiry Go to Order Inquiry-Domestic Stock-Credit-List.', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    await I.scrollAndClick('//*[@data-testid="marginList_orderDetail_9_id"]');
    await I.waitFor();
    I.see('信用取引', '//*[@data-testid="common_header_title_id"]');
    const orderConfirmationButton = '//button[@data-testid="stockDetail_cancelOrder_id"]';
    I.waitForElement(orderConfirmationButton, 2);
    I.scrollAndClick(orderConfirmationButton);
    I.saveScreenshot('1119.Test_item_No.43_order_execution_inquiry_go_to_order_inquiry_domestic_stock_credit_list.png');
    
});



// // 1135
Scenario('Check UI of Credit Stocks Details-Receipt and delivery page', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    // Ensure that the current tab is 現物
    const creditStocksButton = '//button[.//p[contains(text(), "信用")]]';

    I.clickFixed(creditStocksButton);
    await I.waitFor();

    try {
        // Find the first order based on the actual HTML structure
        const orderSelectors = '//div[@data-testid="marginList_orderDetail_0_id"]';

        I.clickFixed(orderSelectors);
        await I.waitFor();
        // Confirm that you have navigated to the detail page
        I.see('品受・品渡');
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail.png');
    } catch (e) {
        console.log('No credit order found to check details:', e);
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail_no_orders.png');
    }
});
Scenario('15. Open Position Trading History Tap "Go to the following URL', async ({ I }) => {
    const positionButton = '//span[contains(text(), "建玉1")]';
    I.waitForElement(positionButton, 1);
    I.scrollToElement(positionButton);

    // Go to {member-site-url}/ap/iPhone/Stocks/Margin/History/Detail?TradeID={TradeID}&OrderType={OrderType}
    await common.clickCardItem(positionButton, '/ap/iPhone/Stocks/Margin/History/Detail', 'kcMemberSite');
    I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail.png');

});
Scenario('20.1 Open Position Trading History - Navigate to URL when first character is "E"', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    // Ensure that the current tab is 現物
    const creditStocksButton = '//button[.//p[contains(text(), "信用")]]';

    I.clickFixed(creditStocksButton);
    await I.waitFor();

    try {
        // Find the first order based on the actual HTML structure
        const orderSelectors = '//div[@data-testid="marginList_orderDetail_0_id"]';

        I.clickFixed(orderSelectors);
        await I.waitFor();
        // Confirm that you have navigated to the detail page
        I.see('品受・品渡');
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail.png');
    } catch (e) {
        console.log('No credit order found to check details:', e);
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail_no_orders.png');
    }


    const currentPositionButton = '//span[contains(text(), "現物1")]';
    I.waitForElement(currentPositionButton, 1);
    I.scrollToElement(currentPositionButton);

    // Go to {member-site-url}/ap/iPhone/Stocks/Stock/History/Detail
    await common.clickCardItem(currentPositionButton, '/ap/iPhone/Stocks/Stock/History/Detail', 'kcMemberSite');
    I.saveScreenshot('1119.Test_item_20_1_open_position_trading_history_E.png');
});

Scenario('20.2 Open Position Trading History - Navigate to URL when first character is "G"', async ({ I,        orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    // Ensure that the current tab is 現物
    const creditStocksButton = '//button[.//p[contains(text(), "信用")]]';

    I.clickFixed(creditStocksButton);
    await I.waitFor();

    try {
        // Find the first order based on the actual HTML structure
        const orderSelectors = '//div[@data-testid="marginList_orderDetail_0_id"]';

        I.clickFixed(orderSelectors);
        await I.waitFor();
        // Confirm that you have navigated to the detail page
        I.see('品受・品渡');
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail.png');
    } catch (e) {
        console.log('No credit order found to check details:', e);
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail_no_orders.png');
    }
    
    const positionButton = '//span[contains(text(), "現物1")]';
    I.waitForElement(positionButton, 1);
    I.scrollToElement(positionButton);

    // Go to {member-site-url}/ap/iPhone/Stocks/Margin/History/Detail
    await common.clickCardItem(positionButton, '/ap/iPhone/Stocks/Stock/History/Detail', 'kcMemberSite');
    I.saveScreenshot('1119.Test_item_20_2_open_position_trading_history_G.png');
});

Scenario('21. Cancel order - Navigate to Domestic Stock Credit Trading Order Modification/Cancellation - Cancel - Receipt and Delivery screen', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    // Ensure that the current tab is 現物
    const creditStocksButton = '//button[.//p[contains(text(), "信用")]]';

    I.clickFixed(creditStocksButton);
    await I.waitFor();

    try {
        // Find the first order based on the actual HTML structure
        const orderSelectors = '//div[@data-testid="marginList_orderDetail_0_id"]';

        I.clickFixed(orderSelectors);
        await I.waitFor();
        // Confirm that you have navigated to the detail page
        I.see('品受・品渡');
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail.png');
    } catch (e) {
        console.log('No credit order found to check details:', e);
        I.saveScreenshot('1119.Test_item_go_to_credit_stocks_detail_no_orders.png');
    }

    
    const cancelOrderButton = '//button[@data-testid="marginDetailReceiptDelivery_cancelOrder_id"]';
    I.waitForElement(cancelOrderButton, 1);
    I.scrollAndClick(cancelOrderButton);
    I.saveScreenshot('1119.Test_item_No.21_cancel_order_domestic_stock_margin_trading_order_modification_cancellation_cancel.png');
    // back
    await I.backToPreviousScreen();
});
Scenario('22.Order Execution Inquiry Order Inquiry - Domestic Stocks - Credit - Go to List Screen ', async ({ I }) => {
    const orderConfirmationButton = '//button[@data-testid="marginDetailReceiptDelivery_orderExecutionInquiry_id"]';
    I.waitForElement(orderConfirmationButton, 1);
    I.scrollAndClick(orderConfirmationButton);
    I.saveScreenshot('1119.Test_item_No.22_Order_Execution_Inquiry_Order_Inquiry_Domestic_Stocks_Credit_Go_to_List_Screen.png');
    // back
    await I.backToPreviousScreen();
});
Scenario('16.Check Caution: Opening and closing the accordion', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionCreditStocksPage();
    const cautionButton = '//div[p[contains(text(), "ご注意(必ずお読みください)")]]';
    await I.scrollAndClick(cautionButton);
    await I.swipeDirection('up');
    await I.waitFor();
    await I.saveScreenshot('1119.Test_item_No.16_Check_Caution_Opening_and_closing_the_accordion.png');
    await I.clickFixed(cautionButton);
    await I.waitFor();
    I.saveScreenshot('1119.Test_item_No.16_Check_Caution_Closing_the_accordion.png');
});
