Feature('Referral - MailConfirmPage');

// Import the ReferralMailInput page object
import { COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import ReferralMailConfirm from '../pages/referralMailConfirm';
import ReferralMailInput from '../pages/referralMailInput';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I<PERSON>closeBrowser();
});

Scenario('go to referral mail input page', async ({ I }) => {
    I.amOnPage(PAGE_URL.referralMailInput);;
    await I.waitFor('mediumWait');
    I.see(
        `ご家族・ご友人
紹介プログラム`,
        '//*[@data-testid="common_header_title_id"]',
    );
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_page.png`);
    await ReferralMailInput.fillLastName('123456');
    await ReferralMailInput.fillFirstName('123456');
    await ReferralMailInput.fillLastNameKana('123456');
    await ReferralMailInput.fillFirstNameKana('123456');
    await ReferralMailInput.fillMailAddress('<EMAIL>');
    await ReferralMailInput.fillMessage('123456');
    await ReferralMailInput.clickConfirm();
});

Scenario('Test Item 8: Edit Content', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralMailConfirm.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailConfirm}_edit_content_before_click.png`);

    // Click on the edit content button
    const currentUrl = await ReferralMailConfirm.clickEditContent();

    // Verify the URL contains the edit path
    I.assertContain(currentUrl, ReferralMailConfirm.urls.mailInputPage, 'URL should contain the mail input page path');

    // Take a screenshot after redirect
    await ReferralMailConfirm.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailConfirm}_edit_content_after_click.png`);

    // Navigate back to confirmation page
    await ReferralMailConfirm.navigateBack();
});

Scenario('Test Item 9: Cancel Sending', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralMailConfirm.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailConfirm}_cancel_send_before_click.png`);

    // Click on the cancel sending button
    const currentUrl = await ReferralMailConfirm.clickCancelSend();

    // Verify the URL contains the referral page path
    I.assertContain(currentUrl, ReferralMailConfirm.urls.referralPage, 'URL should contain the referral page path');

    // Take a screenshot after redirect
    await ReferralMailConfirm.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailConfirm}_cancel_send_after_click.png`);

    // Navigate back
    await ReferralMailConfirm.navigateBack();
});

Scenario('Test Item 7: Send', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralMailConfirm.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailConfirm}_send_before_click.png`);

    // Click on the send button
    const currentUrl = await ReferralMailConfirm.clickSend();

    // Verify the URL contains the completion page path
    I.assertContain(
        currentUrl,
        ReferralMailConfirm.urls.mailCompletePage,
        'URL should contain the mail completion page path',
    );

    // Take a screenshot after redirect
    await ReferralMailConfirm.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailConfirm}_send_after_click.png`);
});
