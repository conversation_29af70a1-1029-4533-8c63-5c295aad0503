import { COOKIE_KEY, USER_ID } from '../const/constant';
import domesticStockCashtrading from '../pages/domesticStockCashtrading';

Feature('InvestmentProducts - DomesticStockCashtrading');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor('mediumWait');

    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user25 });
    await I.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Go to the stock replay buy page', async ({ I }) => {
    await domesticStockCashtrading.goToStockRelayBuyPage();
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.0_Go_to_the_stock_replay_buy_page.png`);
});

// 1.閉じるボタン -> 当モーダルを閉じる。また、呼び出し元の画面では何も処理を行わない。（仮発注しない）
Scenario('Item 1. Close button -> Close this modal', async ({ I }) => {
    await I.clickFixed(domesticStockCashtrading.locators.closeModalButton);
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.1_Close_button_to_Close_this_modal.png`);
});

// 2.銘柄絞込 -> プルダウンメニューを表示する。
// 項目を選択した際は、表示ページを1ページ目にした上で8.銘柄選択リストの再描画を行う
Scenario('Item 2. Filter stocks -> Display the pull-down menu', async ({ I }) => {
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.buyOrderConfirmButton).toXPath());
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.relayOrderSymbolFilter).toXPath());
    await I.scrollAndClick(domesticStockCashtrading.locators.symbolFilterItem(2));
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.2_Filter_stock_display_the_pulldown_menu.png`,
    );
});

// 3.ソート -> プルダウンメニューを表示する。
// 項目を選択した際は、表示ページを1ページ目にした上で8.銘柄選択リストの再描画を行う
Scenario('Item 3. Sort -> Display the pull-down menu', async ({ I }) => {
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.relayOrderSortFilter).toXPath());
    await I.scrollAndClick(domesticStockCashtrading.locators.sortFilterItem(2));
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.3_Sort_display_the_pulldown_menu.png`);
});

// 4.件数 -> 共通UI-ページネーション記載の処理を行う
Scenario('Item 4. Number of items -> Common UI-Pagination description processing', async ({ I }) => {
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.relayOrderSymbolFilter).toXPath());
    await I.scrollAndClick(domesticStockCashtrading.locators.symbolFilterItem(1));
    I.seeElement(domesticStockCashtrading.locators.totalRecordText);
    const text = await I.grabTextFrom(domesticStockCashtrading.locators.totalRecordText);
    if (text) {
        I.say('I see: ' + text);
        await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.uturn}.4_${text}.png`);
    } else {
        I.say('No number of items displayed on the screen');
    }
});

// 5.前ページへ -> 共通UI-ページネーション記載の処理を行う
Scenario('Item 5. Go to previous page -> Common UI -Perform processing for pagination description', async ({ I }) => {
    await I.scrollAndClick(domesticStockCashtrading.locators.pageNumber(2));
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.prevPageButton).toXPath());
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.5_Go_to_previous_page-Common_UI-Perform_processing_for_pagination_description.png`,
    );
});

// 6.ページ番号 -> 共通UI-ページネーション記載の処理を行う
Scenario('Item 6. Page number -> Common UI -Perform processing for pagination description', async ({ I }) => {
    await I.scrollAndClick(domesticStockCashtrading.locators.pageNumber(2));
    await I.scrollAndClick(domesticStockCashtrading.locators.pageNumber(1));
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.6_Page_number-Common_UI-Perform_processing_for_pagination_description.png`,
    );
});

// 7.次ページへ -> 共通UI-ページネーション記載の処理を行う
Scenario('Item 7. Go to next page -> Common UI -Perform processing for pagination description', async ({ I }) => {
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.nextPageButton).toXPath());
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.7_Go_to_next_page-Common_UI-Perform_processing_for_pagination_description.png`,
    );
});

// 8.銘柄選択リスト -> スワイプ方向へスクロール移動する
Scenario('Item 8. Stock Selection List -> Scroll to Swipe', async ({ I }) => {
    I.swipeLeftFixed(domesticStockCashtrading.locators.symbolSelectionTableList);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.8_Stock_Selection_List-Scroll_to_Swipe.png`,
    );
});

// 9.選択ボタン -> ラジオボタンを選択状態にする
// TODO: click radio button on table list
Scenario('Item 9. Select button -> Put the radio button in the selected stat', async ({ I }) => {
    I.swipeRightFixed(domesticStockCashtrading.locators.symbolSelectionTableList);
    await I.scrollAndClick(domesticStockCashtrading.locators.tableRadioButton);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.9_Select_button-Put_the_radio_button_in_the_selected_stat.png`,
    );
});

// 19.確認画面へ -> 当モーダルを閉じる
// 元の画面で、「現物-買い発注API」または「現物-売り発注API」のリクエストパラメータを以下の通りに設定した上で、仮発注処理を行う
Scenario('Item 19. Go to confirmation screen -> Close this modal', async ({ I }) => {
    await I.scrollAndClick(locate(domesticStockCashtrading.locators.relayOrderConfirmButton).toXPath());
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.uturn}.19_Go_to_confirmation_screen-Close_this_modal.png`,
    );
});
