import { COOKIE_KEY, USER_ID } from "../const/constant";

import common from '../pages/search/common';
Feature('OrderInquiry - CustomerOrderStatusFundList');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user40 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 1: Check UI of Domestic Credit Stocks page', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionFundsPage();
    I.saveScreenshot('1141.Test_item_No.1_UI_of_Domestic_Funds_Stocks_page.png');
});
Scenario('Test Item 2: Check Go to the MMF/China F URL', async ({ I }) => {
    const mmfChinaF = '//button[@data-testid="orderInquiryFund_mmfChinaF_id"]';
    await common.clickCardItem(mmfChinaF, '/iPhone/Trade/Status/OrderStatusToshin', 'kcMemberSite');
    await I.waitFor();
    const visibleExpiredSessionDialog = await I.grabNumberOfVisibleElements('~ログイン');
    if (visibleExpiredSessionDialog > 0) {
        I.click('~ログイン');
        // Login again if session is expired
        await I.waitFor(); // wait for login page appear
    }
    
});
Scenario('Test Item 3: Check Go to the Foreign currency MMF URL', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionFundsPage();
    const foreignCurrencyMMF = '//button[@data-testid="orderInquiryFund_foreignCurrencyMMF_id"]';
    await common.clickCardItem(foreignCurrencyMMF, '/iPhone/Trade/Status/OrderStatustMultiCurMMF', 'kcMemberSite');
    await I.waitFor();
    const visibleExpiredSessionDialog = await I.grabNumberOfVisibleElements('~ログイン');
    if (visibleExpiredSessionDialog > 0) {
        I.click('~ログイン');
        // Login again if session is expired
        await I.waitFor(); // wait for login page appear
    }
    

});
Scenario('Test Item 5: Check button filter ', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionFundsPage();
    const settingButton = '//*[@data-testid="orderInquiryFund_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1141.Test_item_No.5_show_filler_modal.png');
    const closeButton = '//*[@data-testid="common_rightSlide_close_id"]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1141.Test_item_No.5_close_filler_modal.png');
});
Scenario('Test Item 7: Check Display the order details transition modal', async ({ I }) => {
    await I.waitForElement('//*[@data-testid="orderInquiryFund_detailList_id"]', 5);

    try {
        const firstOrderSelector = '//*[@data-testid="orderInquiryFund_detailList_id"]/div[1]';
        if (await I.grabNumberOfVisibleElements(firstOrderSelector) > 0) {
            I.saveScreenshot('1141.Test_item_No.7_before_click_order.png');
            I.clickFixed(firstOrderSelector);
            await I.waitFor();
            I.saveScreenshot('1141.Test_item_No.7_order_details_modal.png');
            const cancelButton = '//button[@aria-label="cancel-btn"]';
            I.clickFixed(cancelButton);
            I.saveScreenshot('1141.Test_item_No.7_close_order_details_modal.png');
        } else {
            console.log('No orders found to check details');
            I.saveScreenshot('1141.Test_item_No.7_no_orders_found.png');
        }
    } catch (e) {
        console.log('Error when clrcrweglkrderoderadls', e);
        I.saveScreenshot('1141.Test_item_No.7_error.png');
    }
});
Scenario('Test Item 17: Check scroll top', async ({ I }) => {
    // scroll to the last item in orderInquiryFund_detailList_id
    const lastItem = '//*[@data-testid="orderInquiryFund_detailList_id"]/div[last()]';
    I.scrollToElement(lastItem);
    // Test scroll to top button
    const scrollToTopButton = '//*[@id="scrollButton"]';

    I.saveScreenshot('1130.Test_item_No.17_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');

    // Dont see scroll to top button
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1141.Test_item_No.17_scrollToTop_dont_see_button.png');
});
Scenario('Test Item 18: Check Display stocks Display pull-down menu excluding duplicates', async ({ I }) => {
    const settingButton = '//*[@data-testid="orderInquiryFund_filter_id"]';
    I.scrollAndClick(settingButton);
    const displaySymbolButton = '//*[@data-testid="orderInquiryFund_displaySymbol_id"]';
    I.clickFixed(displaySymbolButton);
    I.saveScreenshot('1141.Test_item_No.18_Display_stocks_Display_pull-down_menu_excluding_duplicates.png');
    const thirdOption = '//*[@data-testid="orderInquiryFund_displaySymbol_id"]//p[3]';
    I.clickFixed(thirdOption);

});
Scenario('Test Item 19: Check Display Order Display the drop-down menu', async ({ I }) => {
    const displayOrderButton = '//*[@data-testid="orderInquiryFund_displayOrder_id"]';
    I.waitForElement(displayOrderButton, 2);
    I.scrollAndClick(displayOrderButton);
    I.saveScreenshot('1141.Test_item_No.19_Display_Order_Display_the_drop-down_menu.png');
    const thirdOption = '//*[@data-testid="orderInquiryFund_displayOrder_id"]//p[3]';
    I.clickFixed(thirdOption);

});
Scenario('Test Item 20: Check Switch account filter ON/OFF', async ({ I }) => {
    const accountFilterButton = '//*[@data-testid="orderInquiryFund_accountFilter_id"]';
    I.waitForElement(accountFilterButton, 2);
    I.scrollToElement(accountFilterButton);
    const accountFilterLabels = await I.grabTextFromAll(`${accountFilterButton}//label`);

    for (let i = 0; i < accountFilterLabels.length; i++) {
        const labelSelector = `${accountFilterButton}//label[${i + 1}]`;
        I.clickFixed(labelSelector);
        I.saveScreenshot(`1141.Test_item_No.20_Switch_account_filter_label_${i + 1}_ON.png`);
        I.clickFixed(labelSelector);
        I.saveScreenshot(`1141.Test_item_No.20_Switch_account_filter_label_${i + 1}_OFF.png`);
    }
});
Scenario('Test Item 21: Initial display sticks and order', async ({ I }) => {
    const clearButton = '//*[@data-testid="orderInquiryFund_clear_id"]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1141.Test_item_No.21_Initial_display_sticks_and_order.png');
});
Scenario('Test Item 22: Filter and sort based on entered parameters', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionFundsPage();
    const settingButton = '//*[@data-testid="orderInquiryFund_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    const displaySymbolButton = '//*[@data-testid="orderInquiryFund_displaySymbol_id"]';
    I.clickFixed(displaySymbolButton);
    const thirdOption = '//*[@data-testid="orderInquiryFund_displaySymbol_id"]//p[3]';
    I.clickFixed(thirdOption);
    const displayOrderButton = '//*[@data-testid="orderInquiryFund_displayOrder_id"]';
    I.waitForElement(displayOrderButton, 2);
    I.scrollAndClick(displayOrderButton);
    const thirdOption2 = '//*[@data-testid="orderInquiryFund_displayOrder_id"]//p[3]';
    I.clickFixed(thirdOption2);
    const accountFilterButton = '//*[@data-testid="orderInquiryFund_accountFilter_id"]';
    I.scrollToElement(accountFilterButton);
    await I.waitFor('shortWait');
    const labelSelector = `${accountFilterButton}//label[1]`;
    I.clickFixed(labelSelector);

    const filterButton = '//*[@data-testid="orderInquiryFund_confirm_id"]';
    I.clickFixed(filterButton);
    I.saveScreenshot('1141.Test_item_No.22_Filter_and_sort_based_on_entered_parameters.png');
});
Scenario('Test Item 23: Check View stock information Go to fund details', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionFundsPage();
    await I.waitForElement('//*[@data-testid="orderInquiryFund_detailList_id"]', 5);

    try {
        const firstOrderSelector = '//*[@data-testid="orderInquiryFund_detailList_id"]/div[1]';
        if (await I.grabNumberOfVisibleElements(firstOrderSelector) > 0) {
            I.saveScreenshot('1141.Test_item_No.7_before_click_order.png');
            I.clickFixed(firstOrderSelector);
            const viewSymbolInfoButton = '//*[@data-testid="orderInquiryFund_viewSymbolInfo_id"]';
            I.clickFixed(viewSymbolInfoButton);
            await I.waitFor();
            I.saveScreenshot('1141.Test_item_No.23_View_stock_information_Go_to_fund_details.png');

        } else {
            console.log('No orders found to check details');
            I.saveScreenshot('1141.Test_item_No.23_no_orders_found.png');
        }
    } catch (e) {
        console.log('Error when clicking order details:', e);
        I.saveScreenshot('1141.Test_item_No.23_error.png');
    }
});
Scenario('Test Item 24: Check Cancel order Go to confirmation of mutual fund cancellation', async ({ I, orderStatus }) => {
    await orderStatus.goToOrderExecutionFundsPage();
    await I.waitForElement('//*[@data-testid="orderInquiryFund_detailList_id"]', 5);

    try {
        const firstOrderSelector = '//*[@data-testid="orderInquiryFund_detailList_id"]/div[1]';
        if (await I.grabNumberOfVisibleElements(firstOrderSelector) > 0) {
            I.saveScreenshot('1141.Test_item_No.7_before_click_order.png');
            I.clickFixed(firstOrderSelector);
            const cancelOrderButton = '//*[@data-testid="orderInquiryFund_deleteOrder_id"]';
            I.clickFixed(cancelOrderButton);
            await I.waitFor();
            I.saveScreenshot('1141.Test_item_No.24_Cancel_order_Go_to_confirmation_of_mutual_fund_cancellation.png');
            await I.backToPreviousScreen();
        } else {
            console.log('No orders found to check details');
            I.saveScreenshot('1141.Test_item_No.24_no_orders_found.png');
            await I.backToPreviousScreen();
        }
    } catch (e) {
        console.log('Error when clicking order details:', e);
        I.saveScreenshot('1141.Test_item_No.24_error.png');
    }
});
