import { COL<PERSON>, COOKIE_KEY, PAGE_URL, USER_ID } from '../const/constant';

Feature('AssetStatus - AssetPerformanceTabPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/InvestmentResult/AssetPerformance.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user4 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

Scenario('test asset performance page', async ({ I }) => {
    // Navigate to asset balance page
    await I.amOnPage(PAGE_URL.mypagePortfolio);
    await I.waitFor('mediumWait');
    const processingButton = '//button[@data-testid="portfolio_investmentResult_id"]';
    await I.clickFixed(processingButton);
    await I.waitFor();

    const performanceButton = '//*[@data-testid="investmentResult_assetPerformance_id"]';
    I.waitForElement(performanceButton);
    I.click(performanceButton);
    I.saveScreenshot('96.asset_balance_page_performance_tab_assetPerformancePage.png');
});

Scenario('test Item 11 Date Type page', async ({ I }) => {
    I.saveScreenshot('96.asset_balance_page_performance_tab_assetPerformancePageItem11.png');
    // Navigate to asset balance page
    const investmentProfitLossButton = '//*[@data-testid="assetTransition_dateType_id"]';
    await I.waitForElement({ xpath: investmentProfitLossButton });
    const dateTypeText = await I.grabTextFromAll(`${investmentProfitLossButton}/label`);
    const monthTextCheck = [
        ['09/14', '09/15', '09/16', '09/17'],
        ['07/06', '07/09', '08/11', '08/13', '09/15', '09/17'],
        ['06/03', '07/06', '08/10', '08/13', '09/16'],
        ['21/05', '22/06', '22/08', '22/09'],
    ];
    for (let i = 0; i < dateTypeText.length; i++) {
        const labelElement = `${investmentProfitLossButton}/label[${i + 1}]/p`;
        I.tapLocationOfElement(labelElement);
        await I.waitFor();
        if (i < dateTypeText.length - 1) {
            const property = await I.grabCssPropertyFrom(labelElement, 'color');
            I.assertEqual(property, COLOR.mainColor, `color is not ${COLOR.mainColor}`);
            await I.waitFor();
            const monthText = await I.grabTextFromAll({ css: '.highcharts-container.hchart-asset-transition svg g.highcharts-axis-labels.highcharts-xaxis-labels text' });
            monthText.forEach((text: string, index: number) => {
                I.assertEqual(text, monthTextCheck[i][index], 'month text is not correct');
            });
        } else {
            const titleBottomSheet = '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[1]/p';
            await I.waitForElement({ xpath: titleBottomSheet });
            const titleText = await I.grabTextFrom(titleBottomSheet);
            I.assertEqual(titleText, '日付指定', 'title text is not correct');
            await I.waitFor();
            const cancelButton = '//*[@data-testid="datePickerSheet_close_id"]';
            await I.waitForElement({ xpath: cancelButton });
            I.click(cancelButton);
            await I.waitFor();
        }
    }
});

Scenario(
    'test Item 14 AssetPerformance View, Item 31 Detailed explanation of AssetPerformance screen',
    async ({ I }) => {
        I.saveScreenshot('96.asset_balance_page_performance_tab_assetPerformancePageItem1431.png');
        const assetPerformanceViewButton = '//*[@data-testid="assetTransition_assetPerformanceView_id"]';
        await I.waitForElement({ xpath: assetPerformanceViewButton });
        I.click(assetPerformanceViewButton);
        await I.waitFor();
        const titleText = await I.grabTextFrom('//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/p');
        I.assertEqual(titleText, '資産推移の見方1', 'title text is not correct');
        await I.waitFor();
        const aElement = '//*[@data-testid="assetTransition_detailedExplanationOfAssetPerformanceScreen_id_0"]/a';
        await I.waitForElement({ xpath: aElement });
        const href = await I.grabAttributeFrom(aElement, 'href');
        I.assertEqual(href, 'https://kabu.com/mobile/app/guide/index.html#asset_trends', 'href is not correct');
        await I.waitFor();
        I.performSwipe({ x: 300, y: 700 }, { x: 50, y: 700 });
        await I.waitFor();
        const aElement2 = '//*[@data-testid="assetTransition_detailedExplanationOfAssetPerformanceScreen_id_1"]/a';
        await I.waitForElement({ xpath: aElement2 });
        const href2 = await I.grabAttributeFrom(aElement2, 'href');
        I.assertEqual(href2, 'https://kabu.com/mobile/app/guide/index.html#asset_trends', 'href is not correct');
        await I.waitFor('mediumWait');
        const cancelButton = '//button[@aria-label="cancel-btn"]';
        await I.waitForElement(cancelButton);
        I.clickFixed(cancelButton);
        await I.waitFor();
    },
);

Scenario('test Item 29 Legend screen', async ({ I }) => {
    I.saveScreenshot('96.asset_balance_page_performance_tab_assetPerformancePageItem29.png');
    const legendButton = '//*[@data-testid="assetTransition_legend_id"]';
    await I.waitForElement({ xpath: legendButton });
    I.click(`${legendButton}/p[1]`);
    const opacity = await I.grabCssPropertyFrom('//*[@data-testid="assetTransition_legend_id"]/p[1]', 'opacity');
    I.assertEqual(opacity, '0.5', 'opacity is not correct');
    await I.waitFor();
    const highChart =
        '.highcharts-container.hchart-asset-transition svg g.highcharts-series-group g.highcharts-series.highcharts-series-0.highcharts-area-series';
    await I.waitForElement({ css: highChart });
    const highChartVisibility = await I.grabAttributeFrom(highChart, 'visibility');
    I.assertEqual(highChartVisibility, 'hidden', 'highChart visibility is not correct');
    await I.waitFor();
    I.click(`${legendButton}/p[2]`);
    const opacity2 = await I.grabCssPropertyFrom('//*[@data-testid="assetTransition_legend_id"]/p[2]', 'opacity');
    I.assertEqual(opacity2, '0.5', 'opacity is not correct');
    await I.waitFor();
    const highChart2 =
        '.highcharts-container.hchart-asset-transition svg g.highcharts-series-group g.highcharts-series.highcharts-series-1.highcharts-area-series.hchart-area-principal';
    await I.waitForElement({ css: highChart2 });
    const highChartVisibility2 = await I.grabAttributeFrom(highChart2, 'visibility');
    I.assertEqual(highChartVisibility2, 'hidden', 'highChart visibility is not correct');
    await I.waitFor();
});
