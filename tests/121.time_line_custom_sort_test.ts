import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Timeline - TimeLineCustomSort');

Before(async ({ I, loginAndSwitchToWebAs, timeLine }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user72 }, { name: COOKIE_KEY.siteId, value: '1' }]);
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Time Line Custom Sort Modal', async ({ I, timeLine }) => {
    // Navigate to Time Line Top page
    await timeLine.goToTimeLineTop();
    // Open Time Line Custom Sort Modal
    const settingBtn = '//*[@data-testid="timeLineTop_setting_id"]';
    I.waitForElement(settingBtn);
    await I.clickFixed(settingBtn);
    await I.waitFor();
    I.seeElement('//*[@data-testid="common_rightSlide_close_id"]');
    I.waitForText('タイムライン配信設定', 3, 'body');
    await I.saveScreenshot('121.time_line_custom_sort_Test_Time_Line_Custom_Sort_Modal.png');
});

Scenario('Test item No.5 Timely Disclosed Info', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_timelyDisclosedInfo_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.5_Timely_Disclosed_Info_inactive_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.5_Timely_Disclosed_Info_active_display.png');
});

Scenario('Test item No.7 Timely Payment Info', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_paymentInfo_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.7_Timely_Payment_Info_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.7_Timely_Payment_Info_inactive_display.png');
});

Scenario('Test item No.8 symbolInfo', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_symbolInfo_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.8_symbolInfo_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.8_symbolInfo_inactive_display.png');
});

Scenario('Test item No.9 Cash in-out Noti', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_cashInOutNoti_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.9_Cash_in-out_Noti_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.9_Cash_in-out_Noti_inactive_display.png');
});

Scenario('Test item No.10 Execution Invalid Noti', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_executionInvalidNoti_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.10_Execution_Invalid_Noti_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.10_Execution_Invalid_Noti_inactive_display.png');
});

Scenario('Test item No.11 Trading Product Noti', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_tradingProductNoti_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.11_Trading_Product_Noti_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.11_Trading_Product_Noti_inactive_display.png');
});

Scenario('Test item No.12 Electronic Delivery', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_electronicDelivery_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.12_Electronic_Delivery_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.12_Electronic_Delivery_inactive_display.png');
});

Scenario('Test item No.13 Individual Noti', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_individualNoti_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.13_Individual_Noti_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.13_Individual_Noti_inactive_display.png');
});

Scenario('Test item No.14 Simultaneous Noti', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_simultaneousNoti_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.14_Simultaneous_Noti_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.14_Simultaneous_Noti_inactive_display.png');
});

Scenario('Test item No.15 Kabu Call', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_kabuCall_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.15_Kabu_Call_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.15_Kabu_Call_inactive_display.png');
});

Scenario('Test item No.16 Economic Index Noti', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_economicIndexNoti_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.16_Economic_Index_Noti_active_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.16_Economic_Index_Noti_inactive_display.png');
});

Scenario('Test item No.17 App Update Info', async ({ I, timeLine }) => {
    const xpathItem = '//*[@data-testid="timeLineCustomSort_appUpdateInfo_id"]';
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: false });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.17_App_Update_Info_inactive_display.png');
    await I.waitFor();
    await timeLine.clickTimeLineCustomSortItem({ dataTestId: xpathItem, isActive: true });
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.17_App_Update_Info_active_display.png');
});

Scenario('Test item No.18 Confirm', async ({ I }) => {
    const confirmBtn = '//*[@data-testid="timeLineCustomSort_confirm_id"]';
    I.waitForElement(confirmBtn);
    await I.clickFixed(confirmBtn);
    await I.waitFor();
    I.dontSeeElement(confirmBtn);
    I.dontSeeElement('//*[@data-testid="common_rightSlide_close_id"]');
    I.waitForText('タイムライン', 3, 'body');
    I.seeElement('//*[@data-testid="timeLineTop_setting_id"]');
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.18_Confirm.png');
});

Scenario('Test item No.19 Setting Here', async ({ I }) => {
    // Open Time Line Custom Sort Modal
    const settingBtn = '//*[@data-testid="timeLineTop_setting_id"]';
    I.waitForElement(settingBtn);
    await I.clickFixed(settingBtn);
    await I.waitFor();
    const hereLink = '//*[@data-testid="timeLineCustomSort_settingHere_id"]';
    I.waitForElement(hereLink);
    // {member-site-url}/ap/PC/Notify/KabuCall/Stock/Input
    await common.clickCardItem(hereLink, '/ap/PC/Notify/KabuCall/Stock/Input', 'external');
});

Scenario('Test item No.20 close', async ({ I }) => {
    const settingBtn = '//*[@data-testid="timeLineTop_setting_id"]';
    const closeBtn = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(closeBtn);
    await I.clickFixed(closeBtn);
    await I.waitFor();
    I.dontSeeElement(closeBtn);
    I.waitForText('タイムライン', 3, 'body');
    I.seeElement(settingBtn);
    await I.saveScreenshot('121.time_line_custom_sort_Test_item_No.20_Close.png');
});
