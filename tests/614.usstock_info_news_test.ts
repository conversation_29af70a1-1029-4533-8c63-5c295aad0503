import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';

Feature('InvestmentProducts - USStockInfoSummary');

Before(async ({ I }) => {
    console.debug('before');
    // reset context    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    // await I.login(USER_ID.user6, '111111');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Check UI US Stock Information-News', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/news?symbol=123';
    I.amOnPage(pageUrl);
    I.see('米国株式\n個別銘柄情報', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(pageUrl);
    I.saveScreenshot('614.usstock_info_news_No.0_access_the_usstock_info_news_page.png');
});

// 3.ニュース一覧 -> 当該領域をスクロール範囲とする
Scenario('Item 3. The area is the scroll range', async ({ I }) => {
    I.swipeDirection('up', 0.5);
    I.saveScreenshot('614.usstock_info_news_No.3_the_area_is_the_scroll_range.png');
});

// 4.ニュース -> ニュース詳細に遷移
Scenario('Item 4. Go to news detail', async ({ I }) => {
    await I.seeAndClickElement('$usStockNews_newsCard_id_5');
    I.saveScreenshot('614.usstock_info_news_No.4_go_to_news_detail.png');
});
