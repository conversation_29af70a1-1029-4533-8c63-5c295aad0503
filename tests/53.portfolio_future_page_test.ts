import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioFuturePage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to future page
    await portfolio.top.goToPage();
    await portfolio.top.future();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio future page', async ({ I }) => {
    const { common, future } = portfolio;

    await common.verifyProductName(future.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFuturePage}_layout.png`);
});

Scenario('test portfolio future page [17.銘柄カード ]', async ({ I }) => {
    const { common, future } = portfolio;

    await common.clickSymbolCard(future.productType, '日経225mini 23/03');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFuturePage}_17_symbol_card.png`);
});

Scenario('test portfolio future page [26.新規注文]', async ({ I }) => {
    const { common, future } = portfolio;

    await common.clickSymbolCard(future.productType, '日経225mini 23/03');
    await future.newOrder('255');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFuturePage}_26_new_order.png`);
});

Scenario('test portfolio future page [27.残高照会]', async ({ I }) => {
    const { common, future } = portfolio;

    await common.clickSymbolCard(future.productType, '日経225mini 23/03');
    await future.positionInquiry();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioFuturePage}_27_position_inquiry.png`);
});
