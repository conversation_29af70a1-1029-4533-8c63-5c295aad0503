import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('PositionInquiry - CustomerPositionFundList');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user88 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await I.waitFor();
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 1: Check UI of Position Inquiry Funds page', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryFundsPage();
    I.saveScreenshot('1207.Test_item_No.1_UI_of_Position_Inquiry_Funds_page.png');
});
Scenario('Test Item 2: Check Go to the MMF/China F URL', async ({ I }) => {
    const mmfChinaF = '//div[@data-testid="positionInquiryFund_mmfChinaF_id"]/parent::button';
    await I.waitForElement(mmfChinaF, 2);
    I.scrollToElement(mmfChinaF);
    await common.clickCardItem(mmfChinaF, 'iPhone/Account/AccountStatus/MMFList.asp', 'kcMemberSite');
   
});
Scenario('Test Item 3: Check Go to the Foreign currency MMF URL', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryFundsPage();
    const foreignCurrencyMMF = '//div[@data-testid="positionInquiryFund_foreignCurrencyMMF_id"]/parent::button';
    await I.waitForElement(foreignCurrencyMMF, 2);
    I.scrollToElement(foreignCurrencyMMF);
    await common.clickCardItem(foreignCurrencyMMF, 'iPhone/Account/AccountStatus/MultiCurMMFList.asp', 'kcMemberSite');
    
});
Scenario('Test Item 4: Check button filter', async ({ I, orderStatus }) => {
     await orderStatus.goToPositionInquiryFundsPage();
    const settingButton = '//*[@data-testid="positionInquiryFund_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1207.Test_item_No.4_show_filler_modal.png');
    const closeButton = '//*[@data-testid="common_rightSlide_close_id"]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1207.Test_item_No.4_close_filler_modal.png');
});
Scenario('Test Item 9: Check Display the remaining balance details transition modal', async ({ I }) => {
    await I.waitForElement('//*[@data-testid="positionInquiryFund_detailList_id"]', 5);
    try {
        const firstOrderSelector = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[1]';
        if ((await I.grabNumberOfVisibleElements(firstOrderSelector)) > 0) {
            I.saveScreenshot('1207.Test_item_No.9_before_click_balance.png');
            I.clickFixed(firstOrderSelector);
            await I.waitFor();
            I.saveScreenshot('1207.Test_item_No.9_balance_details_modal.png');
            const cancelButton = '//button[@aria-label="cancel-btn"]';
            I.clickFixed(cancelButton);
            I.saveScreenshot('1207.Test_item_No.9_close_balance_details_modal.png');
        } else {
            console.log('No balances found to check details');
            I.saveScreenshot('1207.Test_item_No.9_no_balances_found.png');
        }
    } catch (e) {
        console.log('Error when clicking balance details:', e);
        I.saveScreenshot('1207.Test_item_No.9_error.png');
    }
});
Scenario('Test Item 18: Check scroll top', async ({ I }) => {
    const lastItem = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[last()]';
    I.scrollToElement(lastItem);
    const scrollToTopButton = '//*[@id="scrollButton"]';
    if ((await I.grabNumberOfVisibleElements(scrollToTopButton)) > 0) {
        I.saveScreenshot('1207.Test_item_No.18_scrollToTop_see_button.png');
        I.clickFixed(scrollToTopButton);
        await I.waitFor('shortWait');
    } else {
        I.dontSeeElement('//*[@data-testid="scrollButton"]');
        I.saveScreenshot('1207.Test_item_No.18_scrollToTop_dont_see_button.png');
    }
});
Scenario('Test Item 19: Check Notes text', async ({ I }) => {
    const commonDropDownArrow = '//*[@data-testid="common_dropDown_arrow_id_down"]';
    I.waitForElement(commonDropDownArrow, 2);
    I.scrollAndClick(commonDropDownArrow);
    const chakraCollapse = '//*[@class="chakra-collapse"]';
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1207.Test_item_No.19_Check_Notes_text.png');
});
Scenario('Test Item 20: Check Display stocks Display pull-down menu excluding duplicates', async ({ I }) => {
    const settingButton = '//*[@data-testid="positionInquiryFund_filter_id"]';
    I.scrollAndClick(settingButton);
    const displaySymbolButton = '//*[@data-testid="positionInquiryFund_displaySymbol_id"]';
    I.clickFixed(displaySymbolButton);
    I.saveScreenshot('1207.Test_item_No.20_Display_stocks_Display_pull-down_menu_excluding_duplicates.png');
    const thirdOption = '//*[@data-testid="positionInquiryFund_displaySymbol_id"]//p[2]';
    I.clickFixed(thirdOption);
});
Scenario('Test Item 22: Check Display Order Display the drop-down menu', async ({ I }) => {
    const displayOrderButton = '//*[@data-testid="positionInquiryFund_displayOrder_id"]';
    I.scrollAndClick(displayOrderButton);
    I.saveScreenshot('1207.Test_item_No.22_Display_Order_Display_the_drop-down_menu.png');
    const thirdOption = '//*[@data-testid="positionInquiryFund_displayOrder_id"]//p[3]';
    I.clickFixed(thirdOption);
});
Scenario('Test Item 23: Check Switch account filter ON/OFF', async ({ I }) => {
    const accountFilterButton = '//*[@data-testid="positionInquiryFund_accountFilter_id"]';
    I.scrollToElement(accountFilterButton);
    const accountFilterLabels = await I.grabTextFromAll(`${accountFilterButton}//label`);

    for (let i = 0; i < accountFilterLabels.length; i++) {
        const labelSelector = `${accountFilterButton}//label[${i + 1}]`;
        I.clickFixed(labelSelector);
        I.saveScreenshot(`1207.Test_item_No.23_Switch_account_filter_label_${i + 1}_ON.png`);
        I.clickFixed(labelSelector);
        I.saveScreenshot(`1207.Test_item_No.23_Switch_account_filter_label_${i + 1}_OFF.png`);
    }
});
Scenario('Test Item 24: Initial display sticks and order', async ({ I }) => {
    const clearButton = '//*[@data-testid="positionInquiryFund_clear_id"]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1207.Test_item_No.24_Initial_display_sticks_and_order.png');
});
Scenario('Test Item 25: Filter and sort based on entered parameters', async ({ I }) => {
    const displaySymbolButton = '//*[@data-testid="positionInquiryFund_displaySymbol_id"]';
    I.clickFixed(displaySymbolButton);
    const thirdOption = '//*[@data-testid="positionInquiryFund_displaySymbol_id"]//p[2]';
    I.clickFixed(thirdOption);
    const displayOrderButton = '//*[@data-testid="positionInquiryFund_displayOrder_id"]';
    I.scrollAndClick(displayOrderButton);
    const thirdOption2 = '//*[@data-testid="positionInquiryFund_displayOrder_id"]//p[3]';
    I.clickFixed(thirdOption2);
    const accountFilterButton = '//*[@data-testid="positionInquiryFund_accountFilter_id"]';
    if ((await I.grabNumberOfVisibleElements(accountFilterButton)) > 0) {
        I.scrollToElement(accountFilterButton);
        await I.waitFor('shortWait');
        const labelSelector = `${accountFilterButton}//label[1]`;
        I.clickFixed(labelSelector);
    } else {
        I.say('Account filter button not found, skipping account filter selection');
    }

    const filterButton = '//*[@data-testid="positionInquiryFund_confirm_id"]';
    I.clickFixed(filterButton);
    I.saveScreenshot('1207.Test_item_No.25_Filter_and_sort_based_on_entered_parameters.png');
});
//TODO: direct to fund/search
Scenario('Test Item 26: Check Buy button', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryFundsPage();
    await I.waitForElement('//*[@data-testid="positionInquiryFund_detailList_id"]', 5);
    try {
        const firstOrderSelector = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[1]';
        if ((await I.grabNumberOfVisibleElements(firstOrderSelector)) > 0) {
            I.saveScreenshot('1207.Test_item_No.26_before_click_balance.png');
            I.clickFixed(firstOrderSelector);
            await I.waitFor('shortWait');
            const positionInquiryFundBuyId = '//*[@data-testid="positionInquiryFund_buy_id"]';
            I.clickFixed(positionInquiryFundBuyId);
            await I.waitFor('shortWait');
            I.saveScreenshot('1207.Test_item_No.26_after_click_buy_button.png');
        } else {
            console.log('No balances found to check details');
            I.saveScreenshot('1207.Test_item_No.26_no_balances_found.png');
        }
    } catch (e) {
        console.log('Error when clicking balance details:', e);
        I.saveScreenshot('1207.Test_item_No.26_error.png');
    }
});
Scenario('Test Item 27: Check Sell button', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryFundsPage();
    await I.waitForElement('//*[@data-testid="positionInquiryFund_detailList_id"]', 5);
    try {
        const firstOrderSelector = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[1]';
        if ((await I.grabNumberOfVisibleElements(firstOrderSelector)) > 0) {
            I.saveScreenshot('1207.Test_item_No.26_before_click_balance.png');
            I.clickFixed(firstOrderSelector);
            await I.waitFor('shortWait');
            const positionInquiryFundSellId = '//*[@data-testid="positionInquiryFund_sell_id"]';
            I.clickFixed(positionInquiryFundSellId);
            await I.waitFor('shortWait');
            await I.saveScreenshot('1207.Test_item_No.27_after_click_sell_button.png');
        } else {
            console.log('No balances found to check details');
            I.saveScreenshot('1207.Test_item_No.27_no_balances_found.png');
        }
    } catch (e) {
        console.log('Error when clicking balance details:', e);
        I.saveScreenshot('1207.Test_item_No.26_error.png');
    }
});
Scenario('Test Item 28: Check Details button', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryFundsPage();
    await I.waitForElement('//*[@data-testid="positionInquiryFund_detailList_id"]', 5);
    try {
        const firstOrderSelector = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[1]';
        if ((await I.grabNumberOfVisibleElements(firstOrderSelector)) > 0) {
            I.saveScreenshot('1207.Test_item_No.28_before_click_balance.png');
            I.clickFixed(firstOrderSelector);
            await I.waitFor('shortWait');
            const positionInquiryFundDetailsId = '//*[@data-testid="positionInquiryFund_detail_id"]';
            I.clickFixed(positionInquiryFundDetailsId);
            await I.waitFor('shortWait');
            I.saveScreenshot('1207.Test_item_No.28_after_click_details_button.png');
        } else {
            console.log('No balances found to check details');
            I.saveScreenshot('1207.Test_item_No.28_no_balances_found.png');
        }
    } catch (e) {
        console.log('Error when clicking balance details:', e);
        I.saveScreenshot('1207.Test_item_No.28_error.png');
    }
});
Scenario('Test Item 8: Check Scrolling the list of details', async ({ I, orderStatus }) => {
    await orderStatus.goToPositionInquiryFundsPage();
    const lastItem = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[last()]';
    I.scrollToElement(lastItem);
    I.saveScreenshot('1207.Test_item_No.8_Scrolling_the_list_of_details.png');
});
// Go to detail page
Scenario('Test Item Check Go to Details page', async ({ I }) => {
    await I.waitForElement('//*[@data-testid="positionInquiryFund_detailList_id"]', 5);
    try {
        const firstOrderSelector = '//*[@data-testid="positionInquiryFund_detailList_id"]/div[1]';
        if ((await I.grabNumberOfVisibleElements(firstOrderSelector)) > 0) {
            I.clickFixed(firstOrderSelector);
            await I.waitFor('shortWait');
            const positionInquiryFundDetailsId = '//*[@data-testid="positionInquiryFund_detail_id"]';
            I.clickFixed(positionInquiryFundDetailsId);
            await I.waitFor('shortWait');
            I.saveScreenshot('1207.Test_item_No.Check_Go_to_Details_page_after_click_details_button.png');
        } else {
            I.saveScreenshot('1207.Test_item_No.Check_Go_to_Details_page_no_balances_found.png');
        }
    } catch (e) {
        console.log('Error when clicking balance details:', e);
        I.saveScreenshot('1207.Test_item_No.Check_Go_to_Details_page_error.png');
    }
});
Scenario('Test Item 16: Check caution statement Tap to open/close', async ({ I }) => {
    const commonDropDownArrow = '//*[@data-testid="common_dropDown_arrow_id_down"]';
    I.waitForElement(commonDropDownArrow, 2);
    I.scrollAndClick(commonDropDownArrow);
    const chakraCollapse = '//*[@class="chakra-collapse"]';
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1207.Test_item_No.16_Check_caution_statement_Tap_to_open_close.png');
});
