import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - StockProductSearchInvestmentTrustsInformationSystems');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI of Fund Search page', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    I.saveScreenshot('950.Test_item_No.0_Check_UI_of_Fund_Search_page.png');
});
Scenario('Test Item 2: Narrow down your search by detailed criteria Tap "Show search modal', async ({ I, search }) => {
    await search.goToFundSearchFilterByDetailedConditions();
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.2_Narrow_down_your_search_by_detailed_criteria_Tap_Show_search_modal.png');
});
Scenario('Test Item 3: Check Tooltips for easy search are displayed', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    I.clickFixed('//*[@data-testid="sectionTitle_id"]//img');
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.3_Check_Tooltips_for_easy_search_are_displayed.png');
});
Scenario('Test Item 3a: Check toggle of Simple condition search', async ({ I }) => {
    I.clickFixed('//*[@data-testid="sectionTitle_id"]');
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.3a_Check_toggle_of_Simple_condition_search.png');
});
Scenario('Test Item 4: Check easy search condition selection functionality', async ({ I, search }) => {
    await search.goToFundSearch();
    const easySearchConditions = [
        'NISA(成長投資枠)',
        'NISA(つみたて投資枠)',
        'インデックス',
        'バランス',
        '高リターン',
        '低リスク'
    ];

    const easySearchContainer = '//*[@data-testid="infoFundSearch_kantanConditionSearch_id"]';

    for (const condition of easySearchConditions) {
        const conditionXPath = `${easySearchContainer}//*[./p[contains(text(), "${condition}")]]`;
        I.waitForElement(conditionXPath);
        await I.scrollAndClick(conditionXPath);
        await I.waitFor('mediumWait');
        let screenshotName;
        switch (condition) {
            case 'NISA(成長投資枠)':
                screenshotName = '950.Test_item_No.4_NISA_Growth_Investment_selected.png';
                break;
            case 'NISA(つみたて投資枠)':
                screenshotName = '950.Test_item_No.4_NISA_Cumulative_Investment_selected.png';
                break;
            case 'インデックス':
                screenshotName = '950.Test_item_No.4_Index_Fund_selected.png';
                break;
            case 'バランス':
                screenshotName = '950.Test_item_No.4_Balanced_Fund_selected.png';
                break;
            case '高リターン':
                screenshotName = '950.Test_item_No.4_High_Return_Fund_selected.png';
                break;
            case '低リスク':
                screenshotName = '950.Test_item_No.4_Low_Risk_Fund_selected.png';
                break;
        }
        await I.saveScreenshot(screenshotName);
        await I.waitFor('shortWait');
    }
});
Scenario('Test Item 6: Verify sort dropdown menu functionality', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    I.clickFixed('//*[contains(@class, "chakra-menu__menu-button")]');
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.6_Verify_sort_dropdown_menu_functionality_before_choosing.png');
    I.clickFixed('//*[@class="chakra-menu__group"]//*[contains(text(), "トータルリターン（1年）が高い順")]');
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.6_Verify_sort_dropdown_menu_functionality_after_choosing.png');
});
Scenario('Test Item 8: Check search results scrolling loads additional fund information', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    const fundCardContainer = '//*[@data-testid="infoFundSearch_card_id"]';
    I.waitForElement(fundCardContainer);
    // Scroll to the last fund card in the container
    const lastFundCard = `(${fundCardContainer})[last()]`;
    await I.scrollToElement(lastFundCard);
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.8_Check_search_results_scrolling_loads_additional_fund_information.png');
});
Scenario('Test Item 9: Verify tapping on fund card navigates to fund details', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    const fundCardContainer = '//*[@data-testid="infoFundSearch_card_id"]';
    I.waitForElement(fundCardContainer);
    const firstFundCard = `(${fundCardContainer})[1]`;
    I.clickFixed(firstFundCard);
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.9_Verify_tapping_on_fund_card_navigates_to_fund_details.png');
});
Scenario('Test Item 16: Check scroll to top functionality', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    const fundCardContainer = '//*[@data-testid="infoFundSearch_card_id"]';
    I.waitForElement(fundCardContainer);

    // Scroll to the last fund card in the container
    const lastFundCard = `(${fundCardContainer})[last()]`;
    await I.scrollToElement(lastFundCard);
    await I.waitFor();
    await I.saveScreenshot('950.Test_item_No.16_Check_scroll_to_top_functionality.png');
    I.waitForElement('//*[@id="scrollButton"]', 2);
    await I.clickFixed('//*[@id="scrollButton"]');
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.16_Check_scroll_to_top_functionality_after_clicking.png');
});
Scenario('Test Item 18: Verify easy search help tooltip display', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    await search.goToFundSearchFilterByDetailedConditions();
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.18_Verify_easy_search_help_tooltip_display.png');
    I.clickFixed('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "かんたん検索条件")]]//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.18_Verify_easy_search_help_tooltip_display_after_clicking.png');
});
Scenario('Test Item 19: Check easy search accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][p[contains(text(), "かんたん検索条件")]]');
    I.saveScreenshot('950.Test_item_No.19_Check_easy_search_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 20: Verify easy search selection functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][p[contains(text(), "かんたん検索条件")]]');
    I.waitForElement('//*[@data-testid="infoFundSearch_kantanConditionSearchSelection_id"]', 2);
    I.clickFixed('//*[@data-testid="infoFundSearch_kantanConditionSearchSelection_id"]//label[.//div[contains(text(), "インデックスファンド")]]');
    await I.waitFor('mediumWait');
    I.saveScreenshot('950.Test_item_No.20_Verify_easy_search_selection_functionality.png');
});
Scenario('Test Item 22: Check QUICK fund score help tooltip display', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "QUICK投信スコア")]]//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.22_Check_QUICK_fund_score_help_tooltip_display.png');
});
Scenario('Test Item 23: Verify QUICK fund score accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "QUICK投信スコア")]]');
    await I.waitFor('shortWait');
    I.clickFixed('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "QUICK投信スコア")]]');
    await I.waitFor('shortWait');
    const infoFundSearch = '//*[@data-testid="infoFundSearch_investmentTrustScoreRowSelection_id"]';
    I.waitForElement(infoFundSearch, 2);
    I.scrollToElement(infoFundSearch);
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.23_Verify_QUICK_fund_score_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 24: Check QUICK fund score category selection display change', async ({ I }) => {

    const categoryLabels = [1, 2, 3, 4, 5, 6];
    for (const index of categoryLabels) {
        await I.scrollAndClick(`//*[@data-testid="infoFundSearch_investmentTrustScoreCategory_id"]//label[${index}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.24_Check_QUICK_fund_score_category_selection_${index}.png`);
    }
});
Scenario('Test Item 25: Verify QUICK fund score column selection toggle', async ({ I }) => {
    await I.scrollAndClick(`//*[@data-testid="infoFundSearch_investmentTrustScoreCategory_id"]//label[1]`);
    const investmentTrustScoreColumnSelection = '//*[@data-testid="infoFundSearch_investmentTrustScoreColumnSelection_id"]';
    I.waitForElement(investmentTrustScoreColumnSelection, 3);
    const categoryLabels = [1, 2, 3, 4, 5];
    for (const index of categoryLabels) {
        I.clickFixed(`${investmentTrustScoreColumnSelection}//div[${index}]`);
        await I.waitFor('shortWait');
        I.saveScreenshot(`950.Test_item_No.25_Verify_QUICK_fund_score_column_selection_toggle_${index}.png`);
    }
});
Scenario('Test Item 28: Verify total return help tooltip display', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "トータルリターン")]]//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.28_Verify_total_return_help_tooltip_display.png');
});
Scenario('Test Item 29: Check total return accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "トータルリターン")]]');
    await I.waitFor('shortWait');
    I.clickFixed('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "トータルリターン")]]');
    await I.waitFor('shortWait');
    const totalReturnColumnSelection = '//*[@data-testid="infoFundSearch_totalReturnColumnSelection_id"]';
    I.waitForElement(totalReturnColumnSelection, 2);
    I.scrollToElement(totalReturnColumnSelection);
    I.saveScreenshot('950.Test_item_No.29_Check_total_return_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 30: Verify total return column selection toggle', async ({ I, search }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_totalReturnColumnSelection_id"]//button[@type="button"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_totalReturnColumnSelection_id"]//button[@type="button"])[${i+1}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.30_Verify_total_return_column_selection_toggle_${i}.png`);
    }
});

Scenario('Test Item 33: Verify investment asset help tooltip display', async ({ I }) => {
    const investmentAssetSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "投資対象資産")]]';
    I.waitForElement(investmentAssetSectionTitle, 2);
    I.scrollToElement(investmentAssetSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(investmentAssetSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.33_Verify_investment_asset_help_tooltip_display.png');
});
Scenario('Test Item 34: Check investment asset accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "投資対象資産")]]');
    await I.waitFor('shortWait');
    I.clickFixed('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "投資対象資産")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.34_Check_investment_asset_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 35: Verify investment asset selection toggle', async ({ I }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_investmentTargetAssetSelection_id"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_investmentTargetAssetSelection_id"])[${i}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.35_Verify_investment_asset_selection_toggle_${i}_on.png`);
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_investmentTargetAssetSelection_id"])[${i}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.35_Verify_investment_asset_selection_toggle_${i}_off.png`);
    }
});
Scenario('Test Item 37: Check investment region help tooltip display', async ({ I }) => {
    const investmentRegionSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "投資対象地域")]]';
    I.waitForElement(investmentRegionSectionTitle, 2);
    I.scrollToElement(investmentRegionSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(investmentRegionSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.37_Check_investment_region_help_tooltip_display.png');
});
Scenario('Test Item 38: Verify investment region accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "投資対象地域")]]');
    await I.waitFor('shortWait');
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "投資対象地域")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.38_Verify_investment_region_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 39: Check investment region selection toggle', async ({ I }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_investmentTargetAreaSelection_id"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_investmentTargetAreaSelection_id"])[${i}]`);
        I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.39_Check_investment_region_selection_toggle_${i}_on.png`);
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_investmentTargetAreaSelection_id"])[${i}]`);
        I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.39_Check_investment_region_selection_toggle_${i}_off.png`);
    }
});
Scenario('Test Item 41: Verify transaction type help tooltip display', async ({ I }) => {
    const transactionTypeSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "取引種別")]]';
    I.waitForElement(transactionTypeSectionTitle, 2);
    I.scrollToElement(transactionTypeSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(transactionTypeSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.41_Verify_transaction_type_help_tooltip_display.png');
});
Scenario('Test Item 42: Check transaction type accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "取引種別")]]');
    await I.waitFor('shortWait');
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "取引種別")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.42_Check_transaction_type_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 45: Check features help tooltip display', async ({ I }) => {
    const featuresSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "特色")]]';
    I.waitForElement(featuresSectionTitle, 2);
    I.scrollToElement(featuresSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(featuresSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.45_Check_features_help_tooltip_display.png');
});
Scenario('Test Item 46: Verify features accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "特色")]]');
    await I.waitFor('shortWait');
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "特色")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.46_Verify_features_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 47: Check features selection toggle', async ({ I }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_featureSelection_id"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_featureSelection_id"])[${i}]//label`);
        I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.47_Check_features_selection_toggle_${i}_on.png`);
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_featureSelection_id"])[${i}]//label`);
        I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.47_Check_features_selection_toggle_${i}_off.png`);
    }
});
Scenario('Test Item 49: Verify point program help tooltip display', async ({ I }) => {
    const pointProgramSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "ポイントプログラム")]]';
    I.waitForElement(pointProgramSectionTitle, 2);
    I.scrollToElement(pointProgramSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(pointProgramSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.49_Verify_point_program_help_tooltip_display.png');
});
Scenario('Test Item 50: Check point program accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "ポイントプログラム")]]');
    await I.waitFor('shortWait');
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "ポイントプログラム")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.50_Check_point_program_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 51: Verify point program selection toggle', async ({ I }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_pointProgramSelection_id"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_pointProgramSelection_id"])[${i}]//label`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.51_Verify_point_program_selection_toggle_${i}_on.png`);
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_pointProgramSelection_id"])[${i}]//label`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.51_Verify_point_program_selection_toggle_${i}_off.png`);
    }
});
Scenario('Test Item 53: Check Sharpe ratio help tooltip display', async ({ I }) => {
    const sharpeRatioSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "シャープレシオ")]]';
    I.waitForElement(sharpeRatioSectionTitle, 2);
    I.scrollToElement(sharpeRatioSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(sharpeRatioSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.53_Check_Sharpe_ratio_help_tooltip_display.png');
});
Scenario('Test Item 54: Verify Sharpe ratio accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "シャープレシオ")]]');
    await I.waitFor('shortWait');
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "シャープレシオ")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.54_Verify_Sharpe_ratio_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 55: Check Sharpe ratio column selection toggle', async ({ I }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_sharpeRatioColumnSelection_id"]//button[@type="button"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_sharpeRatioColumnSelection_id"]//button[@type="button"])[${i+1}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.55_Check_Sharpe_ratio_column_selection_toggle_${i}_on.png`);
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_sharpeRatioColumnSelection_id"]//button[@type="button"])[${i+1}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.55_Check_Sharpe_ratio_column_selection_toggle_${i}_off.png`);
    }
});
Scenario('Test Item 58: Check standard deviation help tooltip display', async ({ I }) => {
    const standardDeviationSectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "標準偏差")]]';
    I.waitForElement(standardDeviationSectionTitle, 2);
    I.scrollToElement(standardDeviationSectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(standardDeviationSectionTitle + '//img');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.58_Check_standard_deviation_help_tooltip_display.png');
});
Scenario('Test Item 59: Verify standard deviation accordion expand/collapse functionality', async ({ I }) => {
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "標準偏差")]]');
    await I.waitFor('shortWait');
    await I.scrollAndClick('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "標準偏差")]]');
    await I.waitFor('shortWait');
    I.saveScreenshot('950.Test_item_No.59_Verify_standard_deviation_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 60: Check standard deviation column selection toggle', async ({ I }) => {
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_standardDeviationColumnSelection_id"]//button[@type="button"]');
    for (let i = 1; i <= buttons; i++) {
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_standardDeviationColumnSelection_id"]//button[@type="button"])[${i+1}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.60_Check_standard_deviation_column_selection_toggle_${i}_on.png`);
        await I.scrollAndClick(`(//*[@data-testid="infoFundSearch_standardDeviationColumnSelection_id"]//button[@type="button"])[${i+1}]`);
        await I.waitFor('shortWait');
        await I.saveScreenshot(`950.Test_item_No.60_Check_standard_deviation_column_selection_toggle_${i}_off.png`);
    }
});
Scenario('Test Item 63: Check management company help tooltip display', async ({ I }) => {
    const managementCompanySectionTitle = '//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "運用会社")]]';
    I.waitForElement(managementCompanySectionTitle, 2);
    I.scrollToElement(managementCompanySectionTitle);
    await I.waitFor('shortWait')
    I.clickFixed(managementCompanySectionTitle + '//img');
    await I.waitFor('shortWait')
    I.saveScreenshot('950.Test_item_No.63_Check_management_company_help_tooltip_display.png');
});
Scenario('Test Item 64: Verify management company accordion expand/collapse functionality', async ({ I }) => {
    I.clickFixed('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "運用会社")]]');
    I.clickFixed('//*[@data-testid="sectionTitle_id"][.//p[contains(text(), "運用会社")]]');
    I.saveScreenshot('950.Test_item_No.64_Verify_management_company_accordion_expand_collapse_functionality.png');
});
Scenario('Test Item 70: Check "View Funds" button closes search modal', async ({ I, search }) => {
    await search.goToFundSearch();
    await I.waitForText('ファンド検索', 3, 'body');
    await search.goToFundSearchFilterByDetailedConditions();
    I.clickFixed('//*[@data-testid="infoFundSearch_viewFund_id"]');
    I.saveScreenshot('950.Test_item_No.70_Check_View_Funds_button_closes_search_modal.png');
});
Scenario('Test Item 71: Verify "Clear Conditions" button resets search modal', async ({ I, search }) => {
    await search.goToFundSearchFilterByDetailedConditions();
    I.waitForElement('//*[@data-testid="infoFundSearch_kantanConditionSearchSelection_id"]', 2);
    await I.scrollAndClick('//*[@data-testid="infoFundSearch_kantanConditionSearchSelection_id"]//label[.//div[contains(text(), "インデックスファンド")]]');
    I.clickFixed('//*[@data-testid="infoFundSearch_clearConditions_id"]');
    I.saveScreenshot('950.Test_item_No.71_Verify_Clear_Conditions_button_resets_search_modal.png');
});

Scenario('Test Item 72: Check keyword search updates search modal after input', async ({ I }) => {
    const keyword = 'abv';
    const inputSearch = '//*[@data-testid="infoFundSearch_keywordSearch_id"]//input';
    I.waitForElement(inputSearch, 2);
    await I.scrollAndClick(inputSearch);
    await I.fillField(inputSearch, keyword);
    const searchButton = '//*[@data-testid="infoFundSearch_keywordSearch_id"]//div[1]/img';
    I.waitForElement(searchButton, 2);
    I.clickFixed(searchButton);
    await I.waitFor();
    I.saveScreenshot('950.Test_item_No.72_Click_search_button.png');
});
Scenario('Test Item 73: Verify QUICK fund score row selection toggle', async ({ I }) => {
    const infoFundSearch = '//*[@data-testid="infoFundSearch_investmentTrustScoreRowSelection_id"]';
    I.waitForElement(infoFundSearch, 2);
    I.scrollToElement(infoFundSearch);
    await I.waitFor();
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_investmentTrustScoreRowSelection_id"]//button[@type="button"]');

    await I.waitFor('shortWait');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_investmentTrustScoreRowSelection_id"]//button[@type="button"])[${i}]`);
        await I.waitFor('shortWait');
        I.saveScreenshot(`950.Test_item_No.73_Verify_QUICK_fund_score_row_selection_toggle_${i}_on.png`);
    }
});
Scenario('Test Item 74: Check total return row selection toggle', async ({ I }) => {
    const totalReturnColumnSelection = '//*[@data-testid="infoFundSearch_totalReturnRowSelection_id"]';
    I.waitForElement(totalReturnColumnSelection, 2);
    I.scrollToElement(totalReturnColumnSelection);
    await I.waitFor();
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_totalReturnRowSelection_id"]//button[@type="button"]');

    await I.waitFor('shortWait');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_totalReturnRowSelection_id"]//button[@type="button"])[${i}]`);
        await I.waitFor('shortWait');
        I.saveScreenshot(`950.Test_item_No.74_Check_total_return_row_selection_toggle_${i}_on.png`);
    }
});
Scenario('Test Item 75: Verify Sharpe ratio row selection toggle', async ({ I }) => {
    const sharpeRatioColumnSelection = '//*[@data-testid="infoFundSearch_sharpeRatioRowSelection_id"]';
    I.waitForElement(sharpeRatioColumnSelection, 2);
    I.scrollToElement(sharpeRatioColumnSelection);
    await I.waitFor();
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_sharpeRatioRowSelection_id"]//button[@type="button"]');

    await I.waitFor('shortWait');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_sharpeRatioRowSelection_id"]//button[@type="button"])[${i}]`);
        await I.waitFor('shortWait');
        I.saveScreenshot(`950.Test_item_No.75_Verify_Sharpe_ratio_row_selection_toggle_${i}_on.png`);
    }
});
Scenario('Test Item 76: Check standard deviation row selection toggle', async ({ I }) => {
    const standardDeviationColumnSelection = '//*[@data-testid="infoFundSearch_standardDeviationRowSelection_id"]';
    I.waitForElement(standardDeviationColumnSelection, 2);
    I.scrollToElement(standardDeviationColumnSelection);
    await I.waitFor();
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_standardDeviationRowSelection_id"]//button[@type="button"]');

    await I.waitFor('shortWait');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_standardDeviationRowSelection_id"]//button[@type="button"])[${i}]`);
        await I.waitFor('shortWait');
        I.saveScreenshot(`950.Test_item_No.76_Check_standard_deviation_row_selection_toggle_${i}_on.png`);
    }
});
Scenario('Test Item 26: Check QUICK Investment Trust Score Selection Switch between selected and unselected statesfund score selection toggle functionality', async ({ I }) => {
    I.scrollToElement('//*[@data-testid="infoFundSearch_investmentTrustScoreSelection_id"]');
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_investmentTrustScoreSelection_id"]//label');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_investmentTrustScoreSelection_id"]//label)[${i}]`);
        I.saveScreenshot(`950.Test_item_No.26_Check_QUICK_Investment_Trust_Score_Selection_Switch_between_selected_and_unselected_states.png`);
    }
});
Scenario('Test Item 31: Check Total Return Selection Switch between selected and unselected states', async ({ I }) => {
    I.scrollToElement('//*[@data-testid="infoFundSearch_totalReturnSelection_id"]');
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_totalReturnSelection_id"]//label');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_totalReturnSelection_id"]//label)[${i}]`);
        I.saveScreenshot(`950.Test_item_No.31_Check_Total_Return_Selection_Switch_between_selected_and_unselected_states.png`);
    }
});
Scenario('Test Item 43: Check Transaction type selection Switch between selected and unselected states', async ({ I }) => {
    const buttons = [
        '積立（1147）',
        'NISA(成長投資枠)（1406）', 
        'NISA(つみたて投資枠)（194）'
    ];
    I.scrollToElement('//*[@data-testid="infoFundSearch_tradeTypeSelection_id"]');
    for (let i = 0; i < buttons.length; i++) {
        const buttonXPath = `//*[@data-testid="infoFundSearch_tradeTypeSelection_id"]//label[.//p[contains(text(), "${buttons[i]}")]]`;
        I.clickFixed(buttonXPath);
        await I.waitFor('shortWait');
        I.saveScreenshot(`950.Test_item_No.43_Check_Transaction_type_selection_Switch_between_selected_and_unselected_states.png`);
    }
});
Scenario('Test Item 56: Check Sharpe ratio selection Switch between selected and unselected states', async ({ I }) => {
    I.scrollToElement('//*[@data-testid="infoFundSearch_sharpeRatioSelection_id"]');
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_sharpeRatioSelection_id"]//label');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_sharpeRatioSelection_id"]//label)[${i}]`);
        I.saveScreenshot(`950.Test_item_No.56_Check_Sharpe_ratio_selection_Switch_between_selected_and_unselected_states.png`);
    }
});
Scenario('Test Item 61: Check Standard deviation selection Switch between selected and unselected states', async ({ I }) => {
    I.scrollToElement('//*[@data-testid="infoFundSearch_standardDeviationSelection_id"]');
    const buttons = await I.grabNumberOfVisibleElements('//*[@data-testid="infoFundSearch_standardDeviationSelection_id"]//label');
    for (let i = 1; i <= buttons; i++) {
        I.clickFixed(`(//*[@data-testid="infoFundSearch_standardDeviationSelection_id"]//label)[${i}]`);
        I.saveScreenshot(`950.Test_item_No.61_Check_Standard_deviation_selection_Switch_between_selected_and_unselected_states.png`);
    }
});
Scenario('Test Item 65-66: Check Operating company name and Switch between selected and unselected states', async ({ I }) => {
    const managementCompanyName = '//*[@data-testid="infoFundSearch_managementCompanyName_id"]';
    I.scrollToElement(managementCompanyName);
    I.saveScreenshot('950.Test_item_No.65_Management_company_name_close.png');
    I.clickFixed(managementCompanyName);
    I.saveScreenshot('950.Test_item_No.65_Management_company_name_open.png');
   

    I.clickFixed(`${managementCompanyName}//div[@data-testid="infoFundSearch_managementCompanySelection_id"][1]`);
    I.saveScreenshot(`950.Test_item_No.66_Check_Operating_company_name_Switch_between_selected_and_unselected_states.png`);
    I.clickFixed(`${managementCompanyName}//div[@data-testid="infoFundSearch_managementCompanySelection_id"][1]`);
    I.saveScreenshot(`950.Test_item_No.66_Check_Operating_company_name_Switch_between_selected_and_unselected_states.png`);
});
