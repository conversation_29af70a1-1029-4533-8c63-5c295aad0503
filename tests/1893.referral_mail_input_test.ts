Feature('Referral - MailInputPage');
import { COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import ReferralMailInput from '../pages/referralMailInput';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I<PERSON>closeBrowser();
});

Scenario('go to referral mail input page', async ({ I }) => {
    I.amOnPage(PAGE_URL.referralMailInput);;
    await <PERSON>.waitFor('mediumWait');
    I.see(
        `ご家族・ご友人
紹介プログラム`,
        '//*[@data-testid="common_header_title_id"]',
    );
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_page.png`);
});

Scenario('Test Item 1: Click here for sample email', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_sample_mail_before_click.png`);

    // Click on the sample mail link which opens an external URL
    const externalUrl = await ReferralMailInput.clickSampleMailLink();

    // Verify the URL is correct
    I.assertContain(externalUrl, ReferralMailInput.urls.sampleMailUrl, 'External URL should be the sample mail page');

    // Take a final screenshot
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_sample_mail_after_click.png`);
});

Scenario('Test Item 2: About the Family and Friends program', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_program_link_before_click.png`);

    // Click on the program link which opens an external URL
    const externalUrl = await ReferralMailInput.clickProgramLink();

    // Verify the URL is correct
    I.assertContain(
        externalUrl,
        ReferralMailInput.urls.programUrl,
        'External URL should be the family and friends program page',
    );

    // Take a final screenshot
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_program_link_after_click.png`);
});

Scenario('Test Item 3: Last name', async ({ I }) => {
    // Fill in the last name field
    await ReferralMailInput.fillLastName('123456');
    // Verify the field has the correct value
    I.seeInField(ReferralMailInput.locators.lastName, '123456');
    // Take a screenshot to verify
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_last_name_field.png`);
});

Scenario('Test Item 4: First name', async ({ I }) => {
    // Fill in the first name field
    await ReferralMailInput.fillFirstName('123456');
    // Verify the field has the correct value
    await I.seeInField(ReferralMailInput.locators.firstName, '123456');
    // Take a screenshot to verify
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_first_name_field.png`);
});

Scenario('Test Item 5: Last name (katakana)', async ({ I }) => {
    // Fill in the last name kana field
    await ReferralMailInput.fillLastNameKana('123456');
    // Verify the field has the correct value
    I.seeInField(ReferralMailInput.locators.lastNameKana, '123456');
    // Take a screenshot to verify
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_last_name_kana_field.png`);
});

Scenario('Test Item 7: First name (katakana)', async ({ I }) => {
    // Fill in the first name kana field
    await ReferralMailInput.fillFirstNameKana('123456');
    // Verify the field has the correct value
    I.seeInField(ReferralMailInput.locators.firstNameKana, '123456');
    // Take a screenshot to verify
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_first_name_kana_field.png`);
});

Scenario('Test Item 8: Mail address', async ({ I }) => {
    // Fill in the mail address field
    await ReferralMailInput.fillMailAddress('<EMAIL>');
    // Verify the field has the correct value
    I.seeInField(ReferralMailInput.locators.mailAddress, '<EMAIL>');
    // Take a screenshot to verify
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_mail_address_field.png`);
});

Scenario('Test Item 9: Message', async ({ I }) => {
    // Fill in the message field
    await ReferralMailInput.fillMessage('123456');
    // Verify the field has the correct value
    I.seeInField(ReferralMailInput.locators.message, '123456');
    // Take a screenshot to verify
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_message_field.png`);
});

Scenario('Test Item 10: Confirm button', async ({ I }) => {
    // Take screenshot before confirming
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_before_confirm.png`);

    // Click confirm button and get URL
    const currentUrl = await ReferralMailInput.clickConfirm();

    // Verify the URL contains the confirmation page path
    I.assertContain(
        currentUrl,
        ReferralMailInput.urls.mailConfirmPage,
        'URL should contain the mail confirmation page path',
    );

    // Take screenshot of confirmation page
    await ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_after_confirm.png`);

    // Navigate back
    await ReferralMailInput.navigateBack();

    // Take screenshot after returning
    ReferralMailInput.takeScreenshot(`${SCREENSHOT_PREFIX.referralMailInput}_after_return_from_confirm.png`);
});
