import { COOKIE_KEY, USER_ID } from '../const/constant';
import pdfView from '../pages/common-ui/pdfView';
import electronicDelivery from '../pages/electronicDelivery';
import common from '../pages/search/common';

Feature('Settings_Entry - EasyElectronicDelivery');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user42 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Electronic Delivery Pre Contract Agreement', async ({ I }) => {
    await electronicDelivery.goToAgreementPage();
    I.saveScreenshot(
        `${electronicDelivery.prefix.agreement}.0_Check_UI_Electronic_Delivery_Pre_Contract_Agreement.png`,
    );
});

// 1.改訂のポイント・新旧対照表はこちら -> 以下のURLを別タブで表示する
Scenario(
    'Test Item 1: Click here for the revised points and old comparison table -> Display the following URL in a separate tab',
    async ({ I }) => {
        await I.seeAndClickElement(electronicDelivery.locators.linkNewVersionsHref);
        await pdfView.closePDFView();
    },
);

// 2.約款・規定集 -> 以下のURLを別タブで表示する
Scenario('Test Item 2: Terms and Conditions -> Display the following URL in a separate tab', async ({ I }) => {
    await I.seeAndClickElement(electronicDelivery.locators.contractsAndRegulationsPdfHref);
    await pdfView.closePDFView();
});

// 3.約款・規定集(金融商品仲介口座) -> 以下のURLを別タブで表示する
Scenario(
    'Test Item 3: Terms and Conditions (Financial Product Brokerage Account) -> Display the following URL in a separate tab',
    async ({ I }) => {
        await I.seeAndClickElement(electronicDelivery.locators.financialProductBrokerageAccountPdfHref);
        await pdfView.closePDFView();
    },
);

// 4.約款・規定集(法人口座) -> 以下のURLを別タブで表示する
Scenario(
    'Test Item 4: Terms and Conditions (Corporate Account) -> Display the following URL in a separate tab',
    async ({ I }) => {
        await I.seeAndClickElement(electronicDelivery.locators.corporateAccountPdfHref);
        await pdfView.closePDFView();
    },
);

// 9.内容を理解し同意する
Scenario('Test Item 9: Go to completion page', async ({ I }) => {
    // skip this case
    // isOpe=trueの場合は、opeエラーダイアログを表示し、後続処理を行わない

    // らくらく電子交付（契約締結前交付書面等）_完了画面に遷移する
    await I.seeAndClickElement(electronicDelivery.locators.completeButton);
    I.waitForText('らくらく電子交付\n(契約締結前交付書面)', 5, 'p');
    I.seeInCurrentUrl(electronicDelivery.urls.complete);
    I.saveScreenshot(`${electronicDelivery.prefix.agreement}.9_Go_to_completion_page.png`);
    await I.waitFor();
    await I.performBrowserBack();
});

// 10.特定投資家制度 -> 以下のURLを別タブで表示する
Scenario('Test Item 10: Specified Investor System -> Display the following URL in a separate tab', async ({ I }) => {
    await electronicDelivery.goToAgreementPage();
    // https://kabu.com/sp/rule/tokutei.html
    await common.clickCardItem(electronicDelivery.locators.specialInvestorProgramHref, 'https://kabu.com/sp/rule/tokutei.html', 'external');
});

// 11.外国証券取引口座約款 -> 以下のURLを別タブで表示する
Scenario(
    'Test Item 11: Foreign Securities Trading Account Terms -> Display the following URL in a separate tab',
    async ({ I }) => {
        await I.seeAndClickElement(electronicDelivery.locators.foreignSecuritiesTradingAccountTermsHref);
        await pdfView.closePDFView();
    },
);

// 12.外国株式マーケットデータ表示サービス利用約款 -> 以下のURLを別タブで表示する
Scenario(
    'Test Item 12: Terms and Conditions for Use of Foreign Stock Market Data Display Service -> Display the following URL in a separate tab',
    async ({ I }) => {
        await I.seeAndClickElement(electronicDelivery.locators.foreignStockMarketDataDisplayServiceTermsHref);
        await pdfView.closePDFView();
    },
);

// 13.米国株式取引ルール -> 以下のURLを別タブで表示する
Scenario('Test Item 13:  US Stock Trading Rules -> Display the following URL in a separate tab', async ({ I }) => {
    await I.seeAndClickElement(electronicDelivery.locators.stockTradingRulePdfHref);
    await pdfView.closePDFView();
});

// 5.確認書類リスト -> スワイプした方向にスクロールする
Scenario('Test Item 5: Confirmation Document List -> Scroll in the direction you swipe', async ({ I }) => {
    await electronicDelivery.goToAgreementPage();
    await I.scrollToElement('//div[@data-testid="preContractAgree_document_1_id"]');
    await I.saveScreenshot(`${electronicDelivery.prefix.agreement}.5_Confirmation_Document_List-Swipe_Up.png`);
    await I.waitFor();
    await I.scrollToElement('//div[@data-testid="preContractAgree_document_0_id"]');
    await I.saveScreenshot(`${electronicDelivery.prefix.agreement}.5_Confirmation_Document_List-Swipe_Down.png`);
    await I.waitFor();
});

// 8.確認書類内容 -> スワイプした方向にスクロールする
Scenario('Test Item 8: Confirmation Document Content -> Scroll in the direction you swipe', async ({ I }) => {
    await I.swipeElementDirection('up', electronicDelivery.locators.agreeDocumentContentZero, 0.5);
    await I.saveScreenshot(`${electronicDelivery.prefix.agreement}.8_Confirmation_Document_Content-Swipe_Up.png`);
    await I.waitFor();
    await I.swipeElementDirection('down', electronicDelivery.locators.agreeDocumentContentZero, 0.5);
    await I.saveScreenshot(`${electronicDelivery.prefix.agreement}.8_Confirmation_Document_Content-Swipe_Down.png`);
    await I.waitFor();
});
