import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginDeliveryOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginDelivery }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginDelivery.goToMarginDeliveryOrderConfirm();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Delivery Order Confirm', async ({ I, stockMarginDelivery }) => {
    await stockMarginDelivery.compareUrl(stockMarginDelivery.urls.marginDeliveryOrderConfirm);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderConfirm(
        'Display_Domestic_Stock_Margin_Trading_Delivery_Order_Confirm',
    );
});

Scenario('Test item No.12+13 Fill password and check for omitted password', async ({ I, stockMarginDelivery }) => {
    const passwordSelector = '//*[@data-testid="marginDeliveryConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginDeliveryConfirm_passwordOmissionCheck_id"]';
    const confirmButton = '//*[@data-testid="marginDeliveryConfirm_orderConfirmBtn_id"]';

    I.fillField(passwordSelector, stockMarginDelivery.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(confirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderConfirm(
        'Test_item_No.12_13_See_order_confirmation_button_state',
    );
});

Scenario('Test item No.14 Order Confirmation', async ({ I, stockMarginDelivery }) => {
    const passwordSelector = '//*[@data-testid="marginDeliveryConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginDeliveryConfirm_passwordOmissionCheck_id"]';
    const confirmButton = '//*[@data-testid="marginDeliveryConfirm_orderConfirmBtn_id"]';
    I.fillField(passwordSelector, stockMarginDelivery.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(confirmButton);
    await I.waitFor('mediumWait');
    await stockMarginDelivery.compareUrl(stockMarginDelivery.urls.marginDeliveryOrderCompleted);
    stockMarginDelivery.takeScreenshot.marginDeliveryOrderConfirm(
        'Test_item_No.14_Transition_to_delivery_completion_screen',
    );
});

Scenario('Test item No.15 Caution statement', async ({ I, stockMarginDelivery }) => {
    const cautionSelector = '//*[@data-testid="marginDeliveryConfirm_cautionMessage_id"]/div[1]';
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    await stockMarginDelivery.takeScreenshot.marginDeliveryOrderConfirm(
        'Test_item_No.15_Opening_the_caution_accordion',
    );
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockMarginDelivery.takeScreenshot.marginDeliveryOrderConfirm(
        'Test_item_No.15_Closing_the_caution_accordion',
    );
});
