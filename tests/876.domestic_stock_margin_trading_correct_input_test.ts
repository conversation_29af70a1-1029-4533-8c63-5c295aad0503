import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginCorrectInput');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginCorrect }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCorrect.goToMarginCorrectInput();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Correct input', async ({ stockMarginCorrect }) => {
    await stockMarginCorrect.compareUrl('/mobile/trade/margin/correction');
    stockMarginCorrect.takeScreenshot.marginCorrectInput('Display_Domestic_Stock_Margin_Trading_Correct_input');
});

Scenario('Test item No.7 Bidding premium fee', async ({ I, stockMarginCorrect }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user83 });
    await stockMarginCorrect.goToMarginCorrectInput();
    const biddingPremiumFeeSelector = '//div[p[contains(text(), "入札プレミアム料")]]/form/div';
    const minusButton = `${biddingPremiumFeeSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusButton = `${biddingPremiumFeeSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.clickFixed(plusButton);
    await I.waitFor('shortWait');
    await I.clickFixed(minusButton);
    stockMarginCorrect.takeScreenshot.marginCorrectInput('Test_item_No.7_See_Numeric_stepper');
});

Scenario('Test item No.2 Correction - Reduction quantity', async ({ I, stockMarginCorrect }) => {
    const reductionQuantitySelector = '//div[p[contains(text(), "削減数量")]]/form/div';
    const minusButton = `${reductionQuantitySelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusButton = `${reductionQuantitySelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.clickFixed(plusButton);
    await I.waitFor('shortWait');
    await I.clickFixed(minusButton);
    stockMarginCorrect.takeScreenshot.marginCorrectInput('Test_item_No.2_Reduction_quantity_See_Numeric_stepper');
});

Scenario('Test item No.2 Correction - Select execution conditions', async ({ I, stockMarginCorrect }) => {
    const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
    const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
    const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
    await I.scrollToElement(executionConditionsSelector);
    await I.clickFixed(executionConditionsSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(limitOrderOptionSelector);
    await I.waitFor('shortWait');
    stockMarginCorrect.takeScreenshot.marginCorrectInput(
        'Test_item_No.2_Select_execution_conditions_Perform_the_payment_according_to_the_drop_down_menu',
    );
});

Scenario('Test item No.2 Correction details - Correction/trigger price', async ({ I, stockMarginCorrect }) => {
    const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
    const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
    const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
    const triggerPriceWrapperSelector = '//div[p[contains(text(), "執行条件の訂正内容")]]//form/div';
    const minusButton = `${triggerPriceWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusButton = `${triggerPriceWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.scrollToElement(executionConditionsSelector);
    await I.clickFixed(executionConditionsSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(limitOrderOptionSelector);
    await I.waitFor();
    await I.clickFixed(plusButton);
    await I.clickFixed(minusButton);
    stockMarginCorrect.takeScreenshot.marginCorrectInput('Test_item_No.2_Correct_trigger_price_See_Numeric_stepper');
});

Scenario(
    'Test item No.2 Correction details - Correction/Trigger price-Book entry',
    async ({ I, stockMarginCorrect }) => {
        const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
        const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
        const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
        const triggerPriceWrapperSelector = '//div[p[contains(text(), "執行条件の訂正内容")]]';
        const triggerPriceBookEntrySelector = `${triggerPriceWrapperSelector}//div[p[contains(text(), "板入力")]]`;
        await I.scrollToElement(executionConditionsSelector);
        await I.clickFixed(executionConditionsSelector);
        await I.waitFor('shortWait');
        await I.clickFixed(limitOrderOptionSelector);
        await I.waitFor();
        await I.clickFixed(triggerPriceBookEntrySelector);
        await I.waitFor();
        I.see('板入力', 'body');
        stockMarginCorrect.takeScreenshot.marginCorrectInput(
            'Test_item_No.2_Correction_trigger_price_book_entry_Display_book_entry_modal',
        );
    },
);

Scenario(
    'Test item No.2 Correction details - Correction/Trigger price-Chart input',
    async ({ I, stockMarginCorrect }) => {
        const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
        const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
        const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
        const triggerPriceWrapperSelector = '//div[p[contains(text(), "執行条件の訂正内容")]]';
        const triggerPriceChartInputSelector = `${triggerPriceWrapperSelector}//div[p[contains(text(), "チャート入力")]]`;
        await I.scrollToElement(executionConditionsSelector);
        await I.clickFixed(executionConditionsSelector);
        await I.waitFor('shortWait');
        await I.clickFixed(limitOrderOptionSelector);
        await I.waitFor();
        await I.clickFixed(triggerPriceChartInputSelector);
        await I.waitFor();
        I.seeElement('.chart-input');
        stockMarginCorrect.takeScreenshot.marginCorrectInput(
            'Test_item_No.2_Correction_trigger_price_chart_input_Show_chart_input_modal',
        );
    },
);

Scenario('Test item No.2 Correction - Order price after hit', async ({ I, stockMarginCorrect }) => {
    const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
    const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
    const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
    const triggerPriceWrapperSelector = '//div[p[contains(text(), "執行条件の訂正内容")]]//form/div';
    const minusButton = `${triggerPriceWrapperSelector}//button[@data-testid="groupInputNumber_minus_id"]`;
    const plusButton = `${triggerPriceWrapperSelector}//button[@data-testid="groupInputNumber_plus_id"]`;
    await I.scrollToElement(executionConditionsSelector);
    await I.clickFixed(executionConditionsSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(limitOrderOptionSelector);
    await I.waitFor();
    await I.clickFixed(plusButton);
    await I.clickFixed(minusButton);
    stockMarginCorrect.takeScreenshot.marginCorrectInput(
        'Test_item_No.2_Correction_Order_price_after_hit_See_Numeric_stepper',
    );
});

Scenario('Test item No.2 Correction - Order price after hit - Book entry', async ({ I, stockMarginCorrect }) => {
    const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
    const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
    const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
    const triggerPriceWrapperSelector = '//div[p[contains(text(), "執行条件の訂正内容")]]';
    const triggerPriceBookEntrySelector = `${triggerPriceWrapperSelector}//div[p[contains(text(), "板入力")]]`;
    await I.scrollToElement(executionConditionsSelector);
    await I.clickFixed(executionConditionsSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(limitOrderOptionSelector);
    await I.waitFor();
    await I.clickFixed(triggerPriceBookEntrySelector);
    I.see('板入力', 'body');
    stockMarginCorrect.takeScreenshot.marginCorrectInput(
        'Test_item_No.2_Correction_Order_price_after_hit_book_entry_Display_book_entry_modal',
    );
});

Scenario('Test item No.2 Correction - Hit order price - Chart input', async ({ I, stockMarginCorrect }) => {
    const executionConditionsWrapperSelector = '//div[button[p[contains(text(), "変更しない")]]]';
    const executionConditionsSelector = `${executionConditionsWrapperSelector}//button[.//img[contains(@alt, "drop-arrow")]]`;
    const limitOrderOptionSelector = `${executionConditionsWrapperSelector}//p[contains(text(), "指値")]`;
    const triggerPriceWrapperSelector = '//div[p[contains(text(), "執行条件の訂正内容")]]';
    const triggerPriceChartInputSelector = `${triggerPriceWrapperSelector}//div[p[contains(text(), "チャート入力")]]`;
    await I.scrollToElement(executionConditionsWrapperSelector);
    await I.clickFixed(executionConditionsSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(limitOrderOptionSelector);
    await I.waitFor();
    await I.clickFixed(triggerPriceChartInputSelector);
    I.seeElement('.chart-input');
    await I.waitFor();
    stockMarginCorrect.takeScreenshot.marginCorrectInput(
        'Test_item_No.2_Correction_Hit_order_price_chart_input_Show_chart_input_modal',
    );
});

Scenario('Test item No.3 Make corrections', async ({ I, stockMarginCorrect }) => {
    const correctionButtonSelector = '//*[@data-testid="marginCorrection_doCorrect_id"]';
    await I.scrollToElement(correctionButtonSelector);
    await I.clickFixed(correctionButtonSelector);
    await I.waitFor('mediumWait');
    await stockMarginCorrect.compareUrl('/mobile/trade/margin/correction/confirm');
    stockMarginCorrect.takeScreenshot.marginCorrectInput(
        'Test_item_No.3_Tap_corrections_to_transition_to_the_correction_confirmation_screen',
    );
});

Scenario('Test item No.4 Order details before correction - Relay order number', async ({ I, stockMarginCorrect }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user85 });
    await stockMarginCorrect.goToMarginCorrectInput();
    const relayOrderNumberSelector = '//*[@data-testid="correction_relayTargetOrderID_id"]//a';
    await I.scrollToElement(relayOrderNumberSelector);
    await I.clickFixed(relayOrderNumberSelector);
    await I.waitFor('mediumWait');
    await stockMarginCorrect.compareUrl('/mobile/order-inquiry/margin/detail');
    stockMarginCorrect.takeScreenshot.marginCorrectInput(
        'Test_item_No.4_Tap_replay_order_number_to_transition_to_details',
    );
});
