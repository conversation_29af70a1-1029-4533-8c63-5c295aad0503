import { COOKIE_KEY, USER_ID } from '../const/constant';
import commonUi from '../pages/common-ui';
import tradeFund from '../pages/trade-fund';

Feature('InvestmentProducts - TradeFundBuyConfirm');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });

    await tradeFund.buyInput.goToPage({ fundCode: '02311862' });
    await I.waitFor(); // wait for page loaded
    await tradeFund.buyInput.goToConfirmScreen();
    await I.waitFor(); // wait for page loaded
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await <PERSON><PERSON>switchToNative();
});

// レイアウト
<PERSON>enario('test trade fund buy confirm [レイアウト]', async ({ I }) => {
    I.waitInUrl('/mobile/trade/fund/buy/confirm', 5);
    await commonUi.header.verifyTitle('投資信託買注文');
    I.saveScreenshot('1049.trade_fund_buy_confirm_page.png');
});

// 10.予定受渡金額ヘルプ
Scenario('test trade fund buy confirm [10.予定受渡金額ヘルプ]', async ({ I }) => {
    await tradeFund.buyConfirm.openPlannedDeliveryAmountHelpTooltip();
    I.saveScreenshot('1049.trade_fund_buy_confirm_10_planned_delivery_amount_help.png');
});

// 21.パスワード
Scenario('test trade fund buy confirm [21.パスワード]', async ({ I }) => {
    await tradeFund.buyConfirm.fillPassword('111111');
    I.saveScreenshot('1049.trade_fund_buy_confirm_21_password.png');
});

// 22.パスワード省略チェック
Scenario('test trade fund buy confirm [22.パスワード省略チェック]', async ({ I }) => {
    await tradeFund.buyConfirm.togglePasswordOmissionCheck();
    I.saveScreenshot('1049.trade_fund_buy_confirm_22_password_omission_check.png');
});

// 24.パスワード入力チェック
Scenario('test trade fund buy confirm [24.パスワード入力チェック]', async ({ I }) => {
    const storageObject = await tradeFund.buyConfirm.sessionData();
    const modifyStorageObject = {
        ...storageObject,
        isOmmitPassword: true,
    };
    await tradeFund.buyConfirm.setSessionData(modifyStorageObject);
    await I.refreshPage();

    await tradeFund.buyConfirm.togglePasswordInputCheck();
    I.saveScreenshot('1049.trade_fund_buy_confirm_24_password_input_check.png');
});

// 25.申し込む
Scenario('test trade fund buy confirm [25.申し込む]', async ({ I }) => {
    await tradeFund.buyConfirm.apply();
    I.saveScreenshot('1049.trade_fund_buy_confirm_25_apply.png');
});

// 26.注文後の株式注文にご注意
Scenario('test trade fund buy confirm [26.注文後の株式注文にご注意]', async ({ I }) => {
    await tradeFund.buyConfirm.verifyOrderingStocksCautionAfterPlacingAnOrder();
    I.saveScreenshot('1049.trade_fund_buy_confirm_26_ordering_stocks_caution_after_placing_an_order.png');
});

// 27.ご注意文言
Scenario('test trade fund buy confirm [27.ご注意文言]', async ({ I }) => {
    await tradeFund.buyConfirm.verifyCautionMessage();
    await I.swipeDirection('up', 0.6, 600);
    I.saveScreenshot('1049.trade_fund_buy_confirm_27_caution_message.png');
});
