import { COOKIE_KEY, PAGE_URL, USER_ID } from "../const/constant";

Feature('AssetStatus - AssetInvestmentTabPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr/vn/Customer/InvestmentResult/InvestmentProfitLoss.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user4 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await <PERSON>.switchToNative();
});

Scenario('test asset investment page', async ({ I }) => {
    // Navigate to asset balance page
    await I.amOnPage(PAGE_URL.mypagePortfolio);
    await I.waitFor('mediumWait');
    const processingButton = '//*[@data-testid="portfolio_investmentResult_id"]';
    await I.clickFixed(processingButton);
    await I.waitFor('longWait');
    // Navigate to investment profit loss page
    const investmentProfitLossButton = '//*[@data-testid="investmentResult_investmentProfitLoss_id"]';
    await I.waitForElement(investmentProfitLossButton, 5);

    await I.waitFor();
    await I.clickFixed(investmentProfitLossButton);
    I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPage.png');
});

// Item 3
Scenario('test Item 3 Profit Loss View', async ({ I }) => {
    I.waitForElement({ xpath: '//*[@data-testid="investmentProfitLoss_profitLossView_id"]' });
    await I.clickFixed({ xpath: '//*[@data-testid="investmentProfitLoss_profitLossView_id"]' });
    I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem3.png');
    await I.waitFor();
    I.waitForElement({ xpath: '//*[@data-testid="portfolioTop_close_id"]' });
    await I.clickFixed({ xpath: '//*[@data-testid="portfolioTop_close_id"]' });
    await I.waitFor();
});

// Item 4
Scenario('test Item 4 Term View', async ({ I }) => {
    I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem4.png');
    const expectedTerms = ['今週', '今月', '今年', '全期間', '期間指定'];
    const termSelector = '//*[@data-testid="investmentProfitLoss_term_id"]/label';

    // Wait for elements to be present
    I.waitForElement(termSelector);

    // Grab all term elements
    const termDivs = await I.grabTextFromAll(termSelector);

    // Verify number of elements
    I.assertEqual(termDivs.length, expectedTerms.length, 'Number of term elements should match expected');

    // Verify text content and click each element
    for (let i = 0; i < termDivs.length; i++) {
        const termText = termDivs[i];
        I.assertEqual(termText, expectedTerms[i], `Term text should be ${expectedTerms[i]}`);
        await I.clickFixed(`${termSelector}[${i + 1}]/p[contains(text(),"${expectedTerms[i]}")]`);
        await I.waitFor();
        if (i === expectedTerms.length - 1) {
            const periodText = await I.grabTextFrom(
                '//*[@id="bottom-sheet-container"]/div/div[2]/div/div/div/div[1]/p',
            );
            I.assertEqual(periodText, '日付指定', 'Period text should be 日付指定');
            await I.clickFixed('//*[@data-testid="datePickerSheet_close_id"]');
            await I.waitFor();
        }
    }
});

// Item 6, Item 7, Item 16
Scenario(
    'test Item 6, Item 7, Item 16 Sort Button View and Investment Product Card View',
    async ({ I, investmentProfitLossPage }) => {
        const dataSelector = '//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]';
        I.waitForElement(dataSelector);
        const dataTexts = await I.grabTextFromAll({ xpath: `${dataSelector}//p[1]` });
        const baseDataList = dataTexts
        .filter((_, index) => index % 2 !== 0)
        .map((item) => item.replace(/[+,円]/g, ''));
        // Item 6
        await investmentProfitLossPage.sortButtonView({ baseDataList, dataTexts, dataSelector });
        I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem6716.png');
        await I.waitFor();
        //  Item 7, Item 16
        await investmentProfitLossPage.productCardView({ dataTexts });
        I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem6716.png');
    },
);

// Item 11, Item 12
Scenario('test Item 11, Item 12 Previous and Next Button View', async ({ I, investmentProfitLossPage }) => {
    await investmentProfitLossPage.previousNextButtonView({});
    await I.clickFixed('//*[@data-testid="investmentProfitLoss_back_id"]');
    I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem1112.png');
});

// Item 17
Scenario('test Item 17 Symbol Card View', async ({ I }) => {
    I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem17.png');
    await I.clickFixed('//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]/div[1]');
    await I.waitFor();
    await I.clickFixed('//*[@data-testid="investmentProfitLoss_symbolCard_id_0"]');
    await I.waitFor();
    I.waitForElement('//button[@aria-label="cancel-btn"]');
    await I.clickFixed('//button[@aria-label="cancel-btn"]');
    const nextSymbolCard = async () => {
        await I.clickFixed('#arrow-left');
        const titleText = await I.grabTextFrom('//*[@data-testid="portfolio_productName_id"]');
        if (titleText === '国内株式現物') {
            await I.clickFixed('#arrow-left');
            const titleText1 = await I.grabTextFrom('//*[@data-testid="portfolio_productName_id"]');
            if (titleText1 === 'オプション') {
                await I.clickFixed('//*[@data-testid="investmentProfitLoss_back_id"]');
                return;
            }
        }
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_symbolCard_id_1"]');
        await I.waitFor('mediumWait');
        I.waitForElement('//button[@aria-label="cancel-btn"]');
        I.tapLocationOfElement('//button[@aria-label="cancel-btn"]');
        await nextSymbolCard();
    };
    await nextSymbolCard();
});

// Item 22
Scenario('test Item 22 Symbol Info View', async ({ I }) => {
    await I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem22.png');
    const dataSelector = '//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]/div';
    I.waitForElement(dataSelector);
    const dataTexts = await I.grabTextFromAll(`${dataSelector}//p[1]`);
    const dataTextsFilter = dataTexts.filter((_, index) => index % 2 === 0);
    // const symbolTexts = ['投資信託', '国内株式現物', '国内株式信用'];
    const symbolTexts = ['国内株式現物', '国内株式信用'];
    // const urlTexts = ['mobile/info/fund/detail', 'mobile/info/stock/basic', 'mobile/info/stock/basic'];
    const urlTexts = ['mobile/info/stock/basic', 'mobile/info/stock/basic'];
    for (let i = 0; i < symbolTexts.length; i++) {
        const index = dataTextsFilter.findIndex((item) => item === symbolTexts[i]);
        await I.clickFixed(`${dataSelector}[${index + 1}]`);
        await I.waitFor();
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_symbolCard_id_0"]');
        await I.waitFor();
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_symbolInforView_id"]');
        await I.waitFor();
        const url = await I.grabCurrentUrl();
        I.assertContain(url, urlTexts[i], 'URL should match');
        await I.backToPreviousScreen();
        await I.waitFor('mediumWait');
        I.waitForElement('//*[@data-testid="investmentProfitLoss_back_id"]');
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_back_id"]');
    }
});

// Item 23
Scenario('test Item 23 Balance Inquiry View', async ({ I }) => {
    await I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem23.png');
    const dataSelector = '//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]/div';
    I.waitForElement(dataSelector);
    const dataTexts = await I.grabTextFromAll(`${dataSelector}//p[1]`);
    const dataTextsFilter = dataTexts.filter((_, index) => index % 2 === 0);
    const balanceTexts = ['国内株式信用', '国内株式現物'];
    const urlTexts = ['mobile/position-inquiry/margin', 'mobile/position-inquiry/stock'];
    for (let i = 0; i < balanceTexts.length; i++) {
        const index = dataTextsFilter.findIndex((item) => item === balanceTexts[i]);
        await I.clickFixed(`${dataSelector}[${index + 1}]`);
        await I.waitFor();
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_symbolCard_id_0"]');
        await I.waitFor();
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_balanceInquiry_id"]');
        await I.waitFor();
        const url = await I.grabCurrentUrl();
        I.assertContain(url, urlTexts[i], 'URL should match');
        await I.backToPreviousScreen();
        await I.waitFor('mediumWait');
        I.waitForElement('//*[@data-testid="investmentProfitLoss_back_id"]');
        await I.clickFixed('//*[@data-testid="investmentProfitLoss_back_id"]');
    }
});

// Item 24
Scenario('test Item 24 Detail Explanation View', async ({ I }) => {
    await I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem24.png');
    await I.clickFixed('//*[@data-testid="investmentProfitLoss_profitLossView_id"]');
    I.seeAttributesOnElements('//*[@data-testid="investmentProfitLoss_detailedExplanationOfProfitLossScreen_id_0"]/a', {
        href: 'https://kabu.com/mobile/app/guide/index.html#performance',
    });
    I.performSwipe({ x: 300, y: 700 }, { x: 50, y: 700 });
    await I.waitFor();
    I.seeAttributesOnElements('//*[@data-testid="investmentProfitLoss_detailedExplanationOfProfitLossScreen_id_0"]/a', {
        href: 'https://kabu.com/mobile/app/guide/index.html#performance',
    });
    await I.waitFor();
    await I.tapLocationOfElement('//button[@aria-label="cancel-btn"]');
});

// Item 10
Scenario('test Item 10 Scroll to Top', async ({ I }) => {
    await I.saveScreenshot('74.asset_balance_page_investment_tab_investmentProfitLossPageItem10.png');
    await I.swipeUpFixed('#mypage-inves');
    I.waitForElement('//button[@aria-label="scroll-to-top-btn"]');
    await I.clickFixed('//button[@aria-label="scroll-to-top-btn"]');
});
