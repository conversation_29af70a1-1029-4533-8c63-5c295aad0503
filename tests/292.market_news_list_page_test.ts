import { COOKIE_KEY, USER_ID } from '../const/constant';
import newsList from '../pages/newsList';

Feature('Market - NewsListPage');

Before(async ({ I }) => {
  console.debug('before');
  await I.activateApp();
  await <PERSON><PERSON>waitFor('mediumWait');
  await I.switchToWeb();
  await I.waitFor('mediumWait');
  I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user12 });
  await newsList.goToIndicator();
  await newsList.goToNewsTab();

});

After(async ({ I }) => {
  console.debug('after');
  await I.closeBrowser();
  await I.switchToNative();
});

Scenario('Test item No.0 Check news interface display', async ({ I }) => {
  I.waitForElement(newsList.locator.newsListContainer);
  I.waitForElement(newsList.locator.searchInput);
  I.waitForElement(newsList.locator.newsFilterBtn);
  I.waitForElement(newsList.locator.newsFirstItem);
  I.saveScreenshot('292.market_news_list_page_news_list_interface.png');
});

Scenario('Test item No.1 Check switching from news tab to indicator tab', async ({ I }) => {
  // Switch to indicatorListTab/rankingTab/newsTab
  await newsList.clickMarketTabGroup();
  I.saveScreenshot('292.market_news_list_page_Test_item_No.1_Check_switching_from_news_tab_to_indicator_tab.png');
});


Scenario('Test item No.2 Check scrolling news list', async ({ I }) => {
  const newsContainer = newsList.locator.newsListContainer;

  // Screenshot before scroll
  I.saveScreenshot('292.market_news_list_page_Test_item_No.2_Check_scrolling_news_list_before_scroll.png');

  // Scroll news list
  I.waitForElement(newsContainer);
  await I.swipeUpFixed(newsContainer);
  await I.waitFor('shortWait');
  await I.swipeUpFixed(newsContainer);
  await I.waitFor('shortWait');

  // Screenshot after scroll
  I.saveScreenshot('292.market_news_list_page_Test_item_No.2_Check_scrolling_news_list_after_scroll.png');
});

Scenario('Test item No.3 Check searching news by keyword', async ({ I }) => {
  const keyword = 'abc';
  await newsList.searchNewsByKeyword(keyword);
  if (await I.grabTextFrom(newsList.locator.newsFirstItem) === keyword) {
    I.saveScreenshot('292.market_news_list_page_Test_item_No.3_Check_searching_news_by_keyword_find_news.png');
  } else {
    //TODO: Data mock - can not check => failed
    // I.see('該当するニュースはございません。');
    I.saveScreenshot('292.market_news_list_page_Test_item_No.3_Check_searching_news_by_keyword_not_find_news.png');
  }
});

Scenario('Test item No.3.5 Check cancel search function', async ({ I }) => {
  const keyword = 'test';
  await newsList.searchNewsByKeyword(keyword);
  I.saveScreenshot('292.market_news_list_page_Test_item_No.3.5_Check_cancel_search_function_before_cancel_search.png');
  await newsList.cancelSearch();
  I.dontSeeElement(newsList.locator.cancelSearchButton);
  I.saveScreenshot('292.market_news_list_page_Test_item_No.3.5_Check_cancel_search_function_after_cancel_search.png');
});

Scenario('Test item No.4 Check news custom sort function', async ({ I }) => {
  // Make sure news list is loaded
  I.waitForElement(newsList.locator.newsListContainer);
  // 1. Take screenshot before custom sort
  I.saveScreenshot('292.market_news_list_page_Test_item_No.4_before_custom_sort.png');
  // 2. Open custom sort modal
  await newsList.openCustomSort();
  I.saveScreenshot('292.market_news_list_page_Test_item_No.4_custom_sort_modal.png');
  // 3. Turn off all checkboxes (to ensure starting from clean state)
  await newsList.selectNewsCategory('kabuレター®');
  await newsList.selectNewsCategory('PRESSニュース');
  I.saveScreenshot('292.market_news_list_page_Test_item_No.4_select_press_news.png');
  // 5. Confirm filter
  await newsList.confirmCustomSort();
});

Scenario('Test item No.6 Check news detail and return', async ({ I }) => {
  const newsTitle = await newsList.selectNewsItem(0);
  console.log('newsTitle', newsTitle);
  I.saveScreenshot('292.market_news_list_page_Test_item_No.6_Check_news_detail_and_return_before_click_news_detail.png');
  await newsList.verifyNewsDetailPage(newsTitle);
  I.saveScreenshot('292.market_news_list_page_Test_item_No.6_Check_news_detail.png');
  await newsList.returnToNewsList();
  I.saveScreenshot('292.market_news_list_page_Test_item_No.6_Check_news_detail_and_return_after_return_to_news_list.png');
});

Scenario('Test item No.11 Check scroll to top function', async ({ I }) => {
  await newsList.scrollNewsContent();
  I.saveScreenshot('292.market_news_list_page_Test_item_No.11_Check_scroll_to_top_function_scrolled_down.png');
  await newsList.scrollToTop();
  I.saveScreenshot('292.market_news_list_page_Test_item_No.11_Check_scroll_to_top_function_scrolled_to_top.png');
});

