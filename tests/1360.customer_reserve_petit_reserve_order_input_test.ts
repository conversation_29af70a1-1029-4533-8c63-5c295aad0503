import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReservePetitReserveOrderInput');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user48 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI of Savings application input Page', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    I.saveScreenshot('1360.Test_item_No.0_UI_of_Savings_application_input_Page.png');
});
Scenario('Test Item 1a: Check Tap on the stock name to go to domestic stock investment information', async ({ I }) => {
    const symbolName = '//*[@data-testid="changeReserveOrderInput_symbolName_id"]';
    await I.waitForElement(symbolName, 1);
    I.clickFixed(symbolName);
    await I.waitFor();
    I.saveScreenshot('1360.Test_item_No.1a_Tap_on_the_stock_name_to_go_to_domestic_stock_investment_information.png');
    // back to savings application input page
    I.clickFixed('//*[@data-testid="common_back_id"]');
    await I.waitFor('longWait');
    I.saveScreenshot('1360.Test_item_No.1a_Back_to_savings_application_input_page.png');
});
Scenario('Test Item 2: Check Tap to select the item Account Category', async ({ I }) => {
    const restrictionButton = '//*[@data-testid="reserveOrderInput_restrictionButton_id"]';
    await I.waitForElement(restrictionButton, 1);
    I.clickFixed(restrictionButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1360.Test_item_No.2_Tap_to_select_the_item_Account_Category.png');
    const closeButton = '//*[@data-testid="common_rightSlide_close_id"]';
    await I.waitForElement(closeButton, 1);
    I.clickFixed(closeButton);
    I.saveScreenshot('1360.Test_item_No.2_Close_by_common_rightSlide_close_id.png');
});
Scenario('Test Item 10: Check Account Category Select the item you tap', async ({ I }) => {
    // get all labels in account type radio group
    const accountTypeLabels = '//*[@data-testid="reserveOrderInput_accountType_id"]//label';
    // scroll to label
    I.waitForElement(accountTypeLabels, 1);
    I.scrollToElement(accountTypeLabels);
    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(accountTypeLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${accountTypeLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1360.Test_item_No.10_Account_Type_Label_${i}.png`);
    }
});
Scenario('Test Item 10a: Check Account Classification Help Show Tooltip', async ({ I }) => {
    const accountClassificationHelp = '//*[contains(text(), "口座区分")]';
    await I.waitForElement(accountClassificationHelp, 1);
    I.clickFixed(accountClassificationHelp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1360.Test_item_No.10a_Account_Classification_Help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 10c: Check NISA usage limit help Show tooltip', async ({ I }) => {
    const nisaUsageLimitHelp = '//*[contains(text(), "NISA利用枠")]';
    await I.waitForElement(nisaUsageLimitHelp, 1);
    I.clickFixed(nisaUsageLimitHelp);
    await I.waitFor('shortWait');
    I.saveScreenshot('1360.Test_item_No.10c_NISA_usage_limit_help_Show_Tooltip.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');
});
Scenario('Test Item 17: Check Payment method Select the item you tapped', async ({ I }) => {
    // get all labels in payment type radio group
    const paymentTypeLabels = '//*[@data-testid="reserveOrderInput_paymentType_id"]//div[@role="radiogroup"]//label';

    // scroll to label
    I.waitForElement(paymentTypeLabels, 1);
    I.scrollToElement(paymentTypeLabels);

    // get the number of labels
    const count = await I.grabNumberOfVisibleElements(paymentTypeLabels);

    // click each label and take screenshot
    for (let i = 1; i <= count; i++) {
        const label = `(${paymentTypeLabels})[${i}]`;
        // click on label
        I.clickFixed(label);
        await I.waitFor('shortWait');
        // take screenshot
        I.saveScreenshot(`1360.Test_item_No.17_Payment_Type_Label_${i}.png`);
    }

});
Scenario('Test Item 19: Check specified amount "Perform the process described in the Common UI - Numeric Stepper with the following parameters.', async ({ I }) => {
    const plusButton = '//p[contains(text(), "毎月の指定金額")]/parent::div//button[@data-testid="groupInputNumber_plus_id"]';
    await I.waitForElement(plusButton, 5);
    I.scrollAndClick(plusButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1360.Test_item_No.19_Check_specified_amount.png');

});
Scenario('Test Item 20: Check selectable DatePicker: Select the date you tap', async ({ I }) => {
    const datePicker = '//*[@data-testid="reserveOrderInputBox_monthlySpecifiedDate_id"]';
    // Scroll to datepicker
    I.waitForElement(datePicker, 1);
    I.scrollAndClick(datePicker);
    // Click on a random available date
    const availableDates = '//*[@data-testid="reserveOrderInputBox_monthlySpecifiedDate_id"]//div[not(@disabled)]';
    I.clickFixed(availableDates);
    await I.waitFor('shortWait');
    I.saveScreenshot('1360.Test_item_No.20_Check_selectable_DatePicker.png');
});
Scenario('Test Item 21: Check turn on/off the switch Increase designation', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';

    
    await I.waitForElement(increaseDesignationSwitch, 2);
    I.scrollAndClick(increaseDesignationSwitch);
    await I.waitForElement(increaseDesignationSwitch, 2);
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.saveScreenshot('1360.Test_item_No.21_Increase_designation_switch_OFF.png');
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor('shortWait');
        I.saveScreenshot('1360.Test_item_No.21_Increase_designation_switch_ON.png');
    } else {
        I.saveScreenshot('1360.Test_item_No.21_Increase_designation_switch_ON.png');
    }

});
Scenario('Test Item 23: Check Increase amount', async ({ I }) => {
    // Click plus button 2 times
    const plusButton = '//p[contains(text(), "増額金額")]/following-sibling::*//button[@data-testid="groupInputNumber_plus_id"]';
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    I.clickFixed(plusButton);
    await I.waitFor('shortWait');

    I.saveScreenshot('1360.Test_item_No.23_increase_amount.png');

});
Scenario('Test Item 24: Check Increase Month 1 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }

    const firstDropdown = '//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")][1]';
    I.clickFixed(firstDropdown);
    await I.waitFor();


    I.saveScreenshot('1360.Test_item_No.24_increase_month1_dropdown_opened.png');

    const option = '//div[@data-testid="common_MenuList_id"]//button[@value="3"]';
    I.clickFixed(option);
    await I.waitFor();

    I.saveScreenshot('1360.Test_item_No.24_increase_month1_selected.png');
});
Scenario('Test Item 25: Check Increase Month 2 Dropdown', async ({ I }) => {
    const increaseDesignationSwitch = '//*[@data-testid="reserveOrderInput_specifyIncrease_id"]';
    const isIncreaseAmountVisible = await I.grabNumberOfVisibleElements('//p[contains(text(), "増額金額")]');
    if (isIncreaseAmountVisible === 0) {
        I.clickFixed(increaseDesignationSwitch);
        await I.waitFor();
    }

    const firstDropdown = '(//div[p[contains(text(), "増額月")]]//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(firstDropdown);
    await I.waitFor();

    I.saveScreenshot('1360.Test_item_No.25_increase_month2_dropdown_opened.png');

    const option = '(//div[@data-testid="common_MenuList_id"]//button[@value="3"])[2]';
    I.clickFixed(option);
    await I.waitFor();

    I.saveScreenshot('1360.Test_item_No.25_increase_month2_selected.png');
});
Scenario('Test Item 26: Check Notification Service Contact Settings - Go to the following URL', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    await I.waitFor('shortWait');
    const withdrawalResultNotification = '//*[@data-testid="reserveOrderInput_withdrawalResultNotification_id"]';
    await I.waitForElement(withdrawalResultNotification, 1);
    I.scrollToElement(withdrawalResultNotification);
    const notificationServiceContactSettings = '//span[contains(text(), "通知サービス連絡先設定")]';
    await common.clickCardItem(notificationServiceContactSettings, '/ap/iPhone/personal/contact/List', 'kcMemberSite');
});
Scenario('Test Item 27: Check Insider confirmation panel Toggle selection', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    await I.waitFor('shortWait');
    const button = '//div[@data-testid="reserveOrderInput_notInsiderTradeConfirm_id"]//button';
    await I.waitForElement(button, 1);
    I.scrollAndClick(button);
    I.saveScreenshot('1360.Test_item_No.27_Check_button_of_div_with_data-testid_reserveOrderInput_notInsiderTradeConfirm_id_ON.png');
    I.clickFixed(button);
    I.saveScreenshot('1360.Test_item_No.27_Check_button_of_div_with_data-testid_reserveOrderInput_notInsiderTradeConfirm_id_OFF.png');

});
Scenario('Test Item 29: Check Tax-free investment consent check', async ({ I }) => {
    const accountTypeLabels = '//*[@data-testid="reserveOrderInput_accountType_id"]';
    await I.waitForElement(accountTypeLabels, 1);
    I.scrollToElement(accountTypeLabels);
    const nisaLabel = `${accountTypeLabels}//label[3]`;

    I.clickFixed(nisaLabel);
    await I.waitFor();


    const taxFreeInvestmentArea = '//*[@data-testid="reserveOrderInput_taxFreeInvestmentAgreeArea_id"]';
    await I.waitForElement(taxFreeInvestmentArea, 1);
    I.scrollToElement(taxFreeInvestmentArea);
    const taxFreeInvestmentButton = `${taxFreeInvestmentArea}//button`;

    if (await I.grabNumberOfVisibleElements(taxFreeInvestmentArea)) {
        I.clickFixed(taxFreeInvestmentButton);
        await I.waitFor('shortWait');
        I.saveScreenshot('1360.Test_item_No.29_Tax-free_investment_consent_OFF.png');

        I.clickFixed(taxFreeInvestmentButton);
        await I.waitFor('shortWait');
        I.saveScreenshot('1360.Test_item_No.29_Tax-free_investment_consent_ON.png');
    }
});
Scenario('Test Item 30: Check submit and Go to Savings - Petit Stocks - Savings Application Confirmation', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user49 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveOrderInputPage();
    await I.waitFor();
    //Step 1: Increase amount
    const plusButton = '//p[contains(text(), "毎月の指定金額")]/parent::div//button[@data-testid="groupInputNumber_plus_id"]';
    await I.waitForElement(plusButton, 3);
    I.scrollAndClick(plusButton);
    //Step 2: Check Insider confirmation panel Toggle selection
    const button = '//div[@data-testid="reserveOrderInput_notInsiderTradeConfirm_id"]//button';
    await I.waitForElement(button, 3);
    I.scrollAndClick(button);
    //Step 3: Click submit button
    const confirmButton = '//*[@data-testid="reserveOrderInput_confirmOrderButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollAndClick(confirmButton);
    I.saveScreenshot('1360.Test_item_No.30_Check_submit_and_Go_to_Savings_Petit_Stocks_Savings_Application_Confirmation.png');
    //back 
    await I.backToPreviousScreen();
});
Scenario('Test Item 31: Check Caution statement Opening and closing the accordion ', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    const caution = '//*[@data-testid="reserveOrderInput_caution_id"]/div[1]';
    await I.waitForElement(caution, 3);
    await I.scrollAndClick(caution);
    await I.scrollToElement(caution);
    await I.saveScreenshot('1360.Test_item_No.31_Check_caution_ON.png');
    await I.waitFor('shortWait');
    await I.clickFixed(caution);
    I.saveScreenshot('1360.Test_item_No.31_Check_caution_OFF.png');
});

Scenario('Test Item 32: Savings setting status - Date specified by our company and open/close accordion', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    await I.waitFor();
    const caution = '//*[@data-testid="reserveOrderInput_reserveSettingStatus_id"]/div[1]';
    const companySpecifiedDate = '//*[contains(text(), "当社指定日")]';
    await I.waitForElement(caution, 3);
    await I.scrollAndClick(caution);
    await I.scrollToElement(caution);
    await I.saveScreenshot('1360.Test_item_No.32.1_Check_Savings_Setting_Status_Savings_Setting_Status_Header_Accordion_Open_Close_ON.png');
    await I.waitFor('shortWait');
    await I.clickFixed(caution);
    await I.saveScreenshot('1360.Test_item_No.32.1_Check_Savings_Setting_Status_Savings_Setting_Status_Header_Accordion_Open_Close_OFF.png');
    await I.clickFixed(caution);
    await I.waitFor('shortWait');
    await I.scrollToElement(companySpecifiedDate);
    await common.clickCardItem(companySpecifiedDate, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
});
Scenario('Test Item 37: Check Click Open the following URL in a new tab:', async ({ I }) => {
    const accountTypeLabels = '//*[@data-testid="reserveOrderInput_accountType_id"]';
    await I.waitForElement(accountTypeLabels, 1);
    I.scrollToElement(accountTypeLabels);

    const identificationLabel = `${accountTypeLabels}//label[1]`;
    await I.waitFor('shortWait');
    I.clickFixed(identificationLabel);
    await I.waitFor('shortWait');
    // get all labels in payment type radio group
    const paymentTypeLabels = '//*[@data-testid="reserveOrderInput_paymentType_id"]//div[@role="radiogroup"]//label[4]';
    await I.waitForElement(paymentTypeLabels, 1);
    I.scrollAndClick(paymentTypeLabels);
    const hereLink = '//p[@data-testid="reserveOrderInputBox_hereLink_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 1);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');

});
Scenario('Test Item 43 44 45: Check Modal when click Confirm button', async ({ I, accumulation }) => {
    await accumulation.goToReserveOrderInputPage();
    await I.waitFor();
    //Step 1: Increase amount
    const plusButton = '//p[contains(text(), "毎月の指定金額")]/parent::div//button[@data-testid="groupInputNumber_plus_id"]';
    await I.waitForElement(plusButton, 3);
    I.scrollAndClick(plusButton);
    //Step 2: Check Insider confirmation panel Toggle selection
    const button = '//div[@data-testid="reserveOrderInput_notInsiderTradeConfirm_id"]//button';
    await I.waitForElement(button, 3);
    I.scrollAndClick(button);
    //Step 3: Click submit button
    const confirmButton = '//*[@data-testid="reserveOrderInput_confirmOrderButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollAndClick(confirmButton);
    // Show modal
    // click button
    const backButton = '//*[contains(text(), "戻って毎月の指定金額を見直す")]';
    I.clickFixed(backButton);
    I.dontSeeElement(backButton);
    I.saveScreenshot('1360.Test_item_No.43_Check_back_button.png');

    // click button submit again
    I.clickFixed(confirmButton);
    await I.waitFor('shortWait');
    const submitButton = '//*[contains(text(), "このまま申込む")]';
    I.clickFixed(submitButton);
    I.saveScreenshot('1360.Test_item_No.43_Check_submit_button.png');
    //back to previous screen
    await I.backToPreviousScreen();

    // click button submit again
    I.scrollAndClick(confirmButton);
    await I.waitFor('shortWait');
    const checkBox = '//div[p[contains(text(), "次回からこのメッセージを表示しない")]]//button';
    I.clickFixed(checkBox);
    I.saveScreenshot('1360.Test_item_No.43_Check_check_box_On.png');
    I.clickFixed(checkBox);
    I.saveScreenshot('1360.Test_item_No.43_Check_check_box_Off.png');
});

