Feature('Referral - GenerateURLPage');

// Import the page object
import { COOKIE_KEY, PAGE_URL, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import Referral from '../pages/referral';
import ReferralGenerateURL from '../pages/referralGenerateURL';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I<PERSON>closeBrowser();
});

Scenario('go to referral generate URL page', async ({ I }) => {
    I.amOnPage(PAGE_URL.referral);
    I.see(
        `ご家族・ご友人
紹介プログラム`,
        '//*[@data-testid="common_header_title_id"]',
    );
    await Referral.navigateToSnsReferral();
    await ReferralGenerateURL.takeScreenshot(`${SCREENSHOT_PREFIX.referralGenerateURL}_page.png`);
});

Scenario('Test Item 1: About Referral Program', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralGenerateURL.takeScreenshot(
        `${SCREENSHOT_PREFIX.referralGenerateURL}_about_referral_program_before_click.png`,
    );

    // Click on the About Referral Program link
    const externalUrl = await ReferralGenerateURL.clickAboutFriendsProgram();

    // Verify the URL is correct
    I.assertContain(
        externalUrl,
        ReferralGenerateURL.urls.programUrl,
        'External URL should be the referral program page',
    );

    // Take a final screenshot
    await ReferralGenerateURL.takeScreenshot(
        `${SCREENSHOT_PREFIX.referralGenerateURL}_about_referral_program_after_click.png`,
    );
});

Scenario('Test Item 3: Copy Introduction URL', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralGenerateURL.takeScreenshot(`${SCREENSHOT_PREFIX.referralGenerateURL}_copy_url_before_click.png`);

    // Click on the Copy URL button
    await ReferralGenerateURL.clickCopyReferralUrl();

    // Verify toast notification is displayed
    const isToastVisible = await ReferralGenerateURL.isToastNotificationVisible();
    I.assertTrue(isToastVisible, 'Toast notification should be visible after copying URL');

    // Take a screenshot showing the toast notification
    await ReferralGenerateURL.takeScreenshot(`${SCREENSHOT_PREFIX.referralGenerateURL}_copy_url_after_click_with_toast.png`);
});

Scenario('Test Item 4: Go to Friend Introduction program', async ({ I }) => {
    // Take a screenshot before clicking
    await ReferralGenerateURL.takeScreenshot(`${SCREENSHOT_PREFIX.referralGenerateURL}_go_to_friend_intro_before_click.png`);

    // Click on the Go to Friend Introduction TOP button
    const currentUrl = await ReferralGenerateURL.clickGoToFriendIntroductionTOP();

    // Verify the URL contains the referral page path
    I.assertContain(currentUrl, ReferralGenerateURL.urls.referralPage, 'URL should contain the referral page path');

    // Take a screenshot after redirecting
    await ReferralGenerateURL.takeScreenshot(`${SCREENSHOT_PREFIX.referralGenerateURL}_go_to_friend_intro_after_click.png`);

    // Navigate back to the generate URL page
    await ReferralGenerateURL.navigateBack();

    // Take a final screenshot
    await ReferralGenerateURL.takeScreenshot(`${SCREENSHOT_PREFIX.referralGenerateURL}_go_to_friend_intro_after_return.png`);
});
