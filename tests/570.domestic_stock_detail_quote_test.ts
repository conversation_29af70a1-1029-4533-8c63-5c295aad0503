import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('InvestmentProducts - DomesticStockDetailQuote');

Before(async ({ I, loginAndSwitchToWebAs, stockDetailBasicPage }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToDetailQuoteTab();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Detail Quote page', async ({ I, stockDetailBasicPage }) => {
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/board');
    stockDetailBasicPage.takeScreenshot.stockDetailQuote('Display_Domestic_Stock_Detail_Quote_page');
});

Scenario('Test item No.2 Previous', async ({ I, stockDetailBasicPage }) => {
    const previousButtonSelector = '//div[contains(@class, "slick-prev")]';
    await I.clickFixed(previousButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.detailInfoContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailQuote('Test_item_No.2_Click_previous_go_to_Detail_information_tab');
});

Scenario('Test item No.3 Detail information', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.detailInformationTab);
    await I.waitFor();
    I.seeElement('//*[@data-testid="stockDetailBasic_basicInfo_id"]');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/detail');
    stockDetailBasicPage.takeScreenshot.stockDetailQuote('Test_item_No.3_Click_detail_tab');
});

Scenario('Test item No.5 Next', async ({ I, stockDetailBasicPage }) => {
    const nextButtonSelector = '//div[contains(@class, "slick-next")]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    I.see('四季報', 'body');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/quarterly');
    stockDetailBasicPage.takeScreenshot.stockDetailQuote('Test_item_No.5_Click_next_go_to_stock_detail_Quarterly_Report_tab');
});

Scenario('Test item No.4 Quarterly Report', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.quarterlyTab);
    await I.waitFor();
    I.see('四季報', 'body');
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/quarterly');
    stockDetailBasicPage.takeScreenshot.stockDetailQuote('Test_item_No.4_Click_Quarterly_Report_tab');
});

Scenario('Test item No.6 Scroll Board Information', async ({ I, stockDetailBasicPage }) => {
    await I.swipeUpFixed(stockDetailBasicPage.locator.boardInfoContainer);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailQuote('Test_item_No.6_Scroll_Board_information');
});

Scenario('Test item No.23 Stock Flash', async () => {
    const stockFlash = '//*[@data-testid="stockBoard_boardFlash_id"]';
    // Go to the following screen [{member-site-url}]/iPhone/TradeTool/KBF/KabucomItaFlashStock.asp?Symbol={Symbol}&Market={Market}
    await common.clickCardItem(stockFlash, '/iPhone/TradeTool/KBF/KabucomItaFlashStock.asp', 'kcMemberSite');
});
