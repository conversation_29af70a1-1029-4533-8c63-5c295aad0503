import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Notify - NotifyTradingListPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyTradingList.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I<PERSON>closeBrowser();
    await I.switchToNative();
});

Scenario('test notify trading list page', async ({ I }) => {
    I.waitForElement('//*[@data-testid="common_noti_id"]');
    I.click('//*[@data-testid="common_noti_id"]');
    await I.waitFor('mediumWait');

    const notifyTradingTab = '//*[@data-testid="noticeTab_tradingNoti_id"]';
    I.waitForElement(notifyTradingTab);
    I.click(notifyTradingTab);
    await I.waitFor();
    I.saveScreenshot('257.notify_trading_list_tradingListTab.png');
});

Scenario('test Item 1 notify trading item page', async ({ I }) => {
    const tradingItem = '//*[@data-testid="notifyTradingList_notiTradingItem_id_0"]';
    I.waitForElement(tradingItem);
    I.click(tradingItem);
    await I.waitFor('extraLongWait');
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, 'order-inquiry/margin', 'currentUrl is not equal to order-inquiry/stock');
    await I.waitFor();
    I.click('//*[@data-testid="common_back_id"]');
    I.saveScreenshot('257.notify_trading_list_tradingItem.png');
});

Scenario('test Item 5 Contract Expiration notice item page', async ({ I }) => {
    const expirationNotice = '//*[@data-testid="notifyTradingList_contractExpirationNotice_id"]/span';
    I.waitForElement(expirationNotice);
    I.click(expirationNotice);
    await I.waitFor('mediumWait');
    const { tryTo } = require('codeceptjs/effects');

    const isChecked = await tryTo(async () => await I.grabAttributeFrom(expirationNotice, 'data-checked'));
    I.say(isChecked.toString());
    I.assertFalse(isChecked, 'isChecked is not equal to null');
    await I.saveScreenshot('257.notify_trading_list_expirationNotice.png');
    I.click(expirationNotice);
});

Scenario('test Item 6 Scroll to Top page', async ({ I }) => {
    await I.swipeDirection('up', 0.4);
    await I.waitFor();
    I.seeElement('//*[@id="scrollButton"]');
    I.click('//*[@id="scrollButton"]');
    I.saveScreenshot('257.notify_trading_list_notifyListDetail.png');
});
