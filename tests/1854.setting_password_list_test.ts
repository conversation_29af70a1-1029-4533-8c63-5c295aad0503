Feature('Settings_Entry - SettingPasswordListPage');
import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import SettingPasswordList from '../pages/settingPasswordList';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('go to setting password list page', async ({ I }) => {
    await I<PERSON>waitFor();
    await I.amOnPage(SettingPasswordList.urls.passwordList);
    await I.waitFor('mediumWait');
    I.see('各種パスワード変更', SettingPasswordList.locators.pageDescription);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.settingPasswordList}_UI_of_Setting_Password_List_page.png`);
});

Scenario('Test Item 1: Change Login Password', async () => {
    await SettingPasswordList.testButtonNavigation(
        SettingPasswordList.locators.changeLoginPasswordButton,
        [SettingPasswordList.urls.portfolio, SettingPasswordList.urls.changeLoginPassword],
        `${SCREENSHOT_PREFIX.settingPasswordList}_button_click_to_login_password_page.png`,
    );
});

Scenario('Test Item 2: Change Withdrawal Password', async ({ I }) => {
    await I.amOnPage(SettingPasswordList.urls.passwordList);
    await SettingPasswordList.testButtonNavigation(
        SettingPasswordList.locators.changeWithdrawalPasswordButton,
        [SettingPasswordList.urls.portfolio, SettingPasswordList.urls.changeWithdrawalPassword],
        `${SCREENSHOT_PREFIX.settingPasswordList}_button_click_to_change_withdrawal_password_page.png`,
    );
});

Scenario('Test Item 3: Reset Withdrawal Password', async ({ I }) => {
    await I.amOnPage(SettingPasswordList.urls.passwordList);
    await SettingPasswordList.testButtonNavigation(
        SettingPasswordList.locators.resetWithdrawalPasswordButton,
        [SettingPasswordList.urls.portfolio, SettingPasswordList.urls.resetWithdrawalPassword],
        `${SCREENSHOT_PREFIX.settingPasswordList}_button_click_to_reset_withdrawal_password_page.png`,
    );
});
