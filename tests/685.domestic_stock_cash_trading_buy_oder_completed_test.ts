import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockCashTradingBuyOrderCompleted');

Before(async ({ I, loginAndSwitchToWebAs, stockCashOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user25 });
    await stockCashOrder.goToCashBuyOrderCompleted();
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Display Domestic Stock Cash Trading Buy Order Completed', async ({ stockCashOrder }) => {
    await stockCashOrder.compareUrl(stockCashOrder.urls.cashBuyCompleted);
    stockCashOrder.takeScreenshot.cashBuyOrderCompleted('Display_Domestic_Stock_Cash_Trading_Buy_Order_Completed');
});

Scenario('Test item No.2 Order number', async ({ I, stockCashOrder }) => {
    const orderIdSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    await I.clickFixed(orderIdSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/order-inquiry/stock/detail');
    stockCashOrder.takeScreenshot.cashBuyOrderCompleted('Test_item_No.2_Tap_order_id_to_transition_to_details_page');
});

Scenario('Test item No.3 Order Inquiry', async ({ I, stockCashOrder }) => {
    const orderInquiryButtonSelector = '//*[@data-testid="complete_orderStatusButton_id"]';
    await I.clickFixed(orderInquiryButtonSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/order-inquiry/stock');
    stockCashOrder.takeScreenshot.cashBuyOrderCompleted('Test_item_No.3_Transition_to_order_inquiry_list');
});

Scenario('Test item No.4 Balance inquiry', async ({ I, stockCashOrder }) => {
    const balanceInquiryButtonSelector = '//*[@data-testid="complete_positionInquiryButton_id"]';
    await I.clickFixed(balanceInquiryButtonSelector);
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/position-inquiry/stock');
    stockCashOrder.takeScreenshot.cashBuyOrderCompleted('Test_item_No.4_Transition_to_balance_inquiry_list');
});

Scenario('Test Browser back', async ({ I, stockCashOrder }) => {
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    await stockCashOrder.compareUrl('/mobile/search');
    stockCashOrder.takeScreenshot.cashBuyOrderCompleted('Test_Browser_back_transition_to_general_search_page');
});
