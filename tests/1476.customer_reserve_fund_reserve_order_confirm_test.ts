import { COOKIE_KEY, USER_ID } from "../const/constant";
import pdfView from "../pages/common-ui/pdfView";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveFundReserveOrderConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user52 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Fund ReserveOrder Confirm', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const fundReserveOrderInput_paymentMethod_id = '//*[@data-testid="fundReserveOrderInput_paymentMethod_id"]//label';
    await I.waitForElement(fundReserveOrderInput_paymentMethod_id, 3);
    I.scrollAndClick(`(${fundReserveOrderInput_paymentMethod_id})[4]`);
    await I.waitFor('shortWait');
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    I.scrollAndClick(orderConfirmButton);
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");
    I.saveScreenshot('1445.Test_item_No.51_Go_to_order_confirmation_screen.png');
 
});
Scenario('Test Item 15: Check Open the following URL in a new tab', async ({ I, accumulation }) => {
    const hereLink = '//p[@data-testid="fundReserveOrderConfirm_here_id"]//span[contains(text(), "こちら")]';
    await I.waitForElement(hereLink, 3);
    await common.clickCardItem(hereLink, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
});
Scenario('Test Item 16: Check Investment Trust Regular Accumulation Transaction Handling Regulations', async ({ I, accumulation }) => {
    const fundRegulationsLink = '//*[@data-testid="fundReserveOrderConfirm_fundRegulationsLink_id"]';
    await I.waitForElement(fundRegulationsLink, 1);
    I.scrollAndClick(fundRegulationsLink);
    await I.waitFor('shortWait');
    // back
    await pdfView.closePDFView();
    await I.waitFor('shortWait');
    I.saveScreenshot('1476.Test_item_No.16_Back_to_order_confirmation_screen.png');
});
Scenario('Test Item 17: Check Investment Trust Regular Accumulation Transaction Handling Regulations for NISA Account', async ({ I }) => {
    const fundRegulationsLink = '//*[@data-testid="fundReserveOrderConfirm_nisaAccountFundRegulationsLink_id"]';
    await I.waitForElement(fundRegulationsLink, 1);
    I.scrollAndClick(fundRegulationsLink);
    await I.waitFor('shortWait');
     // back
     await pdfView.closePDFView();
     await I.waitFor('shortWait');
     I.saveScreenshot('1476.Test_item_No.16_Back_to_order_confirmation_screen.png');
});
Scenario('Test Item 20: Check Password Text Input Form', async ({ I }) => {
    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 1);
    I.scrollAndFill(passwordInput, '123@abc'); 
     // click img alt="eye" to see password
     const eyeIcon = '//img[@alt="eye"]';
     await I.waitForElement(eyeIcon, 1);
     I.clickFixed(eyeIcon);
     await I.waitFor('shortWait');
    I.saveScreenshot('1476.Test_item_No.20_Password_Text_Input_Form.png');
});
Scenario('Test Item 21: Check Password omission check: Switch ON/OFF', async ({ I }) => {
    const passwordInputOmissionCheck = '//*[@data-testid="fundReserveOrderConfirm_passwordOmissionCheck_id"]';
    await I.waitForElement(passwordInputOmissionCheck, 1);
    // ON
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1476.Test_item_No.21_Password_omission_check_ON.png');
    // OFF
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1476.Test_item_No.21_Password_omission_check_OFF.png');
});
Scenario('Test Item 23: Check Password input check: Switch between ON and OFF', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user54 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    I.scrollAndClick(orderConfirmButton);
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");

    const passwordInputCheck = '//*[@data-testid="fundReserveOrderConfirm_passwordInputCheck_id"]//button';
    await I.waitForElement(passwordInputCheck, 2);
    I.scrollAndClick(passwordInputCheck);
    I.saveScreenshot('1476.Test_item_No.23_Password_input_check_1.png');
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1476.Test_item_No.23_Password_input_check_2.png');
});
//TODO: 24.1: Skip bacause cannot check maintenance
Scenario.skip('Test Item 24.2: Browser back during loading redirects to reserve plan', async ({ I, accumulation }) => {
    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundReserveOrderConfirm_apply_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1476.Test_item_No.24.2_before_click_button.png');
    I.clickFixed(confirmButton);
    
    I.executeScript(() => window.history.back());
    
    I.see('申込', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1476.Test_item_No.24.2_redirect_to_reserve_plan.png');
    //back 
    await I.backToPreviousScreen();

});
Scenario('Test Item 24.3: Check Loading will be performed. Transition to savings - investment trust - savings application completion', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    I.scrollAndClick(orderConfirmButton);
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");

    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundReserveOrderConfirm_apply_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1476.Test_item_No.24.3_before_click_button.png');
    I.clickFixed(confirmButton);
    await I.waitFor('shortWait');
    I.seeElement('//p[contains(text(), "積立申込が完了しました。")]');
    I.saveScreenshot('1476.Test_item_No.24.3_Check_Loading_will_be_performed_Transition_to_savings_investment_trust_savings_application_completion.png');
});
Scenario('Test Item 25: Check Caution Message', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    I.scrollAndClick(orderConfirmButton);
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");


    const cautionMessage = '//*[@data-testid="fundReserveOrderConfirm_cautionMessage_id"]';
    await I.waitForElement(cautionMessage, 1);
    I.scrollAndClick(cautionMessage);
    const chakraCollapse = cautionMessage + '//*[@class="chakra-collapse"]';
    await I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1476.Test_item_No.25_Check_Caution_Message.png');
    const arrowUpSelector = '//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.clickFixed(arrowUpSelector);
    I.saveScreenshot('1476.Test_item_No.25_Check_Caution_Message_Close.png');
});


