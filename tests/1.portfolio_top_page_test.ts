import { COOKIE_KEY, LOCAL_STORAGE_KEY, USER_ID } from '../const/constant';
import { modalDialog } from '../const/locator';
import common from '../pages/search/common';

Feature('AssetStatus - PortfolioTopPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    const { tryTo } = require('codeceptjs/effects');
    await I.activateApp();
    if (await tryTo(() => I.seeElement('~ログイン'))) {
        await loginAndSwitchToWebAs('user1');
    } else {
        await I.switchToWeb();
        await I.waitFor('longWait');
    }
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Display portfolio top page', async ({ I, portfolioTopPage }) => {
    await portfolioTopPage.goToPortfolioPage();
    await I.waitFor();
    await I.saveScreenshot('1.portfolio_top_page_Test_display_portfolio_top_page.png');
});

Scenario('Test Item No.1 Pie chart', async ({ I, portfolioTopPage }) => {
    const pieChart = '//*[@data-testid="pieChart_id"]';
    await portfolioTopPage.goToPortfolioPage();
    await I.waitForVisible(pieChart, 5);
    await I.waitFor();
    await I.tapByCoordinate(pieChart, { x: -120, y: 0 });
    await I.waitFor();
    await I.waitForVisible('//*[contains(@class, "highcharts-tooltip")]', 5);
    await I.saveScreenshot('1.portfolio_top_page_Test_Item_No.1_Tooltips_are_displayed.png');
});

Scenario('Test Button 23', async ({ I, portfolioTopPage }) => {
    await portfolioTopPage.goToPortfolioPage();
    await I.clickFixed('//*[@data-testid="portfolioTop_portfolioView_id"]');
    await I.waitFor();
    // Click button 23 (Close button)
    await I.clickFixed('//*[@data-testid="portfolioTop_close_id"]');
    await I.saveScreenshot('1.portfolio_top_page_Test_case_button_23.png');
});

Scenario('Test Case Button 24', async ({ I }) => {
    // Click button 24 (Help button)
    await I.clickFixed('//button[@data-testid="common_header_help_id"]');
    await I.waitFor();
    await I.saveScreenshot('1.portfolio_top_page_Test_case_button_24.png');
    // I.see('ヘルプ');
    // Click close button in bottom sheet
    await I.clickFixed('//*[@data-testid="portfolioTop_close_id"]');
    await I.waitFor();
});

Scenario('Test item No.25', async ({ I, portfolioTopPage }) => {
    await portfolioTopPage.goToPortfolioPage();
    const appSettings = JSON.parse(await I.getLocalStorage(LOCAL_STORAGE_KEY.appSettingList));
    // Set is-disp-top-coach to false for show coach modal
    await I.setLocalStorage(LOCAL_STORAGE_KEY.appSettingList, JSON.stringify([{ ...appSettings[0], "is-disp-top-coach": false }]))
    await portfolioTopPage.goToPortfolioPage();
    await I.waitFor();
    await I.saveScreenshot('1.portfolio_top_page_Test_item_No.25.png');
});

Scenario('Test Case Button 27', async ({ I, portfolioTopPage }) => {
    await portfolioTopPage.goToPortfolioPage();
    // Click button 24 (Help button)
    I.click("//button[@data-testid='common_header_help_id']");
    await I.waitFor();
    I.waitForVisible({ xpath: '//*[@id="bottom-sheet-container"]' }, 5);
    // Click button 27 (Operation guide button)
    I.click('//div[@data-testid="portfolioTop_operationGuide_id"]');
    await common.clickCardItem('//div[@data-testid="portfolioTop_operationGuide_id"]', 'https://kabu.com/sp_ssl_content/user_guide/index.html', 'external');
    await I.switchToWeb();
    await I.waitFor();
    // Click close button in bottom sheet
    await I.clickFixed('//*[@data-testid="portfolioTop_close_id"]');
    await I.waitFor();
});

Scenario('Test Case Button 28', async ({ I, portfolioTopPage }) => {
    await portfolioTopPage.goToPortfolioPage();
    // Click button 24 (Help button)
    await I.clickFixed('//button[@data-testid="common_header_help_id"]');
    await I.waitFor();
    I.waitForVisible('//*[@id="bottom-sheet-container"]', 5);
    // Click button 28 (Customer support button)
    await common.clickCardItem('//div[@data-testid="portfolioTop_customerSupport_id"]', 'https://kabu.com/sp_ssl_content/help/index.html', 'external');
    await I.switchToWeb();
    await I.waitFor();

    // Click close button in bottom sheet
    await I.clickFixed('//*[@data-testid="portfolioTop_close_id"]');
    await I.waitFor();
});

Scenario('Test Case Button 9, 29', async ({ I }) => {
    // Click button 9
    I.click('//div[p[contains(text(), "ポートフォリオの見方")] and img]');
    await I.waitFor();
    I.waitForVisible('//*[@id="bottom-sheet-container"]', 5);
    // Click button 29 (Customer support button)
    await common.clickCardItem('//*[@id="bottom-sheet-container"]//p[@data-testid="portfolioTop_detailedExplanationPortfolioScreen_id_0"]//a', 'https://kabu.com/mobile/app/guide/index.html#portfolio', 'external');
    await I.switchToWeb();
    await I.waitFor();
    // Click close button in bottom sheet
    await I.clickFixed('//*[@data-testid="portfolioTop_close_id"]');
    await I.waitFor();
});

Scenario('Test Case Swipe 22', async ({ I }) => {
    // Click button 9
    await I.clickFixed('//div[p[contains(text(), "ポートフォリオの見方")] and img]');
    await I.waitFor();
    I.waitForVisible('//*[@id="bottom-sheet-container"]', 5);

    const scrollableElementInPortfolioHelpModal = locate('div').after(
        locate('p').withText('ポートフォリオの見方').inside(modalDialog),
    );
    await I.swipeLeftFixed(scrollableElementInPortfolioHelpModal);
    await I.waitFor();
    I.waitForText('ポートフォリオの見方2', 3, 'body');

    // // Click button 29 (Customer support button)
    //Todo change xpath
    // I.click({ xpath: '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div/div/div[2]/div/p/a' });
    // await I.waitFor('mediumWait');

    // Back to app
    await I.activateApp();
    await I.switchToWeb();
    await I.waitFor();
    // Click close button in bottom sheet
    await I.clickFixed('//*[@data-testid="portfolioTop_close_id"]');
    await I.waitFor();
    I.saveScreenshot('1.portfolio_top_page_Test_case_swipe_22.png');
});

Scenario('Test Case Button 30', async ({ I }) => {
    const srcBefore = await I.grabAttributeFrom('//div[p[contains(text(), "金額表示")] and img]/img', 'src');
    //Toggle button 30
    await I.clickFixed({ xpath: '//div[p[contains(text(), "金額表示")] and img]' });
    await I.waitFor();
    const srcAfter = await I.grabAttributeFrom('//div[p[contains(text(), "金額表示")] and img]/img', 'src');
    I.assertNotEqual(srcBefore, srcAfter, 'Button 30 is toggled');
    I.saveScreenshot('1.portfolio_top_page_Test_case_button_30.png');
});

Scenario('Test Case Button 31', async () => {
    // Go to https://notice.kabu.co.jp/notifyList
    await common.clickCardItem('//*[@data-testid="portfolioTop_unreadNoti_id"]', 'https://notice.kabu.co.jp/notifyList', 'external');
});

Scenario('Test Case Button 33', async ({ I }) => {
    // Click open 33
    await I.clickFixed('//button[@data-testid="portfolioTop_aboutFundAssetValuation_id"]');
    I.waitForText('投資信託の資産評価額について', 5, 'div.chakra-modal__content-container');
    await I.waitFor();
    await I.clickFixed('//button[text()="閉じる"]');
    I.saveScreenshot('1.portfolio_top_page_Test_case_button_33.png');
});

Scenario('Test Case Button 12', async ({ I }) => {
    // Click open 33
    await I.clickFixed('//*[@id="mypage-portfolio"]/div/button');
    I.waitForText('投資信託の資産評価額について', 5, 'div.chakra-modal__content-container');
    await I.waitFor();
    await I.clickFixed('//button[text()="閉じる"]');
    I.saveScreenshot('1.portfolio_top_page_Test_case_button_12.png');
});
