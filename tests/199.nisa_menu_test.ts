Feature('NISAMenu - Page');
import { COOKIE_KEY, LAST_EXTERNAL_URL, USER_ID } from '../const/constant';
import NisaMenu from '../pages/nisaMenu';
import common from '../pages/search/common';

const locators = NisaMenu.locators;

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user8 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await NisaMenu.goToPage();
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

<PERSON>enario('Go to nisa menu page', async () => {
    await NisaMenu.takeScreenshot('199_nisa_menu_page.png');
});

<PERSON>enario('Test item A1 Account status', async ({ I }) => {
    // Switch to userId ******** to access NISA account status
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user9 });
    await NisaMenu.clickItem(locators.accountStatus);
    // Navigate to Nisa Summary Top
    I.seeElement('//*[@data-testid="nisaSummaryTop_nisaInvestmentLimit_id"]');
    I.waitForText('NISA口座状況', 3, 'body');
    await NisaMenu.compareUrl('/mobile/nisa/summary/top');
    await NisaMenu.takeScreenshot('199_Test_item_A1_Account_status.png');
    await NisaMenu.back();
});

Scenario('Test item A2 Symbol search', async ({ I }) => {
    await NisaMenu.clickItem(locators.symbolSearchA2);
    // Navigate to Nisa Search Top
    I.seeElement('//*[@id="searchPage"]');
    I.waitForText('銘柄検索', 3, 'body');
    await NisaMenu.compareUrl('/mobile/search');
    await NisaMenu.takeScreenshot('199_Test_item_A2_Symbol_search.png');
    await NisaMenu.back();
});

Scenario('Test item A3 Position inquiry', async ({ I }) => {
    await NisaMenu.clickItem(locators.positionInquiryA3);
    // Navigate to Position Inquiry Stock
    I.seeElement('//*[@id="position-inquiry--tabpanel-0"]');
    I.waitForText('残高照会 現物株式', 3, 'body');
    await NisaMenu.compareUrl('/mobile/position-inquiry/stock');
    await NisaMenu.takeScreenshot('199_Test_item_A3_Position_inquiry.png');
    await NisaMenu.back();
});

Scenario('Test item A4 Order inquiry', async ({ I }) => {
    await NisaMenu.clickItem(locators.orderInquiry);
    // Navigate to Order Inquiry Stock
    I.seeElement('//*[@id="order-inquiry--tabpanel-0"]');
    I.waitForText('注文約定照会 現物株式', 3, 'body');
    await NisaMenu.compareUrl('/mobile/order-inquiry/stock');
    await NisaMenu.takeScreenshot('199_Test_item_A4_Order_inquiry.png');
    await NisaMenu.back();
});

Scenario('Test item A5 Distribution history', async ({ I }) => {
    await NisaMenu.clickItem(locators.distributionHistory);
    // Navigate to Dividend History
    I.seeElement('//*[@id="dividend-history-page"]');
    I.waitForText('配当金履歴', 3, 'body');
    await NisaMenu.compareUrl('/mobile/dividend-history/list');
    await NisaMenu.takeScreenshot('199_Test_item_A5_Distribution_history.png');
    await NisaMenu.back();
});

Scenario('Test item B1 Small Stocks', async () => {
    await NisaMenu.clickItem(locators.petitStock);
    // Progress to Accumulation - Small Stocks - Search
    await NisaMenu.compareUrl('/mobile/reserve/petit');
    await NisaMenu.takeScreenshot('199_Test_item_B1_Small_Stocks.png');
    await NisaMenu.back();
});

Scenario('Test item B2 Investment trusts', async () => {
    await NisaMenu.clickItem(locators.investmentTrust);
    // Go to fund search
    await NisaMenu.compareUrl('/mobile/info/fund/search');
    await NisaMenu.takeScreenshot('199_Test_item_B2_Investment_trusts.png');
    await NisaMenu.back();
});

Scenario('Test item C1 Stock search (purchase)', async () => {
    await NisaMenu.clickItem(locators.symbolSearchC1);
    // Transition to domestic spot stock search
    await NisaMenu.compareUrl('/mobile/search-stock');
    await NisaMenu.takeScreenshot('199_Test_item_C1_Stock_search.png');
    await NisaMenu.back();
});

Scenario('Test item C3 Transaction history', async () => {
    // Go to the following URL: {member-site-url}/ap/iPhone/Nisa/Stock/History/List
    await common.clickCardItem(locators.tradingHistoryC3, '/ap/iPhone/Nisa/Stock/History/List', 'kcMemberSite');
});

Scenario('Test item D1 Stock search (purchase)', async () => {
    await NisaMenu.goToPage();
    await NisaMenu.clickItem(locators.symbolSearchD1);
    // Go to general search TOP
    await NisaMenu.compareUrl('/mobile/search');
    await NisaMenu.takeScreenshot('199_Test_item_D1_Stock_search_purchase.png');
    await NisaMenu.back();
});

Scenario('Test item D3 Transaction history', async () => {
    // Go to the following URL {member-site-url}/ap/iPhone/Nisa/Stock/History/List
    await common.clickCardItem(locators.tradingHistoryD3, '/ap/iPhone/Nisa/Stock/History/List', 'kcMemberSite');
});

Scenario('Test item E1 Stock search (buy)', async () => {
    // Go to the following URL {member-site-url}/ap/iPhone/InvInfo/USMarket/Search/ByKeyword
    await common.clickCardItem(locators.symbolSearchE1, '/ap/iPhone/InvInfo/USMarket/Search/ByKeyword', 'kcMemberSite');
});

Scenario('Test item E2 Balance inquiry (sell)', async () => {
    // Go to the following URL: {member-site-url}/ap/iPhone/ForeignStocks/USStock/Position/List?actType=Nisa
    await common.clickCardItem(locators.positionInquiryE2, '/ap/iPhone/ForeignStocks/USStock/Position/List?actType=Nisa', 'kcMemberSite');
});

Scenario('Test item E3 Transaction history', async () => {
    // Go to the following URL: {member-site-url}/ap/iPhone/ForeignStocks/USStock/History/List?actType=Nisa&SettleType=Oazukari
    await common.clickCardItem(locators.tradingHistoryE3, '/ap/iPhone/ForeignStocks/USStock/History/List?actType=Nisa&SettleType=Oazukari', 'kcMemberSite');
});

Scenario('Test item F1 Fund Search (Purchase)', async () => {
    // Go to fund search
    await common.clickCardItem(locators.fundSearch, '/mobile/info/fund/search');
});

Scenario('Test item F2 Balance inquiry (sale)', async () => {
    // Balance inquiry - Investment trust - Go to list
    await common.clickCardItem(locators.positionInquiryF2, '/iPhone/account/accountstatusNISA/ToshinList.asp', 'kcMemberSite');
});

Scenario('Test item F3 Transaction history', async () => {
    // Go to the following URL {member-site-url}/iPhone/Account/TradeRirekiNISA/TradeRirekiToshin.asp
    await common.clickCardItem(locators.tradingHistoryF3, '/iPhone/Account/TradeRirekiNISA/TradeRirekiToshin.asp', 'kcMemberSite');
});

Scenario('Test item B3.Reserve plan', async ({ I }) => {
    await NisaMenu.clickItem(locators.reservePlan);
    // Navigate to Reserve plan
    I.seeElement('//*[@id="reserve-plan-tab"]');
    I.waitForText('積立プラン', 3, 'body');
    await NisaMenu.compareUrl('/mobile/reserve/inquiry/reserve-plan');
    await NisaMenu.takeScreenshot('199_Test_item_B3_Reserve_plan.png');
    await NisaMenu.back();
});

Scenario('Test item B4.Reserve calendar', async ({ I }) => {
    await NisaMenu.clickItem(locators.reserveCalendar);
    // Navigate to Reserve calendar
    I.seeElement('//*[@id="reserve-calendar-tab"]');
    I.waitForText('積立プラン', 3, 'body');
    await NisaMenu.compareUrl('/mobile/reserve/inquiry/reserve-calendar');
    await NisaMenu.takeScreenshot('199_Test_item_B4_Reserve_calendar.png');
    await  NisaMenu.back();
});

Scenario('Test item B5.Reserve history', async ({ I }) => {
    await NisaMenu.clickItem(locators.reserveHistory);
    // Navigate to Reserve history
    I.seeElement('//*[@id="reserve-history-tab"]');
    I.waitForText('積立プラン', 3, 'body');
    await NisaMenu.compareUrl('/mobile/reserve/inquiry/reserve-history');
    await NisaMenu.takeScreenshot('199_Test_item_B5_Reserve_history.png');
    await NisaMenu.back();
});

Scenario('Test item B6-1.Credit card - Unregistered', async ({ I }) => {
    await I.scrollToElement(locators.creditCard);
    await NisaMenu.clickItem(locators.creditCard);
    // + Not registered: Transition to Card Selection
    await NisaMenu.compareUrl('/mobile/setting/card-select');
    await NisaMenu.takeScreenshot('199_Test_item_B6_Credit_card_unregistered.png');
    await NisaMenu.back();
});

Scenario('Test item B6-2.Credit card - Registered', async ({ I }) => {
    I.setCookie([
        { name: COOKIE_KEY.userId, value: USER_ID.user80 },
        { name: COOKIE_KEY.siteId, value: '1' },
    ]);
    await NisaMenu.goToPage();
    await I.scrollToElement(locators.creditCard);
    await NisaMenu.clickItem(locators.creditCard);
    // + Registered: Transition to General-Card Cancellation
    await NisaMenu.compareUrl('/mobile/setting/card-common/unregister');
    await NisaMenu.takeScreenshot('199_Test_item_B6_Credit_card_registered.png');
    await NisaMenu.back();
});

Scenario('Test item C2.Position inquiry', async ({ I }) => {
    await NisaMenu.clickItem(locators.positionInquiryC2);
    // Navigate to Position Inquiry Stock
    I.seeElement('//*[@id="position-inquiry--tabpanel-0"]');
    I.waitForText('残高照会 現物株式', 3, 'body');
    await NisaMenu.compareUrl('/mobile/position-inquiry/stock');
    await NisaMenu.takeScreenshot('199_Test_item_C2_Position_inquiry.png');
    await NisaMenu.back();
});

Scenario('Test item D2.Position inquiry', async ({ I }) => {
    await NisaMenu.clickItem(locators.positionInquiryD2);
    // Navigate to Position Inquiry Stock
    I.seeElement('//*[@id="position-inquiry--tabpanel-0"]');
    I.waitForText('残高照会 現物株式', 3, 'body');
    await NisaMenu.compareUrl('/mobile/position-inquiry/stock');
    await NisaMenu.takeScreenshot('199_Test_item_D2_Position_inquiry.png');
    await NisaMenu.back();
});

Scenario('Test item 2 NISA-QA', async ({ I }) => {
    await NisaMenu.clickItem(locators.nisaQA);
    await I.waitFor('extraLongWait');
    await I.activateApp();
    const externalUrl = await I.getLocalStorage(LAST_EXTERNAL_URL);
    I.say(externalUrl);
    I.assertEqual(externalUrl, locators.nisaQAUrl, 'URL is not matching');
    await NisaMenu.takeScreenshot('199_Test_item_2 NISA_QA.png');
});
