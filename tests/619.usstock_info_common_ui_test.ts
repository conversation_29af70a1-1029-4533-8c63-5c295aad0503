import { COMMON_CLOSE_RIGHT_SLIDE, COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('InvestmentProducts - USStockInfoSummary');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Check UI US Stock Information-Common UI', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/summary?symbol=123';
    I.amOnPage(pageUrl);
    I.see('米国株式\n個別銘柄情報', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(pageUrl);
    I.saveScreenshot('619.usstock_info_common_ui_No.0_access_the_usstock_info_summary_page.png');
});

// 1.戻る -> 1つ前のタブの画面にに遷移
Scenario('Item 1. Click prev button navigate to prev page', async ({ I }) => {
    const locator = '//*[@data-testid="usStockCommon_tab_id"]/div/div[1]';
    I.seeElement(locator);
    await I.clickFixed(locator);
    I.saveScreenshot('619.usstock_info_common_ui_No.1_Click_prev_button_navigate_to_prev_page.png');
});

// 2.戻る -> 選択したボタンに応じる画面に遷移
// + 企業情報:米国株式銘柄情報-企業情報
// + 業績:米国株式銘柄情報-業績
// + ニュース:米国株式銘柄情報-ニュース
// + 株価分析:米国株式銘柄情報-株価分析
// + サマリ:米国株式銘柄情報-サマリ
Scenario('Item 2. Transition to the screen corresponding to the selected button', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/summary?symbol=123';
    I.amOnPage(pageUrl);
    await I.waitFor();
    const tabNames = ['企業情報', '業績', 'ニュース', '株価分析', 'サマリ'];
    for (let i in tabNames) {
        const tabName = tabNames[i];
        const locator = '//*[@data-testid="usStockCommon_tab_id"]/div/div[3]';
        await I.seeAndClickElement(locator);
        I.saveScreenshot(`619.usstock_info_common_ui_No.2_Click_${tabName}_button_navigate_to_the_${tabName}_page.png`);
        await I.waitFor();
    }
});

// 3.次へ -> 1つ次のタブの画面に遷移
Scenario('Item 3. Click next button navigate to next page', async ({ I }) => {
    const locator = '//*[@data-testid="usStockCommon_tab_id"]/div/div[3]';
    await I.seeAndClickElement(locator);
    I.saveScreenshot('619.usstock_info_common_ui_No.3_Click_next_button_navigate_to_next_page.png');
});

// 12.米国株式検索 -> 米国株式検索TOPに遷移
Scenario('Item 12. Navigate to the search-usstock page.', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/summary?symbol=123';
    I.amOnPage(pageUrl);
    await I.waitFor('shortWait');
    const locator = '//button[@data-testid="usStockCommon_usStockSearch_id"]';
    I.seeElement(locator);
    await I.swipeElementDirection('up', locator, 0.5);
    await I.saveScreenshot('619.usstock_info_common_ui_No.12_swipe_to_search_usstock_button.png');
    await I.waitFor('shortWait');
    await I.clickFixed(locator);
    I.saveScreenshot('619.usstock_info_common_ui_No.12_click_button_navigate_to_the_search_usstock_page.png');
});

// 13.取引する -> 遷移先選択モーダルを表示
Scenario('Item 13. Show How to trade modal.', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/corporateinfo?symbol=123';
    I.amOnPage(pageUrl);
    await I.waitFor();

    const locator = '$usStockCommon_trade_id';
    await I.seeAndClickElement(locator);
    I.saveScreenshot('619.usstock_info_common_ui_No.13_show_how_to_trade_modal.png');

    I.seeElement('#bottom-sheet-container');
    const cancelBtnLocator = '//*[@id="bottom-sheet-container"]//button[@aria-label="cancel-btn"]';
    await I.seeAndClickElement(cancelBtnLocator);
});

// 14.お気に入り -> お気に入り登録モーダルを表示
Scenario('Item 14. Show the Add to Favorites modal.', async ({ I }) => {
    const locator = '$usStockCommon_favorite_id';
    await I.seeAndClickElement(locator);
    I.see('お気に入り追加', 'p');
    I.saveScreenshot('619.usstock_info_common_ui_No.14_show_the_add_to_favorites_modal.png');

    await I.waitFor('shortWait');
    await I.seeAndClickElement(COMMON_CLOSE_RIGHT_SLIDE);
});

// 15.買付 -> 以下のURLに遷移
// {member-site-url}/ap/iPhone/ForeignStocks/USStock/Buy/Input?Symbol=元画面からのパラメータ.symbol
Scenario('Item 15. Navigate to the following URL.', async ({ I }) => {
    const modalLocator = '$usStockCommon_trade_id';
    await I.seeAndClickElement(modalLocator);

    const locator = '$usStockCommon_buy_id';
    await common.clickCardItem(locator, '/ap/iPhone/ForeignStocks/USStock/Buy/Input', 'kcMemberSite');
});

// 16.売却 -> 以下のURLに遷移
// /ap/iPhone/ForeignStocks/USStock/Position/List?Symbol=元画面からのパラメータ.symbol
Scenario('Item 16. Navigate to the following URL.', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/corporateinfo?symbol=123';
    I.amOnPage(pageUrl);
    await I.waitFor();

    const modalLocator = '$usStockCommon_trade_id';
    await I.seeAndClickElement(modalLocator);

    const locator = '$usStockCommon_sell_id';
    await common.clickCardItem(locator, '/ap/iPhone/ForeignStocks/USStock/Position/List', 'kcMemberSite');
});
