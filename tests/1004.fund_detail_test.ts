import { COOKIE_KEY, USER_ID } from "../const/constant";
import pdfView from "../pages/common-ui/pdfView";
import common from "../pages/search/common";

Feature('InvestmentProducts - FundDetail');

Before(async ({ I, loginAndSwitchToWebAs, infoFund }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });
    await infoFund.goToFundDetail();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Fund Detail', async ({ infoFund }) => {
    await infoFund.compareUrl(infoFund.urls.infoFundDetail);
    infoFund.takeScreenshot.fundDetail('Display_Fund_Detail');
});

Scenario('Test item No.10 Ranking Help', async ({ I, infoFund }) => {
    const rankingHelpSelector = '//*[@data-testid="sectionTitle_id"][p[contains(text(), "ランキング ベスト50")]]//div[1]';
    await I.scrollAndClick(rankingHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.10_Tooltips_are_displayed');
});

Scenario('Test item No.14 View the ranking list', async () => {
    const item = '//*[@data-testid="infoFundDetail_viewRankingList_id"]';
    // Go to the following URL: {member-site-url}/iPhone/TradeTool/ranking/ToshinRankingMenu.asp
    await common.clickCardItem(item, '/iPhone/TradeTool/ranking/ToshinRankingMenu.asp', 'kcMemberSite');
});

Scenario('Test item No.15 QUICK Investment Trust Score Help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const quickInvestmentTrustScoreHelpSelector = '//*[@data-testid="sectionTitle_id"][p[contains(text(), "QUICK投信スコア")]]//div[1]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(quickInvestmentTrustScoreHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.15_Tooltips_are_displayed');
});

Scenario('Test item No.16 Score collection period', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const scoreCollectionPeriodSelector = '//*[@data-testid="infoFundDetail_scoreCalculationPeriod_id"]';
    const oneYearItemsSelector = `${scoreCollectionPeriodSelector}//label[p[contains(text(), "1年")]]`;
    const threeYearItemSelector = `${scoreCollectionPeriodSelector}//label[p[contains(text(), "3年")]]`;
    const threeYearIconItemSelector = `${threeYearItemSelector}/p`;
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(threeYearItemSelector);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(threeYearIconItemSelector, 'background-color'), 'rgba(255, 86, 0, 1)', 'Background color is not equal');
    await infoFund.takeScreenshot.fundDetail('Test_item_No.16_Toggle_selected_states');
    await I.scrollAndClick(oneYearItemsSelector);
    await I.waitFor();
    I.assertEqual(await I.grabCssPropertyFrom(threeYearIconItemSelector, 'background-color'), 'rgba(0, 0, 0, 0)', 'Background color is not equal');
    infoFund.takeScreenshot.fundDetail('Test_item_No.16_Toggle_unselected_states');
});

Scenario('Test item No.24 View top holdings', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const viewTopSelector = '//*[@data-testid="infoFundDetail_viewTopConstituentStocks_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(viewTopSelector);
    await I.waitFor();
    I.see('組入アセット・銘柄', 'body');
    infoFund.takeScreenshot.fundDetail('Test_item_No.24_Show_portfolio_modal');
});

Scenario('Test item No.29 Prospectus', async ({ I }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const prospectus = '//*[@data-testid="infoFundDetail_prospectus_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollToElement(prospectus);
    await I.clickFixed(prospectus);
    await I.waitFor();
    await pdfView.closePDFView();
});

Scenario('Test item No.30 Important Information Sheet', async ({ I }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const importantInformationSheet = '//*[@data-testid="infoFundDetail_importantInformationSheet_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollToElement(importantInformationSheet);
    await I.clickFixed(importantInformationSheet);
    await I.waitFor();
    await pdfView.closePDFView();
});

Scenario('Test item No.48 Asset composition ratio help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const assetCompositionRatioHelpSelector = '//*[@data-testid="sectionTitle_id"][p[contains(text(), "資産構成比")]]/div[1]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(assetCompositionRatioHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.48_Tooltips_are_displayed');
});

Scenario('Test item No.49 Dividend History Help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const dividendHistoryHelpSelector = '//*[@data-testid="sectionTitle_id"][p[contains(text(), "分配金履歴")]]/div[1]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(dividendHistoryHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.49_Tooltips_are_displayed');
});

Scenario('Test item No.54 Total Return (after deducting trustee fees) Help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const totalReturnAfterDeductingHelpSelector = '//*[@data-testid="infoFundDetail_totalReturnAfterHelp_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(totalReturnAfterDeductingHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.54_Tooltips_are_displayed');
});

Scenario('Test item No.55 Total Return (Before Trust Fees) Help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const totalReturnBeforeTrustHelpSelector = '//*[@data-testid="infoFundDetail_totalReturnBeforeHelp_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(totalReturnBeforeTrustHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.55_Tooltips_are_displayed');
});

Scenario('Test item No.56 Standard deviation help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const standardDeviationHelpSelector = '//*[@data-testid="infoFundDetail_standardDeviationHelp_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(standardDeviationHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.56_Tooltips_are_displayed');
});

Scenario('Test item No.57 Sharpe Ratio Help', async ({ I, infoFund }) => {
    const moreInfoTabSelector = '//button[.//p[contains(text(), "詳細情報")]]';
    const sharpeRatioHelpSelector = '//*[@data-testid="infoFundDetail_standardDeviationHelp_id"]';
    await I.clickFixed(moreInfoTabSelector);
    await I.waitFor();
    await I.scrollAndClick(sharpeRatioHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.57_Tooltips_are_displayed');
});

Scenario('Test item No.64 Chart Help', async ({ I, infoFund }) => {
    const chartHelpSelector = '//*[@data-testid="sectionTitle_id"][p[contains(text(), "チャート")]]/div[1]';
    await I.scrollAndClick(chartHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.64_Tooltips_should_be_displayed');
});

Scenario('Test item No.65 Help for base price trends', async ({ I, infoFund }) => {
    const basePriceTrendsHelpSelector = '//*[@data-testid="sectionTitle_id"][p[contains(text(), "基準価額推移")]]/div[1]';
    await I.scrollAndClick(basePriceTrendsHelpSelector);
    await I.waitFor();
    infoFund.takeScreenshot.fundDetail('Test_item_No.65_Tooltips_are_displayed');
});

Scenario('Test item No.69 Purchase', async ({ I, infoFund }) => {
    const purchaseButtonSelector = '//*[@data-testid="infoFundDetail_buy_id"]';
    await I.scrollAndClick(purchaseButtonSelector);
    await I.waitFor('mediumWait');
    infoFund.takeScreenshot.fundDetail('Test_item_No.69_Transition_to_Investment_Trust_Purchase_Order_Entry');
});

Scenario('Test item No.70 Savings', async ({ I, infoFund }) => {
    const savingsButtonSelector = '//*[@data-testid="infoFundDetail_reserve_id"]';
    await I.scrollAndClick(savingsButtonSelector);
    await I.waitFor('mediumWait');
    infoFund.takeScreenshot.fundDetail('Test_item_No.70_Proceed_to_Investment_Trust_Savings_Application_Input');
});

Scenario('Test item No.71 Favorites', async ({ I, infoFund }) => {
    const favoritesButtonSelector = '//*[@data-testid="infoFundDetail_favorite_id"]';
    await I.scrollAndClick(favoritesButtonSelector);
    await I.waitFor('mediumWait');
    I.see('お気に入り追加', 'body');
    infoFund.takeScreenshot.fundDetail('Test_item_No.71_Show_favorite_registration_modal');
});

Scenario('Test item No.77 Easy electronic delivery application', async ({ I, infoFund }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user36 });
    await infoFund.goToFundDetail();
    const infoFundDetailBuy = '//*[@data-testid="infoFundDetail_buy_id"]';
    const easyElectronicDeliveryApplication = '//*[@data-testid="infoFundDetail_easyElectronicDeliveryApplication_id"]';
    await I.clickFixed(infoFundDetailBuy);
    await I.waitFor();
    // Go to the following URL: {member-site-url}/iPhone/Personal/PDFSettei/PS01101.asp
    await common.clickCardItem(easyElectronicDeliveryApplication, '/iPhone/Personal/PDFSettei/PS01101.asp', 'kcMemberSite');
});
