import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('HamburgerMenu - Page');

Before(async ({ I, loginAndSwitchToWebAs, hamburgerMenu }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user5 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await hamburgerMenu.openMenu();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Hamburger Menu', async ({ hamburgerMenu }) => {
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_Display_Hamburger_Menu');
});

Scenario('Test item No.1 Close', async ({ I, hamburgerMenu }) => {
    const closeBtn = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(closeBtn);
    I.click(closeBtn);
    await I.waitFor();
    I.dontSeeElement(closeBtn);
    I.dontSee('メニュー', 'body');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.1_Close_Hamburger_Menu');
});

Scenario('Test item No.2 Asset status', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_assetStatus_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to PortfolioTop
    I.seeElement('//*[@id="mypage-tab"]');
    I.waitForText('資産状況', 3, 'body');
    await hamburgerMenu.compareUrl('/mobile/mypage/portfolio');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.2_Asset_status');
});

Scenario('Test item No.7 Symbol search', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_symbolSearch_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    await I.waitFor('mediumWait');
    // Navigate to SearchTop
    I.waitForText('銘柄検索', 3, 'body');
    I.seeElement('//*[@data-testid="searchTop_keywordInput_id"]');
    await hamburgerMenu.compareUrl('/mobile/search');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.7_Symbol_search');
});

Scenario('Test item No.8-1 Credit card savings - registered', async ({ I, hamburgerMenu }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user76 });
    await hamburgerMenu.openMenu();
    const menuItem = '//*[@data-testid="menu_creditCardReserve_id"]';
    // Go to {member-site-url}/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp
    await common.clickCardItem(menuItem, '/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp', 'kcMemberSite');
});

Scenario('Test item No.8-2 Credit card savings - not registered', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_creditCardReserve_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    await I.waitFor('mediumWait');
    // Navigate to card-select
    I.seeInCurrentUrl('/mobile/setting/card-select');
    await hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.8_Credit_card_savings_not_registered');
});

Scenario('Test item No.9 Futures', async ({ I }) => {
    const menuItem = '//*[@data-testid="menu_future_id"]';
    await I.scrollToElement(menuItem);
    // Go to {member-site-url}/iPhone/Trade/FuturesNew/NFuturesSelect.asp
    await common.clickCardItem(menuItem, '/iPhone/Trade/FuturesNew/NFuturesSelect.asp', 'kcMemberSite');
});

Scenario('Test item No.10 Options', async ({ I }) => {
    const menuItem = '//*[@data-testid="menu_option_id"]';
    await I.scrollToElement(menuItem);
    // Go to {member-site-url}/iPhone/Trade/OptionNew/NOptionSelect.asp
    await common.clickCardItem(menuItem, '/iPhone/Trade/OptionNew/NOptionSelect.asp', 'kcMemberSite');
});

Scenario('Test item No.11 NISA', async ({ I, hamburgerMenu }) => {
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user6 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await hamburgerMenu.openMenu();
    const menuItem = '//*[@data-testid="menu_nisa_id"]';
    // + D. Customer transaction master API call successful and D. Customer transaction master API.isNisaAccountOpenPast=false:
    // Transition to {member-site-url}/ap/iPhone/Nisa/Summary/Top
    await common.clickCardItem(menuItem, '/iPhone/nisa/notapply.asp', 'kcMemberSite');
    await I.switchToWeb();
    await I.waitFor();

    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user8 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await hamburgerMenu.openMenu();
    // + Other than the above: Transition to NISA general menu
    await common.clickCardItem(menuItem, '/mobile/nisa/menu');
    await hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.11_Click_NISA_go_to_nisa_menu');
});

Scenario('Test item No.12 Credit Robo-Advisor', async ({ I }) => {
    const menuItem = '//*[@data-testid="menu_marginRoboAd_id"]';
    await I.scrollToElement(menuItem);
    await I.waitFor();
    // Transition to {member-site-url}/ap/iPhone/Personal/MarginRoboFlow/List in the same tab
    await common.clickCardItem(menuItem, '/ap/iPhone/Personal/MarginRoboFlow/List', 'kcMemberSite');
});

Scenario('Test item No.13 Savings', async () => {
    const menuItem = '//*[@data-testid="menu_reserve_id"]';
    // Go to {member-site-url}/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp
    await common.clickCardItem(menuItem, '/iphone/trade/teikikaitsuke/tsumitatemenu_sp.asp', 'kcMemberSite');
});

Scenario('Test item No.15 Position inquiry', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_positionInquiry_id"]';
    // Swipe down to see the menu item
    await I.swipeDownFixed('body');
    await hamburgerMenu.clickMenuItem(menuItem);
    await I.waitFor('mediumWait');
    // Navigate to PositionInquiry-DomesticStock-StockList
    I.waitForText('残高照会 現物株式', 3, 'body');
    I.seeElement('//*[@id="position-inquiry"]');
    I.seeElement('//*[@id="position-inquiry--tabpanel-0"]');
    await hamburgerMenu.compareUrl('/mobile/position-inquiry/stock');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.15_Position_inquiry');
});

Scenario('Test item No.16 Order execution status', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_orderStatus_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to OrderStatus-DomesticStock-StockList
    I.waitForText('注文約定照会 現物株式', 3, 'body');
    I.seeElement('//*[@id="order-inquiry"]');
    I.seeElement('//*[@id="order-inquiry--tabpanel-0"]');
    await hamburgerMenu.compareUrl('/mobile/order-inquiry/stock');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.16_Order_execution_status');
});

Scenario('Test item No.17 Transaction History', async () => {
    const menuItem = '//*[@data-testid="menu_tradeHistory_id"]';
    // Go to {member-site-url}/ap/iPhone/Stocks/Stock/History/List
    await common.clickCardItem(menuItem, '/ap/iPhone/Stocks/Stock/History/List', 'kcMemberSite');
});

Scenario('Test item No.18 Purchase amount', async () => {
    const menuItem = '//*[@data-testid="menu_availableAmount_id"]';
    // Go to {member-site-url}/ap/iphone/Assets/Kanougaku/Stock
    await common.clickCardItem(menuItem, '/ap/iphone/Assets/Kanougaku/Stock', 'kcMemberSite');
});

Scenario('Test item No.19 Deposit/Withdrawal Request', async () => {
    const menuItem = '//*[@data-testid="menu_depositRequest_id"]';
    // Go to /mobile/cashflow/depositrequest
    await common.clickCardItem(menuItem, '/mobile/cashflow/depositrequest', 'external');
});

Scenario('Test item No.20 Confirm deposits and withdrawals', async () => {
    const menuItem = '//*[@data-testid="menu_confirm_id"]';
    // Go to {member-site-url}/iPhone/Account/CashStatus/CashStatus.asp
    await common.clickCardItem(menuItem, '/iPhone/Account/CashStatus/CashStatus.asp', 'kcMemberSite');
});

Scenario('Test item No.21 Deposit transfer', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_depositTransfer_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to Deposit Transfer
    I.waitForText('お預り金振替', 3, 'body');
    I.seeElement('//*[@id="cash-input"]');
    await hamburgerMenu.compareUrl('/mobile/transfer/transfercash/cashinput');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.21_Deposit_transfer');
});

Scenario('Test item No.22 Investment Trust Transfer', async () => {
    const menuItem = '//*[@data-testid="menu_investmentTrustTransfer_id"]';
    // Go to {member-site-url}/iPhone/Trade/MarginSecurityInvestment/MSI1101.asp
    await common.clickCardItem(menuItem, '/iPhone/Trade/MarginSecurityInvestment/MSI1101.asp', 'kcMemberSite');
});

Scenario('Test item No.23 Domestic stock transfer', async () => {
    const menuItem = '//*[@data-testid="menu_domesticStock_id"]';
    // Go to {member-site-url}/iPhone/Trade/MarginSecurityStock/MSS1101.asp
    await common.clickCardItem(menuItem, '/iPhone/Trade/MarginSecurityStock/MSS1101.asp', 'kcMemberSite');
});

Scenario('Test item No.24 Specific/General Account Transfer', async () => {
    const menuItem = '//*[@data-testid="menu_specificAccount_id"]';
    // Go to {member-site-url}/iPhone/Trade/KabuSPKouza/KSK1101.asp
    await common.clickCardItem(menuItem, '/iPhone/Trade/KabuSPKouza/KSK1101.asp', 'kcMemberSite');
});

Scenario('Test item No.25 Procedure', async () => {
    const menuItem = '//*[@data-testid="menu_procedure_id"]';
    // Go to {member-site-url}/iPhone/Personal/SonotaShiryou/ss01101.asp
    await common.clickCardItem(menuItem, '/iPhone/Personal/SonotaShiryou/ss01101.asp', 'kcMemberSite');
});

Scenario('Test item No.26 Easy Electronic Contract', async () => {
    const menuItem = '//*[@data-testid="menu_easyContract_id"]';
    // Go to {member-site-url}/iphone/personal/dkstatus/dk01101.asp
    await common.clickCardItem(menuItem, '/iphone/personal/dkstatus/dk01101.asp', 'kcMemberSite');
});

Scenario('Test item No.27 Contact and notification settings first', async () => {
    const menuItem = '//*[@data-testid="menu_contactNotification_id"]';
    // {member-site-url}/ap/iPhone/personal/contact/List
    await common.clickCardItem(menuItem, '/ap/iPhone/personal/contact/List', 'kcMemberSite');
});

Scenario('Test item No.28 auID registration/change', async () => {
    const menuItem = '//*[@data-testid="menu_auIDRegister_id"]';
    // Web browser: {member-site-url}/iphone/personal/auID/auIDRegister.asp
    // Native app: /iphone/personal/auID/auIDRegister.asp"
    await common.clickCardItem(menuItem, 'auIDRegister.asp', 'external');
});

Scenario('Test item No.29 Money Connect', async () => {
    const menuItem = '//*[@data-testid="menu_moneyConnect_id"]';
    // Go to {member-site-url}/ap/iPhone/Personal/AutoSweepWithdrawalConfig/Input
    await common.clickCardItem(menuItem, '/ap/iPhone/Personal/AutoSweepWithdrawalConfig/Input', 'kcMemberSite');
});

Scenario('Test item No.30 App settings', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_appSetting_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to Settings/Applications-Other-App settings
    I.waitForText('アプリ設定', 3, 'body');
    await hamburgerMenu.compareUrl('/mobile/setting/app');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.30_Go_to_Settings_Applications_Other_App_settings');
});

Scenario('Test item No.34b Point usage status', async ({ I, hamburgerMenu }) => {
    // + A.Ponta point inquiry API.pontaPointStatus=PONTA_POINT_STATUS_NONAVAILABLE_UNREGISTERED:
    // Transition to {member-site-url}/ap/iphone/personal/pontapointdoui/agree
    const unregisterItem = '//button[contains(text(), "auID登録")]';
    await I.scrollToElement(unregisterItem);
    await common.clickCardItem(unregisterItem, '/ap/iphone/personal/pontapointdoui/agree', 'kcMemberSite');

    await I.switchToWeb();
    await I.waitFor();

    // + A.Ponta point inquiry API.pontaPointStatus=PONTA_POINT_STATUS_NONAVAILABLE_UNCONFIRM:
    // Transition to {member-site-url}/ap/iphone/personal/pontapointdoui/agree
    const unConfirmItem = '//button[contains(text(), "利用規約同意")]';
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user6 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await hamburgerMenu.openMenu();
    await I.scrollToElement(unConfirmItem);
    await common.clickCardItem(unConfirmItem, '/ap/iphone/personal/pontapointdoui/agree', 'kcMemberSite');

    await I.switchToWeb();
    await I.waitFor();

    // Other than the above: Transition to {member-site-url}/ap/iPhone/Personal/UsePoint/List
    const otherThanItem = '//button[contains(text(), "ポイント")]';
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user7 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await hamburgerMenu.openMenu();
    await I.scrollToElement(otherThanItem);
    await common.clickCardItem(otherThanItem, '/ap/iPhone/Personal/UsePoint/List', 'kcMemberSite');
});

Scenario('Test item No.34c Setting points to be used for savings', async ({ I, hamburgerMenu }) => {
    const settingPointsButton = '//button[p[contains(text(), "積立への")]]';
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user75 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await hamburgerMenu.openMenu();
    // Go to setting points to be used for savings
    await I.scrollToElement(settingPointsButton);
    await I.clickFixed(settingPointsButton);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl('/mobile/reserve/point-setting');
    await hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.34c_Go_to_Setting_points_to_be_used_for_savings');
});

Scenario('Test item No.35 Privacy Policy Open', async () => {
    const menuItem = '//*[@data-testid="menu_privacyPolicy_id"]';
    // https://kabu.com/sp/company/policy/privacy.html in a new tab
    await common.clickCardItem(menuItem, 'https://kabu.com/sp/company/policy/privacy.html', 'external');
});

Scenario('Test item No.36 License Agreement', async () => {
    const menuItem = '//*[@data-testid="menu_terms_id"]';
    // Open the following URL in a separate tab https://kabu.com/sp/app/rule/kabucom_app_terms.html
    await common.clickCardItem(menuItem, 'https://kabu.com/sp/app/rule/kabucom_app_terms.html', 'external');
});

Scenario('Test item No.37 Logout', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_logout_id"]';
    await I.scrollToElement(menuItem);
    await I.tap(menuItem);
    await I.waitFor('mediumWait');
    // Switch to native context for login againt
    await I.switchToNative().then(async () => {
        await hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.37_Go_to_logout_screen');
        await I.terminateAndActivateAppFixed();
        await I.waitFor('mediumWait');
        await I.login(process.env.LOGIN_USERNAME || '09109911', process.env.LOGIN_PASSWORD || '111111', true);
    });
});

Scenario('Test item No.38 Previous OP Navigation', async () => {
    const menuItem = '//*[@data-testid="menu_senOPNavi_id"]';
    // Open ${dsUrl} in a new tab
    await common.clickCardItem(menuItem, 'https://aukabucom.ds.dev.guide.inc/', 'external');
});

Scenario('Test item No.39 Futures Board Flash', async () => {
    const menuItem = '//*[@data-testid="menu_futureBoard_id"]';
    // Go to the following screen: {member-site-url}/iPhone/tradetool/KBF/KabucomFuturesBoardFlash.asp
    await common.clickCardItem(menuItem, '/iPhone/tradetool/KBF/KabucomFuturesBoardFlash.asp', 'kcMemberSite');
});

Scenario('Test item No.40 OP Board Flash', async () => {
    const menuItem = '//*[@data-testid="menu_opBoard_id"]';
    // Go to the following screen: {member-site-url}/iPhone/tradetool/KBF/KabucomOptionBoardFlash.asp
    await common.clickCardItem(menuItem, '/iPhone/tradetool/KBF/KabucomOptionBoardFlash.asp', 'kcMemberSite');
});

Scenario('Test item No.44 Operation Guide', async ({ I }) => {
    const helpModal = '//button[@aria-label="help"]';
    const menuItem = '//div[p[contains(text(), "操作ガイド")]]';
    await I.clickFixed(helpModal);
    await I.waitFor();
    // Open the following screen in a new tab https://kabu.com/sp_ssl_content/user_guide/index.html
    await common.clickCardItem(menuItem, 'https://kabu.com/sp_ssl_content/user_guide/index.html', 'external');
});

Scenario('Test item No.45 Customer Support', async ({ I }) => {
    const helpModal = '//button[@aria-label="help"]';
    const menuItem = '//div[p[contains(text(), "お客さまサポート")]]';
    await I.clickFixed(helpModal);
    await I.waitFor();
    // Open the following screen in a new tab https://kabu.com/sp_ssl_content/help/index.html
    await common.clickCardItem(menuItem, 'https://kabu.com/sp_ssl_content/help/index.html', 'external');
});

Scenario('Test item No.50 Kabucom iDeCo', async () => {
    const menuItem = '//*[@data-testid="menu_kabucomiDeCo_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/Ideco/Ideco/Top
    await common.clickCardItem(menuItem, '/ap/iPhone/Ideco/Ideco/Top', 'kcMemberSite');
});

Scenario('Test item No.52 abucom Calendar', async () => {
    const menuItem = '//*[@data-testid="menu_topicsCalendar_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/Calendar/Home/MonthlySP
    await common.clickCardItem(menuItem, '/ap/iPhone/Calendar/Home/MonthlySP', 'kcMemberSite');
});

Scenario('Test item No.55 License information, etc.', async () => {
    const menuItem = '//*[@data-testid="menu_licenseInfo_id"]';
    // Open the following screen in a new tab https://kabu.com/sp/info/investment_advisory.html
    await common.clickCardItem(menuItem, 'https://kabu.com/sp/info/investment_advisory.html', 'external');
});

Scenario('Test item No.56 Inquiry', async () => {
    const menuItem = '//*[@data-testid="menu_contact_id"]';
    // Go to the following screen: {member-site-url}/iphone/support/enquete/inquiry01.asp
    await common.clickCardItem(menuItem, '/iphone/support/enquete/inquiry01.asp', 'kcMemberSite');
});

Scenario('Test item No.58 Foreign Currency Deposit Transfer', async () => {
    const menuItem = '//*[@data-testid="menu_foreignDeposit_id"]';
    // Go to the following screen: {member-site-url}/ap/iPhone/CashFlow/Transfer/ForeignCash/Input
    await common.clickCardItem(menuItem, '/ap/iPhone/CashFlow/Transfer/ForeignCash/Input', 'kcMemberSite');
});

Scenario('Test item No.61 PC version trading site', async () => {
    const menuItem = '//*[@data-testid="menu_pcVersionTrading_id"]';
    // Open the following URL in a new tab {member-site-url}/members/
    await common.clickCardItem(menuItem, '/members/', 'external');
});

Scenario('Test item No.63 KabuBoard Flash', async () => {
    const menuItem = '//*[@data-testid="menu_kabuBoardFlash_id"]';
    // Go to the following screen: {member-site-url}/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp
    await common.clickCardItem(menuItem, '/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp', 'kcMemberSite');
});

Scenario('Test item No.47 Mitsubishi UFJ eSmart Securities FX', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_mitsubishiUFJSecurities_id"]';
    await I.swipeDirection('up');
    await hamburgerMenu.clickMenuItem(menuItem);
    I.waitForText('以下の内容をご確認ください。', 3, 'body');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.47_Display_dialog_transition_URL_according_to_the_process_described_in_the_credit_transaction_link_specifications');
});

Scenario('Test item No.48 Click 365', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_click365_id"]';
    await I.swipeDirection('up');
    await hamburgerMenu.clickMenuItem(menuItem);
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.48_Display_dialog_transition_URL_according_to_the_process_described_in_the_credit_transaction_link_specifications');
});

Scenario('Test item No.49 Exchange CFD', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_exchangeCFD_id"]';
    await I.swipeDirection('up');
    await hamburgerMenu.clickMenuItem(menuItem);
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.49_Display_dialog_transition_URL_according_to_the_process_described_in_the_credit_transaction_link_specifications');
});

Scenario('Test item No.60 Family and Friends Referrals', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_referralForFriends_id"]';
    // Swipe down to see the menu item
    await I.swipeUpFixed('body');
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to Friend Referrals TOP
    I.waitForText('ご家族・ご友人\n紹介プログラム', 3, 'body');
    await hamburgerMenu.compareUrl('/mobile/referral');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.60_Go_to_Friend_Referrals_TOP');
});

Scenario('Test item No.62 Security Setting', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_securitySetting_id"]';
    await I.waitForElement(menuItem, 2);
    await I.clickFixed(menuItem);
    await I.waitFor('mediumWait');
    await hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.62_Security_Setting');
});

Scenario('Test item No.64 Domestic Cash Stock', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_domesticCashStock_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to Search-StockSearch
    I.waitForText('国内現物株', 3, 'body');
    I.waitForText('銘柄検索（買注文）', 3, 'body');
    I.seeElement('//*[@id="searchPage"]');
    I.seeElement('//*[@data-testid="stockSearch_keywordInput_id"]');
    await hamburgerMenu.compareUrl('/mobile/search-stock');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.64_Domestic_Cash_Stock');
});

Scenario('Test item No.65 Margin Trading', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_marginTrading_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to MarginTradingSearchTOP
    I.waitForText('信用取引', 3, 'body');
    I.waitForText('銘柄検索（新規建）', 3, 'body');
    I.seeElement('//*[@id="searchPage"]');
    await hamburgerMenu.compareUrl('/mobile/search-margin');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.65_Margin_Trading');
});

Scenario('Test item No.66 US Stock', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_usStock_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to USStockSearchTOP
    I.waitForText('米国株', 3, 'body');
    I.waitForText('銘柄検索（買注文）', 3, 'body');
    I.seeElement('//*[@id="searchPage"]');
    I.seeElement('//*[@data-testid="searchUsStock_keywordInput_id"]');
    await hamburgerMenu.compareUrl('/mobile/search-usstock');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.66_US_Stock');
});

Scenario('Test item No.67 Investment trusts', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_investmentTrust_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to Fund Search
    await hamburgerMenu.compareUrl('/mobile/info/fund/search');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.67_Go_to_Fund_search');
});

Scenario('Test item No.68 Change Various Password', async ({ I, hamburgerMenu }) => {
    const menuItem = '//*[@data-testid="menu_changeVariousPassword_id"]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Navigate to ChangeVariousPassword
    I.waitForText('各種パスワード変更', 3, 'body');
    I.waitForText('パスワードを変更する項目を選択してください。', 3, 'body');
    await hamburgerMenu.compareUrl('/mobile/setting/password/list');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.68_Change_Various_Password');
});

Scenario('Test item No.69 Dividend and distribution history', async ({ I, hamburgerMenu }) => {
    const menuItem = '//div[a[.//p[contains(text(), "配当金・分配金履歴")]]]';
    await hamburgerMenu.clickMenuItem(menuItem);
    // Go to dividend history
    await hamburgerMenu.compareUrl('/mobile/dividend-history/list');
    hamburgerMenu.takeScreenshot.hamburgerMenu('Test_item_No.69_Go_to_dividend_history');
});
