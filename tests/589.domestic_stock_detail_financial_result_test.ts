import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockDetailFinancialResult');

Before(async ({ I, loginAndSwitchToWebAs, stockDetailBasicPage }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToDetailFinancialTab();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Detail Financial result', async ({ I, stockDetailBasicPage }) => {
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/financial');
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Display_Domestic_Stock_Detail_Financical_result');
});

Scenario('Test item No.2 Previous', async ({ I, stockDetailBasicPage }) => {
    const previousButtonSelector = '//div[contains(@class, "slick-prev")]';
    await I.clickFixed(previousButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.infoRootContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/quarterly');
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.2_Click_previous_go_to_Detail_Quarterly_tab');
});

Scenario('Test item No.3 Detail Quarterly tab', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.quarterlyTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.infoRootContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/quarterly');
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.3_Click_Detail_Quarterly_tab');
});

Scenario('Test item No.5 Next', async ({ I, stockDetailBasicPage }) => {
    const nextButtonSelector = '//div[contains(@class, "slick-next")]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.newsContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/news');
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.5_Click_next_go_to_detail_News_tab');
});

Scenario('Test item No.4 News tab', async ({ I, stockDetailBasicPage }) => {
    await I.clickFixed(stockDetailBasicPage.locator.newsTab);
    await I.waitFor();
    I.seeElement(stockDetailBasicPage.locator.newsContainer);
    await stockDetailBasicPage.compareUrl('/mobile/info/stock/news');
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.4_Click_News_tab');
});

Scenario('Test item No.42 Rating tooltip', async ({ I, stockDetailBasicPage }) => {
    const ratingSelector = '//*[@data-testid="stockFinancial_rating_id"]';
    const dialogSelector = '//section[@role="dialog"]';
    await I.clickFixed(ratingSelector);
    await I.waitFor();
    I.seeElement(dialogSelector);
    I.see('アナリスト予想の平均', dialogSelector);
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.42_Open_Rating_tooltip');
});

Scenario('Test item No.52 Target stock price tooltip', async ({ I, stockDetailBasicPage }) => {
    const targetStockPriceSelector = '//*[@data-testid="stockFinancial_targetStockPrice_id"]';
    const dialogSelector = '//section[@role="dialog"]';
    await I.clickFixed(targetStockPriceSelector);
    await I.waitFor();
    I.seeElement(dialogSelector);
    I.see('前週からの変化率', dialogSelector);
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.52_Open_Target_stock_price_tooltip');
});

Scenario('Test item No.6 Scroll Financial Information', async ({ I, stockDetailBasicPage }) => {
    await I.swipeUpFixed(stockDetailBasicPage.locator.financialInfoContainer);
    await I.waitFor();
    stockDetailBasicPage.takeScreenshot.stockDetailFinancialResult('Test_item_No.6_Scroll_Financial_information');
});
