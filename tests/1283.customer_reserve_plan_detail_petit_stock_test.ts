import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from '../pages/search/common';
Feature('Reserve - CustomerReservePlanDetailPetitStock');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user47 }); //08309911
    I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});
// // ReservePlanDetail-PetitStock
Scenario('Test Item 0: Check UI  Petit Stocks plan detail page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    // Find div  contains span with text "プチ株" and click on it
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reservePlan_reserve")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    I.clickFixed(reservePlanDetailId);

});
Scenario('Test Item 1: Check Go To Savings Calendar', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const reserveCalendarTab = '//*[@data-testid="reservePlan_reserveCalendar_tab"]';
    I.clickFixed(reserveCalendarTab);
    I.saveScreenshot('1250.Test_item_No.1_Go_To_Savings_Calendar.png');
});
Scenario('Test Item 2: Check Go To Savings History', async ({ I }) => {

    const reserveHistoryTab = '//*[@data-testid="reservePlan_reserveHistory_tab"]';
    I.clickFixed(reserveHistoryTab);
    I.saveScreenshot('1250.Test_item_No.2_Go_To_Savings_History.png');
});
Scenario('Test Item 12: Check Go To Individual Symbol Information', async ({ I, accumulation }) => {
    // TODO: need to check again
    await accumulation.goToCustomerReservePlanPage();
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    // Find div  contains span with text "プチ株" and click on it
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reservePlan_reserve")][1]';
    await I.waitFor('shortWait');
    I.scrollAndClick(petitStockItem);
    await I.waitFor('shortWait');
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    const individualSymbolInfoTab = '//*[@data-testid="reservePlan_individualStockInformation_id"]';
    await I.waitFor('shortWait');
    I.scrollAndClick(individualSymbolInfoTab);
    await I.waitFor('shortWait');
    I.saveScreenshot('1250.Test_item_No.12_Go_To_Individual_Symbol_Information.png');

});
Scenario('Test Item 21: Check Date specified by our company', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReservePlanPage();
    const fundItem = '//*[@data-testid="reservePlan_reservePetitDetail_0_id"]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    const pointUsageLink = '//span[contains(text(), "当社指定日")]';
    I.waitForElement(pointUsageLink, 2);
    await common.clickCardItem(pointUsageLink, 'kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
    I.saveScreenshot('1250.Test_item_No.28_Go_To_Point_Usage.png');
});