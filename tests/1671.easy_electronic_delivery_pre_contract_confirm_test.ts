import { COOKIE_KEY, USER_ID } from '../const/constant';
import electronicDelivery from '../pages/electronicDelivery';

Feature('Settings_Entry - EasyElectronicDelivery');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await <PERSON>.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user42 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Electronic Delivery Pre Contract Confirm', async ({ I }) => {
    await electronicDelivery.goToNoticePage();
    I.saveScreenshot(`${electronicDelivery.prefix.notice}.0_Check_UI_Electronic_Delivery_Pre_Contract_Confirm.png`);
});

// 2.「契約締結前交付書面等」の再同意手続き画面へ -> 契約締結前交付書面等」の再同意手続き画面に遷移する
Scenario(
    'Test Item 2: Go to the re-conciliation procedure screen for "Request for the documents before contract conclusion',
    async ({ I }) => {
        await electronicDelivery.goToAgreementPage();
        I.saveScreenshot(
            `${electronicDelivery.prefix.notice}.2_Go_to_the_re-conciliation_procedure_screen_for_"Request_for_the_documents_before_contract_conclusion.png`,
        );
        await I.seeAndClickElement(electronicDelivery.locators.commonBack);
    },
);

// 3.後で手続きをする(トップページへ) -> 資産状況_資産ポートフォリオTOPへ遷移する
Scenario('Test Item 3: Follow the procedure later (Go to the top page)', async ({ I }) => {
    await I.seeAndClickElement(electronicDelivery.locators.backToTopButton);
    I.seeInCurrentUrl(electronicDelivery.urls.portfolio);
    I.saveScreenshot(`${electronicDelivery.prefix.notice}.3_Follow_the_procedure_later_(Go_to_the_top_page).png`);
});
