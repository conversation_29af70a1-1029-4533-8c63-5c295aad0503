import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginTradingPaymentOrderCompleted');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user33 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Trading New Order completed', async ({ stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    await stockMarginPaymentOrder.compareUrl('/mobile/trade/margin/repayment/complete');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Display_Domestic_Stock_Margin_Trading_New_Order_completed',
    );
});

Scenario('Test item No.1 Order number', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    const orderNumberSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    const orderInquiryDetailSelector = '#order-inquiry-detail';
    await I.clickFixed(orderNumberSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquiryDetailSelector);
    await stockMarginPaymentOrder.compareUrl('/mobile/order-inquiry/margin/detail');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Test_item_No.1_Tap_Order_number_to_transition_to_details_page',
    );
});

Scenario('Test item No.2 Order Inquiry', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    const orderInquiryButtonSelector = '//*[@data-testid="complete_orderStatusButton_id"]';
    const orderInquirySelector = '#order-inquiry';
    await I.clickFixed(orderInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquirySelector);
    await stockMarginPaymentOrder.compareUrl('/mobile/order-inquiry/margin');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Test_item_No.2_Tap_Order_Inquiry_to_transition_to_order_list_page',
    );
});

Scenario('Test item No.3 Balance inquiry', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    const balanceInquiryButtonSelector = '//*[@data-testid="complete_positionInquiryButton_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(balanceInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(positionInquirySelector);
    await stockMarginPaymentOrder.compareUrl('/mobile/position-inquiry/margin');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Test_item_No.3_Tap_Balance_inquiry_to_transition_to_balance_list_page',
    );
});

Scenario('Test item No.4 Order number - Tokyo', async ({ I, stockMarginPaymentOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user84 });
    await I.waitFor();
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    const orderTokyoIdSelector = '//*[@data-testid="complete_orderIdTokyo_id"]';
    const orderInquiryDetailSelector = '//*[@id="order-inquiry-detail"]';
    await I.clickFixed(orderTokyoIdSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquiryDetailSelector);
    await stockMarginPaymentOrder.compareUrl('/mobile/order-inquiry/margin/detail');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Test_item_No.4_Tap_Order_number_Tokyo_to_transition_to_details_page',
    );
});

Scenario('Test item No.5 Order number - Tokyo+', async ({ I, stockMarginPaymentOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user84 });
    await I.waitFor();
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    const orderTokyoPlusIdSelector = '//*[@data-testid="complete_orderIdTokyoPlus_id"]';
    const orderInquiryDetailSelector = '//*[@id="order-inquiry-detail"]';
    await I.clickFixed(orderTokyoPlusIdSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquiryDetailSelector);
    await stockMarginPaymentOrder.compareUrl('/mobile/order-inquiry/margin/detail');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Test_item_No.5_Tap_Order_number_Tokyo_plus_to_transition_to_details_page',
    );
});

Scenario('Test Back to browser', async ({ I, stockMarginPaymentOrder }) => {
    await stockMarginPaymentOrder.goToMarginPaymentOrderCompleted();
    await I.performBrowserBack();
    await I.waitFor();
    I.seeElement('#searchPage');
    await stockMarginPaymentOrder.compareUrl('/mobile/search');
    stockMarginPaymentOrder.takeScreenshot.marginPaymentOrderCompleted(
        'Test_Back_to_browser_to_transition_to_general_search_page',
    );
});
