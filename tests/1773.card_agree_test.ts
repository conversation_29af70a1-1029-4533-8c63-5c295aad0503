Feature('Settings_Entry - CardAgreePage');
import { COOKIE_KEY, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE, USER_ID } from '../const/constant';
import common from '../pages/search/common';
import SettingCard from '../pages/settingCard';

const locators = SettingCard.locators;

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user60 });
    I.setCookie({ name: COOKIE_KEY.siteId, value: '1' });
    await I.setSessionStorage(SESSION_STORAGE_KEY.storedStateSettingCardAgree, SESSION_STORAGE_VALUE.storedStateSettingCardAgree({ aupayErrorCode: undefined }));
});

After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I<PERSON>closeBrowser();
});

Scenario('Go to card agree page', () => {
    SettingCard.goToPageCardAgree();
    SettingCard.takeScreenshot('1773_card_agree_page.png');
});

Scenario('Test item 1 Terms', async () => {
    await SettingCard.clickPdfUrl(locators.term);
    SettingCard.takeScreenshot('1773_Test_Item_1_Terms.png');
});

Scenario('Test item 2 Term HTML', async ({ I }) => {
    SettingCard.takeScreenshot('1773_Test_Item_2_Term_HTML_1.png');

    // Swipe up html
    await I.swipeUpFixed(locators.termHTML);
    await I.waitFor();
    SettingCard.takeScreenshot('1773_Test_Item_2_Term_HTML_2.png');

    // Swipe down html
    await I.swipeDownFixed(locators.termHTML);
    await I.waitFor();
    SettingCard.takeScreenshot('1773_Test_Item_2_Term_HTML_3.png');
});

Scenario('Test item 3 Agreement document', async () => {
    await SettingCard.clickPdfUrl(locators.agreeDocument);
    SettingCard.takeScreenshot('1773_Test_Item_3_Agreement_document.png');
});

Scenario('Test item 4 Agreement Document HTML', async ({ I }) => {
    await I.swipeUpFixed(locators.termHTML);
    SettingCard.takeScreenshot('1773_Test_Item_4_Agreement_Document_HTML_1.png');

    // Swipe up html
    await I.scrollToElement(locators.agreeDocumentHTML);
    await I.swipeUpFixed(locators.agreeDocumentHTML);
    await I.waitFor();
    SettingCard.takeScreenshot('1773_Test_Item_4_Agreement_Document_HTML_2.png');

    // Swipe down html
    await I.swipeDownFixed(locators.agreeDocumentHTML);
    await I.waitFor();
    SettingCard.takeScreenshot('1773_Test_Item_4_Agreement_Document_HTML_3.png');
});

Scenario('Test item 5 Agreement Check', async ({ I }) => {
    await I.scrollToElement(locators.agreeCheck);
    await SettingCard.clickItem(locators.agreeCheck);
    SettingCard.takeScreenshot('1773_Test_Item_5_Agreement_Check_1.png');
    await SettingCard.clickItem(locators.agreeCheck);
    SettingCard.takeScreenshot('1773_Test_Item_5_Agreement_Check_2.png');
});

Scenario('Test item 6 Next', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user63 });
    await I.waitFor();
    await SettingCard.goToPageCardAgree();
    await I.swipeElementDirection('up', 'body');
    await SettingCard.clickItem(locators.agreeCheck);
    await SettingCard.clickItem(locators.nextBtn);
    await I.waitFor('mediumWait');
    // Proceed to the confirmation screen
    I.seeInCurrentUrl('/mobile/setting/card/confirm');
    await SettingCard.takeScreenshot('1773_Test_Item_6_Proceed_to_the_confirmation_screen.png');
});

Scenario('Test item 7 Create au PAY Card', async ({ I }) => {
    await I.waitFor();
    SettingCard.goToPageCardAgree();
    await I.swipeElementDirection('up', 'body');
    SettingCard.clickItem(locators.agreeCheck);
    SettingCard.clickItem(locators.nextBtn);
    await SettingCard.clickExternalUrl(locators.aupayCard, locators.lp200Url);
    SettingCard.takeScreenshot('1773_Test_Item_7_Create_au_PAY_Card.png');
});

Scenario('Test item 8 Check your au ID for your securities account', async ({ I }) => {
    await SettingCard.goToPageCardAgree();
    await I.swipeElementDirection('up', 'body');
    await SettingCard.clickItem(locators.agreeCheck);
    await SettingCard.clickItem(locators.nextBtn);
    await I.waitFor('mediumWait');
    // Open the following URL in a new tab: {member-site-url}/members/personal/kokyakucard.asp
    await common.clickCardItem('//*[@data-testid="settingCardAgreePage_checkAccount_id"]', '/members/personal/kokyakucard.asp', 'external');
});

Scenario('Test item 9 Confirmation Method', async () => {
    await SettingCard.clickExternalUrl(locators.confirmMethod, locators.faqSiteUrl);
    SettingCard.takeScreenshot('1773_Test_Item_9_Confirmation_Method.png');
});

Scenario('Test item 10 Create New au PAY Card', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user61 });
    await I.waitFor();
    SettingCard.goToPageCardAgree();
    await I.swipeElementDirection('up', 'body');
    SettingCard.clickItem(locators.agreeCheck);
    SettingCard.clickItem(locators.nextBtn);
    await SettingCard.clickExternalUrl(locators.createNewAupayCard, locators.lp200Url);
    SettingCard.takeScreenshot('1773_Test_Item_10_Create_New_au_PAY_Card.png');
});

Scenario('Test item 11 QA', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user62 });
    await I.waitFor();
    SettingCard.goToPageCardAgree();
    await I.swipeElementDirection('up', 'body');
    SettingCard.clickItem(locators.agreeCheck);
    SettingCard.clickItem(locators.nextBtn);
    await SettingCard.clickExternalUrl(locators.cardAgreeQA, locators.faqKabuUrl);
    SettingCard.takeScreenshot('1773_Test_Item_11_QA.png');
});

Scenario('Test item 12 Securities account registration information', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user62 });
    await I.waitFor();
    SettingCard.goToPageCardAgree();
    await I.swipeElementDirection('up', 'body');
    await SettingCard.clickItem(locators.agreeCheck);
    await SettingCard.clickItem(locators.nextBtn);
    await I.waitFor('mediumWait');
    // Open the following URL in a new tab: {member-site-url}/members/personal/kokyakucard.asp
    await common.clickCardItem('//*[@data-testid="settingCardAgreePage_securitiesAccountRegistrationInfo_id"]', '/members/personal/kokyakucard.asp', 'external');
});

Scenario('Test item 13 Click here for au PAY card', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user62 });
    await I.waitFor();
    await SettingCard.clickExternalUrl(locators.cardAgreeConfirm, locators.kddifsUrl);
    SettingCard.takeScreenshot('1773_Test_Item_13_Click_here_for_au_PAY_card.png');
});
