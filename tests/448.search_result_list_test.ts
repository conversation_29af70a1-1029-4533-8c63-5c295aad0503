import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('Symbol_ProductSearch - SearchResultList');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Search Result list page', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search/result', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Display_Search_Result_list_page.png`);
});

<PERSON>ena<PERSON>('Test item No.1 Keyword input', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultKeywordInputSelector = '//*[@data-testid="searchResult_keywordInput_id"]//input';
    const searchResultListSelector = '//*[@data-testid="searchResult_symbolList_id"]';
    await I.clickFixed(searchResultKeywordInputSelector);
    I.assertEqual(await I.isKeyboardShown(), true, 'The keyboard must be shown');
    I.dontSeeElement(searchResultListSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.1_Show_Keyword_input_and_transition_to_stock_product_search_screen.png`);
});

Scenario('Test item No.3 Search result stock items (domestic stocks)', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultListSelector = '//*[@data-testid="searchResult_symbolList_id"]';
    const firstDomesticStockItem = `${searchResultListSelector}//*[@data-testid="searchResult_domesticStockItem_id_0"]`
    await I.clickFixed(firstDomesticStockItem);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/info/stock/basic', 'URL does not contain expected path');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.3_Click_on_domestic_stock_item_go_to_chart_display.png`);
});

Scenario('Test item No.3-1 Favorites Domestic Stock item', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const favoriteButton = '//div[starts-with(@data-testid, "searchResult_domesticStockItem_id_")]/div[1]';
    const favoriteModal = '//*[@data-testid="common_rightSlide_favorite_id"]';
    const addButton = '//*[@data-testid="favoriteRegisterModal_listAdd_id"]';
    const confirmButton = '//*[@data-testid="favoriteRegisterModal_confirm_id"]';
    await I.clickFixed(favoriteButton);
    await I.waitFor();
    I.seeElement(favoriteModal);
    I.seeElement(addButton);
    I.seeElement(confirmButton);
    I.see('お気に入り追加', favoriteModal);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.3-1_Open_Favorites_Domestic_stock_item_modal.png`);
});

Scenario('Test item No.4 Search result stock items (US stocks)', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user17 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultListSelector = '//*[@data-testid="searchResult_symbolList_id"]';
    const firstUSStockItem = `${searchResultListSelector}//div[starts-with(@data-testid, "searchResult_usStockItem_id_")]`;
    // Go to the following URL: {member-site-url}/ap/iPhone/InvInfo/USMarket/StockDetail/Default?Ticker=${Ticker}
    await common.clickCardItem(firstUSStockItem, '/mobile/info/usstock/summary');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.4_Go_to_US_stock_summary.png`);
});

Scenario('Test item No.4-1 Favorites US Stock item', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user17 });
    await search.goToPage();
    await search.goToSearchResult();
    const favoriteButton = '//div[starts-with(@data-testid, "searchResult_usStockItem_id_")]/div[1]';
    const favoriteModal = '//*[@data-testid="common_rightSlide_favorite_id"]';
    const addButton = '//*[@data-testid="favoriteRegisterModal_listAdd_id"]';
    const confirmButton = '//*[@data-testid="favoriteRegisterModal_confirm_id"]';
    await I.clickFixed(favoriteButton);
    await I.waitFor();
    I.seeElement(favoriteModal);
    I.seeElement(addButton);
    I.seeElement(confirmButton);
    I.see('お気に入り追加', favoriteModal);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.4-1_Open_Favorites_US_Stock_item_modal.png`);
});

Scenario('Test item No.5 Search result stock item (mutual fund)', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultListSelector = '//*[@data-testid="searchResult_symbolList_id"]';
    const firstMutualFundItem = `${searchResultListSelector}//div[starts-with(@data-testid, "searchResult_investmentTrustItem_id_")]`;
    // US stock stock information - transition to summary
    await common.clickCardItem(firstMutualFundItem, '/mobile/info/fund/detail');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.5_Go_to_fund_detail.png`);
});

Scenario('Test item No.5-1 Favorites Investment Trust item', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const favoriteButton = '//div[starts-with(@data-testid, "searchResult_investmentTrustItem_id_")]/div[1]';
    const favoriteModal = '//*[@data-testid="common_rightSlide_favorite_id"]';
    const addButton = '//*[@data-testid="favoriteRegisterModal_listAdd_id"]';
    const confirmButton = '//*[@data-testid="favoriteRegisterModal_confirm_id"]';
    await I.clickFixed(favoriteButton);
    await I.waitFor();
    I.seeElement(favoriteModal);
    I.seeElement(addButton);
    I.seeElement(confirmButton);
    I.see('お気に入り追加', favoriteModal);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.5-1_Open_Favorites_Investment_Trust_item_modal.png`);
});

Scenario('Test item No.7 Search result stock item (US stocks) - Uncontracted', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultListSelector = '//*[@data-testid="searchResult_symbolList_id"]';
    const firstUSStockUncontractedItem = `${searchResultListSelector}//div[starts-with(@data-testid, "searchResult_usStockItem_id_")]`;
    // Open the following URL in a new tab {member-site-url}/members/personal/dkstatus/dk01101.asp#tradeservice
    await common.clickCardItem(firstUSStockUncontractedItem, '/members/personal/dkstatus/dk01101.asp#tradeservice', 'external');
});

Scenario('Test item No.8 Load more', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultList = '//*[@data-testid="searchResult_symbolList_id"]';
    const loadMoreButtonSelector = '//*[@data-testid="searchResult_readMore_id"]';
    await I.swipeUpFixed(searchResultList);
    await I.waitFor();
    await I.clickFixed(loadMoreButtonSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.8_Click_load_more_button.png`);
});

Scenario('Test item No.2 Swipe up or down search result stock list', async ({ I, search }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user16 });
    await search.goToPage();
    await search.goToSearchResult();
    const searchResultList = '//*[@data-testid="searchResult_symbolList_id"]';
    await I.swipeUpFixed(searchResultList);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.2_Swipe_up_search_result_stock_list.png`);
    await I.swipeDownFixed(searchResultList);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.searchResultList}_Test_item_No.2_Swipe_down_search_result_stock_list.png`);
});
