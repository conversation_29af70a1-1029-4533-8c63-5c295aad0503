import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Timeline - TimeLineIndividualPage');

Before(async ({ I, loginAndSwitchToWebAs, timeLine }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user72 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    // Navigate to Time Line Top page
    await timeLine.goToTimeLineIndividual();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Time Line Individual page', async ({ I }) => {
    I.assertContain(await I.grabCurrentUrl(), '/mobile/timeline/detail', 'URL is not matching');
    await I.saveScreenshot('137.time_line_individual_page_Test_Display_Time_Line_Individual_page.png');
});
