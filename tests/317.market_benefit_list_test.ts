import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import common from '../pages/search/common';

Feature('Market - BenefitList');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user14 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Market Benefit List page', async ({ I, market }) => {
    await I.switchToWeb();
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_Display_Market_Benefit_List.png`);
});

Scenario('Test item No.2 Shareholder Benefits Search', async ({ I, market }) => {
    await market.goToShareholderBenefitsSearch();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.2_Shareholder_Benefits_Search.png`);
});

Scenario('Test item No.3 About shareholder benefits', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.clickFixed('//*[@data-testid="benefitList_benefit_id"]');
    await I.waitFor();
    I.seeElement('#bottom-sheet-container');
    I.seeElement('//*[@data-testid="benefitList_benefitClose_id"]');
    I.waitForText('株主優待とは', 3, '#bottom-sheet-container');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.3_About_shareholder_benefits.png`);
});

Scenario('Test item No.4 Introduction to shareholder benefits', async ({ market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    const item = '//*[@data-testid="benefitList_benefitIntroduction_id"]';
    // Open the following URL in a new tab: https://kabu.com/kabuyomu/money/43.html
    await common.clickCardItem(item, 'https://kabu.com/kabuyomu/money/43.html', 'external');
});

Scenario('Test item No.5 Shareholder Benefits Q&A', async ({ market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    const item = '//*[@data-testid="benefitList_benefitQA_id"]';
    // Open the following URL in a new tab: https://faq.kabu.com/s/global-search/%E6%A0%AA%E4%B8%BB%E5%84%AA%E5%BE%85"
    await common.clickCardItem(item, 'https://faq.kabu.com/s/global-search', 'external');
});

Scenario('Test item No.6 Benefits Calendar', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.clickFixed('//*[@data-testid="benefitList_benefitCalendar_id"]');
    await I.waitFor('mediumWait');
    I.seeElement('#bottom-sheet-container');
    I.see("優待カレンダー", '#bottom-sheet-container');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.6_Benefits_Calendar.png`);
    await I.clickFixed('//button[@aria-label="cancel-btn"]');
});

Scenario('Test item No.7 Get benefits wisely', async ({ market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    const item = '//*[@data-testid="benefitList_benefitGet_id"]';
    // Open the following URL in a new tab: https://kabu.com/kabuyomu/money/320.html"
    await common.clickCardItem(item, 'https://kabu.com/kabuyomu/money/320.html', 'external');
});

Scenario('Test item No.8 Month selection tab', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    const unSelectedMonthSelector = 'button[data-testid*="benefitList_monthSelection_id_"][aria-selected="false"]';
    const selectedMonMonthSelector = 'button[data-testid*="benefitList_monthSelection_id_"][aria-selected="true"]';
    const visibleUnSelectedButtons = await market.getVisibleButtons(unSelectedMonthSelector);
    await I.clickFixed(visibleUnSelectedButtons[0]);
    await I.waitFor();
    const selectedMonthColor = await I.grabCssPropertyFrom(selectedMonMonthSelector, "color");
    I.assertEqual(selectedMonthColor, "rgba(255, 86, 0, 1)");
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.8_Month_selection_tab.png`);
});

Scenario('Test item No.8-1 "<" and ">" buttons', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();

    const selectedMonthSelector = '//*[starts-with(@data-testid, "benefitList_monthSelection_id_")][contains(@aria-selected, "true")]';
    const previousMonthButton = '//div[contains(@class, "slick-prev")]';
    const nextMonthButton = '//div[contains(@class, "slick-next")]';

    // Helper function to get the currently selected month
    const getSelectedMonth = async () => {
        const months = await I.grabTextFromAll(selectedMonthSelector);
        return months.find((item) => item.length);
    };

    // Initial
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.8-1_Initial_selected_month.png`);
    const initialMonth = await getSelectedMonth();

    // Click "<" button
    await I.clickFixed(previousMonthButton);
    await I.waitFor('mediumWait');
    const previousMonth = await getSelectedMonth();
    I.assertNotEqual(initialMonth, previousMonth, 'The selected month should change to the previous month.');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.8-1_Change_previous_month.png`);

    // Click ">" button
    await I.clickFixed(nextMonthButton);
    await I.waitFor('mediumWait');
    const nextMonth = await getSelectedMonth();
    I.assertEqual(initialMonth, nextMonth, 'The selected month should return to the initial month.');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.8-1_Change_next_month.png`);
});

Scenario('Test item No.10 Details of preferential stocks', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.clickFixed('//*[@data-testid="benefitList_benefitDetail_id"]');
    await I.waitFor();
    I.seeElement('//*[@data-testid="benefitDetail_symbolInfo_id"]');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/benefit/detail', 'URL is not matching');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.10_Details_of_preferential_stocks.png`);
});

Scenario('Test item No.19 Favorites', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    await I.clickFixed('//*[@data-testid="benefitList_benefitFavorite_id"]');
    await I.waitFor();
    I.seeElement('//*[@data-testid="common_rightSlide_favorite_id"]');
    I.waitForText('お気に入り追加', 3, 'body');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.19_Show_favorite_registration_modal.png`);
});

Scenario('Test item No.20 Close modal what are shareholder benefits?', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    const closeButtonSelector = '//*[@data-testid="benefitList_benefitClose_id"]';
    await I.clickFixed('//*[@data-testid="benefitList_benefit_id"]');
    await I.waitFor();
    I.seeElement('#bottom-sheet-container');
    I.seeElement(closeButtonSelector);
    I.waitForText('株主優待とは', 3, '#bottom-sheet-container');
    await I.clickFixed(closeButtonSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.20_Close_modal_what_are_shareholder_benefits.png`);
});

Scenario('Test item No.9 Swipe up and down list of preferential stocks', async ({ I, market }) => {
    // Navigate to Benefit List page
    await market.goToMarketBenefitList();
    const benefitListSelector = '//*[@data-testid="benefitList_benefitSearch_id"]/ancestor::*[3]';
    await I.swipeUpFixed(benefitListSelector);
    await I.waitFor();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.9_Swipe_up_list_of_preferential_stocks.png`);
    await I.swipeDownFixed(benefitListSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketBenefitList}_Test_item_No.9_Swipe_down_list_of_preferential_stocks.png`);
});
