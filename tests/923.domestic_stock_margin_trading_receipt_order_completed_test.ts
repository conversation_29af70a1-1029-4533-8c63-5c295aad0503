import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginReceiptOrderCompleted');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginReceiptOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginReceiptOrder.goToMarginReceiptOrderCompleted();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Receipt Order Completed', async ({ I, stockMarginReceiptOrder }) => {
    I.assertContain(
        await I.grabCurrentUrl(),
        stockMarginReceiptOrder.urls.marginReceiptOrderCompleted,
        'URL does not contain expected path',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderCompleted(
        'Display_Domestic_Stock_Margin_Trading_Receipt_Order_Completed',
    );
});

Scenario('Test item No.1 Order number', async ({ I, stockMarginReceiptOrder }) => {
    const orderNumberSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    await I.clickFixed(orderNumberSelector);
    await I.waitFor('mediumWait');
    I.assertContain(
        await I.grabCurrentUrl(),
        '/mobile/order-inquiry/margin/detail-receipt-delivery',
        'URL does not contain expected path',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderCompleted(
        'Test_item_No.1_Tap_order_number_to_Transition_to_details_receipt_and_delivery_screen',
    );
});

Scenario('Test item No.2 Order Inquiry', async ({ I, stockMarginReceiptOrder }) => {
    const orderStatusButtonSelector = '//*[@data-testid="complete_orderStatusButton_id"]';
    await I.clickFixed(orderStatusButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/order-inquiry/margin', 'URL does not contain expected path');
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderCompleted(
        'Test_item_No.2_Tap_order_inquiry_to_Transition_to_credit_list_screen',
    );
});

Scenario('Test item No.3 Balance inquiry', async ({ I, stockMarginReceiptOrder }) => {
    const positionInquiryButtonSelector = '//*[@data-testid="complete_positionInquiryButton_id"]';
    await I.clickFixed(positionInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/position-inquiry/margin', 'URL does not contain expected path');
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderCompleted(
        'Test_item_No.3_Proceed_to_the_balance_inquiry_domestic_stock_credit_list_screen',
    );
});

Scenario('Test Back to browser', async ({ I, stockMarginReceiptOrder }) => {
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/search', 'URL does not contain expected path');
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderCompleted(
        'Back_to_browser_to_transition_to_general_search_page',
    );
});
