import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Notify - NotifyWholeListPage');

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyWholeList.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user10 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await <PERSON><PERSON>closeBrowser();
    await I.switchToNative();
});

Scenario('test notify whole list page', async ({ I }) => {
    I.waitForElement('//*[@data-testid="common_noti_id"]');
    I.click('//*[@data-testid="common_noti_id"]');
    await I.waitFor('mediumWait');
    I.saveScreenshot('235.notify_whole_list_notifyTab.png');

    const newInformationTab = '//*[@data-testid="noticeTab_newInformation_id"]';
    I.waitForElement(newInformationTab);
    I.click(newInformationTab);
    await I.waitFor();
    I.saveScreenshot('235.notify_whole_list_newInformationTab.png');
});

Scenario('test Item 3 Noti item page', async ({ I }) => {
    const notiItem = '//*[@data-testid="notifyWholeList_notiItem_id_0"]';
    I.waitForElement(notiItem);
    I.click(notiItem);
    await I.waitFor();
    I.saveScreenshot('235.notify_whole_list_notiItem.png');
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, 'notice/general/detail');
    I.click('//*[@data-testid="common_back_id"]');
});

Scenario('test Item 7 Scroll up to top page', async ({ I }) => {
    await I.waitFor();
    I.swipeUpFixed('//*[@data-testid="notifyWholeList_notiItem_id_0"]');
    I.waitForElement('//button[@aria-label="scroll-to-top-btn"]');
    I.click('//button[@aria-label="scroll-to-top-btn"]');
});
