import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioOptionPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to option page
    await portfolio.top.goToPage();
    await portfolio.top.option();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio option page', async ({ I }) => {
    const { common, option } = portfolio;

    await common.verifyProductName(option.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioOptionPage}_layout.png`);
});

Scenario('test portfolio option page [11.銘柄カード]', async ({ I }) => {
    const { common, option } = portfolio;

    await common.clickSymbolCard(option.productType, '日経平均オプション');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioOptionPage}_11_symbol_card.png`);
});

Scenario('test portfolio option page [20.新規注文]', async ({ I }) => {
    const { common, option } = portfolio;

    await common.clickSymbolCard(option.productType, '日経平均オプション');
    await option.newOrder('133025');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioOptionPage}_20_new_order.png`);
});

Scenario('test portfolio option page [21.残高照会]', async ({ I }) => {
    const { common, option } = portfolio;

    await common.clickSymbolCard(option.productType, '日経平均オプション');
    await option.positionInquiry();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioOptionPage}_14_position_inquiry.png`);
});
