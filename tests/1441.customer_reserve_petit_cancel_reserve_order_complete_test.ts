import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Reserve - CustomerReservePetitCancelReserveOrderComplete');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON><PERSON>setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user50 }); //09109903
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});
Scenario('Test Item 0: Check UI Reserve Petit CancelReserveOrder Complete', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderCompletePage();
    I.saveScreenshot('1441.Test_item_No.0_Check_UI_Reserve_Petit_CancelReserveOrder_Complete.png');
});
Scenario('Test Item 1: Tap Reserve Plan', async ({ I }) => {
    const reservePlanButton = '//*[@data-testid="reserveCommon_reservePlan_id"]';
    await I.waitForElement(reservePlanButton, 3);
    I.clickFixed(reservePlanButton);
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1441.Test_item_No.1_Tap_Reserve_Plan.png');
});

Scenario('Test Item 2: Tap Reserve Calendar', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderCompletePage();

    const reserveCalendarButton = '//*[@data-testid="reserveCommon_reserveCalendar_id"]';
    await I.waitForElement(reserveCalendarButton, 3);
    I.clickFixed(reserveCalendarButton);
    I.see('積立カレンダー', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1441.Test_item_No.2_Tap_Reserve_Calendar.png');
});
Scenario('Test Browser back', async ({ I, accumulation }) => {
    await accumulation.goToReservePetitCancelReserveOrderCompletePage();
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1441.Test_item_Browser_back.png');
});