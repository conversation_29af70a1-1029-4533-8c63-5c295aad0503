import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockPetitTradingSellOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs, stockPetitOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user32 });
    await stockPetitOrder.goToPetitSellOrderConfirm('8306');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Petit Trading Sell Order confirm', async ({ I, stockPetitOrder }) => {
    await stockPetitOrder.compareUrl('/mobile/trade/petit/sell/confirm');
    stockPetitOrder.takeScreenshot.petitSellOrderConfirm('Display_Domestic_Stock_Petit_Trading_Sell_Order_confirm');
});

Scenario('Test item No.17+18 Fill Password + Check for omitted password', async ({ I, stockPetitOrder }) => {
    const passwordSelector = '//*[@data-testid="tradePetitSellConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitSellConfirm_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitSellConfirm_orderConfirm_id"]';
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(orderConfirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitSellOrderConfirm('Test_item_No.17_18_Check_state_of_Order_Confirmation');
});

Scenario('Test item No.24 Check for entered password', async ({ I, stockPetitOrder }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user78 });
    await stockPetitOrder.goToPetitSellOrderConfirm('8306');
    const inputPasswordCheckSelector = '//*[@data-testid="tradePetitSellConfirm_checkInputPassword_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitSellConfirm_orderConfirm_id"]';
    await I.clickFixed(inputPasswordCheckSelector);
    await I.scrollToElement(orderConfirmButton);
    I.assertEqual(
        await I.grabCssPropertyFrom(orderConfirmButton, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockPetitOrder.takeScreenshot.petitSellOrderConfirm('Test_item_No.24_Check_state_of_Order_Confirmation');
});

Scenario('Test item No.19-3 Order Confirmation', async ({ I, stockPetitOrder }) => {
    const passwordSelector = '//*[@data-testid="tradePetitSellConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="tradePetitSellConfirm_passwordOmissionCheck_id"]';
    const orderConfirmButton = '//*[@data-testid="tradePetitSellConfirm_orderConfirm_id"]';
    await I.scrollToElement(passwordSelector);
    I.fillField(passwordSelector, stockPetitOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor('mediumWait');
    await stockPetitOrder.compareUrl('/mobile/trade/petit/sell/complete');
    stockPetitOrder.takeScreenshot.petitSellOrderConfirm(
        'Test_item_No.19-3_Tap_Order_Confirmation_to_transition_to_complete',
    );
});

Scenario('Test item No.20 Order correction', async ({ I, stockPetitOrder }) => {
    const sellQuantitySelector = '//*[@data-testid="tradePetitSell_quantity_id"]';
    const orderModifySelector = '//*[@data-testid="tradePetitSellConfirm_orderModify_id"]';
    await I.clickFixed(orderModifySelector);
    await I.waitFor('mediumWait');
    I.waitForElement(sellQuantitySelector, 2);
    await stockPetitOrder.compareUrl('/mobile/trade/petit/sell');
    stockPetitOrder.takeScreenshot.petitSellOrderConfirm(
        'Test_item_No.20_Tap_Order_correction_to_transition_to_domestic_stock_trading_sell_order_entry',
    );
});

Scenario('Test item No.21 Cancel order', async ({ I, stockPetitOrder }) => {
    const orderCancelSelector = '//*[@data-testid="tradePetitSellConfirm_orderCancel_id"]';
    const searchPageSelector = '#searchPage';
    await I.clickFixed(orderCancelSelector);
    await I.waitFor('mediumWait');
    I.waitForElement(searchPageSelector, 2);
    I.see('銘柄検索', 'body');
    await stockPetitOrder.compareUrl('/mobile/search');
    stockPetitOrder.takeScreenshot.petitSellOrderConfirm(
        'Test_item_No.21_Tap_Cancel_order_to_transition_to_search_stock_page',
    );
});
