import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import portfolio from '../pages/portfolio';

Feature('AssetStatus - PortfolioFMMFPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user2 });

    // Go to fund page
    await portfolio.top.goToPage();
    await portfolio.top.foreignMmf();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test portfolio foreignMmf page', async ({ I }) => {
    const { common, foreignMmf } = portfolio;

    await common.verifyProductName(foreignMmf.productType);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioForeignMmfPage}_layout.png`);
});

Scenario('test portfolio foreignMmf page [3.銘柄カード]', async ({ I }) => {
    const { common, foreignMmf } = portfolio;
    await common.clickSymbolCard(foreignMmf.productType, '');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioForeignMmfPage}_3_symbol_card.png`);
});

Scenario('test portfolio foreignMmf page [9.銘柄情報を見る]', async ({ I }) => {
    const { common, foreignMmf } = portfolio;
    await common.clickSymbolCard(foreignMmf.productType, '');
    await foreignMmf.seeInfo('BIV');
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioForeignMmfPage}_9_see_info.png`);
});

Scenario('test portfolio foreignMmf page [10.残高照会]', async ({ I }) => {
    const { common, foreignMmf } = portfolio;
    await common.clickSymbolCard(foreignMmf.productType, '');
    await foreignMmf.positionInquiry();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.portfolioForeignMmfPage}_10_position_inquiry.png`);
});
