import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Reserve - CustomerReserveFundReserveOrderComplete');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await <PERSON>.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user52 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});
Scenario('Test Item 0: Check UI Reserve Fund ReserveOrder Complete', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    I.scrollAndClick(orderConfirmButton);
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");

    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundReserveOrderConfirm_apply_id"]';

    I.scrollAndClick(confirmButton);
    await I.waitFor('mediumWait');
    I.seeElement('//p[contains(text(), "積立申込が完了しました。")]');
    I.saveScreenshot('1487.Test_item_No.0_Check_UI_Reserve_Fund_ReserveOrder_Complete.png');
});
Scenario('Test Item 1: Tap to transition to the savings plan', async ({ I, accumulation }) => {
    const reservePlan = '//*[@data-testid="reserveCommon_reservePlan_id"]';
    await I.waitForElement(reservePlan, 3);
    I.scrollAndClick(reservePlan);
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1487.Test_item_No.1_Tap_to_transition_to_the_savings_plan.png');
})
Scenario('Test Item 2: Tap to transition to the savings calendar', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    I.scrollAndClick(orderConfirmButton);
    I.seeElement("//p[contains(text(), '積立(投信)申込確認')]");

    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundReserveOrderConfirm_apply_id"]';

    I.scrollAndClick(confirmButton);
    await I.waitFor('shortWait');
    I.seeElement('//p[contains(text(), "積立申込が完了しました。")]');

    const reserveCalendar = '//*[@data-testid="reserveCommon_reserveCalendar_id"]';
    await I.waitForElement(reserveCalendar, 3);
    I.scrollAndClick(reserveCalendar);
    I.see('積立カレンダー', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1487.Test_item_No.2_Tap_to_transition_to_the_savings_calendar.png');
})
Scenario('Test Browser back', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveFundPage();
    const suggestInputButtonGroup = '//*[@data-testid="suggestInputNumber_buttonGroup"]//button';
    await I.waitForElement(suggestInputButtonGroup, 2);
    const button = `(${suggestInputButtonGroup})[1]`;
    await I.scrollAndClick(button);
    const orderConfirmButton = '//*[@data-testid="fundReserveOrderInput_goToConfirmScreen_id"]';
    await I.waitForElement(orderConfirmButton, 1);
    await I.scrollToElement(orderConfirmButton);
    await I.clickFixed(orderConfirmButton);
    await I.waitFor('mediumWait');
    const passwordInput = '//*[@data-testid="fundReserveOrderConfirm_password_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    const confirmButton = '//*[@data-testid="fundReserveOrderConfirm_apply_id"]';
    await I.scrollAndClick(confirmButton);
    await I.waitFor('mediumWait');
    await I.performBrowserBack();
    await I.waitFor('mediumWait');
    I.saveScreenshot('1487.Test_item_No.3_Browser_back.png');
});