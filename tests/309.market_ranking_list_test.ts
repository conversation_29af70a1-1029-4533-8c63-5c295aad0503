import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';

Feature('Market - RankingList');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user13 });
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Market Ranking List page', async ({ I, market }) => {
    await I.switchToWeb();
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    // Navigate to Market Ranking List page
    await market.goToMarketRanking();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_Display_Market_Ranking_List.png`);
});

Scenario('Test item No.1 Market tab info menu', async ({ market }) => {
    await market.clickMarketTabGroup();
});

Scenario('Test item No.2 Swipe up/down Ranking list', async ({ I, market }) => {
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    // Navigate to Market Ranking List page
    await market.goToMarketRanking();
    const rankingList = '//*[@data-testid="rankingList_rankingInfo_id"]';
    I.waitForElement(rankingList);
    await I.swipeUpFixed(rankingList);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_item_No.2_Swipe_up_Ranking_list.png`);
    await I.waitFor();
    await I.swipeDownFixed(rankingList);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_item_No.2_Swipe_down_Ranking_list.png`);
});

Scenario('Test item No.3 Category', async ({ market }) => {
    const rankingTypeButtonSelector = '//*[@data-testid="rankingList_category_id"]';
    const rankingTypePopupSelector = '//*[@data-testid="common_MenuList_id"][(contains(@style, "visibility: visible"))]';
    const rankingTypeItemSelector = `${rankingTypePopupSelector}//*[@data-testid="common_MenuItem_id"]`;
    const apiNames = [
        "RankingService/GetRankingStock",
        "BoardService/GetBoardRegistSymbolList",
        "ChartService/GetChartLegStock",
    ];
    await market.handleDropdownInteraction(rankingTypeButtonSelector, rankingTypePopupSelector, rankingTypeItemSelector, apiNames, "Test_item_No.3_Category");
});

Scenario('Test item No.4 Exchange', async ({ market }) => {
    const exchangeTypeButtonSelector = '//*[@data-testid="rankingList_exchange_id"]';
    const exchangeTypePopupSelector = '//*[@data-testid="common_MenuList_id"][(contains(@style, "visibility: visible"))]';
    const exchangeTypeItemSelector = `${exchangeTypePopupSelector}//*[@data-testid="common_MenuItem_id"]`;
    const apiNames = [
        "RankingService/GetRankingStock",
        "BoardService/GetBoardRegistSymbolList",
        "ChartService/GetChartLegStock",
    ];
    await market.handleDropdownInteraction(exchangeTypeButtonSelector, exchangeTypePopupSelector, exchangeTypeItemSelector, apiNames, "Test_item_No.4_Exchange");
});

Scenario('Test item No.6 Stock details', async ({ I, market }) => {
    const stockItemSelector = '//*[@data-testid="rankingList_symbolDetail_id_0"]';
    await market.goToRakingStockDetail(stockItemSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_item_No.5_Stock_details.png`);
});

Scenario('Test item No.6-1 Favorite button', async ({ I }) => {
    const favoriteButtonSelector = '//*[@data-testid="stockInfo_favoriteButton_id"]';
    const favoriteRegistrationModalSelector = '//*[@data-testid="common_rightSlide_favorite_id"]';
    await I.clickFixed(favoriteButtonSelector);
    await I.waitFor();
    I.waitForText('お気に入り追加', 3, favoriteRegistrationModalSelector);
    I.seeElement(favoriteRegistrationModalSelector);
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_item_No.6_Dispaly_Favorite_registration_modal.png`);
    await I.clickFixed("[data-testid='common_rightSlide_close_id']");
});

Scenario('Test item No.8 Back to top', async ({ I, market }) => {
    // Navigate to Market Indicator List page
    await market.goToMarketIndicatorList();
    // Navigate to Market Ranking List page
    await market.goToMarketRanking();
    const rankingList = '//*[@data-testid="rankingList_rankingInfo_id"]';
    I.waitForElement(rankingList);
    await I.swipeUpFixed(rankingList);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_item_No.8_Swipe_up_Ranking_list.png`);
    await I.waitFor();
    const backToTopButtonSelector = '#scrollButton';
    I.waitForElement(backToTopButtonSelector);
    await I.clickFixed(backToTopButtonSelector);
    await I.waitFor();
    I.saveScreenshot(`${SCREENSHOT_PREFIX.marketRankingList}_Test_item_No.8_Back_to_top.png`);
});
