import { COOKIE_KEY, PAGE_URL, USER_ID } from '../const/constant';

Feature('AssetStatus - AssetBalanceTabPage');

const DATA_FILTER = ['米国株式現物', '投資信託', '外貨建MMF', '国内株式信用', '先物', 'オプション', '国内株式現物'];
// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/InvestmentResult/AssetBalance.html
Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user4 });
    await I<PERSON>waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('test asset balance page', async ({ I }) => {
    // Navigate to asset balance page
    await I.amOnPage(PAGE_URL.mypagePortfolio);
    // await I.clickFixed('//*[@id="/mypage/portfolio"]');
    await I.waitFor('mediumWait');
    const processingButton = locate('button').withChild(locate('div').withChild(locate('p').withText('投資成績')));
    await I.clickFixed(processingButton);
    await I.waitFor();

    // Navigate to investment profit loss page
    await I.waitForElement({ xpath: '//*[@data-testid="investmentResult_balance_id"]' });
    const investmentProfitLossButton = '//*[@data-testid="investmentResult_balance_id"]';

    await I.waitFor();
    await I.clickFixed(investmentProfitLossButton);
    await I.waitFor('mediumWait');
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPage.png');
});

// Item 10
Scenario('test Item 10 Back Button', async ({ I }) => {
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPageItem10.png');
    await I.clickFixed('//*[@data-testid="assetBalance_investmentProductCard_id"]/div[1]');
    await I.waitFor();
    I.seeElement('//*[@id="mypage-inves"]/div/div/div[5]/button');
    await I.waitFor();
    await I.clickFixed('//*[@id="mypage-inves"]/div/div/div[5]/button');
});

// Item 11, Item 29
Scenario('test Item 11 Balance View Button', async ({ I }) => {
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPageItem11.png');
    await I.clickFixed('//*[@data-testid="assetBalance_balanceView_id"]');
    I.seeElement('#bottom-sheet-container');
    I.seeElement('//*[@data-testid="assetBalance_detailedExplanationOfBalanceScreen_id_0"]/a');
    I.seeAttributesOnElements('//*[@data-testid="assetBalance_detailedExplanationOfBalanceScreen_id_0"]/a', {
        href: 'https://kabu.com/mobile/app/guide/index.html#balance',
    });
    I.waitForElement('//*[@data-testid="portfolioTop_close_id"]');
    I.tapLocationOfElement('//*[@data-testid="portfolioTop_close_id"]');
    await I.waitFor();
});

// Item 14
Scenario('test Item 14 Balance Sort Button', async ({ I, investmentProfitLossPage }) => {
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPageItem14.png');
    const dataSelector = '//*[@data-testid="assetBalance_investmentProductCard_id"]';
    I.waitForElement(dataSelector);
    const dataTexts = await I.grabTextFromAll(`${dataSelector}//p[1]`);
    await I.waitFor();
    await investmentProfitLossPage.sortButtonView({
        dataTexts,
        dataSelector,
        baseDataList: dataTexts
        .filter((_, index) => index % 2 !== 0)
        .map((item) => item.replace(/[+,円]/g, '')) ?? ['12', '10', '8', '6', '4', '2'],
        sortButtonSelector: '//*[@data-testid="assetBalance_balanceSort_id"]',
    });
});

// Item 15
Scenario('test Item 15 Balance Product Card View', async ({ I, investmentProfitLossPage }) => {
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPageItem15.png');
    const dataSelector = '//*[@data-testid="assetBalance_investmentProductCard_id"]';
    I.waitForElement(dataSelector);
    const dataTexts = await I.grabTextFromAll(`${dataSelector}//p[1]`);
    await I.waitFor();
    await investmentProfitLossPage.productCardView({
        dataTexts,
        dataSelector,
        dataFilter: DATA_FILTER,
        cancelButtonSelector: '//*[@id="mypage-inves"]/div/div/div[5]/button',
    });
});

// Item 18, Item 19
Scenario('test Item 18, Item 19 Previous, Next Button', async ({ I, investmentProfitLossPage }) => {
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPageItem1819.png');
    
    const dataSelector = '//*[@data-testid="assetBalance_investmentProductCard_id"]/div[1]';
    const cancelButtonSelector = '//*[@id="mypage-inves"]/div/div/div[5]/button';
    await investmentProfitLossPage.previousNextButtonView({ dataSelector, titles: DATA_FILTER, cancelButtonSelector });
    await I.clickFixed(cancelButtonSelector);
});

// Item 27
Scenario('test Item 27 Legend', async ({ I }) => {
    I.saveScreenshot('87.asset_balance_page_balance_tab_balanceTabPageItem27.png');
    const { tryTo } = require('codeceptjs/effects');

    I.seeElement('//*[@data-testid="assetBalance_legend_id"]');
    const incomeLocator =
        '#mypage-inves div div:nth-child(2) div:first-child div div svg g.highcharts-series-group g.highcharts-series.highcharts-series-0.highcharts-line-series';

    I.seeElement(incomeLocator);
    I.seeCssPropertiesOnElements('//*[@data-testid="assetBalance_legend_id"]/p[1]', {
        opacity: '1',
    });
    const isHidden = await tryTo(() =>
        I.seeAttributesOnElements(incomeLocator, {
            visibility: 'hidden',
        }),
    );

    if (isHidden) {
        throw new Error('Legend is hidden');
    }

    await I.clickFixed('//*[@data-testid="assetBalance_legend_id"]/p[1]');
    I.seeCssPropertiesOnElements('//*[@data-testid="assetBalance_legend_id"]/p[1]', {
        opacity: '0.5',
    });
    I.seeAttributesOnElements(incomeLocator, {
        visibility: 'hidden',
    });
    await I.waitFor();

    I.seeElement('//*[@data-testid="assetBalance_legend_id"]/p[2]');
    const benefitLocator =
        '#mypage-inves div div:nth-child(2) div:first-child div div svg g.highcharts-series-group g.highcharts-series.highcharts-series-1.highcharts-column-series.highcharts-tracker';

    I.seeElement(benefitLocator);
    const isHiddenBenefit = await tryTo(() =>
        I.seeAttributesOnElements(benefitLocator, {
            visibility: 'hidden',
        }),
    );

    if (isHiddenBenefit) {
        throw new Error('Legend is hidden');
    }
    I.seeCssPropertiesOnElements('//*[@data-testid="assetBalance_legend_id"]/p[2]', {
        opacity: '1',
    });
    await I.clickFixed('//*[@data-testid="assetBalance_legend_id"]/p[2]');
    I.seeCssPropertiesOnElements('//*[@data-testid="assetBalance_legend_id"]/p[2]', {
        opacity: '0.5',
    });
    I.seeAttributesOnElements(benefitLocator, {
        visibility: 'hidden',
    });
    await I.waitFor();

    I.seeElement('//*[@data-testid="assetBalance_legend_id"]/p[3]');
    const amountLocator =
        '#mypage-inves div div:nth-child(2) div:first-child div div svg g.highcharts-series-group g.highcharts-series.highcharts-series-2.highcharts-column-series.highcharts-tracker';

    I.seeCssPropertiesOnElements('//*[@data-testid="assetBalance_legend_id"]/p[3]', {
        opacity: '1',
    });
    const isHiddenAmount = await tryTo(() =>
        I.seeAttributesOnElements(amountLocator, {
            visibility: 'hidden',
        }),
    );
    if (isHiddenAmount) {
        throw new Error('Legend is hidden');
    }
    await I.clickFixed('//*[@data-testid="assetBalance_legend_id"]/p[3]');
    I.seeCssPropertiesOnElements('//*[@data-testid="assetBalance_legend_id"]/p[3]', {
        opacity: '0.5',
    });
    I.seeAttributesOnElements(amountLocator, {
        visibility: 'hidden',
    });
});
