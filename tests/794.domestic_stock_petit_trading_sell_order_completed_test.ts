import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockPetitTradingSellOrderCompleted');

Before(async ({ I, loginAndSwitchToWebAs, stockPetitOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user32 });
    await stockPetitOrder.goToPetitSellOrderCompleted('8306');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Petit Trading Sell Order completed', async ({ I, stockPetitOrder }) => {
    await stockPetitOrder.compareUrl('/mobile/trade/petit/sell/complete');
    stockPetitOrder.takeScreenshot.petitSellOrderCompleted('Display_Domestic_Stock_Petit_Trading_Sell_Order_completed');
});

Scenario('Test item No.2 Order number', async ({ I, stockPetitOrder }) => {
    const orderNumberSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    const orderInquiryDetailSelector = '#order-inquiry-detail';
    await I.clickFixed(orderNumberSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquiryDetailSelector);
    await stockPetitOrder.compareUrl('/mobile/order-inquiry/petit/detail');
    stockPetitOrder.takeScreenshot.petitSellOrderCompleted(
        'Test_item_No.2_Tap_Order_number_to_transition_to_details_page',
    );
});

Scenario('Test item No.3 Order Inquiry', async ({ I, stockPetitOrder }) => {
    const orderInquiryButtonSelector = '//*[@data-testid="complete_orderStatusButton_id"]';
    const orderInquirySelector = '#order-inquiry';
    await I.clickFixed(orderInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(orderInquirySelector);
    await stockPetitOrder.compareUrl('/mobile/order-inquiry/petit');
    stockPetitOrder.takeScreenshot.petitSellOrderCompleted(
        'Test_item_No.3_Tap_Order_Inquiry_to_transition_to_order_list_page',
    );
});

Scenario('Test item No.4 Balance inquiry', async ({ I, stockPetitOrder }) => {
    const balanceInquiryButtonSelector = '//*[@data-testid="complete_positionInquiryButton_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(balanceInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.seeElement(positionInquirySelector);
    await stockPetitOrder.compareUrl('/mobile/position-inquiry/stock');
    stockPetitOrder.takeScreenshot.petitSellOrderCompleted(
        'Test_item_No.4_Tap_Balance_inquiry_to_transition_to_balance_list_page',
    );
});

Scenario('Test Back to browser', async ({ I, stockPetitOrder }) => {
    await I.performBrowserBack();
    await I.waitFor();
    I.seeElement('#searchPage');
    await stockPetitOrder.compareUrl('/mobile/search');
    stockPetitOrder.takeScreenshot.petitSellOrderCompleted('Test_Back_to_browser_to_transition_to_general_search_page');
});
