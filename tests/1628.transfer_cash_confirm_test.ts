import { COOKIE_KEY, USER_ID } from '../const/constant';
import transferCash from '../pages/transferCash';

Feature('Deposits_Withdrawals - TransferCashConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user102 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Transfer Cash Confirm', async ({ I }) => {
    await transferCash.goToCashConfirmPage();
    I.saveScreenshot(`${transferCash.prefix.input}.0_Transfer_Cash_Confirm.png`);
});
Scenario('Test Item 8: Password', async ({ I }) => {
    const passwordInput = '//*[@data-testid="transferCashConfirm_password_id"]';
    await I.waitForElement(passwordInput, 1);
    I.scrollAndFill(passwordInput, '123@abc'); 
     // click img alt="eye" to see password
     const eyeIcon = '//img[@alt="eye"]';
     await I.waitForElement(eyeIcon, 1);
     I.clickFixed(eyeIcon);
     await I.waitFor('shortWait');
});
Scenario('Test Item 9-2: Go to transfer cash complete page', async ({ I }) => {
    const transferCashInput_confirmScreen_id = '//*[@data-testid="transferCashConfirm_transfer_id"]';
    I.waitForElement(transferCashInput_confirmScreen_id, 2);
    I.scrollAndClick(transferCashInput_confirmScreen_id);
    await I.waitFor();
    I.saveScreenshot(`${transferCash.prefix.input}.9-2_Go_to_transfer_cash_complete_page.png`);
});