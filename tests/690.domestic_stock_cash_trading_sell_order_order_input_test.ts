import { COMMON_BACK_BUTTON, COOKIE_KEY, USER_ID } from '../const/constant';
import domesticStockCashtrading from '../pages/domesticStockCashtrading';

Feature('InvestmentProducts - DomesticStockCashtrading');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor('mediumWait');

    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user26 });
    await I.waitFor('mediumWait');
});

After(async ({ I }) => {
    console.debug('after');
    await I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Access to the trade stock sell page', async ({ I }) => {
    await domesticStockCashtrading.goToStockSellPage();
    I.seeInCurrentUrl(domesticStockCashtrading.urls.sell);
    I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.0_access_to_the_trade_stock_sell_page.png`);
});

// 3.取引制限・取引注意情報 -> 共通UI-取引注意情報モーダルを表示する
Scenario('Item 3. Display transaction warning information modal', async ({ I }) => {
    await I.seeAndClickElement(domesticStockCashtrading.locators.tradeRestriciton);
    I.see('取引制限・取引注意情報', '.chakra-text');
    I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.sell}.3_display_transaction_warning_information_modal.png`,
    );
    await I.waitFor('shortWait');
    await I.seeAndClickElement(domesticStockCashtrading.locators.tradeRestricitonCloseButton);
});

// 10.市場 -> プルダウンメニューを表示する
// 変更した場合、画面の10.市場変更時の処理を実施する
Scenario('Item 10. Display pull-down menu', async ({ I }) => {
    await I.seeAndClickElement(domesticStockCashtrading.locators.marketPulldown);
    I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.10_display_pulldown_menu.png`);
    await I.waitFor('shortWait');
    await I.seeAndClickElement(domesticStockCashtrading.locators.marketFirstOption);
    I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.10_select_pulldown_first_option.png`);
});

// 11.口座区分 -> タップした項目を選択状態にする
// + 「10.市場でSOR指定=ON かつ 14.執行方法選択で自動売買を選択中」の場合にNISA口座 or NISA(成長投資枠)に変更した場合: 14.執行方法選択の選択状態を指値に変更し、14.執行方法選択の自動売買タブを非表示(トルツメ)する
// 選択口座の残高情報を用いて表示内容を更新する
// + 12.注文期限で期間指定を選択しており、選択中の日付が選択不可となった場合: 12.注文期限を当日中にする
Scenario('Item 11. Select the item you tap', async ({ I }) => {
    // click to checked SOR
    const isCheckSOR = await I.executeScript(() => {
        const elm = document.querySelector('label[data-testid="sellInput_exchange_id"]');
        return elm ? elm.hasAttribute('data-checked') : false;
    });
    if (!isCheckSOR) {
        await I.seeAndClickElement('//label[@data-testid="sellInput_exchange_id"]');
    }

    // click to Automated Trading
    await I.seeAndClickElement(domesticStockCashtrading.locators.executionMethodOptionItem(3));
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.10_select_automated_trading_tab.png`);

    // click to NISA
    await I.seeAndClickElement(domesticStockCashtrading.locators.accountType(3));
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.10_select_account_nisa.png`);

    // select Limit Order tab
    await I.seeAndClickElement(domesticStockCashtrading.locators.executionMethodOptionItem(1));

    await domesticStockCashtrading.seeAndFillText('groupNumberInput');

    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.10_select_limit_order_tab.png`);

    // Skip: + 12.注文期限で期間指定を選択しており、選択中の日付が選択不可となった場合: 12.注文期限を当日中にする
    // The current date is outside the time range of the datepicker, this component has automatically handled selecting the same day
});

// 12.注文期限 -> タップした項目を選択状態にする
// + 期間指定 をタップした場合：共通UI-注文期限の期間指定デートピッカーをオーバーレイ表示する
Scenario('Item 12. Select the tapped item', async ({ I }) => {
    await domesticStockCashtrading.goToStockSellPage();
    const labels = ['当日中', '今週中', '期間指定'];
    for (let i = 1; i <= labels.length; i++) {
        const label = labels[i - 1];
        await I.scrollAndClick(domesticStockCashtrading.locators.orderDeadline(i));
        await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.12_select_${label}.png`);
        if (i === labels.length) {
            I.waitForVisible({ xpath: '//*[@id="bottom-sheet-container"]' }, 5);
            await I.saveScreenshot(
                `${domesticStockCashtrading.screenshotPrefix.sell}.12_show_date_picker_bottom_sheet.png`,
            );
        }
    }
    await I.waitFor('shortWait');
    I.clickFixed(locate('button').withAttr({ 'aria-label': 'closeIcon' }));
});

// 13.注文期限期日 -> 期間指定を選択中の場合：共通UI-注文期限の期間指定デートピッカーを表示する
Scenario('Item 13. Display common UI - Order deadline period specification date picker', async ({ I }) => {
    await domesticStockCashtrading.goToStockSellPage();
    await I.scrollAndClick('//*[@data-testid="sellInput_orderPeriod_id"]//label[p[contains(text(), "期間指定")]]');
    I.waitForVisible({ xpath: '//*[@id="bottom-sheet-container"]' }, 5);
    await I.clickFixed(locate('button').withAttr({ 'aria-label': 'closeIcon' }));
    await I.clickFixed(domesticStockCashtrading.locators.expireDatePicker);
    await I.waitFor('shortWait');
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.sell}.13_display_order_deadline_period_specification_date_picker.png`,
    );
    await I.waitFor('shortWait');
    I.clickFixed(locate('button').withAttr({ 'aria-label': 'closeIcon' }));
});

// 14.執行方法選択 -> タップした項目を選択状態にする
Scenario('Item 14. Select Execution Method Selection', async ({ I }) => {
    const tabNames = ['指値', '成行'];
    for (let i = 1; i <= tabNames.length; i++) {
        const tabName = tabNames[i - 1];
        await I.seeAndClickElement(domesticStockCashtrading.locators.executionMethodOptionItem(i));
        await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.14_select_${tabName}_tab.png`);
    }
});

// 15.執行方法設定パネル -> 共通UI-共通注文入力参照
Scenario('Item 15. Execution Method Setting Panel', async ({ I }) => {
    await I.seeAndClickElement(domesticStockCashtrading.locators.executionMethodOptionItem(2));

    await domesticStockCashtrading.swipeToLocator('groupNumberInput');
    await domesticStockCashtrading.seeAndFillText('groupNumberInput');
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.sell}.15_fill_in_the_input_field_with_value_1000.png`,
    );
    await I.waitFor('shortWait');

    await I.seeAndClickElement(domesticStockCashtrading.locators.plusButton);
    await I.seeAndClickElement(domesticStockCashtrading.locators.plusButton);
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.15_click_the_plus_button.png`);
    await I.waitFor('shortWait');

    await I.seeAndClickElement(domesticStockCashtrading.locators.minusButton);
    await I.saveScreenshot(`${domesticStockCashtrading.screenshotPrefix.sell}.15_click_the_minus_button.png`);
});

// 16.注文確認画面へ -> リレー注文を選択している場合：国内株式現物取引-リレー注文選択を表示する
Scenario('Item 16. Display domestic stock cash trading relay order selection', async ({ I }) => {
    // no condition
    await domesticStockCashtrading.goToConfirmPage();
    I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.sell}.16_display_spot_sales_confirmation_of_order_details.png`,
    );
    I.seeElement(COMMON_BACK_BUTTON);
    await I.clickFixed(COMMON_BACK_BUTTON);
    await I.waitFor();

    // selected replay order
    await I.seeAndClickElement(domesticStockCashtrading.locators.accountType(2));
    await I.seeAndClickElement(domesticStockCashtrading.locators.orderDeadline(1));

    await domesticStockCashtrading.swipeToLocator();

    // select Special conditions -> replay order
    await I.seeAndClickElement(domesticStockCashtrading.locators.specialConditionButton);
    await I.seeAndClickElement(domesticStockCashtrading.locators.secondConditionOption);
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.sell}.16_selected_the_replay_order_condition.png`,
    );

    await I.clickFixed(domesticStockCashtrading.locators.orderConfirmButton);
    await I.waitFor();
    I.waitForText('リレー選択現物取引', 5, 'p');
    await I.saveScreenshot(
        `${domesticStockCashtrading.screenshotPrefix.sell}.16_display_the_relay_selection_spot_trading.png`,
    );

    await I.clickFixed('/html/body/div[5]/div/div/div/div[1]/button');
});
