import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginReceiptOrderConfirm');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginReceiptOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginReceiptOrder.goToMarginReceiptOrderConfirm();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Receipt Order Confirm', async ({ I, stockMarginReceiptOrder }) => {
    I.assertContain(
        await I.grabCurrentUrl(),
        stockMarginReceiptOrder.urls.marginReceiptOrderConfirm,
        'URL does not contain expected path',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderConfirm(
        'Display_Domestic_Stock_Margin_Trading_Receipt_Order_Confirm',
    );
});

Scenario('Test item No.11+12 Fill password and check for omitted password', async ({ I, stockMarginReceiptOrder }) => {
    const passwordSelector = '//*[@data-testid="marginReceiptConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginReceiptConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginReceiptConfirm_orderConfirm_id"]';
    I.fillField(passwordSelector, stockMarginReceiptOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(confirmButtonSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderConfirm(
        'Test_item_No.13_14_See_order_confirmation_button_state',
    );
});

Scenario('Test item No.15 Order Confirmation', async ({ I, stockMarginReceiptOrder }) => {
    const passwordSelector = '//*[@data-testid="marginReceiptConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginReceiptConfirm_checkPassword_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginReceiptConfirm_orderConfirm_id"]';
    const completeOrderIdSelector = '//*[@data-testid="complete_orderIdLink_id"]';
    I.fillField(passwordSelector, stockMarginReceiptOrder.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    I.waitForElement(completeOrderIdSelector, 2);
    I.assertContain(
        await I.grabCurrentUrl(),
        '/mobile/trade/margin/receipt/complete',
        'URL does not contain expected path',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderConfirm(
        'Test_item_No.15_Transition_to_the_product_receipt_completion_screen',
    );
});

Scenario('Test item No.16 Caution statement', async ({ I, stockMarginReceiptOrder }) => {
    const cautionSelector = '//*[@data-testid="marginReceiptConfirm_cautionMessage_id"]/div[1]';
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await I.swipeDirection('up');
    await stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderConfirm(
        'Test_item_No.16_Tap_Caution_opening_the_accordion',
    );
    await I.clickFixed(cautionSelector);
    await I.waitFor();
    await stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderConfirm(
        'Test_item_No.16_Tap_Caution_closing_the_accordion',
    );
});
