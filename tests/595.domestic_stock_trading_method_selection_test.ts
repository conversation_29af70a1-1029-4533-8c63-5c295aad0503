import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('InvestmentProducts - DomesticStockTradingMethodSelection');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Trading Method selection', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    I.see('取引方法', stockDetailBasicPage.locator.bottomSheetContainer);
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Display_Domestic_Stock_Trading_method_selection');
});

Scenario('Test item No.1 Tap Close button', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const closeButtonSelector = '//*[@data-testid="tradingMethod_close_id"]';
    await I.clickFixed(closeButtonSelector);
    await I.waitFor();
    I.dontSeeElement(stockDetailBasicPage.locator.bottomSheetContainer);
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.1_Tap_close_button_to_close_modal');
});

Scenario('Test item No.3 Spot purchase - buy order', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const spotPurchaseSelector = '//*[@data-testid="tradingMethod_cashBuy_id"]';
    const buyInputSelection = '//*[@data-testid="buyInput_executionMethodSelection_id"]';
    await I.clickFixed(spotPurchaseSelector);
    await I.waitFor();
    I.waitForElement(buyInputSelection);
    await stockDetailBasicPage.compareUrl('/mobile/trade/stock/buy');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.3_Select_spot_purchase_transition_to_order_entry_page');
});

Scenario('Test item No.4 Spot sales', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const spotSalesSelector = '//*[@data-testid="tradingMethod_cashSell_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(spotSalesSelector);
    await I.waitFor();
    I.waitForElement(positionInquirySelector);
    await stockDetailBasicPage.compareUrl('/mobile/position-inquiry/stock');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.4_Select_spot_sales_transition_to_list_page');
});

Scenario('Test item No.6 Small buying', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const smallBuyingSelector = '//*[@data-testid="tradingMethod_petitBuy_id"]';
    const pettiBuyQuantitySelector = '//*[@data-testid="tradePetitBuy_quantity_id"]';
    await I.clickFixed(smallBuyingSelector);
    await I.waitFor();
    I.waitForElement(pettiBuyQuantitySelector);
    await stockDetailBasicPage.compareUrl('/mobile/trade/petit/buy');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.6_Select_small_buying_transition_to_order_entry_page');
});

Scenario('Test item No.7 Small sales', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const smallSalesSelector = '//*[@data-testid="tradingMethod_petitSell_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(smallSalesSelector);
    await I.waitFor();
    I.waitForElement(positionInquirySelector);
    await stockDetailBasicPage.compareUrl('/mobile/position-inquiry/stock');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.7_Select_small_sales_transition_to_list_page');
});

Scenario('Test item No.9 New margin trade', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user23 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const newMarginTradeSelector = '//*[@data-testid="tradingMethod_newMargin_id"]';
    const marginNewDepositRequestSelector = '//*[@data-testid="marginNew_depositRequest_id"]';
    await I.clickFixed(newMarginTradeSelector);
    await I.waitFor();
    I.waitForElement(marginNewDepositRequestSelector);
    await stockDetailBasicPage.compareUrl('/mobile/trade/margin/new');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.9_Select_new_margin_trade_transition_to_order_entry_page');
});

Scenario('Test item No.10 Credit repayment', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user23 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const marginCloseSelector = '//*[@data-testid="tradingMethod_marginClose_id"]';
    const positionInquirySelector = '#position-inquiry';
    await I.clickFixed(marginCloseSelector);
    await I.waitFor();
    I.waitForElement(positionInquirySelector);
    await stockDetailBasicPage.compareUrl('/mobile/position-inquiry/margin');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.10_Select_credit_repayment_transition_to_list_page');
});

Scenario('Test item No.11 Open a credit account', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const openCreditAccount = '//*[@data-testid="tradingMethod_marginAccountOpening_id"]';
    // Go to the following URL: {member-site-url}/ap/iphone/Personal/WebExaminationMargin/ExaminationInput
    await common.clickCardItem(openCreditAccount, '/ap/iphone/Personal/WebExaminationMargin/ExaminationInput', 'kcMemberSite');
});

Scenario('Test item No.13 Small Stock Savings', async ({ I, stockDetailBasicPage }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user22 });
    await stockDetailBasicPage.goToPage();
    await stockDetailBasicPage.clickTradeButton();
    const petitReserve = '//*[@data-testid="tradingMethod_petitReserve_id"]';
    // Proceed to Savings-Small Stocks-Savings Application Input
    await I.clickFixed(petitReserve);
    await I.waitFor('mediumWait');
    I.seeInCurrentUrl('/mobile/reserve/petit/reserve-order/input');
    stockDetailBasicPage.takeScreenshot.stockTradingMethodSelection('Test_item_No.13_Proceed_to_Savings_Small_Stocks_Savings_Application_Input');
});
