import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginReceiptOrderInput');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginReceiptOrder }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user33 }, { name:COOKIE_KEY.siteId, value: '1' }]);
    
    await stockMarginReceiptOrder.goToMarginReceiptOrderInput();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Receipt Order Input', async ({ I, stockMarginReceiptOrder }) => {
    I.assertContain(
        await I.grabCurrentUrl(),
        stockMarginReceiptOrder.urls.marginReceiptOrderInput,
        'URL does not contain expected path',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Display_Domestic_Stock_Margin_Trading_Receipt_Order_Input',
    );
});

Scenario('Test item No.9 Trade Restrictions and Trade Caution Information', async ({ I, stockMarginReceiptOrder }) => {
    const cautionInfoSelector = '//*[@data-testid="marginReceiptInput_tradeCautionInfo_id"]';
    await I.clickFixed(cautionInfoSelector);
    await I.waitFor();
    I.see('取引制限・取引注意情報', 'body');
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.9_Open_Trade_Caution_Information_modal',
    );
});

Scenario('Test item No.4 Reception status', async ({ I, stockMarginReceiptOrder }) => {
    const receiptStatusSelector = '//*[@data-testid="marginReceiptInput_status_id"]';
    await I.clickFixed(receiptStatusSelector);
    await I.waitFor();
    I.seeElement('#bottom-sheet-container');
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.4_Display_the_item_receipt_and_delivery_modal',
    );
});

Scenario('Test item No.7 Account Category', async ({ I, stockMarginReceiptOrder }) => {
    const positionItemSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const accountTypeWrapperSelector = '//*[@data-testid="marginReceiptInput_accountType_id"]';
    const accountTypeSelector = `${accountTypeWrapperSelector}//label`;
    await I.clickFixed(positionItemSelector);
    await I.waitFor('shortWait');
    const accountTypeCount = await I.grabNumberOfVisibleElements(accountTypeSelector);
    if (accountTypeCount > 0) {
        await I.scrollToElement(accountTypeSelector);
        const accountTypeCount = await I.grabNumberOfVisibleElements(accountTypeSelector);
        if (accountTypeCount > 0) {
            const acountTypeIppanItemSelector = `${accountTypeSelector}[${accountTypeCount}]`;
            const acountTypeIppanItemRadioSelector = `${acountTypeIppanItemSelector}//div[@aria-hidden="true"]`;
            await I.clickFixed(acountTypeIppanItemSelector);
            I.assertEqual(
                await I.grabCssPropertyFrom(acountTypeIppanItemRadioSelector, 'background-color'),
                'rgba(137, 137, 139, 1)',
                'Background color is not equal',
            );
        }
    }
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.7_Tap_account_category_Change_the_radio_button_you_tapped_to_selected',
    );
});

Scenario('Test item No.12 Accept this position', async ({ I, stockMarginReceiptOrder }) => {
    const positionItemSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const positionItemRadioSelector = `${positionItemSelector}/div[1]/div[1]`;
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    await I.clickFixed(positionItemSelector);
    await I.waitFor();
    I.seeElement(quantityInputSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(positionItemRadioSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    await stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.12_Change_selected_state_and_show_quantity_and_update_account_type_display',
    );
    await I.clickFixed(positionItemSelector);
    await I.waitFor();
    I.dontSeeElement(quantityInputSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(positionItemRadioSelector, 'background-color'),
        'rgba(212, 212, 212, 1)',
        'Background color is not equal',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.12_Change_unselected_state_and_hide_quantity_and_update_account_type_display',
    );
});

Scenario('Test item No.13 Detail display', async ({ I, stockMarginReceiptOrder }) => {
    const deatilDisplaySelector = '//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    const deliveryThisDepositSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisDeposit_id"]';
    await I.clickFixed(deatilDisplaySelector);
    await I.waitFor();
    I.seeElement(deliveryThisDepositSelector);
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput('Test_item_No.13_Switch_the_detail_display');
});

Scenario('Test item No.24 Next Step', async ({ I, stockMarginReceiptOrder }) => {
    const positionItemSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const nextStepButtonSelector = '//*[@data-testid="marginReceiptInput_next_id"]';
    await I.clickFixed(positionItemSelector);
    await I.waitFor();
    I.fillField(quantityInputSelector, stockMarginReceiptOrder.inputValues.quantity);
    await I.clickFixed(nextStepButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(
        await I.grabCurrentUrl(),
        '/mobile/trade/margin/receipt/confirm',
        'URL does not contain expected path',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.24_Tap_next_step_to_Go_to_product_confirmation',
    );
});

Scenario('Test item No.25 Quantity', async ({ I, stockMarginReceiptOrder }) => {
    const positionItemSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const minusSelector = '//*[@data-testid="groupInputNumber_minus_id"]';
    const plusSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    await I.clickFixed(positionItemSelector);
    await I.waitFor();
    I.waitForElement(quantityInputSelector);
    await I.clickFixed(plusSelector);
    await I.clickFixed(plusSelector);
    await I.clickFixed(minusSelector);
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput('Test_item_No.25_See_Numeric_Stepper');
});

Scenario('Test item No.28 Accept this position', async ({ I, stockMarginReceiptOrder }) => {
    const positionItemSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const detailDisplaySelector = '//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    const deliveryThisDeposit = '//*[@data-testid="marginDeliveryInput_deliveryThisDeposit_id"]';
    const deliveryThisDepositIcon = '[data-testid="marginDeliveryInput_deliveryThisDeposit_id"] button svg';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    await I.clickFixed(positionItemSelector);
    await I.waitFor();
    await I.clickFixed(detailDisplaySelector);
    await I.waitFor();
    await I.clickFixed(deliveryThisDeposit);
    await I.waitFor();
    I.seeElement(quantityInputSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisDepositIcon, 'color'),
        'rgba(255, 86, 0, 1)',
        'Color is not equal',
    );
    await stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.28_Change_selected_state_and_show_quantity_and_update_account_type_display',
    );
    await I.clickFixed(deliveryThisDeposit);
    await I.waitFor();
    I.dontSeeElement(quantityInputSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(deliveryThisDepositIcon, 'color'),
        'rgba(137, 137, 139, 1)',
        'Color is not equal',
    );
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput(
        'Test_item_No.28_Change_unselected_state_and_hide_quantity_and_update_account_type_display',
    );
});

Scenario('Test item No.33 Quantity', async ({ I, stockMarginReceiptOrder }) => {
    const positionItemSelector = '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]';
    const detailDisplaySelector = '//*[@data-testid="marginDeliveryInput_detailDisplay_id"]';
    const deliveryThisDeposit = '//*[@data-testid="marginDeliveryInput_deliveryThisDeposit_id"]';
    const quantityInputSelector = '//*[@data-testid="groupInputNumber_input_id"]';
    const minusSelector = '//*[@data-testid="groupInputNumber_minus_id"]';
    const plusSelector = '//*[@data-testid="groupInputNumber_plus_id"]';
    await I.clickFixed(positionItemSelector);
    await I.waitFor();
    await I.clickFixed(detailDisplaySelector);
    await I.waitFor();
    await I.clickFixed(deliveryThisDeposit);
    await I.waitFor();
    I.seeElement(quantityInputSelector);
    await I.clickFixed(plusSelector);
    await I.clickFixed(plusSelector);
    await I.clickFixed(minusSelector);
    stockMarginReceiptOrder.takeScreenshot.marginReceiptOrderInput('Test_item_No.33_See_Numeric_Stepper');
});
