import { COL<PERSON>, COOKIE_KEY, USER_ID } from '../const/constant';
import favoritesList from '../pages/favoritesList';

Feature('Favorite - ListPage');
// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
// https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Notify/NotifyTradingList.html
Before(async ({ I }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor('mediumWait');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user15 });
    await <PERSON>.waitFor();
    await I.amOnPage('/mobile/favorite');
    await <PERSON>.waitFor('mediumWait');
});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

// // DuyNH - Done
Scenario('test favorites list page', async ({ I }) => {
    I.saveScreenshot('365.favoritesList.png');
});
Scenario('test Item 1 favorite list name page', async ({ I }) => {
    const favoriteListName = '//*[@data-testid="favoritesList_favoriteListName_id"]';
    I.seeElement(favoriteListName);
    I.clickFixed(favoriteListName);
    await I.waitFor();
    I.waitForElement('//*[@data-testid="common_MenuList_id"]', 3);
    I.saveScreenshot('365.Test_item_No.1_commonMenuList.png');
    I.clickFixed(favoriteListName);
});
Scenario('test Item 2 setting name page', async ({ I }) => {
    const settingName = '//*[@data-testid="favoritesList_setting_id"]';
    I.seeElement(settingName);
    I.clickFixed(settingName);
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.2_settingName.png');
    I.seeElement('//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/p');
    I.clickFixed('//*[@data-testid="favoritesList_closeButton_id"]');
});
Scenario('test Item 3 Display Format Selection page', async ({ I }) => {
    I.clickFixed(favoritesList.locator.listFormatButton);
    await I.waitFor();
    const fillColor = await I.grabCssPropertyFrom(favoritesList.locator.listFormatButton, 'fill');
    I.assertEqual(fillColor, COLOR.mainColorRGB, `fill color is not ${COLOR.mainColorRGB}`);
    I.saveScreenshot('365.Test_item_No.3_displayFormatSelection.png');

});
Scenario('test Item 4 Button Edit page', async ({ I }) => {
    const favoriteList = "//*[@data-testid='favoritesList_edit_id']";
    I.seeElement(favoriteList);
    I.clickFixed(favoriteList);
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.4_buttonEdit.png');
    I.seeElement('//*[@data-testid="favoritesList_deleteButton_id"]');
    I.seeElement('//*[@data-testid="favoritesList_sortHandler_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
});
Scenario('test Item 5 favorite item  page', async ({ I }) => {
    I.clickFixed(favoritesList.locator.listFormatButton);
    await I.waitFor();
    const panelItem = "//*[@data-testid='favoritesList_favoriteSymbolList_id']//div[contains(@data-testid, 'favoritesList_')][1]";
    await I.seeElement(panelItem);
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.3_displayFormatSelection.png');
    I.clickFixed('#list path');
});
Scenario('test Item 36 Scroll to Top button', async ({ I }) => {
    // Fix selector for correct app
    const favoriteListContainer = '//*[@id="tab-panels1"]';

    // Scroll down first
    await I.swipeUpFixed(favoriteListContainer);
    await I.waitFor('shortWait');
    await I.swipeUpFixed(favoriteListContainer);
    await I.waitFor('shortWait');

    // Test scroll to top button
    const scrollToTopButton = '//*[@id="scrollButton"]';
    I.waitForElement(scrollToTopButton);
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('365.Test_item_No.36_scrollToTop.png');

    // Dont see scroll to top button
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
});
Scenario('test Item 48 Various settings', async ({ I }) => {
    // Open settings
    await favoritesList.openVariousSettings();
    I.saveScreenshot('365.Test_item_No.48_variousSettings.png');

    // Toggle display settings
    I.clickFixed('//label[.//p[contains(text(), "出来高、始・高・安値を表示")]]');
    await I.waitFor('shortWait');

    I.clickFixed('//label[.//p[contains(text(), "気配を表示")]]');
    await I.waitFor('shortWait');

    I.clickFixed('//label[.//p[contains(text(), "ミニチャートを表示")]]');
    await I.waitFor('shortWait');

    // Save settings
    I.clickFixed('//button[.//div[contains(text(), "確定する")]]');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.48_afterSettingsChange.png');
});
Scenario('test Item 6 Open Domestic Stock Detail', async ({ I }) => {
    // Click on a domestic stock item
    const domesticStockItem = "//*[@data-testid='favoritesList_favoriteSymbolList_id']//div[contains(@data-testid, 'favoritesList_favoriteSymbolLineIndex')][1]";
    I.waitForElement(domesticStockItem);
    I.clickFixed(domesticStockItem);

    // Verify navigation to detail page
    await I.waitForElement('//*[@data-testid="commonChart_howToViewChart_id"]');
    I.saveScreenshot('365.Test_item_No.6_domestic_stock_detail.png');
    await I.waitFor();
    // Go back to favorites list
    await I.backToPreviousScreen();
});
// ==========
Scenario('test Item 8 Open US Stock Detail', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
    await I.refreshPage();
    await I.waitFor();
    const listContainer = '//*[@data-testid="favoritesList_favoriteSymbolList_id"]';
    I.waitForElement(listContainer);
    await I.waitFor();

    const usStockItem = '//*[@data-testid="favoritesList_favoriteSymbolLineUsStock_id_1"]';

    I.waitForElement(usStockItem, 10);
    I.scrollAndClick(usStockItem);
    I.see('個別銘柄情報', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.8_us_stock_detail.png');


});
Scenario('test Item 10 Open Favorite Stock Details (Index) ', async ({ I }) => {
    // Make sure list is loaded
    I.waitForElement('//*[@data-testid="favoritesList_favoriteSymbolList_id"]');
    await I.waitFor();

    // Find any index in list
    const indexSelector = '(//*[contains(@data-testid, "favoritesList_favoriteSymbolLineIndex_id")])[1]';

    I.waitForElement(indexSelector, 2);
    I.clickFixed(indexSelector);
    await I.waitFor('mediumWait');
    I.see('マーケット', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.10_index_detail.png');
    I.backToPreviousScreen();


});
Scenario('test Item 12 Open Favorite Stock Details (investment fund) ', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user35 });
    await I.waitFor();
    await I.refreshPage();
    await I.waitFor();
    // Make sure list is loaded
    I.waitForElement('//*[@data-testid="favoritesList_favoriteSymbolList_id"]');
    await I.waitFor();

    // Find any fund in list
    const fundSelector = '(//*[contains(@data-testid, "favoritesList_favoriteSymbolLineFund_id")])[1]';

    I.waitForElement(fundSelector, 2);
    I.clickFixed(fundSelector);
    await I.waitFor('mediumWait');
    I.see('ファンド詳細', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.12_fund_detail.png');
    I.backToPreviousScreen();
});
Scenario('test Item 18 Open Unregistered Symbol Search', async ({ I }) => {
    const favoriteListName = '//*[@data-testid="favoritesList_favoriteListName_id"]';
    const common_MenuItem_id = '//*[@data-testid="common_MenuItem_id" and @value="kabu1"]';
    I.seeElement(favoriteListName);
    I.clickFixed(favoriteListName);
    I.clickFixed(common_MenuItem_id);
    await I.waitFor();


    const unregisteredItem = '//*[contains(text(), "未登録") or contains(@class, "未登録")]';
    I.waitForElement(unregisteredItem, 2);
    I.scrollAndClick(unregisteredItem);

    I.see('銘柄検索', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.18_symbol_search.png');
    // back
    I.backToPreviousScreen();
});
Scenario('test Item 14 Open Domestic Stock from Grid View', async ({ I }) => {
    const favoriteListName = '//*[@data-testid="favoritesList_favoriteListName_id"]';
    const common_MenuItem_id = '//*[@data-testid="common_MenuItem_id" and @value="1"]';
    I.seeElement(favoriteListName);
    I.clickFixed(favoriteListName);
    I.clickFixed(common_MenuItem_id);
    await I.waitFor();
    // Change to grid view
    await I.tapLocationOfElement('[data-testid="favoritesList_displayFormatSelect_id"] svg#grid');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.14_grid_view.png');
    // Find domestic stock directly by ID
    const domesticStockSelector = '//*[@data-testid="favoritesList_favoriteSymbolPanelStock_id_2"]';
    I.waitForElement(domesticStockSelector, 2);
    I.clickFixed(domesticStockSelector);
    await I.waitFor('mediumWait');
    I.see('銘柄詳細', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.14_domestic_stock_detail.png');
    I.backToPreviousScreen();
});
Scenario('test Item 15 Open US Stock from Grid View', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
    await I.refreshPage();
    await I.waitFor('mediumWait');
    const favoriteListName = '//*[@data-testid="favoritesList_favoriteListName_id"]';
    const common_MenuItem_id = '//*[@data-testid="common_MenuItem_id" and @value="1"]';
    I.seeElement(favoriteListName);
    I.clickFixed(favoriteListName);
    await I.waitFor();
    I.clickFixed(common_MenuItem_id);
    await I.waitFor();
    // Change to grid view
    await I.tapLocationOfElement('[data-testid="favoritesList_displayFormatSelect_id"] svg#grid');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.14_grid_view.png');
    // Find domestic stock directly by ID
    const usStockSelector = '//*[@data-testid="favoritesList_favoriteSymbolPanelUsStock_id_1"]';
    I.waitForElement(usStockSelector, 2);
    I.clickFixed(usStockSelector);
    await I.waitFor('mediumWait');
    I.see('個別銘柄情報', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.15_us_stock_detail.png');
    I.backToPreviousScreen();
});
Scenario('test Item 16 Open Index from Grid View', async ({ I }) => {
    const favoriteListName = '//*[@data-testid="favoritesList_favoriteListName_id"]';
    const common_MenuItem_id = '//*[@data-testid="common_MenuItem_id" and @value="1"]';
    I.seeElement(favoriteListName);
    I.clickFixed(favoriteListName);
    I.clickFixed(common_MenuItem_id);
    await I.waitFor();
    // Change to grid view
    await I.tapLocationOfElement('[data-testid="favoritesList_displayFormatSelect_id"] svg#grid');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.14_grid_view.png');
    const indexSelector = '//*[@data-testid="favoritesList_favoriteSymbolPanelIndex_id_3"]';
    I.waitForElement(indexSelector, 2);
    I.clickFixed(indexSelector);
    await I.waitFor('mediumWait');
    I.saveScreenshot('365.Test_item_No.16_index_detail_from_grid_view.png');
    I.backToPreviousScreen();
});
Scenario('test Item 17 Open Fund from Grid View', async ({ I }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
    await I.refreshPage();
    await I.waitFor('mediumWait');
    const favoriteListName = '(//*[contains(@data-testid, "favoritesList_favoriteSymbolPanelFund")])[1]';
    I.seeElement(favoriteListName);
    I.clickFixed(favoriteListName);
    await I.waitFor();
    I.see('ファンド詳細', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('365.Test_item_No.17_fund_detail_from_grid_view.png');
    I.backToPreviousScreen();
});
Scenario('test Item 19 Favorite List Name Input', async ({ I }) => {
    // back to list view
    I.clickFixed('#list path');
    await I.waitFor();
    I.waitForElement('//*[@data-testid="favoritesList_edit_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_edit_id"]');
    await I.waitFor();
    I.seeElement('//*[@data-testid="favoritesList_favoriteListName_id"]');
    I.saveScreenshot('365.Test_item_No.19_favoriteListNameInput.png');
    I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
});
Scenario('test Item 20 Complete Button', async ({ I }) => {
    // Enter edit mode directly
    I.waitForElement('//*[@data-testid="favoritesList_edit_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_edit_id"]');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.20_editMode.png');

    // Click complete button
    I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.20_after_complete.png');

    // Verify we're back in normal mode (edit button is visible)
    I.seeElement('//*[@data-testid="favoritesList_edit_id"]');
});
Scenario('test Item 21 Delete Symbol from Normal List', async ({ I }) => {
    // Enter edit mode
    I.waitForElement('//*[@data-testid="favoritesList_edit_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_edit_id"]');
    await I.waitFor();

    // Save screenshot before deletion
    I.saveScreenshot('365.Test_item_No.21_before_delete.png');

    // Save the name of the first stock symbol before deletion
    const symbolListSelector = '//*[@data-testid="favoritesList_favoriteSymbolList_id"]';
    const firstItemSelector = `${symbolListSelector}/div[1]//p[1]`;
    const firstSymbolName = await I.grabTextFrom(firstItemSelector);
    // Save screenshot before deletion
    I.saveScreenshot('365.Test_item_No.21_before_delete.png');

    // Find and click the first delete button in the list
    const deleteButtonSelector = `(${symbolListSelector}//*[@data-testid="favoritesList_deleteButton_id"])[1]`;
    I.waitForElement(deleteButtonSelector);
    I.clickFixed(deleteButtonSelector);
    await I.waitFor();
    // Save screenshot after deletion
    I.saveScreenshot('365.Test_item_No.21_after_delete.png');

    // Check if there is a change after deletion
    const newFirstSymbolName = await I.grabTextFrom(firstItemSelector);
    // Compare to ensure the first symbol has changed (old symbol was deleted)
    I.assertNotEqual(firstSymbolName, newFirstSymbolName, 'Symbol was not deleted properly');

    // Exit edit mode
    I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
    await I.waitFor();
});
Scenario('test Item 22 Drag and Drop to Reorder', async ({ I }) => {
    // Enter edit mode
    I.waitForElement('//*[@data-testid="favoritesList_edit_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_edit_id"]');
    await I.waitFor();

    // Save screenshot before drag and drop
    I.saveScreenshot('365.Test_item_No.22_before_drag_and_drop.png');

    // Identify container containing elements
    const listContainer = '//*[@data-testid="favoritesList_favoriteSymbolList_id"]';
    I.waitForElement(listContainer);

    // Function to create selectors for elements
    const getItemSelectors = (index) => {
        return {
            item: `(//*[@data-testid="favoritesList_sortHandler_id"])[${index + 1}]/ancestor::div[contains(@data-rbd-draggable-id, "")]`,
            dragButton: `(//*[@data-testid="favoritesList_sortHandler_id"])[${index + 1}]`
        };
    };
    // Get selectors for source and target elements
    const sourceSelectors = getItemSelectors(0);
    const targetSelectors = getItemSelectors(1);

    // Ensure elements exist
    I.waitForElement(sourceSelectors.dragButton);
    I.waitForElement(targetSelectors.dragButton);

    // Function to get text of element
    const getItemText = async (selector) => await I.grabTextFrom(`${selector}//p[1]`);

    // Get initial text
    const sourceText = await getItemText(sourceSelectors.item);
    const targetText = await getItemText(targetSelectors.item);
    // Function to perform drag and drop
    const performDragAndDrop = async (source, target) => {
        await I.executeScript((sourceSelector, targetSelector) => {
            const getElement = (selector) =>
                document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;

            const sourceButton = getElement(sourceSelector) as HTMLElement;
            const targetButton = getElement(targetSelector) as HTMLElement;

            if (!sourceButton || !targetButton) {
                console.error('Source or target element not found');
                return;
            }

            const sourceRect = sourceButton.getBoundingClientRect();
            const targetRect = targetButton.getBoundingClientRect();

            const createMouseEvent = (type, x, y) =>
                new MouseEvent(type, { bubbles: true, cancelable: true, view: window, clientX: x, clientY: y });

            // Mouse down on source
            sourceButton.dispatchEvent(createMouseEvent('mousedown', sourceRect.x, sourceRect.y));

            // Move in steps from source to target
            const steps = 20;
            const xStep = (targetRect.x - sourceRect.x) / steps;
            const yStep = (targetRect.y - sourceRect.y) / steps;

            let currentStep = 0;
            const moveInterval = setInterval(() => {
                if (currentStep <= steps) {
                    document.dispatchEvent(createMouseEvent('mousemove',
                        sourceRect.x + xStep * currentStep,
                        sourceRect.y + yStep * currentStep));
                    currentStep++;
                } else {
                    clearInterval(moveInterval);
                    // Mouse up on target
                    targetButton.dispatchEvent(createMouseEvent('mouseup', targetRect.x, targetRect.y));
                }
            }, 50);
        }, source, target);
    };

    // Perform drag and drop
    await performDragAndDrop(sourceSelectors.dragButton, targetSelectors.dragButton);
    await I.waitFor('longWait');

    // Check result
    const newSourceText = await getItemText(sourceSelectors.item);
    const newTargetText = await getItemText(targetSelectors.item);

    // Check success and save screenshot
    if (newSourceText === targetText && newTargetText === sourceText) {
        I.saveScreenshot('365.Test_item_No.22_drag_and_drop_success.png');
    } else {
        I.saveScreenshot('365.Test_item_No.22_drag_and_drop_failed.png');
    }

    // Exit edit mode
    I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
    await I.waitFor();
});
Scenario('test Item 40 Grid View during Edit Mode', async ({ I }) => {
    // Change to grid view
    await I.tapLocationOfElement('[data-testid="favoritesList_displayFormatSelect_id"] svg#grid');
    await I.waitFor();

    // Enter edit mode
    I.waitForElement('//*[@data-testid="favoritesList_edit_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_edit_id"]');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.40_grid_edit_mode.png');

    // Function to create selectors for elements in grid view - edit mode
    const getItemSelectors = (index) => {
        return {
            item: `//*[@data-testid="favoritesList_favoriteSymbolPanelEdit_id_${index}"]`,
            // In grid + edit mode, all items are draggable, no need to handle separately
            dragButton: `//*[@data-testid="favoritesList_favoriteSymbolPanelEdit_id_${index}"]`
        };
    };

    // Get selector for source and target elements
    const sourceSelectors = getItemSelectors(0);
    const targetSelectors = getItemSelectors(1);

    // Ensure elements exist
    I.waitForElement(sourceSelectors.item, 5);
    I.waitForElement(targetSelectors.item, 5);

    // Get text of elements to confirm after drag and drop
    // Find a text element inside panel
    const getItemText = async (selector) => {
        try {
            return await I.grabTextFrom(`${selector}//div`);
        } catch (e) {
            return await I.grabTextFrom(`${selector}//p`);
        }
    };

    // Get initial text
    const sourceText = await getItemText(sourceSelectors.item);
    const targetText = await getItemText(targetSelectors.item);

    // Function to perform drag and drop
    const performDragAndDrop = async (source, target) => {
        await I.executeScript((sourceSelector, targetSelector) => {
            const getElement = (selector) =>
                document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;

            const sourceButton = getElement(sourceSelector) as HTMLElement;
            const targetButton = getElement(targetSelector) as HTMLElement;

            if (!sourceButton || !targetButton) {
                console.error('Source or target element not found');
                return;
            }

            const sourceRect = sourceButton.getBoundingClientRect();
            const targetRect = targetButton.getBoundingClientRect();

            const createMouseEvent = (type, x, y) =>
                new MouseEvent(type, { bubbles: true, cancelable: true, view: window, clientX: x, clientY: y });

            // Mouse down on source
            sourceButton.dispatchEvent(createMouseEvent('mousedown', sourceRect.x, sourceRect.y));

            // Move in steps from source to target
            const steps = 20;
            const xStep = (targetRect.x - sourceRect.x) / steps;
            const yStep = (targetRect.y - sourceRect.y) / steps;

            let currentStep = 0;
            const moveInterval = setInterval(() => {
                if (currentStep <= steps) {
                    document.dispatchEvent(createMouseEvent('mousemove',
                        sourceRect.x + xStep * currentStep,
                        sourceRect.y + yStep * currentStep));
                    currentStep++;
                } else {
                    clearInterval(moveInterval);
                    // Mouse up on target
                    targetButton.dispatchEvent(createMouseEvent('mouseup', targetRect.x, targetRect.y));
                }
            }, 50);
        }, source, target);
    };

    // Perform drag and drop
    await performDragAndDrop(sourceSelectors.dragButton, targetSelectors.dragButton);
    await I.waitFor('longWait');

    // Check result
    const newSourceText = await getItemText(sourceSelectors.item);
    const newTargetText = await getItemText(targetSelectors.item);

    // Check success and save screenshot
    if (newSourceText !== sourceText || newTargetText !== targetText) {
        I.saveScreenshot('365.Test_item_No.40_drag_and_drop_success.png');
    } else {
        I.saveScreenshot('365.Test_item_No.40_drag_and_drop_failed.png');
    }

    // Exit edit mode
    I.clickFixed('//*[@data-testid="favoritesList_complete_id"]');
    await I.waitFor();

    // Change back to list view
    I.clickFixed('#list path');
    await I.waitFor();
});
Scenario('test Item 23 Close Button in List Settings Modal', async ({ I }) => {
    // First open settings modal by clicking settings button
    I.waitForElement('//*[@data-testid="favoritesList_setting_id"]');
    I.clickFixed('//*[@data-testid="favoritesList_setting_id"]');
    // Verify settings modal is displayed
    I.waitForElement('//*[@id="bottom-sheet-container"]', 2);
    I.saveScreenshot('365.Test_item_No.23_settings_modal_open.png');
    // Click X (close) button
    I.clickFixed('//*[@data-testid="favoritesList_closeButton_id"]');
    // Verify modal is closed
    I.saveScreenshot('365.Test_item_No.23_after_close.png');
});
Scenario('test Item 25 Edit Button in List Settings', async ({ I }) => {
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();
    // Click edit option
    I.clickFixed('//p[contains(text(), "編集")]');
    await I.waitFor();

    // Verify edit screen is displayed (see button edit mode)
    I.seeElement('//*[@data-testid="favoritesList_complete_id"]');
    I.saveScreenshot('365.Test_item_No.25_edit_mode.png');
});
Scenario('test Item 26 & 29 Delete List Button and Cancel', async ({ I }) => {
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();
    // Click delete list button
    I.clickFixed('//p[contains(text(), "削除")]');
    await I.waitFor();
    // case 26: Take screenshot before cancel
    I.saveScreenshot('365.Test_item_No.26_delete_list_button.png');
    // Click cancel button
    I.clickFixed('//footer[contains(@class, "chakra-modal__footer")]//button[contains(text(), "キャンセル")]');
    await I.waitFor();
    // Verify cancel modal is closed
    I.dontSeeElement('//footer[contains(@class, "chakra-modal__footer")]//button[contains(text(), "キャンセル")]');
    I.saveScreenshot('365.Test_item_No.26_delete_list_button_after_cancel.png');
});
Scenario('test Item 27 Open New Favorite List', async ({ I }) => {
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();
    I.waitForElement('//*[@data-testid="favoritesList_addFavoriteListButton_id"]', 2);
    I.clickFixed('//*[@data-testid="favoritesList_addFavoriteListButton_id"]');
    await I.waitFor();
    I.seeElement('//*[@data-testid="favoritesList_createButton_id"]');


    I.saveScreenshot('365.Test_item_No.27_add_list_screen.png');
    //close add list screen
    I.clickFixed('//*[@data-testid="favoritesList_cancelButton_id"]');
    await I.waitFor();
});
Scenario('test Item 30 Confirm Delete List', async ({ I }) => {
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();
    // Click Delete
    I.clickFixed('//p[contains(text(), "削除")]');
    await I.waitForElement('//div[contains(@class, "chakra-modal__content-container")]//button[contains(text(), "削除") and contains(@class, "chakra-button")]');
    await I.clickFixed('//div[contains(@class, "chakra-modal__content-container")]//button[contains(text(), "削除") and contains(@class, "chakra-button")]');
    await I.waitFor();
    I.dontSeeElement('//div[contains(@class, "chakra-modal__content-container")]//button[contains(text(), "削除") and contains(@class, "chakra-button")]');
    I.saveScreenshot('365.Test_item_No.30_after_delete.png');
});
Scenario('test Item 31 Favorite List Name Input (have space)', async ({ I }) => {
    await favoritesList.openSettings();
    // Click add new list button
    I.waitForElement('//*[@data-testid="favoritesList_addFavoriteListButton_id"]', 2);
    await I.clickFixed('//*[@data-testid="favoritesList_addFavoriteListButton_id"]');
    await I.waitFor('shortWait');
    I.seeElement('//*[@data-testid="favoritesList_createButton_id"]');
    await favoritesList.addNewFavoriteList(' 3 1 ');


    await I.saveScreenshot('365.Test_item_No.31_add_list_screen.png');
    // Close modal
    await I.clickFixed('//*[@data-testid="favoritesList_cancelButton_id"]');
    await I.waitFor();
    await I.clickFixed('//*[@data-testid="favoritesList_closeButton_id"]');
});

Scenario('test Item 31-1 Swipe left or right', async ({ I }) => {
    await favoritesList.openSettings();
    // Click add new list button
    I.waitForElement('//*[@data-testid="favoritesList_addFavoriteListButton_id"]', 2);
    I.clickFixed('//*[@data-testid="favoritesList_addFavoriteListButton_id"]');
    await I.waitFor('shortWait');
    I.seeElement('//*[@data-testid="favoritesList_createButton_id"]');
    // Input name
    await favoritesList.addNewFavoriteList('AAAAAAAAAABBBBBBBBBBCCCCCCCCCCDDDDDDDDDD');
    // Swipe left
    await I.executeScript(() => {
        const inputElement = document.querySelector('[data-testid="favoritesList_favoriteListName_id"] input') as HTMLInputElement;
        inputElement.focus();
        const length = inputElement.value.length;
        inputElement.setSelectionRange(length, length);
    });
    await I.saveScreenshot('365.Test_item_No.31-1_Swipe_left_to_scroll_the_text_in_the_direction_of_the_swipe.png');
    // Swipe right
    await I.executeScript(() => {
        const inputElement = document.querySelector('[data-testid="favoritesList_favoriteListName_id"] input') as HTMLInputElement;
        inputElement.focus();
        inputElement.setSelectionRange(0, 0);
    });
    await I.saveScreenshot('365.Test_item_No.31-1_Swipe_right_to_scroll_the_text_in_the_direction_of_the_swipe.png');
    // Leave focus input
    await I.tap('//*[@id="modal-overlay"]/div/div');
});

Scenario('test Item 33 Cancel Create List', async ({ I }) => {
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();
    await I.clickFixed('//*[@data-testid="favoritesList_addFavoriteListButton_id"]');
    await I.waitFor();
    await I.clickFixed('//*[@data-testid="favoritesList_cancelButton_id"]');
    await I.waitFor();
    //after create, open settings modal again

    //TODO: Can test because mock data - verify list name don't see text "cancel"
    // const detailTextSelector = `//p[contains(@class, "chakra-text") and contains(text(), "cancel")]`;
    // I.dontSeeElement(detailTextSelector);

    I.saveScreenshot('365.Test_item_No.33_after_cancel.png');
});
Scenario('test Item 34 Create New List', async ({ I }) => {
    // First open settings modal by clicking settings button
    await favoritesList.openSettings();
    I.waitForElement('//*[@data-testid="favoritesList_addFavoriteListButton_id"]', 2);
    I.clickFixed('//*[@data-testid="favoritesList_addFavoriteListButton_id"]');
    await I.waitFor('shortWait');
    await favoritesList.addNewFavoriteList('34');
    I.clickFixed('//*[@data-testid="favoritesList_createButton_id"]');
    await I.waitFor();
    //after create, open settings modal again to check
    await favoritesList.openSettings();
    //TODO: Can test because mock data - verify list name is abc123.see text abc123 in list
    // const detailTextSelector = `//p[contains(@class, "chakra-text") and contains(text(), "test Item 34 Create New List")]`;
    // I.waitForElement(detailTextSelector, 1);

    I.saveScreenshot('365.Test_item_No.34_add_list_screen.png');
    I.clickFixed('//*[@data-testid="favoritesList_closeButton_id"]');
});
Scenario('test Item 90-93 Settings Screen Configuration', async ({ I }) => {
    // Case 90: Display Settings
    await favoritesList.openVariousSettings();
    I.saveScreenshot('365.Test_item_No.90_display_settings.png');
    // Toggle display options (Case 90)
    I.clickFixed('//label[.//p[contains(text(), "出来高、始・高・安値を表示")]]');
    await I.waitFor('shortWait');
    // Case 91: Auto-refresh interval
    I.clickFixed('//label[.//p[contains(text(), "5秒")]]'); // Choose 5 seconds
    await I.waitFor('shortWait');
    I.saveScreenshot('365.Test_item_No.91_auto_refresh_settings.png');
    // // Case 92: Confirm settings
    I.clickFixed('//label[.//p[contains(text(), "気配を表示")]]');
    I.clickFixed('//*[@data-testid="favoritesList_confirmButton_id"]');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.92_after_confirm.png');

    // Open settings again to test cancel
    await favoritesList.openVariousSettings();

    // Case 93: Cancel settings
    await I.waitForElement('//button[.//div[contains(text(), "キャンセル")]]');
    await I.clickFixed('//button[.//div[contains(text(), "キャンセル")]]');
    await I.waitFor();
    I.saveScreenshot('365.Test_item_No.93_after_cancel.png');
});
