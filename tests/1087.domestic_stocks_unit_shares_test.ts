import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('OrderInquiry - DomesticStocksUnitShares');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user39 });
    I.waitFor();

});
After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 1: Check UI of Domestic Stocks Order Inquiry page', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();

    I.saveScreenshot('1087.Test_item_No.1_UI_of_Domestic_Stocks_Order_Inquiry_page.png');
});
Scenario('Test Item 2-1: Check Event of Domestic Stocks Order Inquiry page - Product tab - Spot', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    const genbutsuButton = '//button[.//p[contains(text(), "現物")]]';

    await I.clickFixed(genbutsuButton);
    await I.waitFor('shortWait');
    I.see('現物株式');
    await I.saveScreenshot('1087.Test_item_No.2.1_after_click_genbutsu_tab.png');
});
Scenario('Test Item 2-2: Check Event of Domestic Stocks Order Inquiry page - Product tab - Credit', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    const creditButton = '//button[.//p[contains(text(), "信用")]]';
    await I.clickFixed(creditButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot(`1087.Test_item_No.2.2_after_click_Product_tab_Credit.png`);
});
Scenario('Test Item 2-3: Check Event of Domestic Stocks Order Inquiry page - Product tab - Futures options', async ({ orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    const futuresOptionsButton = '//button[.//p[contains(text(), "先物OP")]]';
    // {member-site-url}/ap/iPhone/Trade/OrderDeriv/OrderStatusSelect へ遷移する
    await common.clickCardItem(futuresOptionsButton, '/ap/iPhone/Trade/OrderDeriv/OrderStatusSelect', 'kcMemberSite');
});
Scenario('Test Item 2-4: Check Event of Domestic Stocks Order Inquiry page - Product tab - Small stocks', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    const smallStocksButton = '//button[.//p[contains(text(), "プチ株")]]';
    await I.clickFixed(smallStocksButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot('1087.Test_item_No.2.4_after_click_Product_tab_small_stocks.png');
});
Scenario('Test Item 2-5: Check Event of Domestic Stocks Order Inquiry page - Product tab - Investment trusts', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    const investmentTrustsButton = '//button[.//p[contains(text(), "投信")]]';
    await I.clickFixed(investmentTrustsButton);
    await I.waitFor('shortWait');
    await I.saveScreenshot('1087.Test_item_No.2.5_after_click_Product_tab_Investment_trusts.png');
});
Scenario('Test Item 2-6: Check Event of Domestic Stocks Order Inquiry page - Product tab - Foreign stocks', async ({ orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    const foreignStocksButton = '//button[.//p[contains(text(), "外国株式")]]';
    // {member-site-url}/ap/iPhone/ForeignStocks/USStock/OrderStatus/List へ遷移する
    await common.clickCardItem(foreignStocksButton, '/ap/iPhone/ForeignStocks/USStock/OrderStatus/List', 'kcMemberSite');
});
Scenario('Test Item 3: Check button filter ', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();

    const settingButton = '//*[@data-testid="orderInquiry_filter_id"]';
    I.clickFixed(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.3_show_filler_modal.png');
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';
    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.3_close_filler_modal.png');
});
Scenario('Test Item 4: Details list Scroll Set the relevant area as the scroll range', async ({ I }) => {
    // order-inquiry--tabpanel-0
    const orderInquiryTabpanel = '//div[@id="order-inquiry--tabpanel-0"]';

    I.scrollToElement(orderInquiryTabpanel);
    I.saveScreenshot('1087.Test_item_No.4_Details_list_Scroll_Set_the_relevant_area_as_the_scroll_range.png');

});
Scenario('Test Item 5: Check order detail navigation', async ({ I, orderStatus }) => {
    // Ensure that the current tab is 現物
    const genbutsuButton = '//button[.//p[contains(text(), "現物")]]';
    I.clickFixed(genbutsuButton);
    await I.waitFor('shortWait');
    I.clickFixed('//*[@data-testid="orderInquiry_orderDetail_1_id"]');
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.5_order_detail_2.png');
    await orderStatus.backToOrderInquiry();
});
Scenario('Test Item 16: Check scroll top', async ({ I }) => {
    const orderInquiryCaution = '//*[@data-testid="orderInquiry_caution_id"]';
    // Scroll down
    await I.scrollToElement(orderInquiryCaution);
    await I.waitFor('shortWait');

    // Test scroll to top button
    const scrollToTopButton = '//*[@id="scrollButton"]';

    I.saveScreenshot('1087.Test_item_No.16_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');

    // Dont see scroll to top button
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1087.Test_item_No.16_scrollToTop_dont_see_button.png');
});
Scenario('Test Item 17: Display pull-down menu excluding duplicates', async ({ I }) => {
    const settingButton = '//*[@data-testid="orderInquiry_filter_id"]';

    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 全銘柄
    const allMokuhyouButton = '//p[contains(text(), "全銘柄")]';

    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.17_display_pull-down_menu_excluding_duplicates.png');
    //click button have p text 三菱ＵＦＪ 8306
    const mitsubishiUfj8306Button = '//p[contains(text(), "三菱ＵＦＪ 8306")]';

    I.clickFixed(mitsubishiUfj8306Button);
})
Scenario('Test Item 18: Display Order Display the drop-down menu', async ({ I }) => {
    const settingButton = '//*[@data-testid="orderInquiry_filter_id"]';

    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 状態更新順
    const statusUpdateButton = '//*[@data-testid="filter_displayOrder_id"]';

    I.clickFixed(statusUpdateButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.18_display_order_display_the_drop-down_menu.png');
    //click button have p text 注文日時順
    const orderDateButton = '//p[contains(text(), "注文日時順")]';

    I.clickFixed(orderDateButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.18_display_order_display_the_drop-down_menu_order_date.png');
})
Scenario('Test Item 19: Set initial display sticks and order', async ({ I }) => {

    const clearButton = '//button[contains(text(), "クリア")]';

    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.19_set_initial_display_sticks_and_order.png');
})
Scenario('Test Item 20: Filter and sort based on entered parameters', async ({ I }) => {
    const settingButton = '//*[@data-testid="orderInquiry_filter_id"]';

    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    //click button have p text 全銘柄
    const allMokuhyouButton = '//*[@data-testid="filter_displaySymbol_id"]';

    I.clickFixed(allMokuhyouButton);
    await I.waitFor('shortWait');

    //click button have p text 三菱ＵＦＪ 8306
    const mitsubishiUfj8306Button = '//p[contains(text(), "三菱ＵＦＪ 8306")]';

    I.clickFixed(mitsubishiUfj8306Button);
    await I.waitFor('shortWait');
    //click button have p text 状態更新順
    const statusUpdateButton = '//*[@data-testid="filter_displayOrder_id"]';

    I.clickFixed(statusUpdateButton);
    await I.waitFor('shortWait');

    //click button have p text 注文日時順
    const orderDateButton = '//p[contains(text(), "注文日時順")]';

    I.clickFixed(orderDateButton);
    await I.waitFor('shortWait');
    const submitButton = '//button[contains(text(), "確定する")]';

    I.clickFixed(submitButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.20_filter_and_sort_based_on_entered_parameters.png');

})
Scenario('Test Item 21: Close fillter and sort modal', async ({ I }) => {
    const settingButton = '//*[@data-testid="orderInquiry_filter_id"]';

    await I.tapLocationOfElement(settingButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.21_show_setting_and_sort_modal.png');
    const closeButton = '//button[.//img[@src="/img/close.svg"]]';

    I.clickFixed(closeButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.21_close_setting_and_sort_modal.png');
});
//Stock Detail
Scenario('Test Go to Stock Detail', async ({ I, orderStatus }) => {
    // Ensure that the current tab is 現物
    const genbutsuButton = '//button[.//p[contains(text(), "現物")]]';

    await I.scrollAndClick(genbutsuButton);
    await I.waitFor();

    I.clickFixed('//*[@data-testid="orderInquiry_orderDetail_1_id"]');
    await I.waitFor('shortWait');
    I.saveScreenshot('1087.Test_item_No.40_go_to_stock_detail.png');
    await orderStatus.backToOrderInquiry();
});
Scenario('Test Item 41: Relay order number', async ({ I }) => {
    const relayOrderNumberButton = '//p[contains(text(), "当注文が約定次第、下記の注文が発注されます。")]';

    I.scrollToElement(relayOrderNumberButton);
    const relayLinks = '//a[contains(@class, "chakra-link") and contains(text(), "relayOrderList")]';
    const linkCount = await I.grabNumberOfVisibleElements(relayLinks);

    if (linkCount > 0) {
        I.saveScreenshot('1087.Test_item_No.41_found_relay_links.png');

        for (let i = 1; i <= linkCount; i++) {
            const link = `(${relayLinks})[${i}]`;

            I.clickFixed(link);
            await I.waitFor('mediumWait');
            I.saveScreenshot(`1087.Test_item_No.41_after_click_link_${i}.png`);
        }
    } else {
        I.saveScreenshot('1087.Test_item_No.41_no_relay_links.png');
    }
});
Scenario('Test Item 37: Transition to Domestic Stock Spot Trading - Modify/Cancel Order - Modify', async ({ I, orderStatus }) => {
    await I.scrollAndClick('//*[@data-testid="orderInquiry_orderDetail_1_id"]');
    await I.waitFor('shortWait');
    const modifyOrderButton = '//button[contains(text(), "注文内容を訂正")]';

    await I.scrollAndClick(modifyOrderButton);
    I.saveScreenshot('1087.Test_item_No.37_transition_to_domestic_stock_spot_trading_modify_cancel_order_modify.png');
    // back
    await I.backToPreviousScreen()
});

Scenario('Test Item 38: Transition to Domestic Stock Spot Trading - Modify/Cancel Order - Cancel', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    await I.waitFor();
    const genbutsuButton = '//button[.//p[contains(text(), "現物")]]';

    I.clickFixed(genbutsuButton);
    await I.waitFor();

    I.clickFixed('//*[@data-testid="orderInquiry_orderDetail_1_id"]');
    await I.waitFor('shortWait');
    const cancelOrderButton = '//button[contains(text(), "注文を取消")]';

    I.scrollAndClick(cancelOrderButton);
    I.saveScreenshot('1087.Test_item_No.38_transition_to_domestic_stock_spot_trading_modify_cancel_order_cancel.png');
    // back
    orderStatus.backToOrderInquiry();
});
Scenario('Test Item 43: Order Execution Inquiry Order Inquiry - Domestic Stocks - Unit Stocks - Go to List', async ({ I, orderStatus }) => {
    await orderStatus.goToDomesticStocksUnitSharesPage();
    await I.waitFor();
    const genbutsuButton = '//button[.//p[contains(text(), "現物")]]';

    I.clickFixed(genbutsuButton);
    await I.waitFor();

    I.clickFixed('//*[@data-testid="orderInquiry_orderDetail_1_id"]');
    await I.waitFor('shortWait');
    const orderExecutionInquiryButton = '//button[contains(text(), "注文約定照会")]';
    I.scrollAndClick(orderExecutionInquiryButton);
    I.saveScreenshot('1087.Test_item_No.43_order_execution_inquiry_order_inquiry_domestic_stocks_unit_stocks_go_to_list.png');
});
