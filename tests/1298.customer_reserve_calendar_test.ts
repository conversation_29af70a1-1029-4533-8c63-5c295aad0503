import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveCalendar');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user46 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI of Customer Reserve Calendar Page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();
    I.saveScreenshot('1298.Test_item_No.0_UI_of_Customer_Reserve_Calendar_Page.png');
});
Scenario('Test Item 1: Check Go To Reserve Plan Page', async ({ I }) => {
    // Go to reserve plan page
    await I.click('//*[@data-testid="reservePlan_reservePlan_tab"]');
    await I.waitFor('shortWait');
    I.saveScreenshot('1298.Test_item_No.1_UI_of_Reserve_Plan_Page.png');
});
Scenario('Test Item 2: Check Go To Reserve History Page', async ({ I }) => {
    // Go to reserve history page
    await I.click('//*[@data-testid="reservePlan_reserveHistory_tab"]');
    await I.waitFor('shortWait');
    I.saveScreenshot('1298.Test_item_No.2_UI_of_Reserve_History_Page.png');
    // Go back to reserve calendar page to continue test
    await I.click('//*[@data-testid="reservePlan_reserveCalendar_tab"]');
}); 
Scenario('Test Item 22: Check Previous Page Navigation', async ({ I }) => {
    const nextPageButton = '//*[@data-testid="reserveCalendar_next_id"]';
    await I.clickFixed(nextPageButton);
    await I.waitFor('shortWait');
    const previousPageButton = '//*[@data-testid="reserveCalendar_prev_id"]';
    I.saveScreenshot('1298.Test_item_No.22_Go_To_Previous_Page_before_click.png');
    I.clickFixed(previousPageButton);
    I.saveScreenshot('1298.Test_item_No.22_Go_To_Previous_Page_after_click.png');
});
Scenario('Test Item 23: Check Page Number Navigation', async ({ I }) => {
    const enabledButton = '//div[@data-testid="reserveCalendar_page_id"]//button[not(@disabled)]';
    
    try {
        if (await I.grabNumberOfVisibleElements(enabledButton) > 0) {
            I.clickFixed(`(${enabledButton})[1]`);
            await I.waitFor('shortWait');
            I.saveScreenshot('1298.Test_item_No.23_click_enabled_pagination_button.png');
        } else {
            console.log('No enabled pagination buttons found');
            I.saveScreenshot('1298.Test_item_No.23_no_enabled_buttons.png');
        }
    } catch (e) {
        console.log('Error when clicking pagination button:', e);
        I.saveScreenshot('1298.Test_item_No.23_error.png');
    }
});
Scenario('Test Item 24: Check Next Page Navigation', async ({ I }) => {
    const previousPageButton = '//*[@data-testid="reserveCalendar_prev_id"]';
    await I.clickFixed(previousPageButton);
    const nextPageButton = '//*[@data-testid="reserveCalendar_next_id"]';
    I.saveScreenshot('1298.Test_item_No.24_Go_To_Next_Page_before_click.png'); 
    I.clickFixed(nextPageButton);
    I.saveScreenshot('1298.Test_item_No.24_Go_To_Next_Page_after_click.png');
});
Scenario('Test Item 7: Check Reserve Detail Modal', async ({ I , accumulation}) => {
    // Check reserve detail modal
    await accumulation.goToCustomerReserveCalendarPage();
    const reserveDetailButton = '//*[@data-testid="reserveCalendar_reserveDetail_0_0_id"]';
    I.clickFixed(reserveDetailButton);
    I.saveScreenshot('1298.Test_item_No.7_Reserve_Detail_Modal.png');
    //close modal
    const cancelButton = '//button[@aria-label="cancel-btn"]';
    I.clickFixed(cancelButton);

});
Scenario('Test Item 14-1: Check navigation to Petit Stocks savings change page', async ({ I }) => {    
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
    I.clickFixed(reservePlanChangeId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1298.Test_item_No.14-1_Go_To_Petit_Stock_Savings_Change.png');
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 14-2: Check navigation to Investment Fund savings change page', async ({ I }) => {
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
    I.clickFixed(reservePlanChangeId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1298.Test_item_No.14-2_Go_To_Investment_Fund_Savings_Change.png');
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 14-3: Check navigation to Foreign MMF savings change page', async ({ I }) => {
    const foreignMMFItem = '//span[contains(@class, "chakra-badge") and contains(text(), "外M")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.clickFixed(foreignMMFItem);
    await I.waitFor('shortWait');
    const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
    await common.clickCardItem(reservePlanChangeId, 'iPhone/Trade/TeikiKaitsuke/GaikaMMF/teikiedit/GE01101.asp', 'kcMemberSite');
    await I.waitFor('shortWait');
   
});
Scenario('Test Item 15-1: Check navigation to Petit Stocks savings cancel page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();    
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);    
    I.scrollAndClick(petitStockItem);
    const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
    I.clickFixed(reservePlanCancelId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1298.Test_item_No.15-1_Go_To_Petit_Stock_Savings_Cancel.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 15-2: Check navigation to Investment Fund savings cancel page', async ({ I }) => {
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
    I.clickFixed(reservePlanCancelId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1298.Test_item_No.15-2_Go_To_Investment_Fund_Savings_Cancel.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 15-3: Check navigation to Foreign MMF savings cancel page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();
    const foreignMMFItem = '//span[contains(@class, "chakra-badge") and contains(text(), "外M")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.clickFixed(foreignMMFItem);
    await I.waitFor('shortWait');
    const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
    await common.clickCardItem(reservePlanCancelId, 'iPhone/Trade/TeikiKaitsuke/GaikaMMF/teikiedit/GE01101.asp', 'kcMemberSite');
    await I.waitFor('shortWait');
   
});
Scenario('Test Item 16-1: Check navigation to Petit Stock savings detail page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1298.Test_item_No.16-1_Go_To_Petit_Stock_Savings_Detail.png');
    const backButton = '//*[@data-testid="common_back_id"]';
    I.clickFixed(backButton);
    await I.waitFor('shortWait');
});
Scenario('Test Item 16-2: Check navigation to Investment Fund savings detail page', async ({ I, accumulation }) => {
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    I.clickFixed(reservePlanDetailId);
    await I.waitFor('shortWait');
    // Verify navigation
    I.saveScreenshot('1298.Test_item_No.16-2_Go_To_Investment_Fund_Savings_Detail.png');
    await I.backToPreviousScreen();
});
Scenario('Test Item 16-3: Check navigation to Foreign MMF savings detail page', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();    
    const foreignMMFItem = '//span[contains(@class, "chakra-badge") and contains(text(), "外M")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.clickFixed(foreignMMFItem);
    await I.waitFor('shortWait');
    const reservePlanDetailId = '//*[@data-testid="reservePlan_planDetail_id"]';
    await common.clickCardItem(reservePlanDetailId, 'iPhone/account/teikikaitsuke_sp/SetteiDetailGaikaMMF.asp', 'kcMemberSite');
    await I.waitFor('shortWait');
   
});
Scenario('Test Item 17: Check navigation to stock/fund detail information', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();
    // Check out the redirect to Petit Stock details
    const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(petitStockItem, 2);
    I.scrollAndClick(petitStockItem);
    
    // Click on link to stock detail information
    const stockDetailLink = '//*[@data-testid="reservePlan_individualSymbolInfo_id"]';
    I.clickFixed(stockDetailLink); 
    await I.waitFor('shortWait');
    
    // Verify the redirect to stock detail page
    I.saveScreenshot('1298.Test_item_No.17-1_Go_To_Stock_Detail.png');
    await I.backToPreviousScreen();
    await I.waitFor('shortWait');

    // Check out the redirect to Investment Fund details
    const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reserveCalendar_reserveDetail")][1]';
    I.waitForElement(fundItem, 2);
    I.scrollAndClick(fundItem);
    await I.waitFor('shortWait');

    // Click on link to fund detail information
    const fundDetailLink = '//*[@data-testid="reservePlan_individualSymbolInfo_id"]';
    I.clickFixed(fundDetailLink);
    await I.waitFor('shortWait');

    // Verify the redirect to fund detail page
    I.saveScreenshot('1298.Test_item_No.17-2_Go_To_Fund_Detail.png');
    await I.backToPreviousScreen();
    
});
Scenario('Test Item 35: Check filter modal', async ({ I, accumulation }) => {
    await accumulation.goToCustomerReserveCalendarPage();
    // Click on filter button
    const filterButton = '//*[@data-testid="reserveCalendar_filterSetting_id"]';
    I.clickFixed(filterButton);
    await I.waitFor('shortWait');
    // Verify the filter modal
    I.saveScreenshot('1298.Test_item_No.35_Filter_Modal.png');
 
});
Scenario('Test Item 36: Check display symbol dropdown menu', async ({ I }) => {
    // Click on display symbol dropdown menu
    const displaySymbolDropdown = '//*[@data-testid="reserveCalendar_displaySymbol_id"]';
    I.clickFixed(displaySymbolDropdown);
    await I.waitFor('shortWait');
    // Verify the display symbol dropdown menu
    I.saveScreenshot('1298.Test_item_No.36_Display_Symbol_Dropdown_Menu.png');
   
});
Scenario('Test Item 39: Check investment fund symbol list', async ({ I }) => {
    const investmentFundSymbolList = '//*[@data-testid="reserveCalendar_investmentTrust_0_id"]';
    I.clickFixed(investmentFundSymbolList);
    await I.waitFor('shortWait');
    // Verify the investment fund symbol list
    I.saveScreenshot('1298.Test_item_No.39_Investment_Fund_Symbol_List.png');
});
Scenario('Test Item 42: Check petit stock symbol list', async ({ I }) => {
    const displaySymbolDropdown = '//*[@data-testid="reserveCalendar_displaySymbol_id"]';
    I.clickFixed(displaySymbolDropdown);
    // Click on petit stock symbol list
    const petitStockSymbolList = '//*[@data-testid="reserveCalendar_petit_0_id"]';
    I.clickFixed(petitStockSymbolList);
    await I.waitFor('shortWait');
    // Verify the petit stock symbol list
    I.saveScreenshot('1298.Test_item_No.42_Petit_Stock_Symbol_List.png');
});
Scenario('Test Item 45: Check foreign currency MMF symbol list', async ({ I }) => {
    const displaySymbolDropdown = '//*[@data-testid="reserveCalendar_displaySymbol_id"]';
    I.clickFixed(displaySymbolDropdown);
    // Click on foreign currency MMF symbol list
    const foreignCurrencyMMFSymbolList = '//*[@data-testid="reserveCalendar_mmf_0_id"]';
    I.clickFixed(foreignCurrencyMMFSymbolList);
    await I.waitFor('shortWait');
    // Verify the foreign currency MMF symbol list
    I.saveScreenshot('1298.Test_item_No.45_Foreign_Currency_MMF_Symbol_List.png');
});
Scenario('Test Item 46.1: Check upper limit reset when lower limit is larger', async ({ I }) => {
    // 1. Choose value 1000 for upper limit (button 2)
    const upperLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(upperLimitButton);
    await I.waitFor('shortWait');
    // Choose value 1000 from menu of button 2
    const upperValue1000 = '(//div[contains(@class, "chakra-menu__menu-list")])[2]//button[@value="1000"]';
    I.clickFixed(upperValue1000);
    await I.waitFor('shortWait');

    // 2. Choose value 5000 for lower limit (button 1)
    const lowerLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[1]';
    I.clickFixed(lowerLimitButton);
    await I.waitFor('shortWait');
    // Choose value 5000 from menu of button 1
    const lowerValue5000 = '(//div[contains(@class, "chakra-menu__menu-list")])[1]//button[@value="5000"]';
    I.clickFixed(lowerValue5000);
    await I.waitFor('shortWait');

    // 3. Verify upper limit automatically changes to "指定なし"
    I.see('指定なし', upperLimitButton);
    I.saveScreenshot('1298.Test_item_No.46.1_Upper_Limit_Reset.png');
});
Scenario('Test Item 46.2: Check lower limit reset when upper limit is smaller', async ({ I }) => {
    // 1. Choose value 5000 for lower limit (button 1)
    const lowerLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[1]';
    I.clickFixed(lowerLimitButton);
    await I.waitFor('shortWait');
    const lowerValue5000 = '(//div[contains(@class, "chakra-menu__menu-list")])[1]//button[@value="5000"]';
    I.clickFixed(lowerValue5000);
    await I.waitFor('shortWait');

    // 2. Choose value 1000 for upper limit (button 2)
    const upperLimitButton = '(//button[contains(@class, "chakra-menu__menu-button")])[2]';
    I.clickFixed(upperLimitButton);
    await I.waitFor('shortWait');
    const upperValue1000 = '(//div[contains(@class, "chakra-menu__menu-list")])[2]//button[@value="1000"]';
    I.clickFixed(upperValue1000);
    await I.waitFor('shortWait');

    // 3. Verify lower limit automatically changes to "指定なし"
    I.see('指定なし', lowerLimitButton);
    I.saveScreenshot('1298.Test_item_No.46.2_Lower_Limit_Reset.png');
});
Scenario('Test Item 47: Check Switch product category ON/OFF', async ({ I }) => {
    const firstLabel = '//label[@value="TSUMITATE_PRODUCT_TYPE_FUND"]';
    
    try {
        for (let i = 1; i <= 3; i++) {
            I.waitForElement(firstLabel, 5);
            I.clickFixed(firstLabel);
        }
        I.saveScreenshot(`1298.Test_item_No.47_Switch_Product_Category.png`);

    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1298.Test_item_No.47_error.png');
    }
});
Scenario('Test Item 48: Check Switch payment method ON/OFF', async ({ I }) => {
    const firstLabel = '//label[@value="PAYMENT_TYPE_AZUKARI"]';
    
    try {
        for (let i = 1; i <= 3; i++) {
            I.waitForElement(firstLabel, 5);
            I.clickFixed(firstLabel);
        }
        I.saveScreenshot(`1298.Test_item_No.48_Switch_Payment_Method.png`);
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1298.Test_item_No.48_error.png');
    }
});
Scenario('Test Item 49: Check Switch display account ON/OFF', async ({ I }) => {
    const firstLabel = '//label[@value="ACCOUNT_TYPE_TOKUTEI"]';
    
    try {
        for (let i = 1; i <= 3; i++) {
            I.waitForElement(firstLabel, 5);
            I.clickFixed(firstLabel);
        }
        I.saveScreenshot(`1298.Test_item_No.49_Switch_Display_Account.png`);
    } catch (e) {
        console.log('Error when clicking label:', e);
        I.saveScreenshot('1298.Test_item_No.49_error.png');
    }
});
Scenario('Test Item 50: Check initial display', async ({ I }) => {
    const clearButton = '//button[contains(text(), "クリア")]';
    I.clickFixed(clearButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1298.Test_item_No.50_Initial_Display.png');
});
Scenario('Test Item 51: Check confirm modal', async ({ I }) => {
    const confirmButton = '//button[contains(text(), "確定する")]';
    I.clickFixed(confirmButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1298.Test_item_No.51_Confirm_Modal.png');
});
Scenario('Test Item 52b: Check go to accumulated points usage setting', async ({ I }) => {
    const setupButton = '//*[@data-testid="reserveCalendar_setup_id"]';
    I.clickFixed(setupButton);
    await I.waitFor('shortWait');
    I.saveScreenshot('1298.Test_item_No.52b_Go_To_Accumulated_Points_Usage_Setting.png');
    //back
    await I.backToPreviousScreen();
});
Scenario('Test Item 54: Check scroll top', async ({ I }) => {
    const dropDownArrow = '//*[@data-testid="reserveCalendar_caution_id"]';
    I.waitForElement(dropDownArrow, 2);
    I.scrollToElement(dropDownArrow);
    const scrollToTopButton = '//*[@id="scrollButton"]';
    I.saveScreenshot('1298.Test_item_No.54_scrollToTop_see_button.png');
    I.clickFixed(scrollToTopButton);
    await I.waitFor('shortWait');
    I.dontSeeElement('//*[@data-testid="scrollButton"]');
    I.saveScreenshot('1298.Test_item_No.54_scrollToTop_dont_see_button.png');
});
Scenario('Test Item 13: Check attention message', async ({ I }) => {
    const dropDownArrow = '//*[@data-testid="reserveCalendar_caution_id"]';
    I.waitForElement(dropDownArrow, 2);
    I.scrollAndClick(dropDownArrow);
    
    const chakraCollapse = '//*[@class="chakra-collapse"]';
    I.waitForElement(chakraCollapse, 2);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1298.Test_item_No.13_Attention_Message.png');

});