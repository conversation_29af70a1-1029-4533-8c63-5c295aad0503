import { COOKIE_KEY, USER_ID } from '../const/constant';
import depositBankAccountRegistration from '../pages/depositBankAccountRegistration';
import common from '../pages/search/common';

Feature('Deposits_Withdrawals - DepositBankAccountRegistration');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user58 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Deposit Bank Account Registration-Complete', async ({ I }) => {
    await depositBankAccountRegistration.goToCompletePage();
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.complete}.0_Deposit_Bank_Account_Registration-Complete.png`,
    );
});

// 1.お客さま基本情報 -> 以下のURLを別タブで開く
// {member-site-url}/members/personal/kokyakucard.asp
Scenario('Test Item 1: Basic Customer Information -> Open the following URL in a new tab', async ({ I }) => {
    await depositBankAccountRegistration.goToCompletePage();
    await I.scrollToElement(depositBankAccountRegistration.locators.customerInformationHref);
    await common.clickCardItem(depositBankAccountRegistration.locators.customerInformationHref, '/members/personal/kokyakucard.asp', 'external');
});

// 2.入金依頼 -> 入金依頼に遷移
Scenario('Test Item 2: Deposit Request -> Transition to Deposit Request', async ({ I }) => {
    await I.seeAndClickElement('$bankAccountRegistrationComplete_depositRequest_id', {
        waitFor: 'mediumWait',
    });
    I.saveScreenshot(
        `${depositBankAccountRegistration.prefix.complete}.2_Deposit_Request-Transition_to_Deposit_Request.png`,
    );
});
