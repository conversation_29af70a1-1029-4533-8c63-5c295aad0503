import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('Reserve - CustomerReserveFundCancelReserveOrderComplete');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Fund Cancel Reserve Order Complete', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    <PERSON><PERSON>fill<PERSON>(passwordInput, '123@abc');

    const confirmButton = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);
    I.seeElement('//p[contains(text(), "積立中止が完了しました。")]');
    I.saveScreenshot('1533.Test_item_No.0_Check_UI_Reserve_Fund_Cancel_Reserve_Order_Complete.png');
});

Scenario('Test Item 1: Tap Reserve Plan', async ({ I }) => {
    const reservePlanButton = '//*[@data-testid="reserveCommon_reservePlan_id"]';
    await I.waitForElement(reservePlanButton, 3);
    I.clickFixed(reservePlanButton);
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1533.Test_item_No.1_Tap_Reserve_Plan.png');
});

Scenario('Test Item 2: Tap Reserve Calendar', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '123@abc');

    const confirmButton = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);
    I.seeElement('//p[contains(text(), "積立中止が完了しました。")]');

    const reserveCalendarButton = '//*[@data-testid="reserveCommon_reserveCalendar_id"]';
    await I.waitForElement(reserveCalendarButton, 3);
    I.clickFixed(reserveCalendarButton);
    I.see('積立カレンダー', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1533.Test_item_No.2_Tap_Reserve_Calendar.png');
});

Scenario('Test Browser back', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundCancelReserveOrderConfirmPage();
    const passwordInput = '//*[@data-testid="fundCancelReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '123@abc');

    const confirmButton = '//*[@data-testid="fundCancelReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    await I.scrollAndClick(confirmButton);
    I.seeElement('//p[contains(text(), "積立中止が完了しました。")]');

    await I.performBrowserBack();
    await I.waitFor('shortWait');
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1533.Test_item_No.3_Browser_back.png');
});
