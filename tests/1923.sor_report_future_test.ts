Feature('SORReport - Future');

import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import sorReportFuture from '../pages/sorReportFuture';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user67 });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('go to SOR Report Future page', async () => {
    await sorReportFuture.goToSorReportFuturePage();
});

Scenario('Item 1: Domestic Stock', async ({ I }) => {
    // First ensure we're on the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Take a screenshot before clicking
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_1_Domestic_Stock_Before_Click.png`);

    // Click on the Stock tab and verify navigation
    await sorReportFuture.goToSorReportStockPage();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_1_Domestic_Stock_After_First_Click.png`);

    // Click back to Future tab and verify navigation
    await sorReportFuture.returnToFuturePage();
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_1_Domestic_Stock_After_Second_Click.png`);
});

Scenario('Item 5: Display Period', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Get the count of display period labels
    const count = await sorReportFuture.getDisplayPeriodLabelsCount();
    I.say(`Found ${count} display period labels`);

    // Test each label except the last one
    for (let i = 1; i < count; i++) {
        const labelLocator = `${sorReportFuture.locator.displayPeriod}/label[${i}]/p`;

        // Click on the label
        await sorReportFuture.clickDisplayPeriodLabel(i);

        // Check color is rgba(255, 86, 0, 1)
        const color = await I.grabCssPropertyFrom(labelLocator, 'color');
        I.assertEqual(color, 'rgba(255, 86, 0, 1)', `Label ${i} should have color rgba(255, 86, 0, 1) when selected`);

        await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_5_Display_Period_Label_${i}.png`);
    }

    // Test the last label which should open a bottom sheet
    await sorReportFuture.openDatePicker();

    // Check if bottom sheet container exists
    I.seeElement(sorReportFuture.locator.bottomSheetContent);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_5_Display_Period_Bottom_Sheet.png`);

    // Click cancel button
    await sorReportFuture.clickCancel();

    // Verify bottom sheet is closed
    I.dontSeeElement(sorReportFuture.locator.bottomSheetContent);
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_5_Display_Period_After_Cancel.png`);
});

Scenario('Item 22: Page No', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Check if the page number locator is displayed
    I.waitForElement(sorReportFuture.locator.pageNumber);
    I.seeElement(sorReportFuture.locator.pageNumber);
    I.say('Page number buttons are displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_22_Page_No_Before_Click.png`);

    // Click on the second button
    await sorReportFuture.clickPageNumber(2);

    // Check the color of the second button
    const secondButtonLocator = `${sorReportFuture.locator.pageNumber}/button[2]`;
    const buttonColor = await I.grabCssPropertyFrom(secondButtonLocator, 'color');
    I.say(`Button 2 color: ${buttonColor}`);
    I.assertEqual(buttonColor, 'rgba(255, 86, 0, 1)', 'Button 2 should have color rgba(255, 86, 0, 1) when selected');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_22_Page_No_After_Click.png`);
});

Scenario('Item 21: Previous Page', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Define locators for page buttons
    const firstPageButtonLocator = `${sorReportFuture.locator.pageNumber}/button[1]`;

    // Ensure we're on page 2 first by clicking the second page button
    await sorReportFuture.clickPageNumber(2);

    // Take screenshot before clicking previous page button
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_21_Previous_Page_Before_Click.png`);

    // Click on the previous page button
    await sorReportFuture.clickPreviousPage();

    // Check the color of the first page button
    const buttonColor = await I.grabCssPropertyFrom(firstPageButtonLocator, 'color');
    I.say(`First page button color: ${buttonColor}`);
    I.assertEqual(
        buttonColor,
        'rgba(255, 86, 0, 1)',
        'First page button should have color rgba(255, 86, 0, 1) when selected',
    );

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_21_Previous_Page_After_Click.png`);
});

Scenario('Item 23: Next Page', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Define locators for page buttons
    const secondPageButtonLocator = `${sorReportFuture.locator.pageNumber}/button[2]`;

    // Ensure we're on page 1 first
    await sorReportFuture.clickPageNumber(1);

    // Take screenshot before clicking next page button
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_23_Next_Page_Before_Click.png`);

    // Click on the next page button
    await sorReportFuture.clickNextPage();

    // Check the color of the second page button
    const buttonColor = await I.grabCssPropertyFrom(secondPageButtonLocator, 'color');
    I.say(`Second page button color: ${buttonColor}`);
    I.assertEqual(
        buttonColor,
        'rgba(255, 86, 0, 1)',
        'Second page button should have color rgba(255, 86, 0, 1) when selected',
    );

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_23_Next_Page_After_Click.png`);
});

Scenario('Item 25: Previous Month', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Open date picker by clicking on the appropriate label
    await sorReportFuture.openDatePicker();

    // Check if the Previous Month button is displayed
    I.seeElement(sorReportFuture.locator.prevMonth);
    I.say('Previous Month button is displayed');

    // Get the current date and format it as {year}年{month}月
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
    const currentMonthText = `${currentYear}年${currentMonth}月`;

    // Verify the current month is displayed correctly
    const displayedMonthText = await I.grabTextFrom(sorReportFuture.locator.monthHeaderText);
    I.assertEqual(displayedMonthText, currentMonthText, 'Current month is not displayed correctly');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_25_Previous_Month_Before_Click.png`);

    // Click on the Previous Month button
    await sorReportFuture.clickPreviousMonth();

    // Calculate the expected previous month
    let prevMonth = currentMonth - 1;
    let prevYear = currentYear;
    if (prevMonth === 0) {
        prevMonth = 12;
        prevYear -= 1;
    }
    const expectedPrevMonthText = `${prevYear}年${prevMonth}月`;

    // Verify the previous month is displayed correctly
    const displayedPrevMonthText = await I.grabTextFrom(sorReportFuture.locator.monthHeaderText);
    I.assertEqual(displayedPrevMonthText, expectedPrevMonthText, 'Previous month is not displayed correctly');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_25_Previous_Month_After_Click.png`);
});

Scenario('Item 27: Next Month', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Open date picker by clicking on the appropriate label
    await sorReportFuture.openDatePicker();
    await sorReportFuture.clickPreviousMonth();

    // Check if the Next Month button is displayed
    I.seeElement(sorReportFuture.locator.nextMonth);
    I.say('Next Month button is displayed');

    // Get the current date and format it as {year}年{month}月
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
    const currentMonthText = `${currentYear}年${currentMonth}月`;

    // Click on the Next Month button
    await sorReportFuture.clickNextMonth();

    // Verify the next month is displayed correctly (assuming current month is May 2023)
    // Note: This test may need adjustment based on actual implementation
    const displayedNextMonthText = await I.grabTextFrom(sorReportFuture.locator.monthHeaderText);
    I.assertEqual(displayedNextMonthText, currentMonthText, 'Next month is not displayed correctly');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_27_Next_Month_After_Click.png`);
});

Scenario('Item 28: Calendar', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Open date picker by clicking on the appropriate label
    await sorReportFuture.openDatePicker();

    // Navigate to previous month
    await sorReportFuture.clickPreviousMonth();

    // Check if the calendar is displayed
    I.seeElement(sorReportFuture.locator.calendar);
    I.say('Calendar is displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_28_Calendar_Before_Click.png`);

    // Click on the first day of the second week
    await sorReportFuture.clickCalendarDay(3, 'first-child');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_28_Calendar_After_First_Click.png`);

    const firstDaySelector = `.DayPicker-Body .DayPicker-Week:nth-child(3) .DayPicker-Day:first-child div`;
    const firstDayColor = await I.grabCssPropertyFrom(firstDaySelector, 'color');
    I.assertEqual(
        firstDayColor,
        'rgba(255, 86, 0, 1)',
        'First day of the second week should have color rgba(255, 86, 0, 1) when selected',
    );

    // Click on the last day of the last week
    await sorReportFuture.clickCalendarDay(3, 'last-child');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_28_Calendar_After_Last_Click.png`);

    const lastDaySelector = `.DayPicker-Body .DayPicker-Week:nth-child(3) .DayPicker-Day:last-child div div`;
    const lastDayColor = await I.grabCssPropertyFrom(lastDaySelector, 'background-color');
    I.assertEqual(
        lastDayColor,
        'rgba(255, 86, 0, 1)',
        'Last day of the last week should have color rgba(255, 86, 0, 1) when selected',
    );
});

Scenario('Item 29: Reset', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Open date picker by clicking on the appropriate label
    await sorReportFuture.openDatePicker();

    // Change the month by clicking on the Previous Month button
    await sorReportFuture.clickPreviousMonth();

    // Check if the reset button is displayed
    I.seeElement(sorReportFuture.locator.reset);
    I.say('Reset button is displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_29_Reset_Before_Click.png`);

    // Get the current date as reference for reset
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based
    const expectedCurrentMonthText = `${currentYear}年${currentMonth}月`;

    // Click on the Reset button
    await sorReportFuture.clickReset();

    // Verify the month has been reset to current month
    const displayedMonthText = await I.grabTextFrom(sorReportFuture.locator.monthHeaderText);
    I.assertEqual(displayedMonthText, expectedCurrentMonthText, 'Month should be reset to current month');

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_29_Reset_After_Click.png`);
});

Scenario('Item 30: Confirm', async ({ I }) => {
    // Navigate to the Future page
    await sorReportFuture.goToSorReportFuturePage();

    // Open date picker by clicking on the appropriate label
    await sorReportFuture.openDatePicker();

    // Check if the confirm button is displayed
    I.seeElement(sorReportFuture.locator.confirm);
    I.say('Confirm button is displayed');
    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_30_Confirm_Before_Click.png`);

    // Verify bottom sheet is visible before confirming
    I.seeElement(sorReportFuture.locator.bottomSheet);

    // Click on the Confirm button
    await sorReportFuture.clickConfirm();

    // Verify the bottom sheet is closed after confirmation
    I.dontSeeElement(sorReportFuture.locator.bottomSheet);

    await I.saveScreenshot(`${SCREENSHOT_PREFIX.sorReportFuture}_Item_30_Confirm_After_Click.png`);
});
