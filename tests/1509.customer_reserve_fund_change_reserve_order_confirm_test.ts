import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Reserve - CustomerReserveFundChangeReserveOrderConfirm');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user55 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});
Scenario('Test Item 0: Check UI Reserve Fund ReserveOrder Confirm', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);
    I.see('以下の内容で設定の変更を受け付けます。ご確認の上、「変更を確定」ボタンを押してください。', '//p[contains(text(), "以下の内容で設定の変更を受け付けます。ご確認の上、「変更を確定」ボタンを押してください。")]');
});
// case 7: link production
Scenario('Test Item 7: Withdrawal result notification - Notification service contact settings', async ({ I }) => {
    const withdrawalResultNotification = '//*[@data-testid="fundChangeReserveOrderConfirm_withdrawalResultNotification_id"]';
    const contactSettings = `${withdrawalResultNotification}//span[contains(text(), "通知サービス連絡先設定")]`;
    await I.scrollToElement(withdrawalResultNotification);
    // Go to the following URL: {member-site-url}/ap/iPhone/personal/contact/List
    await common.clickCardItem(contactSettings, '/ap/iPhone/personal/contact/List', 'kcMemberSite');
    
});
Scenario('Test Item 11: Check Password Text Input Form', async ({ I, accumulation }) => {
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await accumulation.goToReserveFundReserveOrderInputPage();
    await I.waitForElement(changeConfirmationScreen, 1);
    await I.scrollAndClick(changeConfirmationScreen);
    await I.waitFor('mediumWait');
    const passwordInput = '//*[@data-testid="fundChangeReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 1);
    I.scrollAndFill(passwordInput, '123@abc'); 
     // click img alt="eye" to see password
     const eyeIcon = '//img[@alt="eye"]';
     await I.waitForElement(eyeIcon, 1);
     I.clickFixed(eyeIcon);
     await I.waitFor('shortWait');
    I.saveScreenshot('1509.Test_item_No.11_Password_Text_Input_Form.png');
});
Scenario('Test Item 12: Check Password omission check: Switch ON/OFF', async ({ I }) => {

    const passwordInputOmissionCheck = '//*[@data-testid="fundChangeReserveOrderConfirm_passwordOmissionCheck_id"]';
    await I.waitForElement(passwordInputOmissionCheck, 1);
    // ON
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1509.Test_item_No.12_Password_omission_check_ON.png');
    // OFF
    I.clickFixed(passwordInputOmissionCheck);
    I.saveScreenshot('1509.Test_item_No.12_Password_omission_check_OFF.png');
});
Scenario('Test Item 14: Check Password input check: Switch between ON and OFF', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user56 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor();
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);
    const passwordInputCheck = '//*[@data-testid="fundChangeReserveOrderConfirm_checkInputPassword_id"]//button';
    await I.waitForElement(passwordInputCheck, 1);
    I.scrollAndClick(passwordInputCheck);
    I.saveScreenshot('1509.Test_item_No.14_Password_input_check_1.png');
    I.clickFixed(passwordInputCheck);
    I.saveScreenshot('1509.Test_item_No.14_Password_input_check_2.png');
});
//TODO: 15.1: Skip because cannot check maintenance
Scenario.skip('Test Item 15.2: backed up during loading, the page will transition to the savings plan page', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);

    const passwordInput = '//*[@data-testid="fundChangeReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundChangeReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1509.Test_item_No.15.2_before_click_button.png');
    I.clickFixed(confirmButton);
    
    I.executeScript(() => window.history.back());
    
    I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    I.saveScreenshot('1509.Test_item_No.15.2_redirect_to_reserve_plan.png');
    //back 
    await I.backToPreviousScreen();

});
Scenario('Test Item 15.3: Check Execute loading - Transition to Accumulation - Investment Trust - Accumulation Change Complete', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);

    const passwordInput = '//*[@data-testid="fundChangeReserveOrderConfirm_passwordInput_id"]';
    await I.waitForElement(passwordInput, 3);
    I.fillField(passwordInput, '111111');
    
    const confirmButton = '//*[@data-testid="fundChangeReserveOrderConfirm_confirmButton_id"]';
    await I.waitForElement(confirmButton, 3);
    I.scrollToElement(confirmButton);
    I.saveScreenshot('1509.Test_item_No.15.3_before_click_button.png');
    I.clickFixed(confirmButton);
    await I.waitFor();
    I.seeElement('//p[contains(text(), "積立変更が完了しました。")]');
    I.saveScreenshot('1509.Test_item_No.15.3_Check_Execute_loading_Transition_to_Accumulation_Investment_Trust_Accumulation_Change_Complete.png');
});
Scenario('Test Item 16: Check Caution Message', async ({ I, accumulation }) => {
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const changeConfirmationScreen = '//*[@data-testid="fundChangeReserveOrderInput_confirmButton_id"]';
    await I.waitForElement(changeConfirmationScreen, 1);
    I.scrollAndClick(changeConfirmationScreen);

    const cautionMessage = '//*[@data-testid="fundChangeReserveOrderConfirm_caution_id"]';
    await I.waitForElement(cautionMessage, 1);
    I.scrollAndClick(cautionMessage);
    const chakraCollapse = cautionMessage + '//*[@class="chakra-collapse"]';
    await I.waitForElement(chakraCollapse, 1);
    I.scrollToElement(chakraCollapse);
    I.saveScreenshot('1509.Test_item_No.16_Check_Caution_Message.png');
    const arrowUpSelector = '//img[@data-testid="common_dropDown_arrow_id_up"]';
    I.clickFixed(arrowUpSelector);
    I.saveScreenshot('1509.Test_item_No.16_Check_Caution_Message_Close.png');
});
Scenario('Test Item 20: Check Open URL in new tab', async ({ I, accumulation }) => {
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user56 });
    await I.waitFor()
    await I.refreshPage();
    await I.waitFor()
    await accumulation.goToReserveFundReserveOrderInputPage();
    I.waitFor()
    const linkElement = '//span[text()="こちら"]';
    await I.waitForElement(linkElement, 5);
    I.scrollToElement(linkElement);
    await common.clickCardItem(linkElement, 'https://kabu.com/item/payment_cashout/payment/other/schedule.html', 'external');
    
});
