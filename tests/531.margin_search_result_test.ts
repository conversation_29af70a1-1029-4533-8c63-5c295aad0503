import { COOKIE_KEY, USER_ID } from "../const/constant";
import common from "../pages/search/common";

Feature('Symbol_ProductSearch - MarrginTradingSearchResult');

Before(async ({ I, loginAndSwitchToWebAs, search }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie([{ name: COOKIE_KEY.userId, value: USER_ID.user21 }, { name: COOKIE_KEY.siteId, value: '1' }]);
    await search.goToMarginTradingSearchResult();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

<PERSON>enario('Test Display Margin Trading Search Result', async ({ search }) => {
    await search.compareUrl('/mobile/search-margin/result');
    search.takeScreenshot.marginTradingSearchResult('Display_Margin_Trading_Search_Result');
});

Scenario('Test item No.1 Enter keywords', async ({ I, search }) => {
    const searchResultMarginKeyInputSelector = '//*[@data-testid="searchMarginResult_keywordInput_id"]//input';
    await I.clickFixed(searchResultMarginKeyInputSelector);
    await I.waitFor('shortWait');
    I.assertEqual(await I.isKeyboardShown(), true, 'Keyboard should be shown');
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.1_Open_the_input_pad');
    I.pressKey('Enter');
});

Scenario('Test item No.2 Display conditions', async ({ I, search }) => {
    const displayConditionDownIconSelector = '//*[@data-testid="searchMarginResult_displayCondition_id"]//*[@data-testid="common_dropDown_arrow_id_down"]';
    const displayConditionUpIconSelector = '//*[@data-testid="searchMarginResult_displayCondition_id"]//*[@data-testid="common_dropDown_arrow_id_up"]';
    await I.clickFixed(displayConditionDownIconSelector);
    await I.waitFor('shortWait');
    await search.takeScreenshot.marginTradingSearchResult('Test_item_No.2_Open_the_accordion');
    await I.clickFixed(displayConditionUpIconSelector);
    await I.waitFor('shortWait');
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.2_Close_the_accordion');
});

Scenario('Test item No.3 Filter conditions', async ({ I, search }) => {
    const displayConditionDownIconSelector = '//*[@data-testid="searchMarginResult_displayCondition_id"]//*[@data-testid="common_dropDown_arrow_id_down"]';
    const filterConditionSelector = '//*[@data-testid="searchMarginResult_filterCondition_id"]/button';
    const secondFilterConditionSelector = '//*[@data-testid="searchMarginResult_filterCondition_id"]/div/div/p[2]';
    await I.clickFixed(displayConditionDownIconSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(filterConditionSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(secondFilterConditionSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.3_Update_screen_display_when_changed');
});

Scenario('Test item No.4 Sorting conditions', async ({ I, search }) => {
    const displayConditionDownIconSelector = '//*[@data-testid="searchMarginResult_displayCondition_id"]//*[@data-testid="common_dropDown_arrow_id_down"]';
    const sortingConditionSelector = '//*[@data-testid="searchMarginResult_sortCondition_id"]/button';
    const secondSortingConditionSelector = '//*[@data-testid="searchMarginResult_sortCondition_id"]/div/div/p[2]';
    await I.clickFixed(displayConditionDownIconSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(sortingConditionSelector);
    await I.waitFor('shortWait');
    await I.clickFixed(secondSortingConditionSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.4_Update_screen_display_when_changed');
});

Scenario('Test item No.8a Stock abbreviation', async ({ I, search }) => {
    const symbolShortNameSelector = '//*[@data-testid="searchMarginResult_symbolShortName_0_id"]';
    await I.clickFixed(symbolShortNameSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/info/stock/basic');
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.8a_Transition_to_stock_details_chart_display');
});

Scenario('Test item No.8i New', async ({ I, search }) => {
    const newSelector = '//*[@data-testid="searchMarginResult_new_0_id"]';
    await I.clickFixed(newSelector);
    await I.waitFor('mediumWait');
    await search.compareUrl('/mobile/trade/margin/new');
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.8i_Transition_to_domestic_stock_margin_trading_New_Order_entry');
});

Scenario('Test item No.9 General credit (long-term/day trading) stocks available for short selling', async ({ I, search }) => {
    const generalCredit = '//*[@data-testid="searchMarginResult_generalMargin_id"]';
    // Go to the following URL: {member-site-url}/ap/iPhone/Stocks/Margin/MarginSymbol/GeneralSellList
    await common.clickCardItem(generalCredit, '/ap/iPhone/Stocks/Margin/MarginSymbol/GeneralSellList', 'kcMemberSite');
});

Scenario('Test item No.12 Previous page', async ({ I, search }) => {
    const prevButtonSelector = '//*[@data-testid="searchMarginResult_prev_id"]';
    const secondPageSelector = '//*[@data-testid="searchMarginResult_page_id"]//button[2]';
    await I.clickFixed(secondPageSelector);
    await I.waitFor();
    await I.clickFixed(prevButtonSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.12_Display_the_previous_page');
});

Scenario('Test item No.13 Page Number', async ({ I, search }) => {
    const secondPageSelector = '//*[@data-testid="searchMarginResult_page_id"]//button[2]';
    await I.clickFixed(secondPageSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.13_Display_the_page_with_the_number_you_tapped');
});

Scenario('Test item No.14 Next page', async ({ I, search }) => {
    const nextButtonSelector = '//*[@data-testid="searchMarginResult_next_id"]';
    await I.clickFixed(nextButtonSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.14_Display_the_next_page');
});

Scenario('Test item No.16 Display by stock code', async ({ I, search }) => {
    const searchDisplaySelector = '//*[@data-testid="searchMarginResult_searchDisplay_id"]';
    await I.clickFixed(searchDisplaySelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.16_Display_the_initial_screen_entry_by_stock_code');
});

Scenario('Test item No.17 Search display', async ({ I, search }) => {
    const codeUnitSelector = '//*[@data-testid="searchMarginResult_codeUnit_id"]';
    await I.clickFixed(codeUnitSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.17_Display_the_initial_screen_description_in_search_display');
});

Scenario('Test item No.19 Stock code number', async ({ I, search }) => {
    const stockCodeNumberSelector = '//*[@data-testid="searchMarginResult_symbolCodeNumber_id"]/label[2]';
    const searchDisplaySelector = '//*[@data-testid="searchMarginResult_searchDisplay_id"]';
    await I.clickFixed(searchDisplaySelector);
    await I.waitFor();
    await I.clickFixed(stockCodeNumberSelector);
    await I.waitFor();
    search.takeScreenshot.marginTradingSearchResult('Test_item_No.19_Loading_and_updating_the_screen_display');
});