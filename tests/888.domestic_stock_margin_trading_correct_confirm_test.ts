import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginCorrectConfirm');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginCorrect }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    <PERSON>.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCorrect.goToMarginCorrectConfirm();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Correct confirm', async ({ I, stockMarginCorrect }) => {
    await stockMarginCorrect.compareUrl('/mobile/trade/margin/correction/confirm');
    stockMarginCorrect.takeScreenshot.marginCorrectConfirm('Display_Domestic_Stock_Margin_Trading_Correct_confirm');
});

Scenario(
    'Test item No.5+6 Fill password and check for omitted password',
    async ({ I, stockMarginCorrect, stockMarginOrder }) => {
        await stockMarginOrder.setSessionData('/trade/margin/correction/confirm', { isOmmitPassword: false });
        I.refreshPage();
        await I.waitFor('mediumWait');
        const passwordSelector = '//*[@data-testid="marginCorrectionConfirm_password_id"]';
        const passwordOmissionCheckSelector = '//*[@data-testid="marginCorrectionConfirm_passwordOmissionCheck_id"]';
        const softLimitCheckSelector = '//*[@data-testid="marginCorrectionConfirm_softLimitAgree_id"]//button';
        const confirmCorrectButton = '//*[@data-testid="marginCorrectionConfirm_confirmCorrect_id"]';
        await I.scrollToElement(confirmCorrectButton);
        if ((await I.grabNumberOfVisibleElements(softLimitCheckSelector)) > 0) {
            await I.clickFixed(softLimitCheckSelector);
        }
        I.fillField(passwordSelector, stockMarginCorrect.inputValues.password);
        I.blur(passwordSelector);
        await I.clickFixed(passwordOmissionCheckSelector);
        I.assertEqual(
            await I.grabCssPropertyFrom(confirmCorrectButton, 'background-color'),
            'rgba(255, 86, 0, 1)',
            'Background color is not equal',
        );
        stockMarginCorrect.takeScreenshot.marginCorrectConfirm('Test_item_No.5_6_See_order_confirmation_button_state');
    },
);

Scenario('Test item No.7 Confirm the correction', async ({ I, stockMarginCorrect }) => {
    const passwordSelector = '//*[@data-testid="marginCorrectionConfirm_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCorrectionConfirm_passwordOmissionCheck_id"]';
    const softLimitCheckSelector = '//*[@data-testid="marginCorrectionConfirm_softLimitAgree_id"]//button';
    const confirmCorrectButton = '//*[@data-testid="marginCorrectionConfirm_confirmCorrect_id"]';
    await I.scrollToElement(confirmCorrectButton);
    if ((await I.grabNumberOfVisibleElements(softLimitCheckSelector)) > 0) {
        await I.clickFixed(softLimitCheckSelector);
    }
    I.fillField(passwordSelector, stockMarginCorrect.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(confirmCorrectButton);
    await I.waitFor('mediumWait');
    await stockMarginCorrect.compareUrl('/mobile/order-inquiry/margin/detail');
    stockMarginCorrect.takeScreenshot.marginCorrectConfirm(
        'Test_item_No.7_Tap_confirm_button_to_transition_to_details_screen',
    );
});

Scenario(
    'Test item No.9 Soft limit violation - confirmation check',
    async ({ I, stockMarginCorrect, stockMarginOrder }) => {
        await stockMarginOrder.setSessionData('/trade/margin/correction/confirm', { isSoftlimit: true });
        I.refreshPage();
        await I.waitFor('mediumWait');
        const softLimitCheckSelector = '//*[@data-testid="marginCorrectionConfirm_softLimitAgree_id"]//button';
        const softLimitCheckIconSelector = '[data-testid="marginCorrectionConfirm_softLimitAgree_id"] button svg';
        await I.scrollToElement(softLimitCheckSelector);
        if ((await I.grabNumberOfVisibleElements(softLimitCheckSelector)) > 0) {
            await I.clickFixed(softLimitCheckSelector);
        }
        I.assertEqual(
            await I.grabCssPropertyFrom(softLimitCheckIconSelector, 'color'),
            'rgba(255, 86, 0, 1)',
            'Color is not equal',
        );
        await stockMarginCorrect.takeScreenshot.marginCorrectConfirm('Test_item_No.9_Change_selected_state');
        await I.waitFor();
        await I.clickFixed(softLimitCheckSelector);
        I.assertEqual(
            await I.grabCssPropertyFrom(softLimitCheckIconSelector, 'color'),
            'rgba(137, 137, 139, 1)',
            'Color is not equal',
        );
        stockMarginCorrect.takeScreenshot.marginCorrectConfirm('Test_item_No.9_Change_unselected_state');
    },
);
