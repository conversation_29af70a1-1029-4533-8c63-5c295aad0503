import { COMMON_HEADER_TITLE, COOKIE_KEY, USER_ID } from '../const/constant';

Feature('InvestmentProducts - USStockInfoSummary');

Before(async ({ I }) => {
    console.debug('before');
    // reset context    // await I.resetAppFixed();
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    // await I.login(USER_ID.user6, '111111');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user24 });
    await I.waitFor();
});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Item 0. Check UI US Stock Information-Stock Price Performance', async ({ I }) => {
    const pageUrl = '/mobile/info/usstock/performance?symbol=123';
    I.amOnPage(pageUrl);
    I.see('米国株式\n個別銘柄情報', COMMON_HEADER_TITLE);
    I.seeInCurrentUrl(pageUrl);
    I.saveScreenshot('617.usstock_info_performance_No.0_access_the_usstock_info_performance_page.png');
});

// 3.業績エリア -> 一番左の決算期列を固定し、左右にスクロールする
Scenario('Item 3. Fix the Settlement period column and allow horizontal scrolling.', async ({ I }) => {
    const tableLocator = '//div[@data-testid="usStockPerformance_performanceArea_id"]';
    await I.scrollToElement(tableLocator);
    // scroll to the right
    await I.swipeLeftWithOffsetX(tableLocator);
    I.saveScreenshot('617.usstock_info_performance_No.3_Fix_the_Settlement_period_column_and_swipe_left_to_right.png');
    // scroll to the left
    await I.waitFor();
    await I.swipeRightWithOffsetX(tableLocator);
    I.saveScreenshot('617.usstock_info_performance_No.3_Fix_the_Settlement_period_column_and_swipe_right_to_left.png');
});

// 6.四半期業績エリア -> 一番左の決算期列を固定し、左右にスクロールする
Scenario('Item 6. Fix the Settlement period column and allow horizontal scrolling.', async ({ I }) => {
    const tableLocator = '//div[@data-testid="usStockPerformance_quarterlyPerformanceArea_id"]';
    await I.scrollToElement(tableLocator);
    // scroll to the right
    await I.swipeLeftWithOffsetX(tableLocator);
    I.saveScreenshot('617.usstock_info_performance_No.6_Fix_the_Settlement_period_column_and_swipe_left_to_right.png');
    // scroll to the left
    await I.waitFor();
    await I.swipeRightWithOffsetX(tableLocator);
    I.saveScreenshot('617.usstock_info_performance_No.6_Fix_the_Settlement_period_column_and_swipe_right_to_left.png');
});
