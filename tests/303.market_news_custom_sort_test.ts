import { COOKIE_KEY, USER_ID } from '../const/constant';
import newsList from '../pages/newsList';

Feature('Market - NewsCustomSort');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await <PERSON><PERSON>waitFor('mediumWait');
    await I.switchToWeb();
    await I.waitFor('mediumWait');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user12 });

    await newsList.goToIndicator();
    await newsList.goToNewsTab();
});
After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test item No.0 Check news list is loaded', async ({ I }) => {
    // Make sure news list is loaded
    I.waitForElement(newsList.locator.newsListContainer);

    // Open custom sort modal
    await newsList.openCustomSort();
    await I.saveScreenshot('303.Test_item_No.0_open_custom_sort_modal.png');
});
Scenario('Test item No.1 Check opening and closing custom sort modal', async ({ I }) => {
    // Make sure news list is loaded
    I.waitForElement(newsList.locator.newsListContainer);

    // Open custom sort modal
    await newsList.openCustomSort();
    await I.saveScreenshot('303.Test_item_No.1_open_custom_sort_modal.png');

    // Close modal by close button
    await newsList.closeCustomSortModal();

    // Check modal is closed
    I.dontSeeElement(newsList.locator.closeCustomSortModalButton);
    await I.saveScreenshot('303.Test_item_No.1_close_custom_sort_modal.png');
});

Scenario('Test item No.2.1 Check news category classification', async ({ I }) => {
    // Make sure news list is loaded
    I.waitForElement(newsList.locator.newsListContainer);
    // 1. Take screenshot before custom sort
    await I.saveScreenshot('303.Test_item_No.2.1_before_custom_sort.png');
    // 2. Open custom sort modal
    await newsList.openCustomSort();
    await I.saveScreenshot('303.Test_item_No.2.1_custom_sort_modal.png');
    // 3. Turn off all checkboxes (to ensure starting from clean state)
    await newsList.selectNewsCategory('kabuレター®');
    await I.saveScreenshot('303.Test_item_No.2.1_select_press_news.png');
    // 5. Confirm filter
    await newsList.confirmCustomSort();
});

Scenario('Test item No.2.2 Check select all/deselect all categories function', async ({ I }) => {
    // Open custom sort modal
    await newsList.openCustomSort();

    // check Select all / deselect all
    await newsList.selectNewsCategory('投資情報室');
    await newsList.selectNewsCategory('PRESSニュース');
    await newsList.selectNewsCategory('米株ニュース');
    await I.saveScreenshot('303.Test_item_No.2.2_select_all_checkbox.png');
    await I.waitFor();
    // check else case
    await newsList.selectNewsCategory('投資情報室');
    await newsList.selectNewsCategory('PRESSニュース');
    await newsList.selectNewsCategory('米株ニュース');
    await I.saveScreenshot('303.Test_item_No.2.2_else_case.png');
});
Scenario('Test item No.3 Switch between select and deselect news category', async ({ I }) => {
    // Open custom sort modal   
    await newsList.openCustomSort();
    // Select kabuレター®
    await newsList.selectNewsCategory('kabuレター®');
    await I.saveScreenshot('303.Test_item_No.3_select_kabu_letter.png');
    // deselect kabuレター®
    await newsList.selectNewsCategory('kabuレター®');
    await I.saveScreenshot('303.Test_item_No.3_deselect_kabu_letter.png');
});
// choose any category and check news list
Scenario('Test item No.4 Choose any category and check news list', async ({ I }) => {
    // Open custom sort modal
    await newsList.openCustomSort();
    // Select kabuレター®
    await newsList.selectNewsCategory('投資情報室');
    // Click submit filter
    await newsList.confirmCustomSort();
    await I.saveScreenshot('303.Test_item_No.4_display_the_news_list_screen.png');
});
