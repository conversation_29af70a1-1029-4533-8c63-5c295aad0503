import { COOKIE_KEY, USER_ID } from "../const/constant";

import common from "../pages/search/common";
Feature('Reserve - ReservePointUsageSetting');

Before(async ({ I }) => {
    console.debug('before');
    await I.activateApp();
    await I.waitFor();
    await I.switchToWeb();
    await I.waitFor();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user57 });
    await I.waitFor();

});

After(async ({ I }) => {
    console.debug('after');
    I.switchToNative();
    await I.closeBrowser();
});

Scenario('Test Item 0: Check UI Reserve Point Usage Setting', async ({ I, accumulation }) => {
    await accumulation.goToReservePointUsageSetting();
    I.see('ポイント利用設定', '//*[@data-testid="common_header_title_id"]');
    await I.saveScreenshot('1537.Test_item_No.0_Check_UI_Reserve_Point_Usage_Setting.png');
});
Scenario('Test Item 4: Tap to show tooltip How many points have been used for savings this month', async ({ I }) => {   
    const tooltip = '//*[@data-testid="reservePointSetting_monthlyTotalUsePoint_id"]';
    await I.waitForElement(tooltip, 1);
    await I.scrollAndClick(tooltip);
    await I.saveScreenshot('1537.Test_item_No.4_Tap_to_show_tooltip_How_many_points_have_been_used_for_savings_this_month.png');
    I.clickFixed('//*[@data-testid="common_header_title_id"]');

});
Scenario('Test Item 6: Check Display explanation modal about point usage settings', async ({ I }) => {
    const explanationModal = '//*[@data-testid="reservePointSetting_aboutPointUsageSettings_id"]';
    await I.waitForElement(explanationModal, 1);
    await I.scrollAndClick(explanationModal);
    await I.saveScreenshot('1537.Test_item_No.6_Check_Display_explanation_modal_about_point_usage_settings.png');

    I.clickFixed('//*[@id="bottom-sheet-container"]//button[@aria-label="cancel-btn"]');

});
Scenario('Test Item 7: Check Use Ponta points: Switch ON/OFF status', async ({ I }) => {
    const usePontaPointsSwitch = '//*[@data-testid="reservePointSetting_usePontaPoints_id"]';
    await I.waitForElement(usePontaPointsSwitch, 1);
    await I.scrollAndClick(usePontaPointsSwitch);
    await I.saveScreenshot('1537.Test_item_No.7_Check_Use_Ponta_points_Switch_OFF_status.png');
    await I.scrollAndClick(usePontaPointsSwitch);
    await I.saveScreenshot('1537.Test_item_No.7_Check_Use_Ponta_points_Switch_ON_status.png');
});
Scenario('Test Item 9: Check Set it up - Show Display completion message', async ({ I }) => {
    const setItUp = '//*[@data-testid="reservePointSetting_set_id"]';
    await I.waitForElement(setItUp, 1);
    await I.scrollAndClick(setItUp);
    I.see('設定が完了しました。', '//p[contains(text(), "設定が完了しました。")]');
    await I.saveScreenshot('1537.Test_item_No.9_Check_Set_it_up_Show_Display_completion_message.png');
    await I.waitFor('defaultWait');
});
Scenario('Test Item 10: Check Select usage method Select the item you tapped', async ({ I }) => {
    // make sure Use Ponta points on
    const usePontaPointsSwitch = '//*[@data-testid="reservePointSetting_usePontaPoints_id"]';
    await I.waitForElement(usePontaPointsSwitch, 1);
    await I.scrollToElement(usePontaPointsSwitch);
    const isUsePontaPointsOn = await I.grabNumberOfVisibleElements('//div[contains(text(), "毎月の上限：")]');
    if (isUsePontaPointsOn === 0) {
        await I.scrollAndClick(usePontaPointsSwitch);
        await I.waitFor('shortWait');
    }
    const useMethodSelection = '//*[@data-testid="reservePointSetting_radio_0_id"]';
    const useMethodSelection2 = '//*[@data-testid="reservePointSetting_radio_1_id"]';
    await I.waitForElement(useMethodSelection, 1);
    await I.scrollAndClick(useMethodSelection);
    await I.saveScreenshot('1537.Test_item_No.10_Check_Select_usage_method_Select_the_item_you_tapped.png');
    await I.waitForElement(useMethodSelection2, 1);
    await I.scrollAndClick(useMethodSelection2);
    await I.saveScreenshot('1537.Test_item_No.10_Check_Select_usage_method_Select_the_item_you_tapped_2.png');

});
Scenario('Test Item 11: Check Use method selection: Tap the selected item', async ({ I }) => {
   // make sure Use Ponta points on
   const usePontaPointsSwitch = '//*[@data-testid="reservePointSetting_usePontaPoints_id"]';
   await I.waitForElement(usePontaPointsSwitch, 1);
   await I.scrollToElement(usePontaPointsSwitch);
   const isUsePontaPointsOn = await I.grabNumberOfVisibleElements('//div[contains(text(), "毎月の上限：")]');
   if (isUsePontaPointsOn === 0) {
       await I.scrollAndClick(usePontaPointsSwitch);
       await I.waitFor('shortWait');
   }
   const monthlyLimitInput = '//*[@data-testid="groupInputNumber_input_id"]';
   await I.waitForElement(monthlyLimitInput, 1);
   await I.scrollAndFill(monthlyLimitInput, '99998999');
   await I.saveScreenshot('1537.Test_item_No.11_Check_Use_method_selection_Tap_the_selected_item.png');


});
Scenario('Test Item 12: Check Use method selection: Tap the selected item', async ({ I }) => {
    const isCardPointUseSetting = '//*[@data-testid="reservePointSetting_isCardPointUseSetting_id"]';
    await I.waitForElement(isCardPointUseSetting, 1);
    await I.scrollAndClick(isCardPointUseSetting);
    await I.saveScreenshot('1537.Test_item_No.12_Check_Use_method_selection_Tap_the_selected_item.png');
    await I.scrollAndClick(isCardPointUseSetting);
    await I.saveScreenshot('1537.Test_item_No.12_Check_Use_method_selection_Tap_the_selected_item_2.png');
});
Scenario('Test Item 13: Check Click learn more open link new tab', async ({ I }) => {
    const explanationModal = '//*[@data-testid="reservePointSetting_aboutPointUsageSettings_id"]';
    await I.waitForElement(explanationModal, 1);
    await I.scrollAndClick(explanationModal);

    const learnMore = '//*[@data-testid="reservePointSetting_hereLink_id"]';
    await I.waitForElement(learnMore, 1);
    await common.clickCardItem(learnMore, 'https://kabu.com/item/point_fd/default.html', 'external');
    await I.saveScreenshot('1537.Test_item_No.13_Check_Click_learn_more_open_link_new_tab.png');
});