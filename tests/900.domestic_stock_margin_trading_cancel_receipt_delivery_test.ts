import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('InvestmentProducts - DomesticStockMarginCancelReceiptDelivery');

Before(async ({ I, loginAndSwitchToWebAs, stockMarginCancel }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user34 });
    await stockMarginCancel.goToMarginCancelReceiptDelivery();
});

After(async ({ I }) => {
    console.debug('after');
    await I.closeBrowser();
    await I.switchToNative();
});

Scenario('Test Display Domestic Stock Margin Cancel Receipt Delivery', async ({ I, stockMarginCancel }) => {
    I.assertContain(
        await I.grabCurrentUrl(),
        stockMarginCancel.urls.marginCancelReceiptDelivery,
        'URL does not contain expected path',
    );
    stockMarginCancel.takeScreenshot.marginCancelReceiptDelivery(
        'Display_Domestic_Stock_Margin_Trading_Cancel_Receipt_Delivery',
    );
});

Scenario('Test item No.11+12 Fill password and check for omitted password', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const passwordSelector = '//*[@data-testid="marginCancelReceipt_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCancelReceipt_passwordOmmittedCheck_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginCancelReceipt_confirmCancel_id"]';
    I.fillField(passwordSelector, stockMarginCancel.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    I.assertEqual(
        await I.grabCssPropertyFrom(confirmButtonSelector, 'background-color'),
        'rgba(255, 86, 0, 1)',
        'Background color is not equal',
    );
    stockMarginCancel.takeScreenshot.marginCancelReceiptDelivery(
        'Test_item_No.11_12_See_order_confirmation_button_state',
    );
});

Scenario('Test item No.13 Confirm cancellation', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const passwordSelector = '//*[@data-testid="marginCancelReceipt_password_id"]';
    const passwordOmissionCheckSelector = '//*[@data-testid="marginCancelReceipt_passwordOmmittedCheck_id"]';
    const confirmButtonSelector = '//*[@data-testid="marginCancelReceipt_confirmCancel_id"]';
    I.fillField(passwordSelector, stockMarginCancel.inputValues.password);
    I.blur(passwordSelector);
    await I.clickFixed(passwordOmissionCheckSelector);
    await I.clickFixed(confirmButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(
        await I.grabCurrentUrl(),
        '/mobile/order-inquiry/margin/detail-receipt-delivery',
        'URL does not contain expected path',
    );
    stockMarginCancel.takeScreenshot.marginCancelReceiptDelivery(
        'Test_item_No.13_Tap_confirm_button_to_transition_to_details_Receipt_Delivery_screen',
    );
});

Scenario('Test item No.14 Return to the order inquiry screen', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const backToOrderInquiryButtonSelector = '//*[@data-testid="marginCancelReceipt_backToOrderInquiry_id"]';
    await I.clickFixed(backToOrderInquiryButtonSelector);
    await I.waitFor('mediumWait');
    I.assertContain(await I.grabCurrentUrl(), '/mobile/order-inquiry/margin', 'URL does not contain expected path');
    stockMarginCancel.takeScreenshot.marginCancelReceiptDelivery(
        'Test_item_No.14_Tap_Return_to_the_order_inquiry_button_to_Go_to_the_order_inquiry_domestic_stock_credit_list_screen',
    );
});

Scenario('Test item No.15 Caution', async ({ I, stockMarginCancel }) => {
    await I.swipeDirection('up');
    const cautionSelector = '//*[@data-testid="marginCancelReceipt_caution_id"]/div[1]';
    await I.clickFixed(cautionSelector);
    await I.waitFor('shortWait');
    await I.swipeDirection('up');
    await stockMarginCancel.takeScreenshot.marginCancelReceiptDelivery(
        'Test_item_No.15_Tap_Caution_opening_the_accordion',
    );
    await I.clickFixed(cautionSelector);
    await I.waitFor('shortWait');
    await stockMarginCancel.takeScreenshot.marginCancelReceiptDelivery(
        'Test_item_No.15_Tap_Caution_closing_the_accordion',
    );
});
