import { PAGE_URL } from "../const/constant";

const { I } = inject();

export = {
    async goToPage(): Promise<void> {
        I.amOnPage('/mobile/mypage/portfolio');
        await <PERSON><PERSON>waitFor('longWait');
        I.waitForText('ポートフォリオ', 3, 'body');
        I.waitForElement('#mypage-portfolio');

        const isDisplayTutorial = await I.getAppSetting('is-disp-tutorial');
        const isDisplayTopCoach = await I.getAppSetting('is-disp-top-coach');

        if (!isDisplayTutorial) {
            I.waitForText('新しい投資体験へ', 3, 'body');
            // Semantic locators can be used in web contexts (codeceptjs automatically searches for button text, label text, aria-label property, etc.)
            // see. https://codecept.io/locators/#semantic-locators
            // or "~cancel-btn": accessible id = aria-label (web context)
            I.click('//button[@aria-label="cancel-btn"]');
            await <PERSON>.waitFor();
        }

        if (!isDisplayTopCoach) {
            I.waitForText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。', 3, 'body');
            // Touch and close coach modal.
            const coachModal = locate('div').withChild(
                locate('p').withText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。'),
            );
            const rect = await I.grabElementBoundingRectTyped(coachModal);
            const x = Math.round(rect.x) + 100;
            const y = Math.round(rect.y) + 100;
            await I.tapAction(x, y);
            await I.waitFor();
        }
    },
    async goToPortfolioPage(): Promise<void> {
        await I.amOnPage(PAGE_URL.mypagePortfolio);
        await I.waitFor('longWait');
        I.waitForText('ポートフォリオ', 3, 'body');
        I.waitForElement('#mypage-portfolio');

        const isDisplayTutorial = await I.getAppSetting('is-disp-tutorial');
        const isDisplayTopCoach = await I.getAppSetting('is-disp-top-coach');
        // const isMenuClickable = await I.locateClickable('menu');
        // if (!isMenuClickable) {

        if (!isDisplayTutorial) {
            I.waitForText('新しい投資体験へ', 3, 'body');
            // Semantic locators can be used in web contexts (codeceptjs automatically searches for button text, label text, aria-label property, etc.)
            // see. https://codecept.io/locators/#semantic-locators
            // or "~cancel-btn": accessible id = aria-label (web context)
            await I.clickFixed('cancel-btn');
        }

        if (!isDisplayTopCoach) {
            I.waitForText('お取引などにお困りの場合、ヘルプメニューはこちらから確認いただけます。', 3, 'body');
            // Touch and close coach modal.
            await I.clickFixed('//div[div[contains(@id, "mypage-tab")]]/div[6]');
        }
    },
};
