import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../const/constant";

const { I, search } = inject();

export = {
    locators: {
        searchTopInput: '//*[@data-testid="searchTop_keywordInput_id"]//input',
        searchResultInput: '//*[@data-testid="searchResult_keywordInput_id"]//input',
        searchResultList: '//*[@data-testid="searchResult_symbolList_id"]',
        searchMarginKeyInput: '//*[@data-testid="searchMargin_keywordInput_id"]//input',
        searchResultDisplayCondition: '//*[@data-testid="searchMarginResult_displayCondition_id"]',
        fundSearch: '//*[@data-testid="searchTop_fundSearch_id"]',
        fundSearchFilterByDetailedCondition: '//*[@data-testid="infoFundSearch_filterByDetailedConditions_id"]',
        themeList: '//*[@data-testid="themeTop_themeList_id"]',
        firstThemeItem: '//*[@data-testid="themeTop_themeItem_id_0"]',
        searchPageContainer: '#searchPage',
    },
    inputValues: {
        searchTopInput: '101',
        searchMarginInput: '1069'
    },
    urls: {
        searchTop: '/mobile/search',
        searchTheme: '/mobile/search/theme',
        marginSearch: '/mobile/search-margin',

    },
    takeScreenshot: {
        marginTradingSearchTop: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.marginTradingSearchTop}_${suffix}.png`);
        },
        marginTradingSearchResult: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.marginTradingSearchResult}_${suffix}.png`);
        }
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToPage(): Promise<void> {
        I.amOnPage(this.urls.searchTop);
        await I.waitFor('mediumWait');
        I.waitForText('銘柄検索', 3, 'body');
        I.waitForElement('#searchPage');
    },
    async goToSearchResult(): Promise<void> {
        await I.clickFixed(this.locators.searchTopInput);
        await I.waitFor();
        I.fillField(this.locators.searchTopInput, this.inputValues.searchTopInput);
        I.pressKey('Enter');
        await I.waitFor('mediumWait');
        I.waitForElement(this.locators.searchResultInput);
        I.waitForElement(this.locators.searchResultList);
    },
    async goToDomesticStockThemeList(): Promise<void> {
        I.amOnPage(this.urls.searchTheme);
        await I.waitFor('mediumWait');
        I.waitForText('テーマから探す', 3, 'body');
        I.waitForElement(this.locators.themeList);
    },
    async goToDomesticStockThemeDetail(): Promise<void> {
        await I.setCookie({
            name: SESSION_STORAGE_KEY.themeDetail,
            value: SESSION_STORAGE_VALUE.themeDetail(),
        });
        await I.waitFor();
        await I.clickFixed(this.locators.firstThemeItem);
        await I.waitFor('mediumWait');
    },
    async goToFundSearch(): Promise<void> {
        await this.goToPage();
        await I.clickFixed(this.locators.fundSearch);
        await I.waitFor('mediumWait');
    },
    async goToFundSearchFilterByDetailedConditions(): Promise<void> {
        await I.clickFixed(this.locators.fundSearchFilterByDetailedCondition);
        await I.waitFor('shortWait');
        I.see('キーワード検索', 'body');
    },
    async goToMarginTradingSearchTop(): Promise<void> {
        await I.amOnPage(this.urls.marginSearch);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locators.searchPageContainer);
    },
    async goToMarginTradingSearchResult(): Promise<void> {
        await search.goToMarginTradingSearchTop();
        await I.clickFixed(this.locators.searchMarginKeyInput);
        await I.waitFor();
        I.fillField(this.locators.searchMarginKeyInput, this.inputValues.searchMarginInput);
        await I.waitFor('shortWait');
        I.pressKey('Enter');
        await I.waitFor('mediumWait');
        I.waitForElement(this.locators.searchResultDisplayCondition);
    },
};
