import { SCREENSHOT_PREFIX } from "../const/constant";

const { I, stockDetailBasicPage } = inject();

export = {
    locator: {
        detailInformationTab: '//div[@aria-hidden="false"][.//p[contains(text(), "詳細情報")]]',
        detailQuoteTab: '//div[@aria-hidden="false"][.//p[contains(text(), "板情報")]]',
        quarterlyTab: '//div[@aria-hidden="false"][.//p[contains(text(), "四季報")]]',
        financialTab: '//div[@aria-hidden="false"][.//p[contains(text(), "決算情報")]]',
        newsTab: '//div[@aria-hidden="false"][.//p[contains(text(), "ニュース")]]',
        detailInfoContainer: '//*[@data-testid="stockInfoDetail_detailInfo_id"]',
        boardInfoContainer: '//*[@data-testid="stockBoard_boardInfo_id"]',
        infoRootContainer: '#info-root',
        financialInfoContainer: '//*[@data-testid="stockFinancial_financialInfo_id"]',
        newsContainer: '//*[@data-testid="stockDetailNews_newsContainer_id"]',
        tradeButton: '//*[@data-testid="stockInfo_tradeButton_id"]',
        bottomSheetContainer: '#bottom-sheet-container',
    },
    urls: {
        stockDetailBasic: (symbol?: string) => `/mobile/info/stock/basic?symbol=${symbol ? symbol : '8308'}&exchange=EXCHANGE_TSE`,
    },
    takeScreenshot: {
        stockTradingMethodSelection: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockTradingMethodSelection}_${suffix}.png`);
        },
        stockDetailFinancialResult: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockDetailFinancialResult}_${suffix}.png`);
        },
        stockDetailQuarterlyReport: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockDetailQuarterlyReport}_${suffix}.png`);
        },
        stockDetailNews: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockDetailNews}_${suffix}.png`);
        },
        stockDetailQuote: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockDetailQuote}_${suffix}.png`);
        },
        stockDetailInformation: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockDetailInformation}_${suffix}.png`);
        },
        stockDetailBasic: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockDetailBasic}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToPage(symbol?: string): Promise<void> {
        I.amOnPage(this.urls.stockDetailBasic(symbol));
        await I.waitFor('longWait');
        I.waitForText('基本情報', 3, 'body');
        I.waitForElement(this.locator.infoRootContainer);
    },
    async goToDetailInformationTab(): Promise<void> {
        await stockDetailBasicPage.goToPage();
        await I.clickFixed(this.locator.detailInformationTab);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.detailInfoContainer);
    },
    async goToDetailQuoteTab(): Promise<void> {
        await stockDetailBasicPage.goToDetailInformationTab();
        await I.clickFixed(this.locator.detailQuoteTab);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.boardInfoContainer);
    },
    async goToDetailQuarterlyTab(): Promise<void> {
        await stockDetailBasicPage.goToDetailQuoteTab();
        await I.clickFixed(this.locator.quarterlyTab);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.infoRootContainer);
    },
    async goToDetailFinancialTab(): Promise<void> {
        await stockDetailBasicPage.goToDetailQuarterlyTab();
        await I.clickFixed(this.locator.financialTab);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.financialInfoContainer);
    },
    async goToDetailNewsTab(): Promise<void> {
        await stockDetailBasicPage.goToPage();
        await I.clickFixed(this.locator.newsTab);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.newsContainer);
    },
    async clickTradeButton(): Promise<void> {
        await I.clickFixed(this.locator.tradeButton);
        await I.waitFor();
        I.seeElement(this.locator.bottomSheetContainer);
    },
};
