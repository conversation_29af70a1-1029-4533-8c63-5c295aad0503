/**
 * 投信買付入力/投信売却入力
 * FundBuyInput/FundSellInput
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/Fund/Buy/Input.html
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/Fund/Sell/Input.html
 */

import common from "../search/common";

const { I } = inject();

const verifyVisibleTooltip = () => {
    const tooltipLocator = locate('//section[@role="dialog"]');
    I.waitForVisible(tooltipLocator, 5);
};

const openPDFLink = async (locator: string) => {
    await I.clickFixed(locator);
    await I.waitFor('mediumWait');
};

export const buyInput = {
    locators: {
        depositRequest: '$tradeFundBuy_depositRequest_id', // 11.入金依頼
        accountTypeHelp: '$tradeFundBuy_accountTypeHelp_id', // 13.口座区分ヘルプ
        accountType: '$tradeFundBuy_accountType_id', // 14.口座区分
        nisaUsageLimitHelp: '$tradeFundBuy_NISAUsageLimitHelp_id', // 16.NISA利用枠ヘルプ
        distributionHelp: '$tradeFundBuy_distributionHelp_id', // 23.分配金ヘルプ
        distribution: '$tradeFundBuy_distribution_id', // 24.分配金
        suggestInputNumber: '$suggestInputNumber_id', // 26.金額入力テキストボックス
        suggestInputNumberButtonGroup: '$suggestInputNumber_buttonGroup', // 29.入力補助ボタン
        amountMoneyCellSwitch: '$amountMoneyCell_switch', // 30.Ponta利用
        groupInputNumber: '$groupInputNumber_input_id', // 32.ポイント入力テキストボックス
        amountMoneyCellUseAll: '$amountMoneyCell_useAll', // 33.全て利用
        paymentMethodHelp: '$tradeFundBuy_paymentMethodHelp_id', // 37.受渡方法ヘルプ
        tradeDateHelp: '$tradeFundBuy_tradeDateHelp_id', // 39.約定日ヘルプ
        settlementDateHelp: '$tradeFundBuy_settlementDateHelp_id', // 41.受渡日ヘルプ
        deliveryProspectusLink: '$tradeFundBuy_deliveryProspectusLink_id', // 46.交付目論見書-リンク
        deliveryProspectusConfirmButton: '$tradeFundBuy_deliveryProspectusConfirmButton_id', // 47.交付目論見書-確認ボタン
        spdLink: '$tradeFundBuy_supplementaryProspectusDocumentLink_id', // 49.目論見書補完書面-リンク
        spdConfirmButton: '$tradeFundBuy_supplementaryProspectusDocumentConfirmButton_id', // 50.目論見書補完書面-確認ボタン
        confirmationItemLink: '$tradeFundBuy_confirmationItemLink_id', // 52.ご確認事項-リンク
        confirmationItemConfirmButton: '$tradeFundBuy_confirmationItemConfirmButton_id', // 53.ご確認事項-確認ボタン
        taxFreeInvestmentAgreeCheck: '$tradeFundBuy_taxFreeInvestmentAgreeCheck_id', // 56.非課税投資同意チェック
        goToConfirmScreen: '$tradeFundBuy_goToConfirmScreen_id', // 57.注文確認画面へ
        importantItem: '$tradeFundBuy_importantItem_id', // 58.重要事項
        cautionMessage: '$tradeFundBuy_cautionMessage_id', // 59.ご注意文言
    },

    async goToPage(params: { fundCode: string }) {
        const url = `/mobile/trade/fund/buy/input?fundCode=${params.fundCode}`;
        I.amOnPage(url);
        await I.waitFor('mediumWait');
    },

    async deposit() {
        const btn = locate(this.locators.depositRequest);
        I.waitForVisible(btn, 5);
        // Verify deposit page
        await common.clickCardItem(btn, '/mobile/cashflow/depositrequest', 'external');
    },

    async openAccountTypeHelpTooltip() {
        await I.clickFixed(this.locators.accountTypeHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async verifyAcountTypeSelection() {
        const label = locate('p').inside(this.locators.accountType).withAttr({ 'data-checked': '' });
        I.say(`${await I.grabTextFrom(label)} is selected`);
    },

    async openNISAUtilizationHelpTooltip() {
        await I.clickFixed(this.locators.nisaUsageLimitHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async openDistributionHelpTooltip() {
        await I.clickFixed(this.locators.distributionHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async verifyDistributionSelection() {
        //  TODO: radio button selection
    },

    async fillSuggestInputNumber() {
        const input = locate(this.locators.suggestInputNumber);
        await I.clickFixed(input);
        await I.waitFor(); // wait for keyboard apppear
        await I.fillField(input, '10000\n');
    },

    async selectSuggestInputNumberGroup(screenShotPrefix: string) {
        const priceAmounts = ['+10,000', '+50,000', '+100,000'];

        const btnGroup = locate('button').inside(this.locators.suggestInputNumberButtonGroup);
        const input = locate(this.locators.suggestInputNumber);

        I.waitForVisible(input, 5);
        await I.scrollToElement(input.toXPath());
        await I.waitFor(); // wait for scroll
        for (const priceText of priceAmounts) {
            await I.clickFixed(btnGroup.withText(priceText));
            await I.saveScreenshot(`${screenShotPrefix}_add_${priceText}.png`);
            await I.waitFor('shortWait'); // wait toggle input
        }
        // clear input
        await I.clickFixed(btnGroup.last());
        await I.saveScreenshot(`${screenShotPrefix}_cancel.png`);
    },

    async toggleSwitchMoneyCell() {
        const switchLocator = locate(this.locators.amountMoneyCellSwitch);

        I.waitForVisible(switchLocator, 5);
        await I.scrollToElement(switchLocator.toXPath());
        await I.waitFor(); // wait for scroll
        await I.clickFixed(switchLocator);
    },

    async fillGroupInputNumber() {
        const input = locate(this.locators.groupInputNumber);

        await this.toggleSwitchMoneyCell();
        I.waitForVisible(input, 5);
        await I.fillField(input, '1000\n');
    },

    async useAllGroupInputNumber() {
        const btn = locate(this.locators.amountMoneyCellUseAll);

        await this.toggleSwitchMoneyCell();
        I.waitForVisible(btn, 5);
        await I.clickFixed(btn);
    },

    async openPaymentMethodHelpTooltip() {
        await I.clickFixed(this.locators.paymentMethodHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async verifyPaymentMethodSelection() {
        //  TODO: radio button selection
    },

    async openTradeDateHelpTooltip() {
        await I.clickFixed(this.locators.tradeDateHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async openSettlementDateHelpTooltip() {
        await I.clickFixed(this.locators.settlementDateHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async openDeliveryProspectusLink() {
        await openPDFLink(this.locators.deliveryProspectusLink);
    },

    async openDeliveryProspectusConfirmButton() {
        await openPDFLink(this.locators.deliveryProspectusConfirmButton);
    },

    async openSupplementaryProspectusDocumentLink() {
        await openPDFLink(this.locators.spdLink);
    },

    async openSupplementaryProspectusDocumentConfirmButton() {
        await openPDFLink(this.locators.spdConfirmButton);
    },

    async openConfirmationItemLink() {
        await openPDFLink(this.locators.confirmationItemLink);
    },

    async openConfirmationItemConfirmButton() {
        await openPDFLink(this.locators.confirmationItemConfirmButton);
    },

    async toggleTaxFreeInvestmentAgreeCheck() {
        const freeInvestmentAgreeCheckArea = locate(this.locators.taxFreeInvestmentAgreeCheck);

        await I.scrollPageToBottom();
        await I.waitFor('shortWait'); // wait for scrolling
        await I.clickFixed(freeInvestmentAgreeCheckArea);
    },

    async goToConfirmScreen() {
        const btn = locate(this.locators.goToConfirmScreen);

        await this.fillSuggestInputNumber();
        await this.fillGroupInputNumber();
        await I.scrollToElement(btn.toXPath());
        await I.waitFor();
        await I.clickFixed(btn);
        await I.waitInUrl('/mobile/trade/fund/buy/confirm', 5);
    },

    async expandImportantItem() {
        const btn = locate(this.locators.importantItem);
        await I.clickFixed(btn);
    },

    async expandCautionMessage() {
        const btn = locate(this.locators.cautionMessage);
        await I.clickFixed(btn);
    },

    //
    async selectAccountType(type: string) {
        const btn = locate('label').inside(this.locators.accountType).withText(type);
        await I.clickFixed(btn);
    },
};

export const sellInput = {
    screenShotPrefix: {
        fundSell: 'trade_fund_sell_input_No',
        fundConfirm: 'trade_fund_sell_confirm_No',
        fundComplete: 'trade_fund_sell_complete_No',
        fundCancelCofirm: 'trade_fund_sell_cancel_confirm_No',
    },
    locators: {
        groupInputNumber: '$groupInputNumber_input_id',
        goToConfirmScreen: '$tradeFundSell_goToConfirmScreen_id',
        cautionMessage: '(//*[@data-testid="tradeFundSell_cautionMessage_id"]/div)[1]',
        tradeType: '$tradeFundSell_tradeType_id',
        tradeType2Item: '(//*[@data-testid="tradeFundSell_tradeType_id"]/label)[2]',
        buyRequestLink: '$tradeFundSell_buyRequestLink_id',
        fundSellButton: '$positionInquiryFund_sell_id',
        fundFirstItem: '(//*[@data-testid="positionInquiryFund_orderDetail_id"])[1]',
        amountHelp: '$tradeFundSellConfirm_plannedDeliveryAmountHelp_id',
        passwordInput: '$tradeFundSellConfirm_password_id',
        passwordOmissionCheck: '$tradeFundSellConfirm_passwordOmissionCheck_id',
        passwordInputCheck: '$tradeFundSellConfirm_passwordInputCheck_id',
        confirmOrderButton: '$tradeFundSellConfirm_confirmOrder_id',
        sellConfirmCautionMessage: '(//*[@data-testid="tradeFundSellConfirm_cautionMessage_id"]/div)[1]',
        fundOrderItem: '(//*[@data-testid="fundSuccess_orderID_list_id"])/div[1]/a',
        orderInquiryButton: '$fundSuccess_orderInquiry_id',
        orderCancellationButton: '$fundSuccess_orderCancellation_id',
        fundSearchButton: '$fundSuccess_fundSearch_id',
        cancelConfirmPassword: '$tradeFundCancelConfirm_password_id',
        cancelConfirmPasswordOmissionCheck: '$tradeFundCancelConfirm_passwordOmissionCheck_id',
        cancelConfirmPasswordInputCheck: '$tradeFundCancelConfirm_passwordInputCheck_id',
        cancelOrderButton: '$tradeFundCancelConfirm_doCancel_id',
    },
    urls: {
        fundList: '/mobile/position-inquiry/fund',
        sellInput: '/mobile/trade/fund/sell/input',
        sellConfirm: '/mobile/trade/fund/sell/confirm',
        sellComplete: '/mobile/trade/fund/sell/complete',
        sellCancelConfirm: '/mobile/trade/fund/cancel/confirm',
        orderInquiryFund: '/mobile/order-inquiry/fund',
    },

    async goToTradeFundSellInputPage() {
        I.amOnPage(this.urls.fundList);
        await I.waitFor('mediumWait');
        I.seeElement(this.locators.fundFirstItem);
        I.clickFixed(this.locators.fundFirstItem);
        I.waitFor('mediumWait');
        I.seeElement(this.locators.fundSellButton);
        I.clickFixed(this.locators.fundSellButton);
        I.waitFor('mediumWait');
        I.waitForText('投資信託売注文', 5, 'p');
        I.seeInCurrentUrl(this.urls.sellInput);
    },
    async goToTradeFundConfirmPage() {
        I.seeElement(this.locators.goToConfirmScreen);
        await I.clickFixed(this.locators.goToConfirmScreen);
        await I.waitFor('mediumWait');
        I.waitForText('投資信託売注文', 5, 'p');
        I.seeInCurrentUrl(this.urls.sellConfirm);
    },
    async goToTradeFundCompletePage() {
        // isOmmitPassword:false
        if ((await I.grabNumberOfVisibleElements(this.locators.passwordInput)) > 0) {
            I.seeElement(this.locators.passwordInput);
            I.fillField(this.locators.passwordInput, '123');
        } else {
            I.say('Password input is not found');
        }
        if ((await I.grabNumberOfVisibleElements(this.locators.passwordOmissionCheck)) > 0) {
            I.seeElement(this.locators.passwordOmissionCheck);
            await I.clickFixed(this.locators.passwordOmissionCheck);
        } else {
            I.say('Password omission check is not found');
        }
        // isOmmitPassword:true
        if ((await I.grabNumberOfVisibleElements(this.locators.passwordInputCheck)) > 0) {
            I.seeElement(this.locators.passwordInputCheck);
            await I.clickFixed(this.locators.passwordInputCheck);
        } else {
            I.say('Password input check is not found');
        }
        I.seeElement(this.locators.confirmOrderButton);
        await I.clickFixed(this.locators.confirmOrderButton);
        I.waitForText('ご注文を受付いたしました。', 5, 'p');
        I.seeInCurrentUrl(this.urls.sellComplete);
    },
    async goToTradeFundCancelConfirmPage() {
        await I.waitFor('mediumWait');
        I.seeElement(this.locators.orderCancellationButton);
        await I.clickFixed(this.locators.orderCancellationButton);
        I.waitForText('投資信託\n注文取消', 5, 'p');
        I.seeInCurrentUrl(this.urls.sellCancelConfirm);
    },
    async fillGroupInputNumber() {
        await I.swipeElementDirection('up', this.locators.groupInputNumber);
        I.fillField(this.locators.groupInputNumber, '10');
        await I.waitFor();
    },
    async setSellConfirmSessionData(objectData: Record<string, unknown>) {
        const sessionId = '/trade/fund/sell/confirm';
        const data = await I.executeScript((key) => {
            return window.sessionStorage.getItem(key);
        }, sessionId);
        const newData = { ...JSON.parse(data), ...objectData };
        await I.executeScript(
            (key, value) => {
                return window.sessionStorage.setItem(key, JSON.stringify(value));
            },
            sessionId,
            newData,
        );
        await I.clickFixed('$common_reload_id');
    },
    async backPage() {
        await I.waitFor();
        I.seeElement('$common_back_id');
        await I.clickFixed('$common_back_id');
    },
};
