/**
 * 投信買付確認/投信売却確認
 * FundBuyConfirm/FundSellConfirm
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/Fund/Buy/Confirm.html
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/Fund/Sell/Confirm.html
 */

import { SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from '../../const/constant';
import commonUi from '../common-ui';
import common from '../search/common';

const { I } = inject();

const verifyVisibleTooltip = () => {
    const tooltipLocator = locate('//section[@role="dialog"]');
    I.waitForVisible(tooltipLocator, 5);
};

export const buyConfirm = {
    locators: {
        plannedDeliveryAmountHelp: '$tradeFundConfirm_plannedDeliveryAmountHelp_id', // 10.予定受渡金額ヘルプ
        password: '$tradeFundConfirm_password_id', // 21.パスワード
        passwordOmissionCheck: '$tradeFundConfirm_passwordOmissionCheck_id', // 22.パスワード省略チェック
        passwordInputCheck: '$tradeFundConfirm_passwordInputCheck_id', // 24.パスワード入力チェック
        apply: '$tradeFundConfirm_apply_id', // 25.申し込む
        orderingStocksCautionAfterPlacingAnOrder: '$tradeFundConfirm_orderingStocksCautionAfterPlacingAnOrder_id', // 26.注文後の株式注文にご注意
        cautionMessage: '$tradeFundConfirm_cautionMessage_id', // 27.ご注意文言
    },

    async openPlannedDeliveryAmountHelpTooltip() {
        await I.clickFixed(this.locators.plannedDeliveryAmountHelp);
        await I.waitFor('shortWait');
        await verifyVisibleTooltip();
    },

    async fillPassword(password: string) {
        const pwd = locate(this.locators.password);

        I.waitForVisible(pwd, 5);
        await I.scrollToElement(pwd.toXPath());
        await I.waitFor();
        await I.fillField(pwd, password);
        // hide keyboard by click on header title
        await I.clickFixed(locate(commonUi.header.locators.title));
    },

    async togglePasswordOmissionCheck() {
        const pwdOmissionCheck = locate(this.locators.passwordOmissionCheck);

        I.waitForVisible(pwdOmissionCheck, 5);
        await I.scrollToElement(pwdOmissionCheck.toXPath());
        await I.waitFor();
        await I.clickFixed(pwdOmissionCheck);
    },

    async togglePasswordInputCheck() {
        const pwdInputCheck = locate(this.locators.passwordInputCheck);

        I.waitForVisible(pwdInputCheck, 5);
        await I.scrollToElement(pwdInputCheck.toXPath());
        await I.waitFor();
        await I.clickFixed(pwdInputCheck);
    },

    async apply() {
        const apply = locate(this.locators.apply);

        if (await this.isOmmitPassword()) {
            await this.togglePasswordInputCheck();
        } else {
            await this.fillPassword('111111');
            await this.togglePasswordOmissionCheck();
        }

        await I.clickFixed(apply);
        await I.waitFor();
        await I.waitInUrl('/mobile/trade/fund/buy/complete', 5);
    },

    async verifyOrderingStocksCautionAfterPlacingAnOrder() {
        const orderingStocksCautionAfterPlacingAnOrder = locate(this.locators.orderingStocksCautionAfterPlacingAnOrder);
        await I.scrollToElement(orderingStocksCautionAfterPlacingAnOrder.toXPath());
        await I.waitFor();
        // Verify external link https://kabu.com/company/info/escapeclause.html
        await common.clickCardItem(orderingStocksCautionAfterPlacingAnOrder.toXPath(), 'https://kabu.com/company/info/escapeclause.html', 'external');
    },

    async verifyCautionMessage() {
        const cautionMessage = locate(this.locators.cautionMessage);
        await I.scrollToElement(cautionMessage.toXPath());
        await I.waitFor();
        await I.clickFixed(cautionMessage);
    },

    //
    async sessionData() {
        const sessionData = await I.getSessionStorage(SESSION_STORAGE_KEY.tradeFundBuyConfirm);
        return JSON.parse(sessionData);
    },

    async setSessionData(sessionData: any) {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeFundBuyConfirm, SESSION_STORAGE_VALUE.tradeFundBuyConfirm(sessionData));
    },

    async isOmmitPassword() {
        const sessionData = await this.sessionData();
        console.debug('sessionData.isOmmitPassword', sessionData.isOmmitPassword);
        return sessionData.isOmmitPassword;
    },
};

export const sellConfirm = {
    locators: {
        plannedDeliveryAmountHelp: '$tradeFundSellConfirm_plannedDeliveryAmountHelp_id', // 10.予定受渡金額ヘルプ
        password: '$tradeFundSellConfirm_password_id', // 14.パスワード
        passwordOmissionCheck: '$tradeFundSellConfirm_passwordOmissionCheck_id', // 15.パスワード省略チェック
        passwordInputCheck: '$tradeFundSellConfirm_passwordInputCheck_id', // 17.パスワード入力チェック
        confirmOrder: '$tradeFundSellConfirm_confirmOrder_id', // 18.注文を確定
        cautionMessage: '$tradeFundSellConfirm_cautionMessage_id', // 19.ご注意文言
    },

    // TODO: QC confirm feature not implemented yet
};
