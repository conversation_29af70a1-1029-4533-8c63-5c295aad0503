/**
 * 投信買付完了/投信売却完了
 * FundBuyComplete/FundSellComplete
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/Fund/Buy/Complete.html
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/Fund/Sell/Complete.html
 */

const { I } = inject();

export = {
    locators: {
        orderId: '$fundSuccess_orderID_id', // 1.注文番号
        orderInquiry: '$fundSuccess_orderInquiry_id', // 2.注文照会
        orderCancellation: '$fundSuccess_orderCancellation_id', // 3.注文取消
        fundSearch: '$fundSuccess_fundSearch_id', // 4.ファンド検索
    },

    async verifyOrderId(orderId: string) {
        const orderIdLocator = await I.grabTextFrom(locate(this.locators.orderId));
        I.assertContain(orderIdLocator, orderId);
    },

    async goToOrderInquiry() {
        const orderInquiryLocator = locate(this.locators.orderInquiry);
        I.waitForVisible(orderInquiryLocator, 5);
        await I.clickFixed(orderInquiryLocator);
        I.waitInUrl('/mobile/order-inquiry/fund', 5);
    },

    async goToOrderCancellation() {
        const orderCancellationLocator = locate(this.locators.orderCancellation);
        I.waitForVisible(orderCancellationLocator, 5);
        await I.clickFixed(orderCancellationLocator);
        I.waitInUrl('/mobile/trade/fund/cancel/confirm', 5);
    },

    async goToFundSearch() {
        const fundSearchLocator = locate(this.locators.fundSearch);
        I.waitForVisible(fundSearchLocator, 5);
        await I.clickFixed(fundSearchLocator);
        I.waitInUrl('/mobile/info/fund/search', 5);
    },

    async browserBack() {
        await I.performBrowserBack();
        I.waitInUrl('/mobile/order-inquiry/fund', 5);
    },
};
