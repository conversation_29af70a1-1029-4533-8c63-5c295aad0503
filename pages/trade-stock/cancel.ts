/**
 * 国内株式現物取引-注文訂正・取消-取消
 * DomesticStockCashTrading-OrderCorrect・Cancel-Cancel
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/StockOrder/Cancel.html
 */

const { I } = inject();

export = {
    locators: {
        password: '$stockCancel_password_id', // 12.パスワード
        passwordOmissionCheck: '$stockCancel_passwordOmissionCheck_id', // 13.パスワード省略チェック
        checkInputPassword: '$stockCancel_checkInputPassword_id', // 15.パスワード入力チェック
        confirmButton: '$stockCancel_confirmButton_id', // 16.取消を確定
        backOrderInquiryButton: '$stockCancel_backOrderInquiryButton_id', // 17.注文照会画面に戻る
        caution: '$stockCancel_caution_id', // 18.ご注意
    },

    async fillPassword(password: string) {
        const passwordInput = locate(this.locators.password);
        I.waitForVisible(passwordInput);
        await I.scrollToElement(passwordInput.toXPath());
        await I.clickFixed(passwordInput);
        await I.fillField(passwordInput, password);
    },

    async togglePasswordOmissionCheck() {
        const pwdOmissionCheck = locate(this.locators.passwordOmissionCheck);

        I.waitForVisible(pwdOmissionCheck, 5);
        await I.scrollToElement(pwdOmissionCheck.toXPath());
        await I.waitFor();
        await I.clickFixed(pwdOmissionCheck);
    },

    async togglePasswordInputCheck() {
        const pwdInputCheck = locate(this.locators.checkInputPassword);

        I.waitForVisible(pwdInputCheck, 5);
        await I.scrollToElement(pwdInputCheck.toXPath());
        await I.waitFor();
        await I.clickFixed(pwdInputCheck);
    },

    async apply() {
        const btn = locate(this.locators.confirmButton);
        await I.scrollToElement(btn.toXPath());
        await I.clickFixed(btn);
        await I.waitFor();
        await I.waitInUrl('/mobile/order-inquiry/stock/detail', 5);
    },

    async backToOrderInquiry() {
        const btn = locate(this.locators.backOrderInquiryButton);
        await I.scrollToElement(btn.toXPath());
        await I.clickFixed(btn);
        await I.waitFor();
        await I.waitInUrl('/mobile/order-inquiry/stock/detail', 5);
    },

    async verifyCaution() {
        const caution = locate(this.locators.caution);
        await I.scrollToElement(caution.toXPath());
        await I.waitFor();
        await I.clickFixed(caution);
        await I.swipeDirection('up', 0.6, 500);
        await I.waitFor();
    },
};
