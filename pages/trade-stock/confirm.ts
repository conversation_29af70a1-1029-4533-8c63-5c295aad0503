/**
 * 国内株式現物取引-注文訂正・取消-訂正確認
 * DomesticStockCashTrading-OrderReplace・Cancel-ReplaceConfirm
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/StockOrder/ReplaceConfirm.html
 */

import { SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../../const/constant";

const { I } = inject();

export = {
    locators: {
        password: '$correctionConfirm_password_id', // 18.パスワード
        passwordOmissionCheck: '$correctionConfirm_passwordOmissionCheck_id', // 19.パスワード省略チェック
        checkInputPassword: '$correctionConfirm_checkInputPassword_id', // 21.パスワード入力チェック
        confirmButton: '$correctionConfirm_confirmButton_id', // 22.訂正を確定
    },

    async fillPassword(password: string) {
        const passwordInput = locate(this.locators.password);
        I.waitForVisible(passwordInput);
        await I.scrollToElement(passwordInput.toXPath());
        await I<PERSON>clickFixed(passwordInput);
        await <PERSON>.fillField(passwordInput, password);
    },

    async toggle<PERSON>asswordOmissionCheck() {
        const passwordInput = locate(this.locators.passwordOmissionCheck);
        I.waitForVisible(passwordInput);
        await I.scrollToElement(passwordInput.toXPath());
        await I.clickFixed(passwordInput);
    },

    async toggleCheckInputPassword() {
        const checkInputPassword = locate(this.locators.checkInputPassword);
        I.waitForVisible(checkInputPassword);
        await I.scrollToElement(checkInputPassword.toXPath());
        await I.clickFixed(checkInputPassword);
    },

    async apply() {
        const btn = locate(this.locators.confirmButton);
        await I.scrollToElement(btn.toXPath());
        await I.clickFixed(btn);
        await I.waitFor();
        await I.waitInUrl('/mobile/order-inquiry/stock/detail', 5);
    },

    async sessionData() {
        const sessionData = await I.getSessionStorage(SESSION_STORAGE_KEY.tradeStockCorrectionConfirm);
        return JSON.parse(sessionData);
    },

    async setSessionData(sessionData: any) {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeStockCorrectionConfirm, SESSION_STORAGE_VALUE.tradeStockCorrectionConfirm(sessionData));
    },
};
