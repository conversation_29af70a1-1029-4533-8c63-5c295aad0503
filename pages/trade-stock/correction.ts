/**
 * 国内株式現物取引-注文訂正・取消-訂正
 * DomesticStockCashTrading-OrderReplace・Cancel-Replace
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Product/StockOrder/Replace.html
 */

const { I } = inject();

export = {
    locators: {
        replaceContent: '$correction_replaceContent_id', // 3.訂正内容 - 削減数量
        replaceContentInput: '$groupInputNumber_input_id',
        replaceContentMinus: '$groupInputNumber_minus_id',
        replaceContentPlus: '$groupInputNumber_plus_id',
        doReplaceButton: '$correction_doReplaceButton_id', // 4.訂正を行う
        relayTargetOrderID: '$correction_relayTargetOrderID_id', // 23.ご注意
        caution: '$correction_caution_id', // 26.リレー先注文番号
    },

    async doReplace() {
        const button = locate(this.locators.doReplaceButton);
        I.waitForVisible(button);
        await I.scrollToElement(button.toXPath());
        await I.clickFixed(button);
        I.waitInUrl('/mobile/trade/stock/correction/confirm', 5);
    },

    async verifyRelayTargetOrderID() {
        const relayTargetOrderID = locate(this.locators.relayTargetOrderID);
        I.waitForVisible(relayTargetOrderID);
        await I.scrollToElement(relayTargetOrderID.toXPath());
        await I.clickFixed(locate('a').inside(relayTargetOrderID).first());
        await I.waitFor();
        I.waitInUrl('/mobile/order-inquiry/stock/detail', 5);
    },

    async verifyCaution() {
        const caution = locate(this.locators.caution);
        I.waitForVisible(caution);
        await I.scrollToElement(caution.toXPath());
        await I.clickFixed(caution);
        await I.swipeDirection('up', 0.6, 500);
        await I.waitFor();
    },
};
