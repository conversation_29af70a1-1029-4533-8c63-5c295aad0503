const { I } = inject();

export = {
    locator: {
        // View display formats
        listFormatButton: '#list path',
        gridFormatButton: '[data-testid="favoritesList_displayFormatSelect_id"] svg#grid',
        // List items and panels
        favoriteItemLine: (index = 0) => `//*[@data-testid="favoritesList_favoriteSymbolLineIndex_id_${index}"]`,
        favoriteItemPanel: (index = 0) => `//*[@data-testid="favoritesList_favoriteSymbolPanelIndex_id_${index}"]`,
        // Detail elements
        stockSymbolDetail: (index = 0) => `//*[@data-testid="favoritesList_favoriteSymbolLineIndex_id_${index}"]//p[1]`,
        // Setting modal elements
        usStockSymbol: '//*[contains(text(), "米国株式") or contains(@class, "米国株式")]',
        indexSymbol: '//*[contains(text(), "指数") or contains(@class, "指数")]',
        fundSymbol: '//*[contains(text(), "投資信託") or contains(@class, "投資信託")]',
        unregisteredSymbol: '//*[contains(text(), "未登録") or contains(@class, "未登録")]',

        // Detail page locators
        usStockDetail: '//*[@data-testid="usStockDetail_symbolInfo_id"]',
        indexDetail: '//*[@data-testid="indicatorDetail_symbolInfo_id"]',
        fundDetail: '//*[@data-testid="fundDetail_symbolInfo_id"]',
    },
    async navigateToFavoritesList() {
        const favoriteListTab = '//*[@id="/favorite"]';
        I.waitForElement(favoriteListTab);
        I.tapLocationOfElement(favoriteListTab);
        await I.waitFor('mediumWait');
    },
    async clickFavoriteListName() {
        const favoriteListName = '//*[@data-testid="favoritesList_favoriteListName_id"]';
        I.waitForElement(favoriteListName);
        I.clickFixed(favoriteListName);
        await I.waitFor();
        I.waitForElement(this.locator.commonMenuList);
    },
    async openSettings() {
        const settingButton = '//*[@data-testid="favoritesList_setting_id"]';
        const settingModal = '//*[@data-testid="favoritesList_closeButton_id"]';
        I.waitForElement(settingButton);
        I.clickFixed(settingButton);
        await I.waitFor();
        I.waitForElement(settingModal);
    },
    async openVariousSettings() {
        const variousSettings = '//*[@data-testid="favoritesList_variousSetting_id"]';
        I.waitForElement(variousSettings);
        I.clickFixed(variousSettings);
        await I.waitFor();
    },
    async closeSettings() {
        const closeSettingButton = '//*[@data-testid="favoritesList_closeButton_id"]';
        I.waitForElement(closeSettingButton);
        I.clickFixed(closeSettingButton);
        await I.waitFor('shortWait');
    },
    async switchToGridView() {
        await I.waitFor();
        await I.tapLocationOfElement(this.locator.gridFormatButton);
        await I.waitFor();
        I.waitForElement(this.locator.favoriteItemPanel(0));
    },
    async switchToListView() {
        I.waitForElement(this.locator.listFormatButton);
        I.clickFixed(this.locator.listFormatButton);
        await I.waitFor();
        I.waitForElement(this.locator.favoriteItemLine(0));
    },
    async enterEditMode() {
        const editButton = '//*[@data-testid="favoritesList_edit_id"]';
        const deleteButton = '//*[@data-testid="favoritesList_deleteButton_id"]';
        const sortHandler = '//*[@data-testid="favoritesList_sortHandler_id"]';
        I.waitForElement(editButton);
        I.clickFixed(editButton);
        await I.waitFor();
        I.waitForElement(deleteButton);
        I.waitForElement(sortHandler);
    },
    async exitEditMode() {
        const completeButton = '//*[@data-testid="favoritesList_complete_id"]';
        I.waitForElement(completeButton);
        I.clickFixed(completeButton);
        await I.waitFor();
    },
    async deleteItem(index = 0) {
        await this.enterEditMode();
        const deleteItemButton = `//*[@data-testid="favoritesList_deleteSymbol_id_${index}"]`;
        I.waitForElement(deleteItemButton);
        I.clickFixed(deleteItemButton);
        await I.waitFor('shortWait');
    },
    async dragAndDropItem(fromIndex = 0, toIndex = 1) {
        await this.enterEditMode();
        const sourceItem = `//*[@data-testid="favoritesList_sortHandler_id_${fromIndex}"]`;
        const targetItem = `//*[@data-testid="favoritesList_sortHandler_id_${toIndex}"]`;

        I.waitForElement(sourceItem);
        I.waitForElement(targetItem);

        // Perform drag and drop using native Appium gestures
        I.dragAndDrop(sourceItem, targetItem);
        await I.waitFor();
    },
    async scrollToTop() {
        const scrollToTopButton = '//*[@data-testid="favoritesList_scrollTop_id"]';
        I.waitForElement(scrollToTopButton);
        I.clickFixed(scrollToTopButton);
        await I.waitFor();
    },
    async openFavoriteItemDetail(index = 0) {
        I.waitForElement(this.locator.favoriteItemLine(index));
        I.clickFixed(this.locator.favoriteItemLine(index));
        await I.waitFor('mediumWait');
    },
    async openListSettingsModal() {
        const listSettingsModal = '//*[@data-testid="favoritesList_listSetting_id"]';
        const commonMenuList = '//*[@data-testid="common_MenuList_id"]';

        await this.clickFavoriteListName();
        I.waitForElement(commonMenuList);
        I.clickFixed('//*[text()="設定"]');
        await I.waitFor();
        I.waitForElement(listSettingsModal);
    },
    async addNewList(listName = 'リスト5') {
        const addNewListButton = '//*[@data-testid="favoritesList_addList_id"]';
        const newListNameInput = '//*[@data-testid="favoritesList_listNameInput_id"]';
        const createNewListButton = '//*[@data-testid="favoritesList_createList_id"]';

        I.waitForElement(addNewListButton);
        I.clickFixed(addNewListButton);
        await I.waitFor();

        I.waitForElement(newListNameInput);
        I.fillField(newListNameInput, listName);
        await I.waitFor('shortWait');

        I.clickFixed(createNewListButton);
        await I.waitFor();
    },
    async deleteList() {
        const deleteListButton = '//*[@data-testid="favoritesList_deleteSelectedList_id"]';
        const confirmDeleteButton = '//*[@data-testid="favoritesList_confirmDelete_id"]';

        I.waitForElement(deleteListButton);
        I.clickFixed(deleteListButton);
        await I.waitFor();

        I.waitForElement(confirmDeleteButton);
        I.clickFixed(confirmDeleteButton);
        await I.waitFor('mediumWait');
    },
    async cancelDeleteList() {
        const deleteListButton = '//*[@data-testid="favoritesList_deleteSelectedList_id"]';
        const cancelDeleteButton = '//*[@data-testid="favoritesList_cancelDelete_id"]';

        I.waitForElement(deleteListButton);
        I.clickFixed(deleteListButton);
        await I.waitFor();

        I.waitForElement(cancelDeleteButton);
        I.clickFixed(cancelDeleteButton);
        await I.waitFor('shortWait');
    },
    async openUSStockDetail() {
        // Find and open US stock detail
        I.waitForElement(this.locator.usStockSymbol);
        I.clickFixed(this.locator.usStockSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.usStockDetail);
        I.saveScreenshot('us_stock_detail.png');
    },
    async openIndexDetail() {
        // Find and open index detail
        I.waitForElement(this.locator.indexSymbol);
        I.clickFixed(this.locator.indexSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.indexDetail);
        I.saveScreenshot('index_detail.png');
    },
    async openFundDetail() {
        // Find and open fund detail
        I.waitForElement(this.locator.fundSymbol);
        I.clickFixed(this.locator.fundSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.fundDetail);
        I.saveScreenshot('fund_detail.png');
    },
    async openUnregisteredSymbol() {
        const searchPage = '//*[@data-testid="symbolSearch_page_id"]';

        // Find and open unregistered symbol search
        I.waitForElement(this.locator.unregisteredSymbol);
        I.clickFixed(this.locator.unregisteredSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(searchPage);
        I.saveScreenshot('symbol_search_page.png');
    },
    async switchToGridAndOpenDomesticStock() {
        // Switch to grid view and open domestic stock detail
        const domesticStockSymbol = '//*[contains(text(), "国内株式") or contains(@class, "国内株式")]';
        const domesticStockDetail = '//*[@data-testid="stockDetail_symbolInfo_id"]';

        await this.switchToGridView();
        I.waitForElement(domesticStockSymbol);
        I.clickFixed(domesticStockSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(domesticStockDetail);
        I.saveScreenshot('domestic_stock_detail_from_grid.png');
    },
    async switchToGridAndOpenUSStock() {
        // Switch to grid view and open US stock detail
        await this.switchToGridView();
        I.waitForElement(this.locator.usStockSymbol);
        I.clickFixed(this.locator.usStockSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.usStockDetail);
        I.saveScreenshot('us_stock_detail_from_grid.png');
    },
    async switchToGridAndOpenIndex() {
        // Switch to grid view and open index detail
        await this.switchToGridView();
        I.waitForElement(this.locator.indexSymbol);
        I.clickFixed(this.locator.indexSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.indexDetail);
        I.saveScreenshot('index_detail_from_grid.png');
    },
    async switchToGridAndOpenFund() {
        // Switch to grid view and open fund detail
        await this.switchToGridView();
        I.waitForElement(this.locator.fundSymbol);
        I.clickFixed(this.locator.fundSymbol);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.fundDetail);
        I.saveScreenshot('fund_detail_from_grid.png');
    },
    async addNewFavoriteList(keyword: string): Promise<void> {
        const searchInput = '//*[@data-testid="favoritesList_favoriteListName_id"]/input';
        I.waitForElement(searchInput);
        I.clickFixed(searchInput);
        await I.waitFor('shortWait');
        await I.fillField(searchInput, keyword);
        // Press Enter to submit
        I.pressKey('Enter'); 
        await I.waitFor();
    }
};
