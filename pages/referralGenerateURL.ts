import { LAST_EXTERNAL_URL } from '../const/constant';

const { I } = inject();

export = {
    // Define locators
    locators: {
        headerTitle: '//*[@data-testid="common_header_title_id"]',
        backButton: '//*[@data-testid="common_back_id"]',
        aboutFriendsProgram: '//*[@data-testid="referralGenerateURL_aboutFriendsProgram_id"]',
        copyReferralUrl: '//*[@data-testid="referralGenerateURL_copyReferralUrl_id"]',
        goToFriendIntroductionTOP: '//*[@data-testid="referralGenerateURL_goToFriendIntroductionTOP_id"]',
        toastNotification: '//*[@id="chakra-toast-manager-top"]/li/div/div',
    },

    // URLs
    urls: {
        generateURLPage: '/mobile/referral/generate-url',
        programUrl: 'https://kabu.com/sp/campaign/introduction_program.html',
        referralPage: 'mobile/referral',
    },

    // Navigate to the generate URL page
    async navigateToPage() {
        await I.waitFor();
        await I.amOnPage(this.urls.generateURLPage);
        await I.waitFor();
        I.see(
            `ご家族・ご友人
紹介プログラム`,
            this.locators.headerTitle,
        );
        return this;
    },

    // Click on About Referral Program link and check external URL
    async clickAboutFriendsProgram() {
        I.seeElement(this.locators.aboutFriendsProgram);

        // Click the link
        I.tapLocationOfElement(this.locators.aboutFriendsProgram);
        await I.waitFor('extraLongWait'); // Wait for 5 seconds for external page
        I.activateApp();
        const externalUrl = await I.getLocalStorage(LAST_EXTERNAL_URL);
        I.say(`External URL after clicking About Friends Program: ${externalUrl}`);

        return externalUrl;
    },

    // Click copy referral URL button and check toast notification
    async clickCopyReferralUrl() {
        I.seeElement(this.locators.copyReferralUrl);
        I.click(this.locators.copyReferralUrl);
        await I.waitFor();
        return this;
    },

    // Check if toast notification is visible
    async isToastNotificationVisible() {
        return (await I.grabNumberOfVisibleElements(this.locators.toastNotification)) > 0;
    },

    // Click Go to Friend Introduction TOP button and check URL
    async clickGoToFriendIntroductionTOP() {
        I.seeElement(this.locators.goToFriendIntroductionTOP);
        I.click(this.locators.goToFriendIntroductionTOP);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking Go to Friend Introduction TOP: ${currentUrl}`);

        return currentUrl;
    },

    // Navigate back
    async navigateBack() {
        I.seeElement(this.locators.backButton);
        I.click(this.locators.backButton);
        await I.waitFor();
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name: string) {
        await I.saveScreenshot(name);
        return this;
    },
};
