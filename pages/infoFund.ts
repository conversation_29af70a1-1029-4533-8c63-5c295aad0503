import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../const/constant";

const { I } = inject();

export = {
    locator: {
        tradeButton: '//*[@data-testid="stockInfo_tradeButton_id"]',
    },
    urls: {
        infoFundDetail: '/mobile/info/fund/detail',
    },
    takeScreenshot: {
        fundDetail: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.infoFundDetail}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToFundDetail(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.infoFundDetail, SESSION_STORAGE_VALUE.infoFundDetail());
        I.amOnPage(this.urls.infoFundDetail);
        await <PERSON><PERSON>waitFor('mediumWait');
        I.waitForText('ファンド詳細', 3, 'body');
    },
};
