// newsList.ts

import { PAGE_URL } from "../const/constant";

const { I, market } = inject();

export = {
  locator: {
    newsTab: '//*[@data-testid="marketNews_tab_id"]',
    newsListContainer: '//*[@id="market-news"]',
    searchInput: '//*[@data-testid="marketNewsList_search_id"]',
    indicatorTab: '//*[@data-testid="marketIndicatorList_tab_id"]',
    rankingTab: '//*[@data-testid="marketRanking_tab_id"]',
    // Button common of sort menu
    cancelSearchButton: '//*[@data-testid="search_cancel_id"]',
    customSortButton: '//*[@data-testid="marketNewsList_customSort_id"]',
    customSortContainer: '//*[@data-testid="rightSlide_filterNewsSheet_id"]',
    customSortConfirmButton: '//*[text()="確定する"]',
    backButton: '//*[@data-testid="common_back_id"]',
    closeCustomSortModalButton: '//*[@data-testid="common_rightSlide_close_id"]',
    newsFilterBtn: '//*[@data-testid="marketNewsList_newsFilter_id"]',
    newsFirstItem: '//*[@data-testid="marketNewsList_newsListInfo_id_0"]',
  },
  async goToIndicator(): Promise<void> {
    await I.amOnPage(PAGE_URL.marketIndicator);
    await I.waitFor('mediumWait');
    I.waitForText('マーケット', 2, 'body');
  },
  async goToNewsTab(): Promise<void> {
    I.clickFixed(this.locator.newsTab);
    await I.waitFor('mediumWait');
    I.waitForElement(this.locator.newsListContainer);
    I.waitForElement(this.locator.newsFirstItem);
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/news', 'URL is not matching');
  },
  async clickMarketTabGroup(): Promise<void> {
    I.waitForElement(this.locator.indicatorTab);
    I.waitForElement(this.locator.rankingTab);
    I.waitForElement(this.locator.newsTab);
   
    // Display Market Indicator List
    await I.clickFixed(this.locator.indicatorTab);
    await I.waitFor('mediumWait');
    await market.displayIndicatorTab();
    // Display Market Ranking
    await I.clickFixed(this.locator.rankingTab);
    await I.waitFor('mediumWait');
    await market.displayRankingTab();
    // Display Market News to continue test
    await I.clickFixed(this.locator.newsTab);
    await I.waitFor('mediumWait');
    await market.displayNewsTab();
  },
  async selectNewsItem(index: number = 0): Promise<string> {
    const newsItemLocator = `//*[@data-testid="marketNewsList_newsListInfo_id_${index}"]`;
    I.waitForElement(newsItemLocator);
    const newsTitle = await I.grabTextFrom(`${newsItemLocator}//p[1]`);
    await I.clickFixed(newsItemLocator);
    await I.waitFor();
    return newsTitle;
  },

  async verifyNewsDetailPage(expectedTitle?: string): Promise<void> {
    const newsDetailContent = '//*[@data-testid="marketNewsDetailPage_detail_id"]';
    I.waitForElement(newsDetailContent);
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, '/mobile/market/news/detail', 'URL is not matching');
    
    // const detailTitle = await I.grabTextFrom('//*[@data-testid="marketNewsDetailPage_detail_id"]//p');
    // I.assertContain(detailTitle, expectedTitle, 'News title should match');
  },

  async returnToNewsList(): Promise<void> {
    await I.clickFixed(this.locator.backButton);
    await I.waitFor();
    I.waitForElement(this.locator.newsListContainer);
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/news', 'URL is not matching');
  },

  async switchToIndicatorTab(): Promise<void> {
    await I.clickFixed(this.locator.indicatorTab);
    await I.waitFor();
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/indicator', 'URL is not matching');
  },

  async switchToRankingTab(): Promise<void> {
    await I.clickFixed(this.locator.rankingTab);
    await I.waitFor();
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/ranking', 'URL is not matching');
  },

  async scrollNewsContent(): Promise<void> {
    I.waitForElement(this.locator.newsListContainer);
    await I.swipeUpFixed(this.locator.newsListContainer);
    await I.waitFor();
  },

  async searchNewsByKeyword(keyword: string): Promise<void> {
    I.waitForElement(this.locator.searchInput);
    
    // Click to focus on input
    await I.clickFixed(this.locator.searchInput);
    await I.waitFor('shortWait');
    
    // Clear current content (if any)
    await I.executeScript((selector) => {
      const inputElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLInputElement;
      if (inputElement) {
        inputElement.value = '';
      }
    }, this.locator.searchInput);
    
    // Enter each character one by one to simulate user typing
    for (const char of keyword) {
      I.pressKey(char);
      await I.waitFor('shortWait');
    }
    
    // Press Enter to submit
    I.pressKey('Enter');
    await I.waitFor();
  },

  async cancelSearch(): Promise<void> {
    I.waitForElement(this.locator.cancelSearchButton);
    await I.clickFixed(this.locator.cancelSearchButton);
    await I.waitFor();
  },

  async openCustomSort(): Promise<void> {
    I.waitForElement(this.locator.newsFilterBtn);
    await I.clickFixed(this.locator.newsFilterBtn);
    await I.waitFor();
    
    // Check modal is opened  
    I.waitForElement('//p[contains(text(), "表示したいニュースを以下より選択してください。")]', 3);
  },

  async closeCustomSortModal(): Promise<void> {
    const closeCustomSortModalButton = '//*[@data-testid="common_rightSlide_close_id"]';
    I.waitForElement(closeCustomSortModalButton);
    await I.clickFixed(closeCustomSortModalButton);
    await I.waitFor();
  },

  async selectNewsCategory(category: string): Promise<void> {
    const categoryLocator = `//div[p[contains(text(),"${category}")] and button]`;
    
    I.waitForElement(categoryLocator);
    await I.clickFixed(categoryLocator);
    await I.waitFor('shortWait');
  },
  async confirmCustomSort(): Promise<void> {
    I.waitForElement(this.locator.customSortConfirmButton);
    await I.clickFixed(this.locator.customSortConfirmButton);
    await I.waitFor();
    
    // Check modal is closed
    try {
      I.dontSeeElement('//p[contains(text(), "表示したいニュースを以下より選択してください。")]');
    } catch (e) {
      // If modal is still visible, try clicking again
      await I.clickFixed(this.locator.customSortConfirmButton);
      await I.waitFor();
    }
  },

  async scrollToTop(): Promise<void> {
    // const scrollToTopButton = '//*[@data-testid="marketNewsList_scrollTop_id"]';
    const scrollToTopButton = '//*[@id="scrollButton"]';
    await I.waitFor();
    I.waitForElement(scrollToTopButton);
    await I.clickFixed(scrollToTopButton);
    await I.waitFor();
  },
};