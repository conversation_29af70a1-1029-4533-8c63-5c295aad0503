/**
 * 銘柄/商品検索結果一覧画面
 * Symbol/Product Search Result List Screen
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Search/SearchResult.html
 */

const { I } = inject();

export = {
    locators: {
        keywordInput: '$searchResult_keywordInput_id', // 1.キーワード入力
        input: '//*[@data-testid="searchResult_keywordInput_id"]//input',
        symbolList: '$searchResult_symbolList_id', // 2.検索結果銘柄リスト
        domesticStockItem: (index: number) => `$searchResult_domesticStockItem_id_${index}`, // 3.検索結果銘柄アイテム(国内株)
        usStockItem: (index: number) => `$searchResult_usStockItem_id_${index}`, // 4.検索結果銘柄アイテム(投信)
        investmentTrustItem: (index: number) => `$searchResult_investmentTrustItem_id_${index}`, // 5.検索結果銘柄アイテム(米国株)
        confirmUsStock: 'search_confirmUsStock_id', // 7.検索結果銘柄アイテム(米国株)-未契約
        readMore: 'searchResult_readMore_id', // 8.続きを読み込む
    },

    async clickSymbolItem(index: number) {
        const item = locate(this.locators.domesticStockItem(index));
        I.waitForVisible(item, 5);
        I.click(item);
        I.waitInUrl('/mobile/trade/stock/buy', 5);
    },
};
