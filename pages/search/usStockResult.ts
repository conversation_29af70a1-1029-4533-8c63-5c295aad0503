/**
 * 米国株式検索結果
 * USStockSearchResult
 */

import common from "./common";

const { I } = inject();

export = {
    locators: {
        keywordDisplay: '$searchUsStockResult_keywordDisplay_id', // 1.キーワード入力
        input: '//*[@data-testid="searchUsStockResult_keywordDisplay_id"]//input',
        searchResultSymbolList: '$searchUsStockResult_searchResultSymbolList_id', // 2.検索結果銘柄リスト
        symbolItem: (index) => `$searchUsStockResult_searchResultSymbolItem_id_${index}`, // 3.検索結果銘柄アイテム(米国株)
        uncontractedSymbolItem: (index) => `$searchUsStockResult_searchResultSymbolItemUncontracted_id_${index}`, // 4.検索結果銘柄アイテム(米国株)-未契約
        readMoreButton: '$searchUsStockResult_readMoreButton_id', // 5.続きを読み込む
    },

    async clickInput() {
        const input = locate(this.locators.input);
        I.waitForVisible(input, 5);
        I.click(input);
        I.waitInUrl('/mobile/search-usstock', 5);
    },

    async clickSymbolItem(index: number) {
        const item = this.locators.symbolItem(index);
        I.waitForVisible(item, 5);
        I.click(item);
        I.waitInUrl('/mobile/info/usstock/summary', 5);
    },

    async clickUncontractedSymbolItem(index: number) {
        const item = this.locators.uncontractedSymbolItem(index);
        I.waitForVisible(item, 5);
        // Navigate to external URL: /members/personal/dkstatus/dk01101.asp#tradeservice
        await common.clickCardItem(item, '/members/personal/dkstatus/dk01101.asp#tradeservice', 'external');
    },
};
