import { LAST_EXTERNAL_URL, LAST_KC_MEMBER_SITE_URL } from "../../const/constant";

const { I } = inject();

export = {
    async search(inputLocator: CodeceptJS.LocatorOrString, keyword: string) {
        const input = locate(inputLocator);
        I.waitForVisible(input, 5);

        await I.tap(input);
        await I.waitFor(); // wait keyboard appear
        I.fillField(input, keyword);
    },

    async searchResult(inputLocator: CodeceptJS.LocatorOrString, keyword: string, expectedUrl?: string) {
        await this.search(inputLocator, keyword);
        <PERSON>.fillField(inputLocator, '\n'); // press enter, works on both android and ios
        await I.waitFor('mediumWait');
        if (expectedUrl) {
            I.waitInUrl(expectedUrl, 5);
        }
    },

    async checkAndLoginAgain() {
        await I.waitFor('mediumWait'); // wait for login page appear
        // Relaunch app to ensure the new session is applied
        // Fix issue on real Android devices (WebDriverError: no such window: target window already closed)
        await I.terminateAndActivateAppFixed();
        await I.waitFor('mediumWait');
        // Check if is auto login
        const userNameSelector = await I.grabIsAndroid() ? '//android.widget.EditText' : '//XCUIElementTypeTextField[@type="XCUIElementTypeTextField"]';
        const isLoginFieldVisible = await I.grabNumberOfVisibleElements(userNameSelector);
        if (isLoginFieldVisible > 0) {
            const userNameValue = await I.getValueOfNativeContextElement(userNameSelector);
            if (!userNameValue) {
                await I.login(process.env.LOGIN_USERNAME || '09109911', process.env.LOGIN_PASSWORD || '111111', true);
            }
        }
    },

    async clickCardItem(
        locator: CodeceptJS.LocatorOrString,
        expectedUrl?: string,
        urlType: 'internal' | 'external' | 'kcMemberSite' = 'internal',
        isEncodeURL?: boolean,
    ) {
        const item = locate(locator);
        I.waitForVisible(item, 5);

        if (expectedUrl) {
            if (urlType !== 'internal') {
                const isExternal = urlType === 'external';
                const storageKey = isExternal ? LAST_EXTERNAL_URL : LAST_KC_MEMBER_SITE_URL;

                await I.scrollToElement(item.toXPath());
                await I.clickFixed(item);
                await I.waitFor();

                if (isExternal) {
                    await I.closeBrowser();
                    await I.activateApp();
                } else {
                    await I.amOnPage('/');
                }
                await I.waitFor('mediumWait'); // wait for page load
                await I.getLocalStorage(storageKey).then((url: string) => {
                    const actualUrl = isEncodeURL ? decodeURIComponent(url) : url;
                    I.assertContain(actualUrl, expectedUrl);
                });
                if (!isExternal) {
                    await I.waitFor('mediumWait'); // wait for any potential popups or dialogs
                    await I.switchToNative().then(async () => {
                        const visibleExpiredSessionDialog = await I.grabNumberOfVisibleElements('~ログイン');
                        if (visibleExpiredSessionDialog > 0) {
                            I.click('~ログイン');
                            // Login again if session is expired
                            await this.checkAndLoginAgain();
                        }
                    });
                }
            } else {
                await I.clickFixed(item);
                await I.waitFor();
                I.waitInUrl(expectedUrl, 5);
                await I.waitFor('mediumWait');
            }
        }
    },
    async scrollToLoadMore(locator: CodeceptJS.LocatorOrString) {
        const btn = locate(locator);

        I.executeScript(({ value }) => {
            const element = document.evaluate(value, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null)
                .singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth' });
        }, btn);
        await I.waitFor('mediumWait'); // wait for scrolling
    },

    async performLoadMore(locator: CodeceptJS.LocatorOrString) {
        const btn = locate(locator);
        I.waitForVisible(btn, 5);
        I.click(btn);
        await I.waitFor('mediumWait'); // wait for displaying more items
    },
};
