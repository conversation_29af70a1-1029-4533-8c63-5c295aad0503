/**
 * 銘柄/商品検索画面
 * Symbol/SearchSuggestion
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Search/SearchSuggestion.html
 */

const { I } = inject();

export = {
    locators: {
        searchInput: '$searchTop_keywordInput_id', // 1.キーワード入力
        cancel: '$search_cancel_id', // 2.キャンセル
        searchHistory: '$search_searchHistory_id', // 3.検索履歴
        historyText: '$search_historyText_id', // 4.履歴文言
        domesticStockItem: (index: number) => `$searchSuggestion_domesticStockItem_id_${index}`, // 7.検索候補(国内株式)アイテム
        usStockItem: (index: number) => `$searchSuggestion_usStockItem_id_${index}`, // 12.検索候補(米国株式)アイテム
        domesticStockThemeItem: (index: number) => `$searchSuggestion_domesticStockThemeItem_id_${index}`, // 16.検索候補(国内株式テーマ)アイテム
        usStockThemeItem: (index: number) => `$searchSuggestion_usStockThemeItem_id_${index}`, // 18.検索候補(米国株式テーマ)アイテム
        filterCondition: '$searchSuggestion_filterCondition_id', // 25.フィルタ条件
        investmentTrustItem: (index: number) => `$searchSuggestion_investmentTrustItem_id_${index}`, // 28.検索候補(投資信託)アイテム
    },

    async cancel() {
        const btn = locate(this.locators.cancel);
        await I.waitForVisible(btn, 5);
        await I.click(btn);
        await I.waitForInvisible(btn);
    },
};
