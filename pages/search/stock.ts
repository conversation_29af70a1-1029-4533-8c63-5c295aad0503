/**
 * 銘柄/商品検索-国内現物株検索
 * Symbol_ProductSearch-Domestic Cash Stock Search
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/Search/StockSearch.html
 */

import common from './common';

export = {
    locators: {
        keywordInput: '$stockSearch_keywordInput_id', // 1.キーワード入力
        input: '//*[@data-testid="stockSearch_keywordInput_id"]//input',
        theme: '$stockSearch_theme_id', // 2.テーマ
        ranking: '$stockSearch_ranking_id', // 3.ランキング
        specialBenefit: '$stockSearch_specialBenefit_id', // 4.株主優待
        positionInquiry: '$stockSearch_positionInquiry_id', // 5.残高照会
        orderStatus: '$stockSearch_orderStatus_id', // 6.注文照会
        tradingHistory: '$stockSearch_tradingHistory_id', // 7.取引履歴
        availableAmount: '$stockSearch_availableAmount_id', // 8.買付出金可能額
        petitReserve: '$stockSearch_petitReserve_id', // 9.プチ株積立
        kabuBoardFlash: '$stockSearch_kabuBoardFlash_id', // 10.カブボードフラッシュ
        commission: '$stockSearch_commission_id', // 11.手数料
        tradingRules: '$stockSearch_tradingRules_id', // 12.取引ルール
        searchHistoryText: (index: number) => `$search_historyText_id_${index}`, // 15.検索候補
        searchResultItem: (index: number) => `$stockSearch_searchResultSymbolDomesticStockItem_id_${index}`, // 19.検索結果銘柄アイテム
        searchSuggestionItem: (index: number) => `$stockSearch_searchSuggestionDomesticStock_id_${index}`,
        loadMore: '$stockSearch_loadMore_id', // 20.検索結果銘柄アイテム
    },

    async theme() {
        await common.clickCardItem(this.locators.theme, '/mobile/search/theme');
    },

    async ranking() {
        await common.clickCardItem(this.locators.ranking, '/mobile/market/ranking');
    },

    async specialBenefit() {
        await common.clickCardItem(this.locators.specialBenefit, '/mobile/benefit');
    },

    async positionInquiry() {
        await common.clickCardItem(this.locators.positionInquiry, '/mobile/position-inquiry/stock');
    },

    async orderStatus() {
        await common.clickCardItem(this.locators.orderStatus, '/mobile/order-inquiry/stock');
    },

    async tradingHistory() {
        await common.clickCardItem(this.locators.tradingHistory, '/ap/iPhone/Stocks/Stock/History/List', 'kcMemberSite');
    },

    async availableAmount() {
        await common.clickCardItem(this.locators.availableAmount, '/ap/iphone/Assets/Kanougaku/Stock', 'kcMemberSite');
    },

    async petitReserve() {
        await common.clickCardItem(this.locators.petitReserve, '/mobile/reserve/petit/search');
    },

    async kabuBoardFlash() {
        await common.clickCardItem(
            this.locators.kabuBoardFlash,
            '/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp',
            'kcMemberSite',
        );
    },

    async commission() {
        await common.clickCardItem(this.locators.commission, 'https://kabu.com/cost/default.html#page_01', 'external');
    },

    async tradingRules() {
        await common.clickCardItem(this.locators.tradingRules, 'https://kabu.com/item/stock/rule.html', 'external');
    },
};
