/**
 * 米国株式検索TOP
 * USStockSearchTOP
 */

import common from './common';

export = {
    locators: {
        keywordInput: '$searchUsStock_keywordInput_id', // 1.キーワード入力
        input: '//*[@data-testid="searchUsStock_keywordInput_id"]//input',
        theme: '$searchUsStock_theme_id', // 2.テーマ
        ranking: '$searchUsStock_ranking_id', // 3.ランキング
        usStockSearch: '$searchUsStock_usStockSearch_id', // 4.米国株検索
        positionInquiry: '$searchUsStock_positionInquiry_id', // 5.残高照会
        orderStatus: '$searchUsStock_orderStatus_id', // 6.注文照会
        tradingHistory: '$searchUsStock_tradingHistory_id', // 7.取引履歴
        availableAmount: '$searchUsStock_availableAmount_id', // 8.買付出金可能額
        depositTransfer: '$searchUsStock_depositTransfer_id', // 9.お預り金振替
        foreignCurrencyDepositTransfer: '$searchUsStock_foreignCurrencyDepositTransfer_id', // 10.外貨お預り金振替
        usStocksPaymentMethodSetting: '$searchUsStock_usStocksPaymentMethodSetting_id', // 11.米国株式決済方法設定
        kabuBoardFlash: '$searchUsStock_kabuBoardFlash_id', // 12.カブボードフラッシュ
        commission: '$searchUsStock_commission_id', // 13.手数料
        tradingRules: '$searchUsStock_tradingRules_id', // 14.取引ルール
        searchSuggestionItem: '$searchUsStock_SearchSuggestionItem_id', // 17.検索候補(米国株式)アイテム
    },

    async theme() {
        await common.clickCardItem(this.locators.theme, '/ap/iPhone/InvInfo/USMarket/Theme/List', 'kcMemberSite');
    },

    async ranking() {
        await common.clickCardItem(this.locators.ranking, '/ap/iPhone/InvInfo/Ranking/Market/USStock', 'kcMemberSite');
    },

    async usStockSearch() {
        await common.clickCardItem(this.locators.usStockSearch, '/ap/iPhone/InvInfo/USMarket/Search/ByKeyword', 'kcMemberSite');
    },

    async positionInquiry() {
        await common.clickCardItem(
            this.locators.positionInquiry,
            '/ap/iPhone/ForeignStocks/USStock/Position/List',
            'kcMemberSite',
        );
    },

    async orderStatus() {
        await common.clickCardItem(
            this.locators.orderStatus,
            '/ap/iPhone/ForeignStocks/USStock/OrderStatus/List',
            'kcMemberSite',
        );
    },

    async tradingHistory() {
        await common.clickCardItem(this.locators.tradingHistory, '/ap/iPhone/ForeignStocks/USStock/History/List', 'kcMemberSite');
    },

    async availableAmount() {
        await common.clickCardItem(this.locators.availableAmount, '/ap/iPhone/Assets/Kanougaku/Foreign', 'kcMemberSite');
    },

    async depositTransfer() {
        await common.clickCardItem(this.locators.depositTransfer, '/ap/iphone/CashFlow/Transfer/Cash/Input', 'kcMemberSite');
    },

    async foreignCurrencyDepositTransfer() {
        await common.clickCardItem(
            this.locators.foreignCurrencyDepositTransfer,
            '/ap/iPhone/CashFlow/Transfer/ForeignCash/Input',
            'kcMemberSite',
        );
    },

    async usStockPaymentMethodSetting() {
        await common.clickCardItem(
            this.locators.usStocksPaymentMethodSetting,
            '/ap/iPhone/ForeignStocks/USStock/SettlementcurType/Confirm',
            'kcMemberSite',
        );
    },

    async kabuBoardFlash() {
        await common.clickCardItem(
            this.locators.kabuBoardFlash,
            '/iPhone/tradetool/KBF/KabucomKabuBoardFlash.asp',
            'kcMemberSite',
        );
    },

    async commission() {
        await common.clickCardItem(
            this.locators.commission,
            'https://kabu.com/item/foreign_stock/us_stock/stock/cost.html',
            'external',
        );
    },

    async tradingRules() {
        await common.clickCardItem(
            this.locators.tradingRules,
            'https://kabu.com/item/foreign_stock/us_stock/stock/rule.html',
            'external',
        );
    },
};
