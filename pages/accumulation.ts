import { PAGE_URL } from "../const/constant";

const { I } = inject();

export = {
    // Locators
    locator: {

    },
    //1250
    async goToCustomerReservePlanPage() {
        await I.amOnPage(PAGE_URL.reservePlan);
        await <PERSON>.waitFor('mediumWait');
        I.see('積立プラン', '//*[@data-testid="common_header_title_id"]');
    },
    //1298
    async goToCustomerReserveCalendarPage() {
        await I.amOnPage(PAGE_URL.reserveCalendar);
        await I.waitFor('mediumWait');
        I.see('積立カレンダー', '//*[@data-testid="common_header_title_id"]');
    },
    //1330
    async goToCustomerReserveHistoryPage() {
        await I.amOnPage(PAGE_URL.reserveHistory);
        await <PERSON>.waitFor('mediumWait');
        I.see('積立履歴', '//*[@data-testid="common_header_title_id"]');
    },
    //1346
    async goToCustomerReservePetitSearchPage() {
        await I.amOnPage(PAGE_URL.reservePetitSearch);
        await I.waitFor('mediumWait');
        I.see('検索', '//*[@data-testid="common_header_title_id"]');
    },
    // 1346 search 
    async searchReservePetit(keyword: string) {
        const searchInput = '//*[@data-testid="petitSearch_cancel_id"]//input';
        I.fillField(searchInput, keyword);
        I.clickFixed(searchInput);
        await I.waitFor('shortWait');
        const searchButton = '//*[@data-testid="petitSearch_searchButton_id"]';
        I.waitForElement(searchButton);
        I.clickFixed(searchButton);
        await I.waitFor('mediumWait');
    },
    //1360
    async goToReserveOrderInputPage() {
        await this.goToCustomerReservePetitSearchPage();
        await this.searchReservePetit('1360');
        const reservePetitDetail = '//*[@data-testid="petitSearch_reservePetitDetail_id"]';
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, reservePetitDetail);
        const firstDetailItem = `${reservePetitDetail}/div[1]`;
        I.clickFixed(firstDetailItem);
        await I.waitFor('mediumWait');
        const petitReverseSearch_reserve_id = '//*[@data-testid="petitReverseSearch_reserve_id"]';
        I.clickFixed(petitReverseSearch_reserve_id);
        await I.waitFor('mediumWait');
        I.see('申込', '//*[@data-testid="common_header_title_id"]');

    },
    //1384
    async goToReserveOrderConfirmPage(isApply = false) {
        await this.goToReserveOrderInputPage();
        //Step 1: Increase amount
        const plusButton = '//p[contains(text(), "毎月の指定金額")]/parent::div//button[@data-testid="groupInputNumber_plus_id"]';
        await I.waitForElement(plusButton, 3);
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, plusButton);
        await I.waitFor('shortWait');
        I.clickFixed(plusButton);
        //Step 2: Check Insider confirmation panel Toggle selection
        const button = '//div[@data-testid="reserveOrderInput_notInsiderTradeConfirm_id"]//button';
        await I.waitForElement(button, 3);
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, button);
        await I.waitFor('mediumWait');
        I.clickFixed(button);

        //Step 3: Click submit button       
        const confirmButton = '//*[@data-testid="reserveOrderInput_confirmOrderButton_id"]';
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, confirmButton);
        await I.waitFor('shortWait');
        I.clickFixed(confirmButton);
        await I.waitFor('mediumWait');
        //Step 4: Click submit button
        if (isApply) {
            const applyButton = '//button[text()="このまま申込む"]';
            await I.waitForElement(applyButton, 5);

            await I.clickFixed(applyButton);
        }


    },

    //1399

    async goToReservePetitChangeOrderInputPage() {
        await this.goToCustomerReservePlanPage();
        const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "eservePlan_reserve")][1]';
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, petitStockItem);
        await I.waitFor('mediumWait');
        I.clickFixed(petitStockItem);
        await I.waitFor('mediumWait');
        const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
        I.clickFixed(reservePlanChangeId);
        await I.waitFor('mediumWait');
        I.see('変更', '//*[@data-testid="common_header_title_id"]');
    },
    //1419
    async goToReservePetitChangeOrderConfirmPage() {
        await this.goToReservePetitChangeOrderInputPage();
        await I.waitFor('mediumWait');
        I.see('変更', '//*[@data-testid="common_header_title_id"]');
        const taxFreeInvestmentAgree = '//*[@data-testid="changeReserveOrderInput_taxFreeInvestmentAgree_id"]//button';
        const notInsiderTradeConfirm = '//*[@data-testid="changeReserveOrderInput_notInsiderTradeConfirm_id"]//button';

        const isVisibleTaxFreeInvestmentAgree = await I.grabNumberOfVisibleElements(taxFreeInvestmentAgree) > 0;
        const isSelectedTaxFreeInvestmentAgree = isVisibleTaxFreeInvestmentAgree && await I.grabCssPropertyFrom('[data-testid="changeReserveOrderInput_taxFreeInvestmentAgree_id"] button svg', 'color') === 'rgba(255, 86, 0, 1)';
        const isVisibleNotInsiderTradeConfirm = await I.grabNumberOfVisibleElements(notInsiderTradeConfirm) > 0;
        const isSelectedNotInsiderTradeConfirm = isVisibleNotInsiderTradeConfirm && await I.grabCssPropertyFrom('[data-testid="changeReserveOrderInput_notInsiderTradeConfirm_id"] button svg', 'color') === 'rgba(255, 86, 0, 1)';

        if (isVisibleTaxFreeInvestmentAgree && !isSelectedTaxFreeInvestmentAgree) {
            await I.scrollToElement(taxFreeInvestmentAgree);
            await I.clickFixed(taxFreeInvestmentAgree);
        }
        await I.waitFor('shortWait');
        if (isVisibleNotInsiderTradeConfirm && !isSelectedNotInsiderTradeConfirm) {
            await I.scrollToElement(notInsiderTradeConfirm);
            await I.clickFixed(notInsiderTradeConfirm);
        }
        await I.waitFor('shortWait');

        const buttonConfirm = '//*[@data-testid="changeReserveOrderInput_confirm_id"]';
        await I.waitForElement(buttonConfirm, 3);
        await I.scrollAndClick(buttonConfirm);
        await I.waitFor('mediumWait');
    },
    //1428

    async goToReservePetitChangeOrderCompletePage() {
        await this.goToReservePetitChangeOrderConfirmPage();
        await I.waitFor()
        const passwordInputCheck = '//*[@data-testid="changeReserveOrderConfirm_passwordInputCheck_id"]//button';
        await I.waitForElement(passwordInputCheck, 1);
        I.clickFixed(passwordInputCheck);
        await I.waitFor('shortWait');
        const confirmButton = '//*[@data-testid="changeReserveOrderConfirm_confirm_id"]';
        await I.waitForElement(confirmButton, 1);
        I.clickFixed(confirmButton);
        await I.waitFor('mediumWait');
        I.seeElement('//p[contains(text(), "積立変更が完了しました。")]');
    },
    //1432
    async goToReservePetitCancelReserveOrderConfirmPage() {
        await this.goToCustomerReservePlanPage();
        const petitStockItem = '//span[contains(@class, "chakra-badge") and contains(text(), "プチ株")]/ancestor::div[contains(@data-testid, "eservePlan_")][1]';
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, petitStockItem);
        await I.waitFor('mediumWait');
        I.clickFixed(petitStockItem);
        await I.waitFor('mediumWait');
        const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
        I.clickFixed(reservePlanCancelId);
        await I.waitFor('mediumWait');
        I.see('中止', '//*[@data-testid="common_header_title_id"]');

    },
    //1441
    async goToReservePetitCancelReserveOrderCompletePage() {
        await this.goToReservePetitCancelReserveOrderConfirmPage();
        await I.waitFor()
        const passwordInputCheck = '//*[@data-testid="cancelReserveOrder_passwordInputCheck_id"]//button';
        await I.waitForElement(passwordInputCheck, 1);
        I.clickFixed(passwordInputCheck);
        await I.waitFor('shortWait');
        const confirmButton = '//*[@data-testid="cancelReserveOrder_confirm_id"]';
        await I.waitForElement(confirmButton, 3);
        await I.scrollAndClick(confirmButton);
        I.seeElement('//p[contains(text(), "積立中止が完了しました。")]');
    },
    //1445
    async goToCustomerReserveFundPage() {
        await I.amOnPage(PAGE_URL.fundSearch);
        await I.waitFor('mediumWait');
        const searchInput = '//*[@data-testid="infoFundSearch_search_id"]//input';
        I.fillField(searchInput, "test");
        I.clickFixed(searchInput);
        await I.waitFor('mediumWait');
        const searchButton = '//*[@data-testid="infoFundSearch_search_id"]/div[1]';
        I.waitForElement(searchButton);
        I.clickFixed(searchButton);
        await I.waitFor('mediumWait');
        const fundListData = '//*[@data-testid="infoFundSearch_searchResult_id"]';
        const firstDetailItem = `${fundListData}/div[contains(@data-testid, "infoFundSearch_card_id")][1]`;
        I.clickFixed(firstDetailItem);
        await I.waitFor('mediumWait');
        const reserveButton = '//*[@data-testid="infoFundDetail_reserve_id"]';
        I.clickFixed(reserveButton);
        await I.waitFor('mediumWait');

    },
    async goToReserveFundReserveOrderInputPage() {
        await this.goToCustomerReservePlanPage();
        const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reservePlan_")][1]';
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, fundItem);
        await I.waitFor('mediumWait');
        I.clickFixed(fundItem);
        await I.waitFor('mediumWait');
        const reservePlanChangeId = '//*[@data-testid="reservePlan_change_id"]';
        I.clickFixed(reservePlanChangeId);
        await I.waitFor('mediumWait');
        I.see('変更', '//*[@data-testid="common_header_title_id"]');
    },
    async goToReserveFundCancelReserveOrderConfirmPage() {
        await this.goToCustomerReservePlanPage();
        const fundItem = '//span[contains(@class, "chakra-badge") and contains(text(), "投信")]/ancestor::div[contains(@data-testid, "reservePlan_")][1]';
        I.executeScript(function (selector) {
            const element: HTMLElement = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            if (element) element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, fundItem);
        await I.waitFor('mediumWait');
        I.clickFixed(fundItem);
        await I.waitFor('mediumWait');
        const reservePlanCancelId = '//*[@data-testid="reservePlan_cancel_id"]';
        I.clickFixed(reservePlanCancelId);
        await I.waitFor('mediumWait');
        I.see('中止', '//*[@data-testid="common_header_title_id"]');
    },
    async goToReservePointUsageSetting() {
        await I.amOnPage(PAGE_URL.reservePointSetting);
        await I.waitFor('mediumWait');
        return this;
    },
}
