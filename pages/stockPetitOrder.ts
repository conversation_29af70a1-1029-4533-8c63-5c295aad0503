import { SCREENSHOT_PREFIX } from '../const/constant';

const { I, stockPetitOrder, stockDetailBasicPage } = inject();

export = {
    locator: {
        tradeButton: '//*[@data-testid="stockInfo_tradeButton_id"]',
        cautionInfoPetitBuy: '//*[@data-testid="tradePetitBuy_tradeCautionInfo_id"]',
        petitBuyOrderConfirmButton: '//*[@data-testid="tradePetitBuy_orderConfirm_id"]',
        petitBuyConfirmButton: '//*[@data-testid="tradePetitBuyConfirm_orderConfirm_id"]',
        checkButton: '//button[@aria-label="Check"]',
        petitBuyInput: '//*[@data-testid="groupInputNumber_input_id"]',
        passwordField: '//*[@data-testid="tradePetitBuyConfirm_password_id"]',
        passwordOmissionCheck: '//*[@data-testid="tradePetitBuyConfirm_passwordOmissionCheck_id"]',
        cautionInfoPetitSell: '//*[@data-testid="tradePetitSell_tradeCautionInfo_id"]',
        petitSellTradingMethod: '//*[@data-testid="tradingMethod_petitSell_id"]',
        petitSellOrderConfirmButton: '//*[@data-testid="tradePetitSell_orderConfirm_id"]',
        petitSellConfirmButton: '//*[@data-testid="tradePetitSellConfirm_orderConfirm_id"]',
        sellPasswordField: '//*[@data-testid="tradePetitSellConfirm_password_id"]',
        sellPasswordOmissionCheck: '//*[@data-testid="tradePetitSellConfirm_passwordOmissionCheck_id"]',
        orderIdLinkCompleted: '//*[@data-testid="complete_orderIdLink_id"]',
        orderInquiryDetail: '#order-inquiry-detail',
        stockDetailCancelOrder: '//*[@data-testid="stockDetail_cancelOrder_id"]',
    },
    urls: {
        petitBuyOrderInput: '/mobile/trade/petit/buy?symbol=8306',
    },
    inputValues: {
        quantity: '1',
        password: '111111',
    },
    takeScreenshot: {
        petitBuyOrderInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingBuyOrderInput}_${suffix}.png`);
        },
        petitBuyOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingBuyOrderConfirm}_${suffix}.png`);
        },
        petitBuyOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingBuyOrderCompleted}_${suffix}.png`);
        },
        petitSellOrderInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingSellOrderInput}_${suffix}.png`);
        },
        petitSellOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingSellOrderConfirm}_${suffix}.png`);
        },
        petitSellOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingSellOrderCompleted}_${suffix}.png`);
        },
        petitOrderCancel: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockPetitTradingCancel}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToPetitBuyOrderInput(): Promise<void> {
        I.amOnPage(this.urls.petitBuyOrderInput);
        await I.waitFor('mediumWait');
        I.waitForText('プチ株買', 3, 'body');
        I.waitForElement(this.locator.cautionInfoPetitBuy);
    },
    async goToPetitBuyOrderConfirm(): Promise<void> {
        await stockPetitOrder.goToPetitBuyOrderInput();
        I.fillField(this.locator.petitBuyInput, this.inputValues.quantity);
        I.blur(this.locator.petitBuyInput);
        if ((await I.grabNumberOfVisibleElements(this.locator.checkButton)) > 0) {
            await I.clickFixed(this.locator.checkButton);
        }
        await I.clickFixed(this.locator.petitBuyOrderConfirmButton);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.petitBuyConfirmButton);
    },
    async goToPetitBuyOrderCompleted(): Promise<void> {
        await stockPetitOrder.goToPetitBuyOrderConfirm();
        I.fillField(this.locator.passwordField, this.inputValues.password);
        I.blur(this.locator.passwordField);
        await I.clickFixed(this.locator.passwordOmissionCheck);
        await I.clickFixed(this.locator.petitBuyConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToPetitSellOrderInput(symbol?: string): Promise<void> {
        await stockDetailBasicPage.goToPage(symbol);
        await I.clickFixed(this.locator.tradeButton);
        await I.waitFor();
        await I.clickFixed(this.locator.petitSellTradingMethod);
        await I.waitFor('mediumWait');
        I.waitForText('プチ株売', 3, 'body');
        I.waitForElement(this.locator.cautionInfoPetitSell);
    },
    async goToPetitSellOrderConfirm(symbol?: string): Promise<void> {
        await stockPetitOrder.goToPetitSellOrderInput(symbol);
        I.fillField(this.locator.petitBuyInput, this.inputValues.quantity);
        I.blur(this.locator.petitBuyInput);
        if ((await I.grabNumberOfVisibleElements(this.locator.checkButton)) > 0) {
            await I.clickFixed(this.locator.checkButton);
        }
        await I.clickFixed(this.locator.petitSellOrderConfirmButton);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.petitSellConfirmButton);
    },
    async goToPetitSellOrderCompleted(symbol?: string): Promise<void> {
        await stockPetitOrder.goToPetitSellOrderConfirm(symbol);
        await I.swipeDirection('up');
        if ((await I.grabNumberOfVisibleElements(this.locator.sellPasswordField)) > 0) {
            I.fillField(this.locator.sellPasswordField, this.inputValues.password);
            I.blur(this.locator.sellPasswordField);
        }
        if ((await I.grabNumberOfVisibleElements(this.locator.sellPasswordOmissionCheck)) > 0) {
            await I.clickFixed(this.locator.sellPasswordOmissionCheck);
        }
        await I.clickFixed(this.locator.petitSellConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToPetitOrderCancel(symbol?: string): Promise<void> {
        await stockPetitOrder.goToPetitSellOrderCompleted(symbol);
        await I.clickFixed(this.locator.orderIdLinkCompleted);
        await I.waitFor();
        I.waitForElement(this.locator.orderInquiryDetail, 2);
        await I.clickFixed(this.locator.stockDetailCancelOrder);
        await I.waitFor('mediumWait');
    },
};
