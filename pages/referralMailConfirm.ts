const { I } = inject();

export = {
    // Define locators
    locators: {
        headerTitle: '//*[@data-testid="common_header_title_id"]',
        backButton: '//*[@data-testid="common_back_id"]',
        editContent: '//*[@data-testid="referralMailConfirm_editContent_id"]',
        cancelSend: '//*[@data-testid="referralMailConfirm_cancelSend_id"]',
        send: '//*[@data-testid="referralMailConfirm_send_id"]',
    },

    // URLs
    urls: {
        mailInputPage: 'referral/mail-input',
        referralPage: 'mobile/referral',
        mailCompletePage: 'referral/mail-complete',
    },

    // Click on Edit Content button and check URL
    async clickEditContent() {
        I.seeElement(this.locators.editContent);
        I.click(this.locators.editContent);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking Edit Content: ${currentUrl}`);

        return currentUrl;
    },

    // Click on Cancel Sending button and check URL
    async clickCancelSend() {
        I.seeElement(this.locators.cancelSend);
        I.click(this.locators.cancelSend);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking Cancel Sending: ${currentUrl}`);

        return currentUrl;
    },

    // Click on Send button and check URL
    async clickSend() {
        I.seeElement(this.locators.send);
        I.click(this.locators.send);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking Send: ${currentUrl}`);

        return currentUrl;
    },

    // Navigate back
    async navigateBack() {
        I.seeElement(this.locators.backButton);
        I.click(this.locators.backButton);
        await I.waitFor();
        return this;
    },

    // Take a screenshot with custom name
    takeScreenshot(name) {
        I.saveScreenshot(name);
        return this;
    },
};
