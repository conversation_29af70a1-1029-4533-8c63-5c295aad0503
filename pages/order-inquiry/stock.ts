/*
 * 注文照会-国内株式-単元株-一覧
 * OrderStatus-DomesticStock-UnitStock-List
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/OrderStatus/StockList.html
 */

const { I } = inject();

export const stockList = {
    locators: {
        productTab: '$orderInquiry_productTab_id', // 2.商品タブ-外国株式
        filter: '$orderInquiry_filter_id', // 3.絞り込み
        item: (index: number) => `$orderInquiry_orderDetail_${index}_id`, // 4.明細一覧
        caution: '$orderInquiry_caution_id', // 5.注文明細
        filterDisplaySymbol: '$filter_displaySymbol_id', // 17.表示銘柄
        filterDisplayOrder: '$filter_displayOrder_id', // 18.表示順
        filterClear: '$filter_clear_id', // 19.クリア
        filterConfirm: '$filter_confirm_id', // 20.確定する
        close: '$common_rightSlide_close_id', // 21.閉じる
    },

    async clickItem(index: number) {
        const item = locate(this.locators.item(index));
        I.waitForVisible(item, 5);
        await I.clickFixed(item);
        I.waitInUrl('/mobile/order-inquiry/stock/detail', 5);
    },
};

/*
 * 注文約定照会-国内株式-単元株-明細
 * OrderStatus-DomesticStock-UnitStock-Detail
 * https://gitbook.guide.inc/kcmsr-20250430/vn/Customer/OrderStatus/StockDetail.html
 */

export const stockDetail = {
    locators: {
        relayTargetOrderID: '$correction_relayTargetOrderID_id', // 41.リレー先注文番号
        correctOrder: '$stockDetail_correctOrder_id', // 37.注文内容を訂正
        cancelOrder: '$stockDetail_cancelOrder_id', // 38.注文を取消
        orderExecutionInquiry: '$stockDetail_orderExecutionInquiry_id', // 43.注文約定照会
    },

    async orderCorrection() {
        const item = locate(this.locators.correctOrder);
        I.waitForVisible(item, 5);
        await I.scrollToElement(item.toXPath());
        await I.clickFixed(item);
        I.waitInUrl('/mobile/trade/stock/correction', 5);
    },

    async cancelOrder() {
        const item = locate(this.locators.cancelOrder);
        I.waitForVisible(item, 5);
        await I.clickFixed(item);
        I.waitInUrl('/mobile/trade/stock/cancel', 5);
    },

    async orderExecutionInquiry() {},
};
