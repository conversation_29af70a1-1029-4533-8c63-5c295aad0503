import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../const/constant";

const { I, stockMarginReceiptOrder } = inject();

export = {
    locator: {
        marginReceiptCautionInfo: '//*[@data-testid="marginReceiptInput_tradeCautionInfo_id"]',
        marginReceiptPositionItem: '//*[@data-testid="marginDeliveryInput_deliveryThisPosition_id"]',
        marginReceiptQuantity: '//*[@data-testid="groupInputNumber_input_id"]',
        marginReceiptNextStepButton: '//*[@data-testid="marginReceiptInput_next_id"]',
        marginReceiptConfirmPassword: '//*[@data-testid="marginReceiptConfirm_password_id"]',
        marginReceiptConfirmCheckPassword: '//*[@data-testid="marginReceiptConfirm_checkPassword_id"]',
        marginReceiptOrderConfirmButton: '//*[@data-testid="marginReceiptConfirm_orderConfirm_id"]',
        marginDetailListReceipt: '//*[@data-testid="marginDetailList_receipt_id"]',
    },
    urls: {
        marginReceiptOrderInput: '/mobile/trade/margin/receipt',
        marginReceiptOrderConfirm: '/mobile/trade/margin/receipt/confirm',
        marginReceiptOrderCompleted: '/mobile/trade/margin/receipt/complete',
        marginDetailList: '/mobile/position-inquiry/margin/detail-list',
    },
    inputValues: {
        password: '111111',
        quantity: '100',
    },
    takeScreenshot: {
        marginReceiptOrderInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingReceiptOrderInput}_${suffix}.png`);
        },
        marginReceiptOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingReceiptOrderConfirm}_${suffix}.png`);
        },
        marginReceiptOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingReceiptOrderCompleted}_${suffix}.png`);
        },
    },
    async goToMarginReceiptOrderInput(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginReceipt, SESSION_STORAGE_VALUE.tradeMarginReceipt());
        I.amOnPage(this.urls.marginReceiptOrderInput);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.marginReceiptCautionInfo);
    },
    async goToMarginReceiptOrderConfirm(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginReceiptConfirm, SESSION_STORAGE_VALUE.tradeMarginReceiptConfirm());
        I.amOnPage(this.urls.marginReceiptOrderConfirm);
        await I.waitFor('mediumWait');
        I.waitForText('品受方法設定', 2, 'body');
    },
    async goToMarginReceiptOrderCompleted(): Promise<void> {
        await stockMarginReceiptOrder.goToMarginReceiptOrderConfirm();
        I.fillField(this.locator.marginReceiptConfirmPassword, this.inputValues.password);
        await I.clickFixed(this.locator.marginReceiptConfirmCheckPassword);
        await I.clickFixed(this.locator.marginReceiptOrderConfirmButton);
        await I.waitFor('mediumWait');
        I.waitForText('品受', 2, 'body');
    },
};
