import { PAGE_URL } from "../const/constant";

// /pages/orderInquiry.ts
const { I } = inject();

export = {
  // Locators
  locator: {
  
  },
  
  // Navigation
  async goToDomesticStocksUnitSharesPage() {
    await I.amOnPage(PAGE_URL.orderInquiryStock);
    await I.waitFor('mediumWait');
    I.see('現物株式', '//*[@data-testid="common_header_title_id"]');
  },
  async goToOrderExecutionPetitStocksPage() {
    await I.amOnPage(PAGE_URL.orderInquiryPetit);
    await I.waitFor('mediumWait');
    I.see('プチ株', '//*[@data-testid="common_header_title_id"]');
  },
  async goToOrderExecutionCreditStocksPage() {
    await I.amOnPage(PAGE_URL.orderInquiryMargin);
    await I.waitFor('mediumWait');
    I.see('信用取引', '//*[@data-testid="common_header_title_id"]');
  },
  //1141
  async goToOrderExecutionFundsPage() {
    await I.amOnPage(PAGE_URL.orderInquiryFund);
    I.see('投資信託', '//*[@data-testid="common_header_title_id"]');
  },
  //1156
  async goToPositionInquiryDomesticStockUnitStocksPage() {
    await I.amOnPage(PAGE_URL.positionInquiryStock);
    await I.waitFor('mediumWait');
    I.see('現物株式', '//*[@data-testid="common_header_title_id"]');
  },
  //1179
  async goToPositionInquiryDomesticCreditStocksPage() {
    await I.amOnPage(PAGE_URL.positionInquiryMargin);
    await I.waitFor('mediumWait');
    I.see('信用建玉', '//*[@data-testid="common_header_title_id"]');
  },
  //1207
  async goToPositionInquiryFundsPage() {
    await I.amOnPage(PAGE_URL.positionInquiryFund);
    await I.waitFor('mediumWait');
    I.see('投資信託', '//*[@data-testid="common_header_title_id"]');
  },
  async backToOrderInquiry() {
    const backButton = '//*[@data-testid="common_back_id"]';
    I.waitForElement(backButton, 5);
    I.clickFixed(backButton);
    await I.waitFor('mediumWait');
  },

  
}
