import { PAGE_URL } from "../const/constant";

const { I } = inject();

export = {
    async goToPage(): Promise<void> {
        I.amOnPage(PAGE_URL.mypagePerformance);
        await I.waitFor('longWait');
        const assetBalanceTabButton = locate('button').withText('収支');
        I.click(assetBalanceTabButton);
        I.waitForText('収支の見方', 3, 'body');
        I.waitForElement('#mypage-inves');
    },
};
