import { PAGE_URL } from "../const/constant";

const { I } = inject();

export = {
    async goToIndicator(): Promise<void> {
        I.clickFixed('//*[@id="/market/indicator"]');
        await I.waitFor();
        I.waitForText('マーケット', 2, 'body');
    },
    async clickChart(): Promise<void> {
        await I.amOnPage(PAGE_URL.marketIndicator);
        await I.waitFor('mediumWait');
        I.clickFixed('//*[@data-testid="marketIndicatorList_listItem_id_PRODUCT_TYPE_INDEX_0"]');
        await I.waitFor();
    },
    async clickLineChart(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_line_id"]');
        await I.waitFor();
    },
    async clickCandlestickChart(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_candlestick_id"]');
        await <PERSON>.waitFor();
    },
    async clickTrendButton(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_trend_id"]');
        await I.waitFor();
    },
    async clickOscillatorButton(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_oscillator_id"]');
        await I.waitFor();
    },
    async clickEverChartButton(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_everChart_id"]');
        await I.waitFor();
    },
    async clickChartArea(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_chartContainer_id"]');
        await I.waitFor();
    },
    async selectDisplayPeriodByText(periodText: string): Promise<void> {
        I.clickFixed(`//label[.//p[contains(text(), "${periodText}")]]`);
        await I.waitFor();
    },
    async selectTrendOption(trendType: string = 'bollingerBand'): Promise<void> {
        I.clickFixed(`//label[@value="${trendType}"]`);
        await I.waitFor('shortWait');
    },
    async selectOscillatorOption(oscillatorType: string = 'RSI'): Promise<void> {
        I.clickFixed(`//label[@value="${oscillatorType}"]`);
        await I.waitFor('shortWait');
    },
    async clickToolTip(): Promise<void> {
        I.clickFixed('//*[@data-testid="commonChart_howToViewChart_id"]//p');
        await I.waitFor();
    },
};
