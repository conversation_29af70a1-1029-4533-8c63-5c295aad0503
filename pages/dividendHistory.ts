import { COMMON_CLOSE_RIGHT_SLIDE, COMMON_HEADER_TITLE } from '../const/constant';

const { I } = inject();

type PagePrefix = 'dividendHistoryList' | 'distributionHistoryList';

export = {
    screenshotPrefix: '1225.Test_item_No',
    screenshotDisPrefix: '1236.Test_item_No',

    locators: {
        dividendTabButton: '//*[@data-testid="distributionHistoryList_dividendHistory_id"]',
        distributionTabButton: '//*[@data-testid="dividendHistoryList_distributionHistory_id"]',
        viewThisScreenOpenIcon: (prefix: PagePrefix) => `//*[@data-testid="${prefix}_viewScreenHelp_id"]`,
        viewScreenModal: '//*[@id="bottom-sheet-container"]',
        viewScreenModalCloseButton: '//*[@id="bottom-sheet-container"]/div[2]/div[1]/div/button',
        displaySettingFilter: (prefix: PagePrefix) => `//*[@data-testid="${prefix}_filter_id"]`,
        displaySettingClearButton: (prefix: PagePrefix) => `//*[@data-testid="${prefix}_clear_id"]`,
        displaySettingConfirmButton: (prefix: PagePrefix) => `//*[@data-testid="${prefix}_confirm_id"]`,
        swipeToElement: (page: 'dividend' | 'distribution') => `//*[@id="${page}-history-page"]/p`,
        scrollToTopButton: '//*[@id="scrollButton"]',

        displayOrderButton: (prefix: PagePrefix) => `//*[@data-testid="${prefix}_displayOrder_id"]`,
        displayPeriodButton: (prefix: PagePrefix) => `//*[@data-testid="${prefix}_displayPeriod_id"]`,

        checkerValue: (value: string) => `//label[@value="${value}"]`,

        distributionDetailId: '(//*[@data-testid="distributionHistoryList_distributionCard_id"])[1]',
    },

    urls: {
        base: '/dividend-history/list',
        distribution: '/distribution-history/list',
    },

    async goToDividendHistoryPage() {
        I.amOnPage(this.urls.base);
        await I.waitFor('shortWait');
        I.see('配当金履歴', COMMON_HEADER_TITLE);
    },

    async goToDistributionHistoryPage() {
        I.amOnPage(this.urls.distribution);
        await I.waitFor('shortWait');
        I.see('分配金履歴', COMMON_HEADER_TITLE);
    },

    async openDisplaySetting(prefix: PagePrefix) {
        I.seeElement(this.locators.displaySettingFilter(prefix));
        I.clickFixed(this.locators.displaySettingFilter(prefix));
        I.waitForText('画面表示設定', 1, 'p');
    },

    async closeDisplaySetting() {
        await I.waitFor('shortWait');
        I.clickFixed(COMMON_CLOSE_RIGHT_SLIDE);
    },

    async toggleChecker(values: Array<string>) {
        for (let i = 0; i < values.length; i++) {
            const type = this.locators.checkerValue(values[i]);
            I.seeElement(type);
            await I.clickFixed(type);
            await I.waitFor();
            if (!i) {
                await I.clickFixed(type);
            }
        }
    },

    async getLabelValues(testId: string) {
        const values = await I.executeScript((testId) => {
            return Array.from(document.querySelectorAll(`div[data-testid="${testId}"] label`))
                .map((label) => label.getAttribute('value'))
                .filter(Boolean);
        }, testId);
        return values;
    },
};
