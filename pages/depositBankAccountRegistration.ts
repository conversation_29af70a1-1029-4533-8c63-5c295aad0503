const { I } = inject();

export default {
    prefix: {
        input: '1636.deposit_bank_account_registration_input.Test_No',
        confirm: '1643.deposit_bank_account_registration_confirm.Test_No',
        complete: '1647.deposit_bank_account_registration_conplete.Test_No',
        cancelcomplete: '1650.deposit_bank_account_registration_cancel_conplete.Test_No',
    },
    locators: {
        depositSubjectItem: '//div[@data-testid="bankAccountRegistrationInput_depositSubject_id"]/label',
        toConfirmScreen: '//button[@data-testid="bankAccountRegistrationInput_toConfirmScreen_id"]',
        customerInformationHref: '//p[@data-testid="bankAccountRegistrationComplete_customerInformation_id"]/span[2]',
    },
    urls: {
        input: '/mobile/bank-account-registration/depositbank/input',
        confirm: '/mobile/bank-account-registration/depositbank/confirm',
        complete: '/mobile/bank-account-registration/depositbank/complete',
        cancelcomplete: '/mobile/bank-account-registration/depositbank/cancelcomplete',
    },

    async goToInputPage() {
        I.amOnPage(this.urls.input + '?bankcode=0009');
        await I.waitFor('mediumWait');
        I.waitForText('銀行引落口座\n登録申込', 5, 'p');
        I.seeInCurrentUrl(this.urls.input);
    },

    async goToConfirmPage() {
        await I.scrollAndClick(this.locators.toConfirmScreen);
        await I.waitFor('mediumWait');
        I.seeInCurrentUrl(this.urls.confirm);
    },

    async goToCompletePage() {
        I.amOnPage(this.urls.complete);
        I.waitForText('銀行引落口座\n登録申込', 5, 'p');
        I.seeInCurrentUrl(this.urls.complete);
    },

    async goToCancelCompletePage() {
        I.amOnPage(this.urls.cancelcomplete);
        I.waitForText('銀行引落口座\n登録申込', 5, 'p');
        I.seeInCurrentUrl(this.urls.cancelcomplete);
    },

    async focusAndFillField(locator: CodeceptJS.LocatorOrString, value?: string) {
        await I.seeAndClickElement(locator);
        await I.waitFor();
        I.fillField(locator, value || '123');
        I.blur(locator);
        await I.waitFor();
    },
};
