import { LAST_EXTERNAL_URL } from '../const/constant';

const { I } = inject();

export = {
    locators: {
        closeRightModal: '//*[@data-testid="common_rightSlide_close_id"]',
        closeBottomSheet: '//button[@aria-label="cancel-btn"]',
        hamburgerMenu: '//*[@data-testid="common_menu_id"]',
        appSetting: '//*[@data-testid="menu_appSetting_id"]',
        emailNotificationSettings: '//*[@data-testid="settingApp_emailNotificationSettings_id"]', // 1
        pushNotificationSettings: '//*[@data-testid="settingApp_pushNotificationSettings_id"]', // 2
        priorityOrderingMethodSettings: '//*[@data-testid="settingApp_priorityOrderingMethodSettings_id"]', // 3
        simpleScreenSettings: '//*[@data-testid="settingApp_simpleScreenSettings_id"]', // 4
        automaticUpdateInterval: '//*[@data-testid="settingApp_automaticUpdateInterval_id"]', // 5
        confirmBtn: '//*[@data-testid="settingApp_confirm_id"]', // 9
        cancelBtn: '//*[@data-testid="settingApp_cancel_id"]', // 10
        executionAndExpiration: '//*[@data-testid="settingApp_executionAndExpiration_id"]', // 11
        stockExecutionNotiCheckbox: '//*[@data-testid="settingAppCell_stockExecutionNoti_checkbox_id_0"]/span',
        kabuRelated: '//*[@data-testid="settingApp_kabuRelated_id"]', // 12
        kabuNotiCheckbox: '//*[@data-testid="settingAppCell_kabuNoti_checkbox_id_0"]/span',
        futureOptionRelated: '//*[@data-testid="settingApp_futureOptionRelated_id"]', // 13
        futureOPAlertNotiCheckbox: '//*[@data-testid="settingAppCell_futureOPAlertNoti_checkbox_id_0"]/span',
        depositAndWithdrawRelated: '//*[@data-testid="settingApp_depositAndWithdrawRelated_id"]', // 14
        rentalFeePaymentNotiCheckbox: '//*[@data-testid="settingAppCell_rentalFeePaymentNoti_checkbox_id_0"]/span',
        fundingRelated: '//*[@data-testid="settingApp_fundingRelated_id"]', // 15
        premiumReserveInvestmentTrustPurchaseNotiCheckbox:
            '//*[@data-testid="settingAppCell_premiumReserveInvestmentTrustPurchaseNoti_checkbox_id_0"]/span',
        bondRelated: '//*[@data-testid="settingApp_bondRelated_id"]', // 16
        bondInterestPaymentRedemptionNotiCheckbox:
            '//*[@data-testid="settingAppCell_bondInterestPaymentRedemptionNoti_checkbox_id_0"]/span',
        otherTransactionAndService: '//*[@data-testid="settingApp_otherTransactionAndService_id"]', // 17
        transactionCautionStockNotiCheckbox:
            '//*[@data-testid="settingAppCell_transactionCautionStockNoti_checkbox_id_0"]/span',
        priorityOrderMethod: '//*[@data-testid="settingAppCellModal_priorityOrderMethod_id"]', // 26
        priorityOrderMarket: '//*[@data-testid="settingAppCellModal_priorityOrderMarket_id"]',
        automaticUpdateIntervalSelection: '//*[@data-testid="settingAppCellModal_easyScreenSetting_id"]', // 34
        priorityOrderMethodSettingFutures: '//*[@data-testid="settingApp_priorityOrderMethodSettingFutures_id"]', // 52
        priorityOrderMethodFuture: '//*[@data-testid="settingAppCellModal_priorityOrderMethodFuture_id"]', // 53
        priorityOrderMarketFuture: '//*[@data-testid="settingAppCellModal_priorityOrderMarketFuture_id"]',
        url: '/mobile/setting/app',
        notificationDefaultUrl: 'https://kabu.com/sp/item/notification/default.html',
        kabucallUrl: 'https://kabu.com/sp/investment/market/info/kabucall.html',
        kabucallToushinUrl: 'https://kabu.com/sp/investment/market/info/kabucall_toushin.html',
        assetDepositDefaultUrl: 'https://kabu.com/sp/item/asset_deposit/default.html',
        stockLendingDefaultUrl: 'https://kabu.com/sp/item/stock_lending/default.html',
        haitouUketoriDefaultUrl: 'https://kabu.com/sp/item/haitou_uketori/default.html',
        paymentDefaultUrl: 'https://kabu.com/sp/item/payment_cashout/payment/default.html',
        cashoutDefaultUrl: 'https://kabu.com/sp/item/payment_cashout/cashout/default.html',
        realtimeTransferDefaultUrl: 'https://kabu.com/sp/item/payment_cashout/payment/realtime_transfer/default.html',
        kabuFundUrl: 'https://kabu.com/sp/item/fund/',
        tsumitateDefaultUrl: 'https://kabu.com/sp/item/petit/tsumitate/default.html',
        multiCurMmfDefaultUrl: 'https://kabu.com/sp/item/multi_cur_mmf/default.html',
        bondDefaultUrl: 'https://kabu.com/sp/item/bond/default.html',
        ipoPoDefaultUrl: 'https://kabu.com/sp/item/ipo_po/default.html',
        rakurakuDefaultUrl: 'https://kabu.com/sp/item/rakuraku/default.html',
        stockTransferDefaultUrl: 'https://kabu.com/sp/item/stock_transfer/default.html',
        shinyoDefaultUrl: 'https://kabu.com/sp/item/shinyo/default.html',
        irServiceUrl: 'https://kabu.com/sp/investment/market/info/ir_service.html',
        meigaraReportUrl: 'https://kabu.com/sp/investment/market/info/meigara_report.html',
    },

    async clickItem(dataTestId: string) {
        I.waitForElement(dataTestId);
        I.clickFixed(dataTestId);
        await I.waitFor('mediumWait');
        return this;
    },

    async goToPage() {
        await I.amOnPage(this.locators.url);
        await I.waitFor('mediumWait');
        I.seeElement(this.locators.emailNotificationSettings);
        return this;
    },

    async closeRightModal() {
        this.clickItem(this.locators.closeRightModal);
        return this;
    },

    async closeBottomSheet() {
        this.clickItem(this.locators.closeBottomSheet);
        return this;
    },

    async clickExternalUrl(xpath: string, urlCompare: string) {
        this.clickItem(xpath);
        await I.waitFor('mediumWait');
        I.activateApp();
        const externalUrl = await I.getLocalStorage(LAST_EXTERNAL_URL);
        I.say(externalUrl);
        I.assertEqual(externalUrl, urlCompare, 'URL is not matching');
        return this;
    },

    async clickCheckbox(xpath: string) {
        await this.clickItem(xpath);
        const activeColor = await I.grabCssPropertyFrom(xpath, 'background-color');
        const isChecked = activeColor === 'rgba(255, 86, 0, 1)';
        I.say(`checkbox is ${isChecked ? `active` : `disable`}`);
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name) {
        I.saveScreenshot(name);
        await I.waitFor();
        return this;
    },
};
