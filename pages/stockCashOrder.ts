import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../const/constant";

const { I, stockCashOrder } = inject();

export = {
    locator: {
        buyInputCautionInfo: '//*[@data-testid="buyInput_tradeRestrictionTradeCautionInformation_id"]',
        buyInputConfirmButton: '//*[@data-testid="buyInput_OrderConfirmButton_id"]',
        buyInputExecutionMethodSettingPanel: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]',
        buyInputQuantity: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//input[contains(@placeholder, "数量を入力")]',
        buyInputPrice: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//input[contains(@placeholder, "価格を入力")]',
        buyConfirmConfirmButton: '//*[@data-testid="buyConfirm_confirmOrderButton_id"]',
        buyConfirmSoftCheck: '//div[p[contains(text(), "誤発注ではありませんか")]]//button[@aria-label="Check"]',
        buyConfirmPasswordInput: '//input[@data-testid="buyConfirm_password_id"]',
        buyConfirmPasswordCheck: '//*[@data-testid="buyConfirm_passwordOmissionCheck_id"]',
        buyInputAutomatedTradingMethod: '//*[@data-testid="buyInput_executionMethodSelection_id"]/button[3]',
        buyInputStopLossIndicator: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//button[p[contains(text(), "逆指値")]]',
        buyInputWLimitOrderItem: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//div[contains(@class, "select-collapse")]//p[contains(text(), "W指値")]',
        buyInputTrailingStopOrderItem: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//div[contains(@class, "select-collapse")]//p[contains(text(), "トレーリングストップ")]',
        buyInputAdjustedLimitOrderItem: '//*[@data-testid="buyInput_executionMethodSettingPanel_id"]//div[contains(@class, "select-collapse")]//p[contains(text(), "±指値")]',
    },
    urls: {
        cashBuyInput: '/mobile/trade/stock/buy',
        cashBuyConfirm: '/mobile/trade/stock/buy/confirm',
        cashBuyCompleted: '/mobile/trade/stock/buy/complete',
    },
    inputValues: {
        password: '111111',
        quantity: '100',
        price: '1,130.5',
        trailWidth: '0.1',
        conditionPrice: '0.1',
    },
    takeScreenshot: {
        cashBuyInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockCashTradingBuyInput}_${suffix}.png`);
        },
        cashBuyOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockCashTradingBuyOrderConfirm}_${suffix}.png`);
        },
        cashBuyOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockCashTradingBuyOrderCompleted}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToCashBuyInput(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeStockBuy, SESSION_STORAGE_VALUE.tradeStockBuy());
        I.amOnPage(this.urls.cashBuyInput);
        await I.waitFor('mediumWait');
        I.waitForText('現物買', 3, 'body');
        I.waitForElement(this.locator.buyInputCautionInfo, 2);
    },
    async goToCashBuyOrderConfirm(): Promise<void> {
        await stockCashOrder.goToCashBuyInput();
        await I.scrollToElement(this.locator.buyInputQuantity);
        I.fillField(this.locator.buyInputQuantity, this.inputValues.quantity);
        I.fillField(this.locator.buyInputPrice, this.inputValues.price);
        await I.clickFixed(this.locator.buyInputConfirmButton);
        await I.waitFor('mediumWait');
        I.waitForElement(this.locator.buyConfirmConfirmButton, 2);
    },
    async goToCashBuyOrderCompleted(): Promise<void> {
        await stockCashOrder.goToCashBuyOrderConfirm();
        await I.swipeDirection('up');
        const numberVisibleBuyConfirmSoftCheck = await I.grabNumberOfVisibleElements(this.locator.buyConfirmSoftCheck);
        if (numberVisibleBuyConfirmSoftCheck) {
            await I.clickFixed(this.locator.buyConfirmSoftCheck);
        }
        const numberVisibleBuyConfirmPasswordInput = await I.grabNumberOfVisibleElements(this.locator.buyConfirmPasswordInput);
        if (numberVisibleBuyConfirmPasswordInput) {
            I.fillField(this.locator.buyConfirmPasswordInput, this.inputValues.password);
        }
        const numberVisibleBuyConfirmPasswordCheck = await I.grabNumberOfVisibleElements(this.locator.buyConfirmPasswordCheck);
        if (numberVisibleBuyConfirmPasswordCheck) {
            await I.clickFixed(this.locator.buyConfirmPasswordCheck);
        }
        await I.clickFixed(this.locator.buyConfirmConfirmButton);
        await I.waitFor('mediumWait');
    },
    async selectAutomatedTradingTab(): Promise<void> {
        await I.scrollToElement(this.locator.buyInputAutomatedTradingMethod);
        await I.waitFor();
        await I.clickFixed(this.locator.buyInputAutomatedTradingMethod);
        await I.waitFor();
    },
    async selectWLimitOrderMethod(): Promise<void> {
        await stockCashOrder.selectAutomatedTradingTab();
        await I.scrollToElement(this.locator.buyInputStopLossIndicator);
        await I.clickFixed(this.locator.buyInputStopLossIndicator);
        await I.waitFor();
        await I.clickFixed(this.locator.buyInputWLimitOrderItem);
        await I.waitFor();
    },
    async selectTrailingStopOrderMethod(): Promise<void> {
        await stockCashOrder.selectAutomatedTradingTab();
        await I.scrollToElement(this.locator.buyInputStopLossIndicator);
        await I.clickFixed(this.locator.buyInputStopLossIndicator);
        await I.waitFor();
        await I.clickFixed(this.locator.buyInputTrailingStopOrderItem);
        await I.waitFor();
    },
    async selectAdjustedLimitOrderMethod(): Promise<void> {
        await stockCashOrder.selectAutomatedTradingTab();
        await I.scrollToElement(this.locator.buyInputStopLossIndicator);
        await I.clickFixed(this.locator.buyInputStopLossIndicator);
        await I.waitFor();
        await I.clickFixed(this.locator.buyInputAdjustedLimitOrderItem);
        await I.waitFor();
    },
};
