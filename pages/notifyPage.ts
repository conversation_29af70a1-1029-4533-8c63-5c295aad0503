import { COLOR } from "../const/constant";

const { I } = inject();

export = {
    async notifyTabView(notifyTab: string, notifyTabParent: string): Promise<void> {
        I.waitForElement(notifyTab);
        I.click(notifyTab);
        await I.waitFor();
        const color = await I.grabCssPropertyFrom(notifyTab, 'color');
        I.assertEqual(color, COLOR.mainColor, `color is not ${COLOR.mainColor}`);
        const transformCss = await I.grabCssPropertyFrom(notifyTabParent, 'transform');
        I.assertEqual(transformCss, 'none', 'transform is not none');
        await I.waitFor();
    }
};
