import { COOKIE_KEY, SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE, USER_ID } from '../const/constant';

const { I, stockMarginPaymentOrder, stockMarginOrder } = inject();

export = {
    locator: {
        marginPaymentCautionInfo: '//*[@data-testid="marginPaymentInput_tradeRestrictionTradeCautionInfo_id"]',
        marginPaymentExecutionMethodContent: '//*[@data-testid="marginPaymentInput_executionMethodContent_id"]',
        marginPaymentExecutionMethodQuantityInput:
            '//*[@data-testid="marginPaymentInput_executionMethodContent_id"]//input[contains(@placeholder, "数量を入力")]',
        marginPaymentExecutionMethodPriceInput:
            '//*[@data-testid="marginPaymentInput_executionMethodContent_id"]//input[contains(@placeholder, "価格を入力")]',
        marginPaymentOrderConfirmButton: '//*[@data-testid="marginPaymentInput_orderConfirmButton_id"]',
        marginPaymentOrderPasswordInput: '//*[@data-testid="marginPaymentConfirm_password_id"]',
        marginPaymentOrderPasswordOmitted: '//*[@data-testid="marginPaymentConfirm_checkPassword_id"]',
        marginPaymentOrderConfirmConfirmButton: '//*[@data-testid="marginPaymentConfirm_confirmButton_id"]',
    },
    urls: {
        marginPaymentOrderInput: '/mobile/trade/margin/repayment',
    },
    inputValues: {
        quantity: '100',
        price: '1,585.5',
        password: '111111',
        trailWith: '1.0',
    },
    takeScreenshot: {
        marginPaymentOrderInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingPaymentOrderInput}_${suffix}.png`);
        },
        marginPaymentOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingPaymentOrderConfirm}_${suffix}.png`);
        },
        marginPaymentOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(
                `${SCREENSHOT_PREFIX.domesticStockMarginTradingPaymentOrderCompleted}_${suffix}.png`,
            );
        },
        marginPaymentUturnOrderInput: async (suffix: string) => {
            await I.saveScreenshot(
                `${SCREENSHOT_PREFIX.domesticStockMarginTradingPaymentUturnOrderInput}_${suffix}.png`,
            );
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToMarginPaymentOrderInput(sessionStorage?: {}): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginRepayment, SESSION_STORAGE_VALUE.tradeMarginRepayment(sessionStorage));
        I.amOnPage(this.urls.marginPaymentOrderInput);
        await I.waitFor('mediumWait');
        I.waitForText('信用返済', 3, 'body');
        I.waitForElement(this.locator.marginPaymentCautionInfo);
    },
    async goToMarginPaymentUturnOrderInput(): Promise<void> {
        I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user1 });
        await I.waitFor();
        await stockMarginOrder.goToMarginPaymentUturnOrderInput();
    },
    async goToMarginPaymentOrderConfirm(sessionStorage?: {}): Promise<void> {
        await stockMarginPaymentOrder.goToMarginPaymentOrderInput(sessionStorage);
        await I.scrollToElement(this.locator.marginPaymentExecutionMethodQuantityInput);
        I.fillField(this.locator.marginPaymentExecutionMethodQuantityInput, this.inputValues.quantity);
        I.fillField(this.locator.marginPaymentExecutionMethodPriceInput, this.inputValues.price);
        await I.clickFixed(this.locator.marginPaymentOrderConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToMarginPaymentOrderCompleted(): Promise<void> {
        await stockMarginPaymentOrder.goToMarginPaymentOrderConfirm();
        I.fillField(this.locator.marginPaymentOrderPasswordInput, this.inputValues.password);
        I.blur(this.locator.marginPaymentOrderPasswordInput);
        await I.clickFixed(this.locator.marginPaymentOrderPasswordOmitted);
        await I.clickFixed(this.locator.marginPaymentOrderConfirmConfirmButton);
        await I.waitFor('mediumWait');
    },
};
