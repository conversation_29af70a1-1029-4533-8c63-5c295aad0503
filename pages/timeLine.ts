import { PAGE_URL } from "../const/constant";

const { I, timeLine } = inject();

export = {
    async goToTimeLineTop(): Promise<void> {
        // Navigate to Time Line Top page
        await I.amOnPage(PAGE_URL.timeline);
        await I.waitFor();
        I.waitForText('タイムライン', 2, 'body');
    },
    async goToTimeLineIndividual(): Promise<void> {
        const timeLineCardFirstItem = '//*[@data-testid="timeLineTop_timelineCard_id_0"]';
        await timeLine.goToTimeLineTop();
        await I.clickFixed(timeLineCardFirstItem);
        await I.waitFor('mediumWait');
    },
    async clickTimeLineCustomSortItem({
        dataTestId,
        isActive,
    }: {
        dataTestId: string;
        isActive?: boolean;
    }): Promise<void> {
        const cellItem = dataTestId;
        I.waitForElement(cellItem);
        await I.scrollToElement(cellItem);
        await I.clickFixed(cellItem);
        await I.waitFor();
        const buttonItem = `${dataTestId}/button`;
        // Check button item: get color and border-color
        const activeColor = await I.grabCssPropertyFrom(buttonItem, 'color');
        const activeBorder = await I.grabCssPropertyFrom(buttonItem, 'border-color');
        await I.waitFor('mediumWait');
        const checkColor = isActive ? 'rgba(255, 86, 0, 1)' : 'rgba(137, 137, 139, 1)';
        const checkBoder = isActive ? 'rgb(255, 86, 0)' : '';
        const message = `button is ${isActive ? 'active' : 'disabled'}`;
        I.assertContain(activeColor, checkColor, message);
        I.assertContain(activeBorder, checkBoder, message);
    },
};
