import { CompletedRequest } from "wdio-intercept-service";
import { PAGE_URL } from "../const/constant";

const { I, market } = inject();

export = {
    locator: {
        marketIndicatorList: '//*[@data-testid="marketIndicatorList_tab_id"]',
        marketRanking: '//*[@data-testid="marketRanking_tab_id"]',
        marketNews: '//*[@data-testid="marketNews_tab_id"]',
        marketIndicatorDetailFavorite: '//*[@data-testid="marketIndicatorDetail_favorite_id"]',
        marketIndicatorListEdit: '//*[@data-testid="marketIndicatorList_edit_id"]',
        marketIndicatorListItems: '[data-testid*="marketIndicatorList_listItem_id"]',
        marketIndicatorListItem: '//*[@data-testid="marketIndicatorList_listItem_id_PRODUCT_TYPE_INDEX_0"]',
        marketMainIndicator: '//*[@id="market-main-indicator"]',
        marketIndicatorListIndicatorList: '//*[@data-testid="marketIndicatorList_indicatorList_id"]',
        marketRankingCategory: '//*[@data-testid="rankingList_category_id"]',
        marketRankingSymbolList: '//*[@data-testid="rankingList_symbolList_id"]',
        marketNewsSearch: '//*[@data-testid="marketNewsList_search_id"]',
        marketNewsListInfo: '//*[@data-testid="marketNewsList_newsListInfo_id_0"]',
        commonChartHowToView: '//*[@data-testid="commonChart_howToViewChart_id"]',
        commonRightSlideFavorite: '//*[@data-testid="common_rightSlide_favorite_id"]',
        favoriteRegisterModalListAdd: '//*[@data-testid="favoriteRegisterModal_listAdd_id"]',
        favoriteRegisterModalConfirm: '//*[@data-testid="favoriteRegisterModal_confirm_id"]',
        marketIndicatorListComplete: '//*[@data-testid="marketIndicatorList_complete_id"]',
        commonHeaderTitle: '//*[@data-testid="common_header_title_id"]',
        benefitListSearch: '//*[@data-testid="benefitList_benefitSearch_id"]',
        benefitSearchSearchBox: '//*[@data-testid="benefitSearch_searchBox_id"]',
        benefitSearchSearch: '//*[@data-testid="benefitSearch_search_id"]',
    },
    async goToMarketIndicatorList(): Promise<void> {
        I.amOnPage('/mobile/market/indicator');
        await I.waitFor('mediumWait');
        I.waitForText('マーケット', 3, 'body');
        I.seeElement(this.locator.marketIndicatorList);
        I.seeElement(this.locator.marketRanking);
        I.seeElement(this.locator.marketNews);
        I.assertContain(await I.grabCurrentUrl(), '/mobile/market/indicator', 'URL is not matching');
        await I.waitFor('mediumWait');
    },
    async goToMarketBenefitList(): Promise<void> {
        I.amOnPage(PAGE_URL.benefit);
        await I.waitFor('mediumWait');
        I.waitForText('株主優待', 3, 'body');
        await I.waitFor('mediumWait');
    },
    async goToShareholderBenefitsSearch(): Promise<void> {
        await I.clickFixed(this.locator.benefitListSearch);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.benefitSearchSearchBox);
        I.seeElement(this.locator.benefitSearchSearch);
    },
    async displayIndicatorTab(): Promise<void> {
        I.seeElement(this.locator.marketMainIndicator);
        I.seeElement(this.locator.marketIndicatorListIndicatorList);
        I.seeElement(this.locator.marketIndicatorListEdit);
        I.assertContain(await I.grabCurrentUrl(), '/mobile/market/indicator', 'URL is not matching');
        await I.waitFor('mediumWait');
    },
    async displayRankingTab(): Promise<void> {
        I.seeElement('//*[@id="market-ranking"]');
        I.seeElement(this.locator.marketRankingCategory);
        I.seeElement(this.locator.marketRankingSymbolList);
        I.assertContain(await I.grabCurrentUrl(), '/mobile/market/ranking', 'URL is not matching');
        await I.waitFor('mediumWait');
    },
    async displayNewsTab(): Promise<void> {
        I.seeElement('//*[@id="market-news"]');
        I.seeElement(this.locator.marketNewsSearch);
        I.seeElement(this.locator.marketNewsListInfo);
        I.assertContain(await I.grabCurrentUrl(), '/mobile/market/news', 'URL is not matching');
        await I.waitFor('mediumWait');
    },
    async clickMarketTabGroup(): Promise<void> {
        const indicatorListTab = this.locator.marketIndicatorList;
        const rankingTab = this.locator.marketRanking;
        const newsTab = this.locator.marketNews;
        I.waitForElement(indicatorListTab);
        I.waitForElement(rankingTab);
        I.waitForElement(newsTab);
        // Display Market Ranking
        await I.clickFixed(rankingTab);
        await I.waitFor('mediumWait');
        market.displayRankingTab();
        // Display Market News
        await I.clickFixed(newsTab);
        await I.waitFor('mediumWait');
        market.displayNewsTab();
        // Display Market Indicator List
        await I.clickFixed(indicatorListTab);
        await I.waitFor('mediumWait');
        market.displayIndicatorTab();
    },
    async goToMarketIndicatorDetail(): Promise<void> {
        const cardItem = this.locator.marketIndicatorListItem;
        I.waitForElement(cardItem);
        await I.clickFixed(cardItem);
        await I.waitFor('mediumWait');
        I.seeElement(this.locator.commonChartHowToView);
        I.seeElement(this.locator.marketIndicatorDetailFavorite);
        I.assertContain(await I.grabCurrentUrl(), '/mobile/market/indicator/detail', 'URL is not matching');
    },
    async goToMarketRanking(): Promise<void> {
        const rankingTab = this.locator.marketRanking;
        I.waitForElement(rankingTab);
        await I.clickFixed(rankingTab);
        await I.waitFor('mediumWait');
        market.displayRankingTab();
    },
    async openFavoriteModal(): Promise<void> {
        const favoriteBtn = this.locator.marketIndicatorDetailFavorite;
        I.waitForElement(favoriteBtn);
        await I.clickFixed(favoriteBtn);
        await I.waitFor('mediumWait');
    },
    async toggleFavoriteCheckbox(dataTestId: string): Promise<void> {
        const checkboxItem = dataTestId;
        I.waitForElement(checkboxItem);
        await I.clickFixed(checkboxItem);
        await I.waitFor('mediumWait');
        const checkIcon = `${dataTestId} button svg`;
        const checkOnColor = await I.grabCssPropertyFrom(checkIcon, 'color');
        I.assertContain(checkOnColor, 'rgba(255, 86, 0, 1)');
        await I.waitFor('mediumWait');
        await I.clickFixed(checkboxItem);
        await I.waitFor();
        const checkOffColor = await I.grabCssPropertyFrom(checkIcon, 'color');
        I.assertContain(checkOffColor, 'rgba(137, 137, 139, 1)');
    },
    async clickEditButton(): Promise<void> {
        const editButton = this.locator.marketIndicatorListEdit;
        I.waitForElement(editButton);
        await I.clickFixed(editButton);
    },
    async clickCompleteButton(): Promise<void> {
        const completeButton = this.locator.marketIndicatorListComplete;
        I.waitForElement(completeButton);
        await I.clickFixed(completeButton);
    },
    async clickBenefitSearchButton(): Promise<void> {
        const searchButtonSelector = this.locator.benefitSearchSearch;
        I.waitForElement(searchButtonSelector);
        await I.clickFixed(searchButtonSelector);
        await I.waitFor('mediumWait');
    },
    async verifyNavigationToDetail(itemSelector: string, detailSelector: string, screenshotName: string): Promise<void> {
        const symbolText = await I.grabTextFrom({ xpath: `${itemSelector}//p[1]` });
        I.waitForElement(itemSelector);
        await I.clickFixed(itemSelector);
        await I.waitFor('mediumWait');
        await I.waitForElement(detailSelector, 10);
        const detailSymbolName = await I.grabTextFrom(detailSelector);
        I.assertContain(detailSymbolName, symbolText);
        I.saveScreenshot(screenshotName);
    },
    async handleDropdownInteraction(buttonSelector: string, dropdownSelector: string, itemSelector: string, apiNames?: string[], screenshotName?: string): Promise<void> {
        const getCheckMarkOpacity = async (selector: string) => await I.grabCssPropertyFrom(selector, 'opacity');
        const getSelectedItemText = async (selector: string) => await I.grabTextFrom(selector);

        const verifySelectedItem = async (checkMarkSelector: string, buttonText: string, selectedText: string) => {
            const checkMarkOpacity = await getCheckMarkOpacity(checkMarkSelector);
            I.assertEqual(checkMarkOpacity, "1", 'Check mark icon should be visible');
            I.assertEqual(buttonText, selectedText, 'Selected item should match');
        };

        // Open the dropdown menu
        I.waitForElement(buttonSelector);
        await I.clickFixed(buttonSelector);
        await I.waitFor();
        I.seeElement(dropdownSelector);

        const currentValue = await getSelectedItemText(buttonSelector);
        const selectedItemSelector = `${itemSelector}[contains(@aria-checked, "true")]`;
        const checkMarkIcon = `${selectedItemSelector}/span[1]`;
        const selectedItemText = await getSelectedItemText(selectedItemSelector);

        await verifySelectedItem(checkMarkIcon, currentValue, selectedItemText);
        await I.saveScreenshot(`${screenshotName}_Dropdown_display_menu.png`);

        await I.setupInterceptor();

        // Click on another item in the dropdown
        const otherItemSelector = `${itemSelector}[contains(@aria-checked, "false")]`;
        await I.clickFixed(otherItemSelector);
        await I.waitFor();
        await I.clickFixed(buttonSelector);
        await I.waitFor();
        I.seeElement(dropdownSelector);

        const otherSelectedItemText = await getSelectedItemText(buttonSelector);
        const otherItemText = await getSelectedItemText(selectedItemSelector);
        const otherCheckMarkIcon = `${selectedItemSelector}/span[1]`;

        await verifySelectedItem(otherCheckMarkIcon, otherSelectedItemText, otherItemText);
        await I.saveScreenshot(`${screenshotName}_Dropdown_select_other_item.png`);
        if (apiNames?.length) {
            // Verify the API requests
            const allRequests = await I.getRequests();
            for (const request of allRequests) {
                try {
                    const index = apiNames.findIndex((apiName) => request.url.includes(apiName));
                    I.assertEqual((request as CompletedRequest)?.response.statusCode, 200, 'Status code should be 200');
                    I.assertContain(request.url, apiNames[index], 'URL should match');
                } catch (error) {
                    console.warn('Error fetching requests:', error);
                }
            }
        }

        await I.clickFixed(buttonSelector);
    },
    async goToRakingStockDetail(stockItemSelector: string): Promise<void> {
        I.waitForElement(stockItemSelector);
        await I.clickFixed(stockItemSelector);
        await I.waitFor();
        I.waitForText('銘柄詳細', 3, this.locator.commonHeaderTitle);
        I.waitForText('基本情報', 3, '#tabtitle');
        I.assertContain(await I.grabCurrentUrl(), '/mobile/info/stock/basic', 'URL is not matching');
    },
    async getVisibleButtons(buttonSelector: string): Promise<string[]> {
        return await I.executeScript((selector) =>
            Array.from(document.querySelectorAll(selector))
                .filter(el => {
                    const rect = el.getBoundingClientRect();
                    return (
                        rect.top >= 0 &&
                        rect.left >= 0 &&
                        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                    );
                })
                .map(el => `button[data-testid="${el.getAttribute('data-testid')}"][data-index="${el.getAttribute('data-index')}"]`), buttonSelector
        );
    }
};
