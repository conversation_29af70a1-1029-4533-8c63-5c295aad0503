import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../const/constant";

const { I, stockMarginDelivery } = inject();

export = {
    locator: {
        marginDeliveryCautionInfo: '//*[@data-testid="marginDeliveryInput_TradeRestrictionTradeCautionInfo_id"]',
        marginDeliveryConfirmPassword: '//*[@data-testid="marginDeliveryConfirm_password_id"]',
        marginDeliveryConfirmPasswordOmissionCheck: '//*[@data-testid="marginDeliveryConfirm_passwordOmissionCheck_id"]',
        marginDeliveryConfirmButton: '//*[@data-testid="marginDeliveryConfirm_orderConfirmBtn_id"]',
    },
    urls: {
        marginDeliveryOrderInput: '/mobile/trade/margin/delivery',
        marginDeliveryOrderConfirm: '/mobile/trade/margin/delivery/confirm',
        marginDeliveryOrderCompleted: '/mobile/trade/margin/delivery/complete',
    },
    inputValues: {
        password: '111111',
        quantity: '100',
    },
    takeScreenshot: {
        marginDeliveryOrderInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingDeliveryOrderInput}_${suffix}.png`);
        },
        marginDeliveryOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingDeliveryOrderConfirm}_${suffix}.png`);
        },
        marginDeliveryOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingDeliveryOrderCompleted}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToMarginDeliveryOrderInput(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginDelivery, SESSION_STORAGE_VALUE.tradeMarginDelivery());
        I.amOnPage(this.urls.marginDeliveryOrderInput);
        await I.waitFor('mediumWait');
        I.waitForText('品渡方法設定', 3, 'body');
        I.waitForElement(this.locator.marginDeliveryCautionInfo);
    },
    async goToMarginDeliveryOrderConfirm(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginDeliveryConfirm, SESSION_STORAGE_VALUE.tradeMarginDeliveryConfirm());
        I.amOnPage(this.urls.marginDeliveryOrderConfirm);
        await I.waitFor('mediumWait');
        I.waitForText('品渡方法設定', 3, 'body');
    },
    async goToMarginDeliveryOrderComplete(): Promise<void> {
        await stockMarginDelivery.goToMarginDeliveryOrderConfirm();
        I.fillField(this.locator.marginDeliveryConfirmPassword, stockMarginDelivery.inputValues.password);
        I.blur(this.locator.marginDeliveryConfirmPassword);
        await I.clickFixed(this.locator.marginDeliveryConfirmPasswordOmissionCheck);
        await I.clickFixed(this.locator.marginDeliveryConfirmButton);
        await I.waitFor('mediumWait');
        I.waitForText('品渡', 2, 'body');
    },
};
