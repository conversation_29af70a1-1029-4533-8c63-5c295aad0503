import common from "./search/common";

const { I } = inject();

export default {
    prefix: {
        notice: '1671_easy_electronic_delivery.Test_item_No',
        agreement: '1674_easy_electronic_delivery.Test_item_No',
        complete: '1687_easy_electronic_delivery.Test_item_No',
    },
    locators: {
        commonBack: '$common_back_id',
        contactConclusionButton: '$preContactConfirm_contactConclusion_id',
        backToTopButton: '$preContactConfirm_backToTop_id',
        linkNewVersionsHref: '$preContractAgree_linkNewVersions_id',
        contractsAndRegulationsPdfHref: '$preContractAgree_contractsAndRegulations_id',
        financialProductBrokerageAccountPdfHref: '$preContractAgree_financialProductBrokerageAccount_id',
        corporateAccountPdfHref: '$preContractAgree_corporateAccount_id',
        specialInvestorProgramHref: '$preContractAgree_specialInvestorProgram_id',
        foreignSecuritiesTradingAccountTermsHref: '$preContractAgree_foreignSecuritiesTradingAccountTerms_id',
        foreignStockMarketDataDisplayServiceTermsHref: '$preContractAgree_foreignStockMarketDataDisplayServiceTerms_id',
        stockTradingRulePdfHref: '$preContractAgree_stockTradingRule_id',
        completeButton: '$preContractAgree_complete_id',
        agreeDocumentContentZero: '$preContractAgree_agreeDocumentContent_0_id',
        feeCourseChangeHref: '$preContractAgree_feeCourseChange_id',
        detailOfFeeHref: '$preContractAgree_detailOfFee_id',

        documentButtonId: (number: number) => `//div[@data-testid="preContractAgree_otherAgreementLinkItem_${number}_id"]//button`,
        creditAccountButtonItem: (number: number) => `$preContractAgree_creditAccountLinkItem_${number}_id`,
        creditAccountLinkItem: (number: number) => `$preContractAgree_creditAccountLinkItem_${number}_id`,
    },
    urls: {
        notice: '/mobile/setting/electronic-delivery/pre-contract-documents/notice',
        agree: '/mobile/setting/electronic-delivery/pre-contract-documents/agree',
        complete: '/mobile/setting/electronic-delivery/pre-contract-documents/complete',
        portfolio: '/mobile/mypage/portfolio',
    },

    async goToNoticePage() {
        I.amOnPage(this.urls.notice);
        I.waitForText('らくらく電子交付\n(契約締結前交付書面)', 5, 'p');
        I.seeInCurrentUrl(this.urls.notice);
    },

    async goToAgreementPage() {
        await this.goToNoticePage();
        await I.seeAndClickElement(this.locators.contactConclusionButton);
        I.waitForText('らくらく電子交付\n(契約締結前交付書面)', 5, 'p');
        I.seeInCurrentUrl(this.urls.agree);
    },

    async goToCompletionPage() {
        await this.goToAgreementPage();
        await I.swipeElementDirection('up', this.locators.completeButton);
        await I.seeAndClickElement(this.locators.completeButton);
        I.waitForText('らくらく電子交付\n(契約締結前交付書面)', 5, 'p');
        I.seeInCurrentUrl(this.urls.complete);
    },

    async openAccountLink(number?: number, expectedUrl?: string) {
        I.refreshPage();
        await I.waitFor('mediumWait');
        const locator = this.locators.creditAccountLinkItem(number || 0);
        await common.clickCardItem(locator, expectedUrl, 'kcMemberSite');
    },

    async openCreditAccount(number?: number, expectedUrl?: string) {
        I.refreshPage();
        await I.waitFor('mediumWait');
        const locator = this.locators.creditAccountButtonItem(number);
        await common.clickCardItem(locator, expectedUrl, 'kcMemberSite');
    },
};
