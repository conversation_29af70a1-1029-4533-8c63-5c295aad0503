/*
 * ヘッダ
 * Header
 */

const { I } = inject();

export = {
    locators: {
        title: '$common_header_title_id',
        back: '$common_back_id',
        reload: '$common_reload_id',
        noti: '$common_noti_id',
        menu: '$common_menu_id',
    },

    /**
     * 1.戻る
     * Same as browser back button
     */
    async clickBack() {
        I.click(this.locators.back);
    },

    /**
     * 2.更新
     * Refresh page
     */
    async clickReload() {
        I.click(this.locators.reload);
    },

    /**
     * 3.通知
     * Redirect to NotifyWholeList page
     */
    async clickNoti() {
        I.click(this.locators.noti);
    },

    /**
     * 4.ハンバーガーメニュー
     * Open Hamburger Menu
     */
    async clickMenu() {
        I.click(this.locators.menu);
    },

    async verifyTitle(title: string) {
        const headerTitle = await I.grabTextFrom(this.locators.title);
        I.assertEqual(title, headerTitle);
    },
};
