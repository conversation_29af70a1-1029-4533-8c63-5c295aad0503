/*
 * PDFView
 */

const { I } = inject();

export = {
    /**
     * Close the PDF viewer.
     * This method clicks the close button in the PDF viewer.
     */
    async closePDFView() {
        await <PERSON>.waitFor();
        await <PERSON>.switchToNative();
        await <PERSON><PERSON>waitFor('mediumWait');
        // Click on the close button in native context
        await I.click({ android: '//android.widget.ImageView', ios: '//XCUIElementTypeImage' });
        await <PERSON>.waitFor();
        await I.switchToWeb();
        await <PERSON><PERSON>waitFor('mediumWait');
    },
};
