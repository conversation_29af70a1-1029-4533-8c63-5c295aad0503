/**
 * その他共通UI
 * Others Common UI
 * https://gitbook.guide.inc/kcmsr/vn/Customer/AssetStatus/CommonUI/Others.html
 */

const { I } = inject();

export = {
    locators: {
        scrollUpBtn: '//button[@aria-label="scroll-to-top-btn"]',
    },

    /**
     * 11.上にスクロール
     * Scroll Up
     */
    async clickScrollUp(): Promise<void> {
        const btn = this.locators.scrollUpBtn;
        await I.waitForVisible(btn, 5);
        await I.click(btn);
        await I.waitForInvisible(btn, 5);
    },
};
