import { LAST_EXTERNAL_URL } from '../const/constant';

const { I } = inject();

export = {
    // Define locators
    locators: {
        headerTitle: '//*[@data-testid="common_header_title_id"]',
        backButton: '//*[@data-testid="common_back_id"]',
        sampleMailLink: '//*[@data-testid="referralMail_sampleMail_id"]',
        programLink: '//*[@data-testid="referralMail_programLink_id"]',
        mailInputField: '//*[@data-testid="referralMail_mailInput_id"]',
        sendButton: '//*[@data-testid="referralMail_sendButton_id"]',
        lastName: '//*[@data-testid="referralMail_lastName_id"]',
        firstName: '//*[@data-testid="referralMail_firstName_id"]',
        lastNameKana: '//*[@data-testid="referralMail_lastNameKana_id"]',
        firstNameKana: '//*[@data-testid="referralMail_firstNameKana_id"]',
        mailAddress: '//*[@data-testid="referralMail_mailAddress_id"]',
        message: '//*[@data-testid="referralMail_message_id"]',
        confirm: '//*[@data-testid="referralMail_confirm_id"]',
        form: '//form',
    },

    // URLs
    urls: {
        mailInputPage: '/mobile/referral/mail-input',
        mailConfirmPage: 'referral/mail-confirm',
        sampleMailUrl: 'members/support/help',
        programUrl: 'campaign/introduction_program.html',
    },

    // Navigate to the mail input page
    async navigateToPage() {
        await I.waitFor();
        await I.amOnPage(this.urls.mailInputPage);
        await I.waitFor();
        I.see(
            `ご家族・ご友人
紹介プログラム`,
            this.locators.headerTitle,
        );
        return this;
    },

    // Click on sample mail link and check external URL
    async clickSampleMailLink() {
        I.seeElement(this.locators.sampleMailLink);

        // Click the link
        await I.tapLocationOfElement(this.locators.sampleMailLink);
        await I.waitFor('extraLongWait'); // Wait for 5 seconds for external page
        await I.activateApp();
        const localStorage = await I.getLocalStorage(LAST_EXTERNAL_URL);
        I.say(localStorage);

        return localStorage;
    },

    // Click on program link and check external URL
    async clickProgramLink() {
        I.seeElement(this.locators.programLink);

        // Click the link
        await I.tapLocationOfElement(this.locators.programLink);
        await I.waitFor('extraLongWait'); // Wait for 5 seconds for external page
        await I.activateApp();
        const externalUrl = await I.getLocalStorage(LAST_EXTERNAL_URL);
        I.say(externalUrl);

        return externalUrl;
    },

    // Input email address
    async inputEmail(email: string) {
        I.seeElement(this.locators.mailInputField);
        I.fillField(this.locators.mailInputField, email);
        await I.waitFor();
        return this;
    },

    // Click send button
    async clickSendButton() {
        I.seeElement(this.locators.sendButton);
        I.click(this.locators.sendButton);
        await I.waitFor('mediumWait');
        return this;
    },

    // Navigate back from mail input page
    async navigateBack() {
        I.seeElement(this.locators.backButton);
        I.click(this.locators.backButton);
        await I.waitFor();
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name: string) {
        await I.saveScreenshot(name);
        return this;
    },

    // Fill in last name field
    async fillLastName(value = '123456') {
        I.seeElement(this.locators.lastName);
        await I.clickFixed(this.locators.lastName);
        I.fillField(this.locators.lastName, value);
        await I.clickFixed(this.locators.form);
        await I.waitFor('shortWait');
        return this;
    },

    // Fill in first name field
    async fillFirstName(value = '123456') {
        I.seeElement(this.locators.firstName);
        await I.clickFixed(this.locators.firstName);
        I.fillField(this.locators.firstName, value);
        await I.clickFixed(this.locators.form);
        await I.waitFor('shortWait');
        return this;
    },

    // Fill in last name kana field
    async fillLastNameKana(value = '123456') {
        I.seeElement(this.locators.lastNameKana);
        await I.clickFixed(this.locators.lastNameKana);
        I.fillField(this.locators.lastNameKana, value);
        await I.clickFixed(this.locators.form);
        await I.waitFor('shortWait');
        return this;
    },

    // Fill in first name kana field
    async fillFirstNameKana(value = '123456') {
        I.seeElement(this.locators.firstNameKana);
        await I.clickFixed(this.locators.firstNameKana);
        I.fillField(this.locators.firstNameKana, value);
        await I.clickFixed(this.locators.form);
        await I.waitFor('shortWait');
        return this;
    },

    // Fill in mail address field
    async fillMailAddress(value = '<EMAIL>') {
        I.seeElement(this.locators.mailAddress);
        await I.clickFixed(this.locators.mailAddress);
        I.fillField(this.locators.mailAddress, value);
        await I.clickFixed(this.locators.form);
        await I.waitFor('shortWait');
        return this;
    },

    // Fill in message field
    async fillMessage(value = '123456') {
        I.seeElement(this.locators.message);
        await I.clickFixed(this.locators.message);
        I.fillField(this.locators.message, value);
        await I.clickFixed(this.locators.form);
        await I.waitFor('shortWait');
        return this;
    },

    // Click confirm button and verify redirect to confirmation page
    async clickConfirm() {
        I.seeElement(this.locators.confirm);
        I.click(this.locators.confirm);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking confirm: ${currentUrl}`);

        return currentUrl;
    },
};
