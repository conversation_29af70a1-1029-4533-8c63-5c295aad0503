import { LAST_EXTERNAL_URL } from '../const/constant';
import pdfView from './common-ui/pdfView';

const { I } = inject();

export = {
    locators: {
        back: '//*[@data-testid="common_back_id"]',
        hamburgerMenu: '//*[@data-testid="common_menu_id"]',
        nisaMenu: '//*[@data-testid="menu_nisa_id"]',
        creditCard: '//*[@data-testid="nisaMenu_creditCard_id"]',
        closeRightModal: '//*[@data-testid="common_rightSlide_close_id"]',

        // Card Select Page
        mitsubishiUFJCardRegister: '//*[@data-testid="settingCardSelect_mitsubishiUFJCardRegister_id"]', // 1
        mitsubishiUFJCardClickHere: '//*[@data-testid="settingCardSelect_mitsubishiUFJCardClickHere_id"]', // 2
        mitsubishiUFJCardTargetCardConfirmMethod:
            '//*[@data-testid="settingCardSelect_mitsubishiUFJCardTargetCardConfirmMethod_id"]', // 3
        auPAYCardRegister: '//*[@data-testid="settingCardSelect_auPAYCardRegister_id"]', // 4
        auPAYCardClickHere: '//*[@data-testid="settingCardSelect_auPAYCardClickHere_id"]', // 5
        cautionMessage: '//*[@data-testid="settingCardSelect_cautionMessage_id"]/div[1]', // 6
        cardSelect: '/mobile/setting/card-select',
        cardAgree: '/mobile/setting/card/agree',
        cardConfirm: '/mobile/setting/card/confirm',
        cardCommonRegister: '/mobile/setting/card-common/register',
        cardCommonUnRegister: '/mobile/setting/card-common/unregister',
        infoFundSearch: '/mobile/info/fund/search',
        mitsubishiUfjCardUrl: 'https://kabu.com/item/fund/mitsubishi_ufj_card/default.html',
        lp200Url: 'https://kabu.com/company/lp/lp200.html',

        // Card Agree Page
        term: '//*[@data-testid="settingCardAgreePage_term_id"]', // 1
        termHTML: '//*[@data-testid="settingCardAgreePage_termHTML_id"]', // 2
        agreeDocument: '//*[@data-testid="settingCardAgreePage_agreeDocument_id"]', // 3
        agreeDocumentHTML: '//*[@data-testid="settingCardAgreePage_agreeDocumentHTML_id"]', // 4
        agreeCheck: '//*[@data-testid="settingCardAgreePage_agreeCheck_id"]', // 5
        nextBtn: '//*[@data-testid="settingCardAgreePage_next_id"]', // 6
        aupayCard: '//*[@data-testid="settingCardAgreePage_aupayCard_id"]', // 7
        confirmMethod: '//*[@data-testid="settingCardAgreePage_confirmMethod_id"]', // 9
        createNewAupayCard: '//*[@data-testid="settingCardAgreePage_createNewAupayCard_id"]', // 10
        cardAgreeQA: '//*[@data-testid="settingCardAgreePage_QA_id"]', // 11
        cardAgreeConfirm: '//*[@data-testid="settingCardAgreePage_confirmCard_id"]', // 13
        serviceCreditcardUrl: 'https://s10.kabu.co.jp/pdf/Gmkpdf/service/creditcard.pdf',
        faqSiteUrl: 'https://qa.kddi-fs.com/faq/show/225?site_domain=1',
        faqKabuUrl: 'https://faq.kabu.com/s/article/k002936',

        // Card Confirm Page
        confirmHereLink: '//*[@data-testid="settingCardConfirmPage_hereLink_id"]', // 2
        confirmPasswordInput: '//*[@data-testid="settingCardConfirmPage_passwordInput_id"]', // 3
        confirmRegisterBtn: '//*[@data-testid="settingCardConfirmPage_register_id"]', // 4
        confirmCompleteMessage: '//*[@data-testid="settingCardConfirmPage_completeMessage_id"]', // 5
        kddifsUrl: 'https://www.kddi-fs.com/',

        // Card Common Register Page
        termRegister: '//*[@data-testid="settingCardCommonRegister_terms_id"]', // 1
        termHTMLRegister: '//*[@data-testid="settingCardCommonRegister_termsHTML_id"]', // 2
        agreeDocumentRegister: '//*[@data-testid="settingCardCommonRegister_agreementDocument_id"]', // 3
        agreeDocumentHTMLRegister: '//*[@data-testid="settingCardCommonRegister_agreementDocumentHTML_id"]', // 4
        agreeCheckRegister: '//*[@data-testid="settingCardCommonRegister_agreementCheck_id"]', // 5
        registerNextBtn: '//*[@data-testid="settingCardCommonRegister_next_id"]', // 6
        cardNumber: '//*[@data-testid="settingCardCommonRegister_cardNumber_id"]', // 7
        expiryDateMM: '//*[@data-testid="settingCardCommonRegister_expiryDateMM_id"]', // 8
        expiryDateYY: '//*[@data-testid="settingCardCommonRegister_expiryDateYY_id"]', // 9
        securityCode: '//*[@data-testid="settingCardCommonRegister_securityCode_id"]', // 10
        inputPasswordRegister: '//*[@data-testid="settingCardCommonRegister_password_id"]', // 11
        registerBtn: '//*[@data-testid="settingCardCommonRegister_register_id"]', // 12

        // Card Common UnRegister Page
        creditCardChange: '//*[@data-testid="settingCardCommonUnRegister_creditCardChange_id"]/div[1]', // 1
        cardType: '//*[@data-testid="settingCardCommonUnRegister_cardType_id"]', // 2
        unRegisterPassword: '//*[@data-testid="settingCardCommonUnRegister_password_id"]', // 4
        unRegisterBtn: '//*[@data-testid="settingCardCommonUnRegister_unRegister_id"]', // 5
        reservePlanLink: '//*[@data-testid="settingCardCommonUnRegister_reservePlan_id"]/span[2]', // 6
        unRegisterClickHere: '//*[@data-testid="settingCardCommonUnRegister_here_id"]/span[2]', // 7
        reservePlan: '/mobile/reserve/inquiry/reserve-plan',
    },

    async clickItem(dataTestId: string) {
        I.waitForElement(dataTestId);
        await I.clickFixed(dataTestId);
        await I.waitFor('mediumWait');
        return this;
    },

    async goToPageCardSelect() {
        // Open Hamburger Menu
        await I.amOnPage(this.locators.cardSelect);
        await I.waitFor('mediumWait');
        return this;
    },

    async goToPageCardAgree() {
        await I.amOnPage(this.locators.cardAgree);
        await I.waitFor('mediumWait');
        I.seeElement(this.locators.term);
        return this;
    },

    async goToPageCardConfirm() {
        await this.goToPageCardAgree();
        // Click agree checkbox
        await I.swipeElementDirection('up', 'body');
        await this.clickItem(this.locators.agreeCheck);
        // Click confirm btn
        await this.clickItem(this.locators.nextBtn);
        await I.amOnPage(this.locators.cardConfirm);
        await I.waitFor('mediumWait');
        return this;
    },

    async goToPageCardCommonRegister() {
        await this.goToPageCardSelect();
        await this.clickItem(this.locators.mitsubishiUFJCardRegister);
        await I.amOnPage(this.locators.cardCommonRegister);
        await I.waitFor('mediumWait');
        return this;
    },

    async goToPageCardCommonUnRegister() {
        // Open Hamburger Menu
        await this.clickItem(this.locators.hamburgerMenu);
        // Open Nisa Menu
        await this.clickItem(this.locators.nisaMenu);
        // Go to Card Select
        await this.clickItem(this.locators.creditCard);
        await I.amOnPage(this.locators.cardCommonUnRegister);
        await I.waitFor('mediumWait');
        return this;
    },

    async clickExternalUrl(xpath: string, urlCompare: string) {
        await this.clickItem(xpath);
        await I.waitFor('mediumWait');
        await I.activateApp();
        const externalUrl = await I.getLocalStorage(LAST_EXTERNAL_URL);
        I.say(externalUrl);
        I.assertEqual(externalUrl, urlCompare, 'URL is not matching');
        return this;
    },

    async clickPdfUrl(xpath: string) {
        await this.clickItem(xpath);
        await pdfView.closePDFView();
    },

    // Common function fill input
    async fillInput(inputPath: string, value: string) {
        I.seeElement(inputPath);
        I.fillField(inputPath, value);
        await I.waitFor();
        return this;
    },

    async back() {
        await this.clickItem(this.locators.back);
        return this;
    },

    async closeRightModal() {
        await this.clickItem(this.locators.closeRightModal);
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name) {
        await I.saveScreenshot(name);
        await I.waitFor();
        return this;
    },
};
