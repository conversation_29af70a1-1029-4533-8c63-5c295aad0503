// newsDetail.ts

const { I, market, newsList } = inject();

export = {
  locator: {
    newsDetailContainer: '//*[@data-testid="marketNewsDetailPage_detail_id"]',
    newsDetailTitle: '//*[@data-testid="marketNewsDetailPage_detail_id"]//p',
    newsDetailContent: '//*[@data-testid="marketNewsDetailPage_content_id"]',
    backButton: '//*[@data-testid="common_back_id"]',
    shareButton: '//*[@data-testid="marketNewsDetailPage_share_id"]',
    relatedNewsContainer: '//*[@data-testid="marketNewsDetailPage_relatedNews_id"]',
    relatedNewsItem: (index: number) => `//*[@data-testid="marketNewsDetailPage_relatedNewsItem_id_${index}"]`,
    stockSymbolList: '//*[@data-testid="marketNewsDetailPage_relatedSymbolItem_index"]',
    relatedSymbolsContainer: '//*[@data-testid="marketNewsDetailPage_relatedSymbols_id"]',
    relatedSymbolItem: (index = 0) => `//*[@data-testid="marketNewsDetailPage_relatedSymbolItem_id_${index}"]`,
  },

  async navigateToNewsDetail(): Promise<string> {
    // Direct to news detail page
    await newsList.goToIndicator();
    await newsList.goToNewsTab();
    
    // Select first news item to view detail
    const newsTitle = await newsList.selectNewsItem(0);
    await I.waitFor('mediumWait');
    
    // Verify that the news detail page is reached
    I.waitForElement(this.locator.newsDetailContainer);
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/news/detail', 'URL is not matching');
    
    return newsTitle;
  },
  
  async verifyNewsDetailPage(expectedTitle: string): Promise<void> {
    I.waitForElement(this.locator.newsDetailContainer);
    const currentUrl = await I.grabCurrentUrl();
    I.assertContain(currentUrl, '/mobile/market/news/detail', 'URL is not matching');
  },
  
  async returnToNewsList(): Promise<void> {
    I.clickFixed(this.locator.backButton);
    await I.waitFor();
    I.waitForElement(newsList.locator.newsListContainer);
    I.assertContain(await I.grabCurrentUrl(), '/mobile/market/news', 'URL is not matching');
  },
  
  async scrollNewsContent(): Promise<void> {
    I.waitForElement(this.locator.newsDetailContainer);
    await I.swipeUpFixed(this.locator.newsDetailContainer);
    await I.waitFor('shortWait');
    await I.swipeUpFixed(this.locator.newsDetailContainer);
    await I.waitFor('shortWait');
  },
  
  async shareNews(): Promise<void> {
    I.waitForElement(this.locator.shareButton);
    I.clickFixed(this.locator.shareButton);
    await I.waitFor();
    // Take screenshot of share dialog
    I.saveScreenshot('news_detail_share_dialog.png');
  },
  
  async checkRelatedNews(): Promise<void> {
    // Scroll down to display related news
    await this.scrollNewsContent();
    
    // Check related news container
    I.waitForElement(this.locator.relatedNewsContainer);
    await I.waitFor();
    // Check first related news item
    const firstRelatedNewsItem = this.locator.relatedNewsItem(0);
    I.waitForElement(firstRelatedNewsItem);
    
    // Get first related news item title
    const relatedNewsTitle = await I.grabTextFrom(`${firstRelatedNewsItem}//p`);
    
    // Click on related news item
    I.clickFixed(firstRelatedNewsItem);
    await I.waitFor();
    
    // Verify that the related news detail page is reached
    I.waitForElement(this.locator.newsDetailContainer);
    const detailTitle = await I.grabTextFrom(this.locator.newsDetailTitle);
    I.assertContain(detailTitle, relatedNewsTitle, 'Related news title should match');
  },
  

};