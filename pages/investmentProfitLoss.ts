import { CompletedRequest } from 'wdio-intercept-service';
const { I } = inject();

export = {
    async sortButtonView({
        dataTexts,
        dataSelector,
        baseDataList,
        sortButtonSelector,
    }: {
        dataTexts: string[];
        dataSelector?: string;
        baseDataList?: string[];
        sortButtonSelector?: string;
    }): Promise<void> {
        const _dataSelector = dataSelector ?? '//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]/div';
        // For API verification, we can use this data for comparison
        const profitLossAllList = baseDataList ?? ['7', '6', '5', '4', '3', '2', '1'];
        // Wait for the page to be loaded
        I.waitForElement(sortButtonSelector ?? '//*[@data-testid="investmentProfitLoss_sort_id"]');

        // Grab all data elements before sorting
        const initialDataTexts = dataTexts
            .filter((_, index) => index % 2 !== 0)
            .map((item) => item.replace(/[+,円]/g, ''));

        profitLossAllList.forEach((item, index) => {
            I.assertEqual(item, initialDataTexts[index], 'Item should match');
        });
        await I.waitFor();
        // Click the sort button
        const _sortButtonSelector = sortButtonSelector ?? '//*[@data-testid="investmentProfitLoss_sort_id"]';
        await I.clickFixed(_sortButtonSelector);
        await I.waitFor('mediumWait');
        // Grab all data elements after sorting
        const sortedDataTexts = (await I.grabTextFromAll(`${_dataSelector}//p[1]`))
            .filter((_, index) => index % 2 !== 0)
            .map((item) => item.replace(/[+,円]/g, ''));

        profitLossAllList
            .sort((a, b) => +a - +b)
            .forEach((item, index) => {
                I.assertEqual(item, sortedDataTexts[index], 'Item should match');
            });

        await I.clickFixed(_sortButtonSelector);
    },
    async productCardView({
        dataTexts,
        dataFilter,
        dataSelector,
        cancelButtonSelector,
        apiNames,
    }: {
        dataTexts: string[];
        dataFilter?: string[];
        dataSelector?: string;
        cancelButtonSelector?: string;
        apiNames?: string[];
    }): Promise<void> {
        const _dataFilter = dataFilter ?? [
            'オプション',
            '先物',
            '国内株式信用',
            '預り金等現金',
            '投資信託',
            '米国株式現物',
            '国内株式現物',
        ];
        const investmentProductCardList = dataTexts.filter((_, index) => index % 2 === 0);
        const _dataSelector = dataSelector ?? '//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]';
        await I.setupInterceptor();
        for (let i = 0; i < investmentProductCardList.length; i++) {
            const investmentProductCardText = investmentProductCardList[i];
            await I.scrollToElement(`${_dataSelector}/div[${i + 1}]/div[1]/p[contains(text(),"${investmentProductCardText}")]`);
            await I.clickFixed(`${_dataSelector}/div[${i + 1}]/div[1]/p[contains(text(),"${investmentProductCardText}")]`);
            await I.waitFor('shortWait');
            const profitLossText = await I.grabTextFrom(`//*[@data-testid="portfolio_productName_id"]`);
            I.assertEqual(
                profitLossText,
                _dataFilter.find((item) => item === investmentProductCardText),
                'Profit loss should match',
            );
            await I.waitFor();

            if (apiNames) {
                try {
                    const allRequests = await I.getRequests();
                    const index = allRequests.findIndex((request) => {
                        return apiNames.some((apiName) => request.url.includes(apiName));
                    });
                    const balanceRequest = allRequests[index] as CompletedRequest;

                    I.assertEqual(balanceRequest?.response.statusCode, 200, 'Status code should be 200');
                    I.assertContain(balanceRequest?.url, apiNames[index], 'URL should match');
                } catch (error) {
                    console.warn('Error fetching requests:', error);
                }
            }
            await I.scrollToElement(cancelButtonSelector ?? '//*[@data-testid="investmentProfitLoss_back_id"]');
            await I.clickFixed(cancelButtonSelector ?? '//*[@data-testid="investmentProfitLoss_back_id"]');
        }
    },
    async checkExternalLink(locator: string): Promise<void> {
        const detailExplanation = locate(
            '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div/div/div[2]/div/p/a',
        );

        const isAndroid = await I.grabIsAndroid();
        let url = '';
        if (isAndroid) {
            I.click(detailExplanation);
            url = await I.grabCurrentUrl();
        } else {
            I.switchToNative();
            I.click(locator);
            await I.waitFor('extraLongWait');
            I.switchToWeb();
            await I.waitFor('extraLongWait');
            url = await I.grabCurrentUrl();
        }
        console.log(url);

        I.activateApp();
        await I.waitFor('mediumWait');
        I.assertContain(url, 'mobile/app/guide/index.html#performance', 'URL should match');
    },
    async previousNextButtonView({
        dataSelector,
        titles,
    }: {
        dataSelector?: string;
        titles?: string[];
        cancelButtonSelector?: string;
    }): Promise<void> {
        const _dataSelector = dataSelector ?? '//*[@data-testid="investmentProfitLoss_investmentProductCart_id"]/div[1]';
        I.seeElement(_dataSelector);
        await I.waitFor();
        await I.scrollToElement(_dataSelector);
        await I.tapLocationOfElement({ xpath: _dataSelector });
        await I.waitFor();

        const _titles = titles ?? [
            '米国株式現物',
            '投資信託',
            '国内株式信用',
            '先物',
            'オプション',
            '国内株式現物',
        ];
        const titleLocator = '//*[@data-testid="portfolio_productName_id"]';
        const checkTitlePage = async (index: number, direction: string) => {
            const titlePage = await I.grabTextFrom({ xpath: titleLocator });
            I.assertEqual(titlePage, _titles[index], 'Title should match');
            await I.scrollToElement(direction === 'left' ? '//*[@data-testid="portfolio_back_id"]' : '//*[@data-testid="portfolio_next_id"]');
            await I.clickFixed(direction === 'left' ? '//*[@data-testid="portfolio_back_id"]' : '//*[@data-testid="portfolio_next_id"]');
            await I.waitFor();

            if (direction === 'left') {
                if (index > 0) {
                    index -= 1;
                    await checkTitlePage(index, direction);
                } else {
                    const titlePage = await I.grabTextFrom(titleLocator);
                    I.assertEqual(
                        titlePage,
                        _titles[direction === 'left' ? _titles.length - 1 : 0],
                        'Title should match',
                    );
                }
            } else {
                if (index === _titles.length - 2) return;
                if (index === _titles.length - 1) {
                    index = 0;
                } else {
                    index += 1;
                }
                await checkTitlePage(index, direction);
            }
        };
        await checkTitlePage(_titles.length - 1, 'left');
        await checkTitlePage(_titles.length - 1, 'right');
    },
};
