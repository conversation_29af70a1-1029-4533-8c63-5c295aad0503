const { I } = inject();

export = {
    locators: {
        back: '//*[@data-testid="common_back_id"]',
        hamburgerMenu: '//*[@data-testid="common_menu_id"]',
        nisaMenu: '//*[@data-testid="menu_nisa_id"]',
        nisaQA: '//*[@data-testid="nisaMenu_nisaQA_id"]',
        accountStatus: '//*[@data-testid="nisaMenu_accountStatus_id"]',
        symbolSearchA2: '//*[@data-testid="nisaMenu_symbolSearchA2_id"]',
        positionInquiryA3: '//*[@data-testid="nisaMenu_positionInquiryA3_id"]',
        orderInquiry: '//*[@data-testid="nisaMenu_orderInquiry_id"]',
        distributionHistory: '//*[@data-testid="nisaMenu_distributionHistory_id"]',
        petitStock: '//*[@data-testid="nisaMenu_petitStock_id"]',
        investmentTrust: '//*[@data-testid="nisaMenu_investmentTrust_id"]',
        reservePlan: '//*[@data-testid="nisaMenu_reservePlan_id"]',
        reserveCalendar: '//*[@data-testid="nisaMenu_reserveCalendar_id"]',
        reserveHistory: '//*[@data-testid="nisaMenu_reserveHistory_id"]',
        creditCard: '//*[@data-testid="nisaMenu_creditCard_id"]',
        symbolSearchC1: '//*[@data-testid="nisaMenu_symbolSearchC1_id"]',
        positionInquiryC2: '//*[@data-testid="nisaMenu_positionInquiryC2_id"]',
        tradingHistoryC3: '//*[@data-testid="nisaMenu_tradingHistoryC3_id"]',
        symbolSearchD1: '//*[@data-testid="nisaMenu_symbolSearchD1_id"]',
        positionInquiryD2: '//*[@data-testid="nisaMenu_positionInquiryD2_id"]',
        tradingHistoryD3: '//*[@data-testid="nisaMenu_tradingHistoryD3_id"]',
        symbolSearchE1: '//*[@data-testid="nisaMenu_symbolSearchE1_id"]',
        positionInquiryE2: '//*[@data-testid="nisaMenu_positionInquiryE2_id"]',
        tradingHistoryE3: '//*[@data-testid="nisaMenu_tradingHistoryE3_id"]',
        fundSearch: '//*[@data-testid="nisaMenu_fundSearch_id"]',
        positionInquiryF2: '//*[@data-testid="nisaMenu_positionInquiryF2_id"]',
        tradingHistoryF3: '//*[@data-testid="nisaMenu_tradingHistoryF3_id"]',
        url: '/mobile/nisa/menu',
        nisaQAUrl:
            'https://faq.kabu.com/s/notice/a0W2t00000AI8ZDEA1/%E6%96%B0nisa%E3%81%AB%E9%96%A2%E3%81%99%E3%82%8B%E3%81%8A%E5%95%8F%E3%81%84%E5%90%88%E3%82%8F%E3%81%9B',
    },

    async clickItem(dataTestId: string) {
        I.waitForElement(dataTestId);
        await I.scrollToElement(dataTestId);
        await I.clickFixed(dataTestId);
        await I.waitFor();
        return this;
    },

    async goToPage() {
        // Open Hamburger Menu
        await this.clickItem(this.locators.hamburgerMenu);
        await I.waitFor();
        // Open Nisa Menu
        await this.clickItem(this.locators.nisaMenu);
        await I.amOnPage(this.locators.url);
        await I.waitFor('mediumWait');
        I.seeElement(this.locators.nisaQA);
        return this;
    },

    async back() {
        await this.clickItem(this.locators.back);
        return this;
    },

    async compareUrl(urlCompare: string) {
        I.assertContain(await I.grabCurrentUrl(), urlCompare, 'URL is not matching');
        await I.waitFor();
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name) {
        await I.saveScreenshot(name);
        await I.waitFor();
        return this;
    },
};
