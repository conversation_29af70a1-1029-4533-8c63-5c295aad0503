const { I } = inject();
export default {
    prefix: {
        input: '1618_transfer_cash_input.Test_No',
        confirm: '1628_transfer_cash_confirm.Test_No',
        complete: '1632_transfer_cash_complete.Test_No',
    },
    locators: {
        tabButton: '//div[@data-testid="transferCashInput_tab_id"]/div/button',
        groupInputNumber: '//input[@data-testid="groupInputNumber_input_id"]',
        noteCautionary: '//div[@data-testid="transferCashInput_cautionary_id"]',
        securitySelectRadioItem:
            '//div[@data-testid="transferCashInput_securitySelect_id_CASH_TRANSFER_INSTRUCTION_AZUKARIKIN_SAKIMONOSYOUKOKIN"]',
        marginSelectRadioItem:
            '//div[@data-testid="transferCashInput_marginSelect_id_CASH_TRANSFER_INSTRUCTION_SAKIMONOSYOUKOKIN_AZUKARIKIN"]',
    },
    urls: {
        input: '/mobile/transfer/transfercash/cashinput',
        confirm: '/mobile/transfer/transfercash/cashconfirm',
        complete: '/mobile/transfer/transfercash/cashcomplete',
    },

    async goToCashInputPage() {
        I.amOnPage(this.urls.input);
        I.waitForText('お預り金振替', 5, 'p');
        I.seeInCurrentUrl(this.urls.input);
    },

    async focusAndFillField(locator: CodeceptJS.LocatorOrString, value?: string) {
        await I.seeAndClickElement(locator);
        await I.waitFor();
        I.fillField(locator, value || '123');
        I.blur(locator);
        await I.waitFor();
    },
    async goToCashConfirmPage() {
        I.amOnPage(this.urls.input);
        I.waitForText('お預り金振替', 5, 'p');
        await I.scrollAndClick(this.locators.groupInputNumber);
        I.fillField(this.locators.groupInputNumber, '1000');
        await I.waitFor('shortWait');
        I.blur(this.locators.groupInputNumber);
        const transferCashInput_confirmScreen_id = '//*[@data-testid="transferCashInput_confirmScreen_id"]';
        I.waitForElement(transferCashInput_confirmScreen_id, 2);
        I.scrollAndClick(transferCashInput_confirmScreen_id);
        await I.waitFor();
    },
    async goToCashCompletePage() {
        await this.goToCashConfirmPage();
        await I.waitFor('shortWait');
        const passwordInput = '//*[@data-testid="transferCashConfirm_password_id"]';
        await I.waitForElement(passwordInput, 1);
        I.scrollAndFill(passwordInput, '123@abc'); 
        const transferCashInput_confirmScreen_id = '//*[@data-testid="transferCashConfirm_transfer_id"]';
        I.waitForElement(transferCashInput_confirmScreen_id, 2);
        I.scrollAndClick(transferCashInput_confirmScreen_id);
        await I.waitFor();
    },
};
