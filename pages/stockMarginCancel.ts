import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from "../const/constant";

const { I } = inject();

export = {
    locator: {
        marginConfirmCancelButton: '//*[@data-testid="marginCancel_confirmCancel_id"]',
        marginConfirmCancelReceiptButton: '//*[@data-testid="marginCancelReceipt_confirmCancel_id"]'
    },
    urls: {
        marginCancelOrder: '/mobile/trade/margin/cancel',
        marginCancelReceiptDelivery: '/mobile/trade/margin/cancel-receipt-delivery'
    },
    inputValues: {
        password: '111111',
    },
    takeScreenshot: {
        marginCancel: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingCancel}_${suffix}.png`);
        },
        marginCancelReceiptDelivery: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingCancelReceiptDelivery}_${suffix}.png`);
        },
    },
    async goToMarginCancel(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginCancel, SESSION_STORAGE_VALUE.tradeMarginCancel());
        I.amOnPage(this.urls.marginCancelOrder);
        await I.waitFor('mediumWait');
        I.waitForText('信用取消', 3, 'body');
        I.waitForElement(this.locator.marginConfirmCancelButton);
    },
    async goToMarginCancelReceiptDelivery(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginCancelReceiptDelivery, SESSION_STORAGE_VALUE.tradeMarginCancelReceiptDelivery());
        I.amOnPage(this.urls.marginCancelReceiptDelivery);
        await I.waitFor('mediumWait');
        I.waitForText('品受・品渡取消', 3, 'body');
        I.waitForElement(this.locator.marginConfirmCancelReceiptButton);
    },
};
