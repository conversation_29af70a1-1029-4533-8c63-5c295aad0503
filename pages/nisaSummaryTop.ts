const { I } = inject();

export = {
    locators: {
        nisaSummaryTop: '//*[@data-testid="nisaMenu_accountStatus_id"]',
        back: '//*[@data-testid="common_back_id"]',
        hamburgerMenu: '//*[@data-testid="common_menu_id"]',
        nisaMenu: '//*[@data-testid="menu_nisa_id"]',
        profitLoss: '//*[@data-testid="nisaSummaryTop_profitLoss_id"]', // 1
        viewInvestmentLimitModal: '//*[@data-testid="nisaSummaryTop_viewInvestmentLimitModal_id"]', // 6
        planProductTab: '//*[@data-testid="nisaSummaryTop_planProductTab_id"]', // 20
        reservePlanCardFund: '//*[@data-testid="nisaSummaryTop_reservePlanCard_id_1_0"]', // 23
        reservePlanCardPetit: '//*[@data-testid="nisaSummaryTop_reservePlanCard_id_20_0"]', // 23
        seeMore: '//*[@data-testid="nisaSummaryTop_seeMore_id"]', // 29
        reservePlanCardMultiPay: '//*[@data-testid="nisaSummaryTop_reservePlanCardMultiPay_id"]', // 30
        nisaInvestmentLimit: '//*[@data-testid="nisaSummaryTop_nisaInvestmentLimit_id"]', // 31
        url: '/mobile/nisa/summary/top',
        reservePlanFund: '/mobile/reserve/inquiry/reserve-plan/fund',
        reservePlanPetit: '/mobile/reserve/inquiry/reserve-plan/petit',
        profitLossTab: '//*[@id="profit-and-loss"]',
        investmentLimitTab: '//*[@id="investment-limit"]',
        closeModal: '//button[@aria-label="cancel-btn"]',
    },

    async clickItem(dataTestId: string) {
        I.waitForElement(dataTestId);
        I.clickFixed(dataTestId);
        await I.waitFor();
        return this;
    },

    async goToPage() {
        // Open Hamburger Menu
        this.clickItem(this.locators.hamburgerMenu);
        // Open Nisa Menu
        this.clickItem(this.locators.nisaMenu);
        // Go to Nisa Summary Top
        this.clickItem(this.locators.nisaSummaryTop);
        await I.amOnPage(this.locators.url);
        await I.waitFor();
        I.seeElement(this.locators.nisaInvestmentLimit);
        return this;
    },

    async back() {
        this.clickItem(this.locators.back);
        return this;
    },

    async compareUrl(urlCompare: string) {
        I.assertContain(await I.grabCurrentUrl(), urlCompare, 'URL is not matching');
        await I.waitFor();
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name) {
        I.saveScreenshot(name);
        await I.waitFor();
        return this;
    },
};
