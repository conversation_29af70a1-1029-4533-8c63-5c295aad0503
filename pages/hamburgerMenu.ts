import { PAGE_URL, SCREENSHOT_PREFIX } from "../const/constant";

const { I } = inject();

export = {
    locators: {
        menu: '$common_menu_id',
        close: '$menu_close_id', // 1.閉じるボタン
        accessStatus: '$menu_accessStatus_id', // 2.資産状況
        symbolSearch: '$menu_symbolSearch_id', // 7.銘柄検索
        creditCardReserve: '$menu_creditCardReserve_id', // 8.クレカ積立
        future: '$menu_future_id', // 9.先物
        option: '$menu_option_id', // 10.オプション
        nisa: '$menu_nisa_id', // 11.NISA
        marginRoboAd: '$menu_marginRoboAd_id', // 12.信用ロボアド
        reserve: '$menu_reserve_id', // 13.信用積立
        positionInquiry: '$menu_positionInquiry_id', // 15.残高照会(売却)
        orderStatus: '$menu_orderStatus_id', // 16.注文約定照会
        tradeHistory: '$menu_tradeHistory_id', // 17.取引履歴
        availableAmount: '$menu_availableAmount_id', // 18.買付出金可能額
        depositRequest: '$menu_depositRequest_id', // 19.入金・出金依頼
        confirm: '$menu_confirm_id', // 20.入出金確認
        depositTransfer: '$menu_depositTransfer_id', // 21.お預り金振替
        investmentTrustTransfer: '$menu_investmentTrustTransfer_id', // 22.投資信託振替
        domesticStock: '$menu_domesticStock_id', // 23.国内株式振替
        specificAccount: '$menu_specificAccount_id', // 24.特定・一般口座振替
        procedure: '$menu_procedure_id', // 25.お手続き
        easyContract: '$menu_easyContract_id', // 26.らくらく電子契約
        contactNotification: '$menu_contactNotification_id', // 27.連絡・通知先設定
        auIDRegister: '$menu_auIDRegister_id', // 28.auID 登録・変更
        moneyConnect: '$menu_moneyConnect_id', // 29.マネーコネクト
        appSetting: '$menu_appSetting_id', // 30.アプリ設定
        pointStatus: '$menu_pointStatus_id', // 34b.ポイント利用状況
        pointSetting: '$menu_pointSetting_id', // 34c.積立へのポイント利用設定
        privacyPolicy: '$menu_privacyPolicy_id', // 35.プライバシーポリシー
        terms: '$menu_terms_id', // 36.使用許諾
        logout: '$menu_logout_id', // 37.ログアウト
        senOPNavi: '$menu_senOPNavi_id', // 38.先 OP ナビ
        futureBoard: '$menu_futureBoard_id', // 39.先物ボードフラッシュ
        opBoard: '$menu_opBoard_id', // 40.OP ボードフラッシュ
        mitsubishiUFJSecurities: '$menu_mitsubishiUFJSecurities_id', // 47.三菱 UFJ e スマート証券 FX
        click365: '$menu_click365_id', // 48.くりっく 365
        exchangeCFD: '$menu_exchangeCFD_id', // 49.取引所 CFD
        kabucomiDeCo: '$menu_kabucomiDeCo_id', // 50.カブコムの iDeCo
        topicsCalendar: '$menu_topicsCalendar_id', // 52.abucom カレンダー
        licenseInfo: '$menu_licenseInfo_id', // 55.ライセンス情報等
        contact: '$menu_contact_id', // 56.お問い合わせ
        foreignDeposit: '$menu_foreignDeposit_id', // 58.外貨お預り金振替
        referralForFriends: '$menu_referralForFriends_id', // 60.ご家族・ご友人紹介
        pcVersionTrading: '$menu_pcVersionTrading_id', // 61.C 版お取引サイト
        securitySetting: '$menu_securitySetting_id', // 62.セキュリティ設定
        kabuBoardFlash: '$menu_kabuBoardFlash_id', // 63.カブボードフラッシュ
        domesticCashStock: '$menu_domesticCashStock_id', // 64.国内現物株
        marginTrading: '$menu_marginTrading_id', // 65.信用取引
        usStock: '$menu_usStock_id', // 66.米国株
        investmentTrust: '$menu_investmentTrust_id', // 67.投資信託
        changeVariousPassword: '$menu_changeVariousPassword_id', // 68.各種パスワード変更
        distributionHistory: '$menu_distributionHistory_id', // 69.配当金・分配金履歴
    },

    takeScreenshot: {
        hamburgerMenu: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.hamburgerMenu}_${suffix}.png`);
        },
    },

    async openMenu(): Promise<void> {
        I.amOnPage(PAGE_URL.search);
        await I.waitFor();
        const menuBtn = '//*[@data-testid="common_menu_id"]';
        I.waitForElement(menuBtn);
        await I.clickFixed(menuBtn);
        await I.waitFor();
        I.seeElement('//*[@data-testid="common_rightSlide_close_id"]');
        I.waitForText('メニュー', 3, 'body');
    },
    async clickMenuItem(dataTestId: string): Promise<void> {
        const menuItem = dataTestId;
        I.waitForElement(menuItem);
        await I.scrollToElement(menuItem);
        await I.clickFixed(menuItem);
        await I.waitFor('mediumWait');
    },
    async compareUrl(urlCompare: string): Promise<void> {
        const currentUrl = await I.grabCurrentUrl();
        await I.waitFor();
        I.assertContain(currentUrl, urlCompare, 'URL is not matching');
        await I.waitFor('mediumWait');
    },

    // common
    async goToMenu() {
        const menu = locate(this.locators.menu);

        I.amOnPage('/');
        await I.waitFor('mediumWait');
        I.waitForElement(menu);
        await I.clickFixed(menu);
        await I.waitFor('mediumWait');
    },

    async clickItem(locator: CodeceptJS.LocatorOrString) {
        I.waitForElement(locator);
        await I.clickFixed(locator);
        await I.waitFor('mediumWait');
    },
};
