const { I } = inject();

export = {
        // Locators
    locators: {
        changeLoginPasswordButton: '//*[@data-testid="passwordList_changeLoginPassword_id"]',
        changeWithdrawalPasswordButton: '//*[@data-testid="passwordList_changeWithdrawalPassword_id"]',
        resetWithdrawalPasswordButton: '//*[@data-testid="passwordList_resetWithdrawalPassword_id"]',
        pageDescription: '//*[@data-testid="common_header_title_id"]',
    },

    // URLs to check
    urls: {
        passwordList: '/mobile/setting/password/list',
        portfolio: '/mobile/mypage/portfolio',
        changeLoginPassword: '/mobile/setting/password/change-login-password',
        changeWithdrawalPassword: '/mobile/setting/password/change-withdrawal-password',
        resetWithdrawalPassword: '/mobile/setting/password/reset-withdrawal-password',
    },

    // Common function to test button click and navigation
    async testButtonNavigation(buttonSelector, targetUrls, screenshotName) {
        await I.waitFor('mediumWait');
        I.seeElement(buttonSelector);

        // Click the button with specified selector
        I.click(buttonSelector);
        await I.waitFor('mediumWait');

        // Get current URL to verify and take appropriate screenshot
        let currentUrl = await I.grabCurrentUrl();
        if (currentUrl.includes(this.urls.passwordList)) {
            I.tapLocationOfElement(buttonSelector);
            await I.waitFor('longWait');
        }
        currentUrl = await I.grabCurrentUrl();

        // Check if we're on one of the expected pages
        const isExpectedUrl = targetUrls.some((url) => currentUrl.includes(url));
        if (isExpectedUrl) {
            I.say(`Navigated to expected page: ${currentUrl}`);
            I.saveScreenshot(screenshotName);
            await I.waitFor();
            await I.amOnPage(this.urls.passwordList);
        } else {
            throw new Error(`Unexpected URL: ${currentUrl}`);
        }
    },
};
