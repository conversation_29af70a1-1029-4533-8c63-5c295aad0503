import { PAGE_URL } from "../const/constant";

const { I } = inject();

/**
 * Page object representing the Security Setting Top page
 */
export = {
    /**
     * Locators for Security Setting Top page elements
     */
    locator: {
        // Common elements
        commonHeaderTitle: '//*[@data-testid="common_header_title_id"]',
        commonBackId: '//*[@data-testid="common_back_id"]',

        // Security Setting Top elements
        caution: '//*[@data-testid="settingSecurityTopPage_caution_id"]',
        cautionContent: '//*[@data-testid="settingSecurityTopPage_caution_id"]/div[2]',
        resendMail: '//*[@data-testid="settingSecurityTopPage_resendMail_id"]/span[2]',
        toastMessage: '//*[@id="chakra-toast-manager-top"]/li/div/div',
        change: '//*[@data-testid="settingSecurityTopPage_change_id"]',
        changeRegister: '//*[@data-testid="settingSecurityTopPage_changeRegister_id"]',
        loginNotifySwitch: '//*[@data-testid="settingSecurityTopPage_switch_id"]',
        changeRegistration: '//*[@data-testid="settingSecurityTopPage_changeRegistration_id"]',
        loginNotifyStatus: '//*[@id="__next"]/div/div[2]/div/div[1]/table[2]/tbody/tr[1]/td[2]/div/p',
        deleteAuthApp: '//*[@data-testid="settingSecurityTopPage_delete_id"]',
        confirmDeleteBtn: '//button[contains(text(), "削除する")]',
        registerAuthApp: '//*[@data-testid="settingSecurityTopPage_register_id"]',

        // Platform specific elements
        // iOS elements
        iosConnectionElement: '//XCUIElementTypeOther[@name="Connection:" and @value="Login-Authentication"]',
        iosSecondCellElement: '//XCUIElementTypeCollectionView/XCUIElementTypeCell[2]',
        iosLoginButton: '//XCUIElementTypeButton[@name="Login"]',
        iosCloseButton: '//XCUIElementTypeButton[@name="アプリへ戻る"]',

        // Android elements
        androidConnectionElement: '//android.view.View[@resource-id="connection"]',
        androidSecondCellElement:
            '//android.widget.CheckedTextView[@resource-id="android:id/text1" and @text="Withdrawal-Authentication"]',
        androidLoginButton: '//android.widget.Button[@resource-id="loginBtn"]',
        androidCloseButton: '//android.widget.Button[@text="アプリへ戻る"]',
    },

    /**
     * Navigate to Security Setting Top page
     */
    async goToSecuritySettingTopPage() {
        I.amOnPage(PAGE_URL.settingSecurityTop);
        await I.waitFor('mediumWait');
        I.see(`セキュリティ設定`, this.locator.commonHeaderTitle);
        await I.waitFor();
        return this;
    },

    /**
     * Click on the caution message element
     */
    async clickCaution() {
        await I.clickFixed(this.locator.caution);
        await I.waitFor();
        return this;
    },

    /**
     * Click on the resend mail button
     */
    async clickResendMail() {
        await I.clickFixed(this.locator.resendMail);
        await I.waitFor();
        return this;
    },

    /**
     * Click on the change button and handle context switching, then perform authentication steps
     */
    async clickChangeAndSwitchContext() {
        // Get current context before clicking
        const currentContext = await I.grabContext();
        I.say(`Current context before clicking: ${currentContext}`);

        // Click on the change button
        await I.clickFixed(this.locator.change);
        await I.waitFor('mediumWait');
        await I.waitFor('extraLongWait'); // Wait for the new context to be available

        // Switch to native context to interact with the native UI
        I.switchToNative();
        await I.waitFor();

        // Check if iOS or Android platform
        const isIos = await I.grabIsIOS();
        if (isIos) {
            I.say('Detected iOS platform');

            // Step 1: Click on Connection element
            I.say('Clicking on Connection element');
            try {
                await I.click(this.locator.iosConnectionElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_connection_click_ios.png');

                // Step 2: Click on the second cell
                I.say('Clicking on second cell');
                await I.click(this.locator.iosSecondCellElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_second_cell_click_ios.png');

                // Step 3: Click on Login button
                I.say('Clicking on Login button');
                await I.click(this.locator.iosLoginButton);
                await I.waitFor('mediumWait');
                await I.waitFor('extraLongWait'); // Wait for UI to respond

                // Step 4: Click on Close button
                I.say('Clicking on Close button on iOS');
                await I.click(this.locator.iosCloseButton);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_close_click_ios.png');
            } catch (error) {
                I.say(`Error during iOS interaction sequence: ${error.message}`);
                I.saveScreenshot('ios_interaction_error.png');
            }
        } else {
            I.say('Detected Android platform');

            // Step 1: Click on Connection element
            I.say('Clicking on Connection element on Android');
            try {
                await I.click(this.locator.androidConnectionElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_connection_click_android.png');

                // Step 2: Click on the second cell
                I.say('Clicking on second cell on Android');
                await I.click(this.locator.androidSecondCellElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_second_cell_click_android.png');

                // Step 3: Click on Login button
                I.say('Clicking on Login button on Android');
                await I.click(this.locator.androidLoginButton);
                await I.waitFor('mediumWait');
                await I.waitFor('extraLongWait'); // Wait for UI to respond

                // Step 4: Click on Close button
                I.say('Clicking on Close button on Android');
                await I.click(this.locator.androidCloseButton);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_close_click_android.png');
            } catch (error) {
                I.say(`Error during Android interaction sequence: ${error.message}`);
                I.saveScreenshot('android_interaction_error.png');
            }
        }

        // Switch back to webview
        I.switchToWeb();
        await I.waitFor();

        return this;
    },

    /**
     * Click on the change/register button and verify navigation
     */
    async clickChangeRegister() {
        await I.clickFixed(this.locator.changeRegister);
        await I.waitFor('mediumWait');
        I.seeInCurrentUrl('/setting/security/email-registration');
        return this;
    },

    /**
     * Click on the back button
     */
    async clickBack() {
        await I.clickFixed(this.locator.commonBackId);
        await I.waitFor();
        return this;
    },

    /**
     * Click on the login notify switch
     */
    async clickLoginNotifySwitch() {
        await I.clickFixed(this.locator.loginNotifySwitch);
        await I.waitFor('mediumWait');
        return this;
    },

    /**
     * Get login notify status text
     */
    async getLoginNotifyStatus() {
        return await I.grabTextFrom(this.locator.loginNotifyStatus);
    },

    /**
     * Click on change registration button and handle context switching
     * (Same flow as clickChangeAndSwitchContext)
     */
    async clickChangeRegistrationAndSwitchContext() {
        // Get current context before clicking
        const currentContext = await I.grabContext();
        I.say(`Current context before clicking: ${currentContext}`);

        // Click on the change registration button
        await I.clickFixed(this.locator.changeRegistration);
        await I.waitFor('mediumWait');
        await I.waitFor('extraLongWait'); // Wait for the new context to be available

        // Switch to native context to interact with the native UI
        I.switchToNative();
        await I.waitFor();

        // Check if iOS or Android platform
        const isIos = await I.grabIsIOS();
        if (isIos) {
            I.say('Detected iOS platform');

            // Step 1: Click on Connection element
            I.say('Clicking on Connection element');
            try {
                await I.click(this.locator.iosConnectionElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_connection_click_ios_item19.png');

                // Step 2: Click on the second cell
                I.say('Clicking on second cell');
                await I.click(this.locator.iosSecondCellElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_second_cell_click_ios_item19.png');

                // Step 3: Click on Login button
                I.say('Clicking on Login button');
                await I.click(this.locator.iosLoginButton);
                await I.waitFor('mediumWait');
                await I.waitFor('extraLongWait'); // Wait for UI to respond

                // Step 4: Click on Close button
                I.say('Clicking on Close button on iOS');
                await I.click(this.locator.iosCloseButton);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_close_click_ios_item19.png');
            } catch (error) {
                I.say(`Error during iOS interaction sequence: ${error.message}`);
                I.saveScreenshot('ios_interaction_error_item19.png');
            }
        } else {
            I.say('Detected Android platform');

            // Step 1: Click on Connection element
            I.say('Clicking on Connection element on Android');
            try {
                await I.click(this.locator.androidConnectionElement);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_connection_click_android_item19.png');

                // Step 2: Click on the second cell
                I.say('Clicking on second cell on Android');
                await I.click(this.locator.androidSecondCellElement);
                await I.waitFor('longWait'); // Wait for UI to respond
                I.saveScreenshot('after_second_cell_click_android_item19.png');

                // Step 3: Click on Login button
                I.say('Clicking on Login button on Android');
                await I.click(this.locator.androidLoginButton);
                await I.waitFor('mediumWait');
                await I.waitFor('extraLongWait'); // Wait for UI to respond

                // Step 4: Click on Close button
                I.say('Clicking on Close button on Android');
                await I.click(this.locator.androidCloseButton);
                await I.waitFor(); // Wait for UI to respond
                I.saveScreenshot('after_close_click_android_item19.png');
            } catch (error) {
                I.say(`Error during Android interaction sequence: ${error.message}`);
                I.saveScreenshot('android_interaction_error_item19.png');
            }
        }

        // Switch back to webview
        I.switchToWeb();
        await I.waitFor();

        return this;
    },

    /**
     * Click on the delete authentication app button and confirm deletion
     */
    async clickDeleteAuthAppAndConfirm() {
        // Click on the delete button
        await I.clickFixed(this.locator.deleteAuthApp);
        await I.waitFor('mediumWait');

        // Check if the confirmation button exists
        I.seeElement(this.locator.confirmDeleteBtn);

        // Take a screenshot before confirming
        I.saveScreenshot('Item_22_Delete_Auth_App_Confirmation.png');

        // Click on the confirm delete button
        await I.clickFixed(this.locator.confirmDeleteBtn);
        await I.waitFor('extraLongWait');
        await I.waitFor('extraLongWait'); // Wait for deletion process to complete

        return this;
    },

    /**
     * Click on the register authentication app button
     */
    async clickRegisterAuthApp() {
        await I.clickFixed(this.locator.registerAuthApp);
        await I.waitFor('mediumWait');
        I.seeInCurrentUrl('/setting/security/app-registration');
        return this;
    },

    /**
     * Take a screenshot with a specified name
     * @param {string} name - The name of the screenshot
     */
    takeScreenshot(name) {
        I.saveScreenshot(name);
        return this;
    },

    /**
     * Check if the caution content is displayed (display: block)
     */
    async isCautionContentDisplayed() {
        const displayStyle = await I.grabCssPropertyFrom(this.locator.cautionContent, 'display');
        I.assertEqual(displayStyle, 'block', 'Caution content should be displayed with display: block');
        return displayStyle === 'block';
    },

    /**
     * Check if the toast message is visible
     */
    async isToastMessageVisible() {
        I.seeElement(this.locator.toastMessage);
        return true;
    },
};
