const { I } = inject();

export = {
    locators: {
        currentPasswordInput: '//*[@data-testid="changeLoginPasswordPage_currentPassword_id"]/div/input',
        newPasswordInput: '//*[@data-testid="changeLoginPasswordPage_newPassword_id"]/div/input',
        newPasswordConfirmInput: '//*[@data-testid="changeLoginPasswordPage_newPasswordConfirm_id"]/div/input',
        pageDescription: '//*[@id="__next"]/div/div[2]/div/p[1]',
        descriptionSelector: '//*[@id="__next"]/div/div[2]/div/p[2]',
        submitButton: '//*[@data-testid="changeLoginPasswordPage_changeButton_id"]',
        modalSelector: '//*[@aria-modal="true"]',
        url: '/mobile/setting/password/change-login-password',
    },
    // Navigate to the change login password page
    async navigateToPage(pageDescriptionSelector, url) {
        await I.waitFor();
        await I.amOnPage(url);
        await I.waitFor('mediumWait');
        I.seeElement(pageDescriptionSelector);
        return this;
    },

    // Fill password field with validation and logging
    async fillPasswordField(selector, password) {
        I.seeElement(selector);
        I.fillField(selector, password);
        await I.waitFor('shortWait');
        return this;
    },

    // Submit the form
    async submitForm(descriptionSelector: string, submitButtonSelector: string, modalSelector: string, isLogin = true, screenShotName?: string) {
        I.click(descriptionSelector);
        I.seeElement(submitButtonSelector);
        I.click(submitButtonSelector);
        await I.waitFor('mediumWait');
        I.seeElement(modalSelector);
        if (screenShotName) {
            await I.saveScreenshot(screenShotName);
        }
        I.click(modalSelector);
        await I.waitFor();
        if (isLogin) {
            await I.waitFor('longWait');
            await I.terminateAndActivateAppFixed();
            await I.loginWithFilledInput();
        }
        return this;
    },

    // Take a screenshot with custom name
    async takeScreenshot(name: string) {
        await I.saveScreenshot(name);
        return this;
    },
};
