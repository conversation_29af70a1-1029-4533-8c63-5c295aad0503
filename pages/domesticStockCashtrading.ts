import { COMMON_HEADER_TITLE } from '../const/constant';

const { I } = inject();

export = {
    screenshotPrefix: {
        sell: '690.domestic_stock_cash_trading_sell_order_order_input',
        confirm: '700.domestic_stock_cash_trading_sell_order_order_confirm',
        complete: '708.domestic_stock_cash_trading_sell_order_order_complete',
        uturn: '713.domestic_stock_cash_trading_sell_order_uturn_order_input',
    },
    locators: {
        stockItem: '//*[@data-testid="stockList_positionDetail_id"]/div[1]',
        stockSellButton: '$stockDetailList_cashSell_id',
        tradeRestriciton: '$sellInput_tradeRestricitonTradeCautionInformation_id',
        tradeRestricitonCloseButton: '/html/body/div[5]/div/div/div/div[1]/button',
        marketPulldown: '$sellInput_exchange_id',
        marketFirstOption: '//button[@value="EXCHANGE_TSE"]',

        accountType: (n: number | string) => `//*[@data-testid="sellInput_accountType_id"]/label[${n}]`,
        executionMethodOptionItem: (n: number | string) =>
            `//*[@data-testid="sellInput_executionMethodSelection_id"]/button[${n}]`,
        orderDeadline: (n: number | string) => `//*[@data-testid="sellInput_orderPeriod_id"]/label[${n}]`,
        executionMethodSelection: (n: number | string) =>
            `//*[@data-testid="buyInput_executionMethodSelection_id"]/button[${n}]`,
        sellUturnExecutionMethodSelection: (n: number | string) =>
            `//*[@data-testid="sellUturn_executionMethodSelection_id"]/button[${n}]`,
        menuListItem: (value: string) => `//button[@value="${value}"]`,
        symbolFilterItem: (n: number | string) => `//*[@data-testid="relayOrder_symbolFilter_id"]/div/div/p[${n}]`,
        sortFilterItem: (n: number | string) => `//*[@data-testid="relayOrder_sort_id"]/div/div/p[${n}]`,
        pageNumber: (n: number | string) => `//*[@data-testid="relayOrder_pageNo_id"]/button[${n}]`,

        groupNumberInput: '$groupInputNumber_input_id',
        plusButton: '$groupInputNumber_plus_id',
        minusButton: '$groupInputNumber_minus_id',
        expireDatePicker: '//*[@data-testid="sellInput_orderExpireDate_id"]/div/div',
        orderConfirmButton: '//*[@data-testid="sellInput_OrderConfirmButton_id"]',
        specialConditionButton: '$sellInput_specialCondition_id',
        secondConditionOption: '//button[@value="SPECIAL_CONDITION_RELAY"]',
        passwordInput: '$sellConfirm_password_id',
        passwordOmissionCheck: '$sellConfirm_passwordOmissionCheck_id',
        checkInputPassword: '$sellConfirm_checkInputPassword_id',
        orderModifyButton: '$sellConfirm_orderModifyButton_id',
        cancelOrderButton: '$sellConfirm_cancelOrderButton_id',
        orderLinkId: '$complete_orderIdLink_id',
        sellConfirmButton: '$sellConfirm_orderConfirmButton_id',
        orderStatusButton: '$complete_orderStatusButton_id',
        positionInquiryButton: '$complete_positionInquiryButton_id',
        rankingFirstItem: '//*[@data-testid="rankingList_symbolList_id"]/div[1]',
        uturnConditionOptionItem: '//button[@value="SPECIAL_CONDITION_U_TURN"]',
        buyOrderConfirmButton: '$buyInput_OrderConfirmButton_id',
        relayOrderSymbolFilter: '$relayOrder_symbolFilter_id',
        relayOrderSortFilter: '$relayOrder_sort_id',
        totalRecordText: '$relayOrder_totalRecord_id',
        prevPageButton: '$relayOrder_prevPage_id',
        nextPageButton: '$relayOrder_nextPage_id',
        symbolSelectionTableList: '$relayOrder_symbolSelectionList_id',
        relayOrderConfirmButton: '$relayOrder_confirmButton_id',
        buyConditionButton: '$buyInput_SpecialCondition_id',
        closeModalButton: locate('button').withAttr({
            'aria-label': 'btn-close',
            'data-testid': 'common_rightSlide_close_id',
        }),
        tableRadioButton: '//*[@data-testid="relayOrder_checkbox_id_0"]/label',
    },
    urls: {
        sell: '/mobile/trade/stock/sell',
        confirm: '/mobile/trade/stock/sell/confirm',
        complete: 'mobile/trade/stock/sell/complete',
        orderInquiry: 'mobile/order-inquiry/stock/detail',
        orderInquiryList: 'mobile/order-inquiry/stock',
        positionInquiryStock: 'mobile/position-inquiry/stock',
        marketIndicator: '/mobile/market/indicator',
        uturnStockPage: '/mobile/trade/stock/uturn',
    },
    conditionOptions: {
        uturn: 'SPECIAL_CONDITION_U_TURN',
        relay: 'SPECIAL_CONDITION_RELAY',
    },

    async seeAndFillText(locator: string, value?: string) {
        const targetLocator = this.locators[locator] || locator;
        I.seeElement(targetLocator);
        I.fillField(targetLocator, value || '1000');
    },

    async swipeToLocator(locator?: string) {
        const selector = locate(this.locators[locator || 'orderConfirmButton']);
        await I.scrollToElement(selector.toXPath());
    },

    async goToConfirmPage() {
        await this.swipeToLocator();
        await I.seeAndClickElement(this.locators.orderConfirmButton);
        I.see('現物売', COMMON_HEADER_TITLE);
    },

    async goToStockSellPage() {
        await I.seeAndClickElement(['$common_menu_id', '$menu_positionInquiry_id'], {
            waitBetweenClicks: 'mediumWait',
        });

        await I.waitFor();
        I.see('残高照会\n現物株式', COMMON_HEADER_TITLE);

        await I.seeAndClickElement(this.locators.stockItem);

        await this.swipeToLocator('stockSellButton');
        await I.waitFor();
        await I.seeAndClickElement(this.locators.stockSellButton, { waitFor: 'mediumWait' });
        I.see('現物売', COMMON_HEADER_TITLE);
        I.seeInCurrentUrl(this.urls.sell);
    },

    async goToStockSellConfirmPage() {
        await this.goToStockSellPage();
        await I.waitFor('mediumWait');
        const quantityInput = '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "数量を入力")]';
        const priceInput = '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "価格を入力")]';

        await I.scrollToElement(quantityInput);
        await I.fillField(quantityInput, '5,000');
        await I.waitFor('shortWait');
        await I.scrollToElement(quantityInput);
        await I.fillField(priceInput, '1,130.5');
        await I.scrollToElement(this.locators.orderConfirmButton);
        await I.clickFixed(this.locators.orderConfirmButton);
        await I.waitFor('mediumWait');
    },

    async goToStockSellCompletedPage() {
        await this.goToStockSellConfirmPage();
        const passwordInput = '//*[@data-testid="sellConfirm_password_id"]';
        const sellConfirmButton = '//*[@data-testid="sellConfirm_orderConfirmButton_id"]';
        await I.scrollToElement(passwordInput);
        await I.fillField(passwordInput, '111111');
        await I.waitFor('shortWait');
        await I.clickFixed(sellConfirmButton);
        await I.waitFor('mediumWait');
    },

    /*
     ** @param {string} conditionValue - The index of the option to select in the menu list.
     */
    async goToCommonBuyUi(conditionValue: string) {
        I.amOnPage(this.urls.marketIndicator);
        await I.waitFor();
        await I.seeAndClickElement('$marketRanking_tab_id');

        // go to the stock basic page
        I.waitForVisible(this.locators.rankingFirstItem, 10);
        await I.seeAndClickElement(this.locators.rankingFirstItem);

        // go to the stock buy page
        await I.seeAndClickElement(['$stockInfo_tradeButton_id', '$tradingMethod_cashBuy_id'], {
            waitBetweenClicks: 'mediumWait',
        });
        await I.waitFor('mediumWait');
        await I.scrollToElement(this.locators.executionMethodSelection(2));
        await I.seeAndClickElement(this.locators.executionMethodSelection(2));
        await I.scrollToElement(locate(this.locators.plusButton).toXPath());
        await I.seeAndClickElement(this.locators.plusButton);

        // select condition
        await I.seeAndClickElement(this.locators.buyConditionButton);
        await I.seeAndClickElement(this.locators.menuListItem(conditionValue));

        // go to the next page selection condition
        await I.seeAndClickElement(this.locators.buyOrderConfirmButton);
    },

    async goToStockUturnBuyPage() {
        await this.goToCommonBuyUi(this.conditionOptions.uturn);
        await I.waitFor('mediumWait');
        I.waitForText('現物買', 5, 'p');
        await I.scrollToElement(locate('$buyConfirm_confirmOrderButton_id').toXPath());

        const isPasswordOmissionCheckEnabled = await I.executeScript(() => {
            return !!document.querySelector('[data-testid="buyConfirm_passwordOmissionCheck_id"]');
        });
        if (isPasswordOmissionCheckEnabled) {
            // password input & omission check
            await this.seeAndFillText('$buyConfirm_password_id');
            await I.seeAndClickElement('$buyConfirm_passwordOmissionCheck_id');
        } else {
            // password input check
            await I.seeAndClickElement('$buyConfirm_checkInputPassword_id');
        }

        await I.seeAndClickElement('$buyConfirm_confirmOrderButton_id');
        await I.waitFor('mediumWait');
        I.see('Uターン現物売', COMMON_HEADER_TITLE);
        I.seeInCurrentUrl(this.urls.uturnStockPage);
    },

    async goToStockRelayBuyPage() {
        await this.goToCommonBuyUi(this.conditionOptions.relay);
        await I.waitFor('mediumWait');
        I.waitForText('リレー選択現物取引', 5, 'p');
    },

    async setConfirmSessionData(objectData: Record<string, unknown>) {
        const sessionId = '/trade/stock/sell/confirm';
        const data = await I.executeScript((key) => {
            return window.sessionStorage.getItem(key);
        }, sessionId);
        const newData = { ...JSON.parse(data), ...objectData };
        await I.executeScript(
            (key, value) => {
                return window.sessionStorage.setItem(key, JSON.stringify(value));
            },
            sessionId,
            newData,
        );
        await I.clickFixed('$common_reload_id');
    },
};
