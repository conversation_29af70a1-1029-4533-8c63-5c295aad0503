import { LAST_EXTERNAL_URL } from '../const/constant';
import common from './search/common';

const { I } = inject();
const { tryTo } = require('codeceptjs/effects');

export = {
    // Define locators
    locators: {
        referralLink: '//*[@data-testid="referral_link_id"]',
        referralMethodCollapse: '//*[@data-testid="referral_referralMethod_id"]/div',
        referralMethodContent: '//*[@data-testid="referral_referralMethod_id"]/div[2]',
        headerTitle: '//*[@data-testid="common_header_title_id"]',
        benefitDetailsCollapse: '//*[@data-testid="referral_benefitDetails_id"]/div',
        benefitDetailsContent: '//*[@data-testid="referral_benefitDetails_id"]/div[2]',
        referralProgramLink: '//*[@data-testid="referral_programLink_id"]',
        lineIntroductionLink: '//*[@data-testid="referral_lineIntroduction_id"]',
        messageTab: '//*[@data-testid="referral_tabContainer_id"]/button[2]',
        snsTab: '//*[@data-testid="referral_tabContainer_id"]/button[3]',
        emailIntroductionButton: '//*[@data-testid="referral_emailIntroduction_id"]',
        snsButton: '//*[@data-testid="referral_issueReferralURL_id"]',
        backButton: '//*[@data-testid="common_back_id"]',
    },

    // URLs
    urls: {
        referral: '/mobile/mypage/referral',
        externalReferralProgram: 'https://kabu.com/sp/campaign/introduction_program.html',
        linePage: 'https://line.me',
        emailInputPage: '/referral/mail-input',
        snsPage: '/referral/generate-url',
    },

    // Navigate to the referral page
    async navigateToPage() {
        await I.waitFor();
        await I.amOnPage(this.urls.referral);
        await I.waitFor();
        I.see(
            `ご家族・ご友人
紹介プログラム`,
            this.locators.headerTitle,
        );
        return this;
    },

    // Toggle the method collapse (open if closed, close if open)
    async toggleReferralMethodCollapse() {
        I.seeElement(this.locators.referralMethodCollapse);
        I.click(this.locators.referralMethodCollapse);
        await I.waitFor();
        return this;
    },

    // Check if the method collapse is open
    async isMethodCollapseOpen() {
        return await tryTo(() => I.seeElement(this.locators.referralMethodContent));
    },

    // Open the method collapse if closed
    async openReferralMethodCollapse() {
        const isOpen = await this.isMethodCollapseOpen();
        if (!isOpen) {
            await this.toggleReferralMethodCollapse();
            I.seeElement(this.locators.referralMethodContent);
        }
        return this;
    },

    // Close the method collapse if open
    async closeReferralMethodCollapse() {
        const isOpen = await this.isMethodCollapseOpen();
        if (isOpen) {
            await this.toggleReferralMethodCollapse();
            await I.waitFor('shortWait');
            I.dontSeeElement(this.locators.referralMethodContent);
        }
        return this;
    },

    // Toggle the benefit details collapse (open if closed, close if open)
    async toggleBenefitDetailsCollapse() {
        I.seeElement(this.locators.benefitDetailsCollapse);
        I.click(this.locators.benefitDetailsCollapse);
        await I.waitFor();
        return this;
    },

    // Check if the benefit details collapse is open
    async isBenefitDetailsCollapseOpen() {
        return await tryTo(() => I.seeElement(this.locators.benefitDetailsContent));
    },

    // Open the benefit details collapse if closed
    async openBenefitDetailsCollapse() {
        const isOpen = await this.isBenefitDetailsCollapseOpen();
        if (!isOpen) {
            await this.toggleBenefitDetailsCollapse();
            I.seeElement(this.locators.benefitDetailsContent);
        }
        return this;
    },

    // Close the benefit details collapse if open
    async closeBenefitDetailsCollapse() {
        const isOpen = await this.isBenefitDetailsCollapseOpen();
        if (isOpen) {
            await this.toggleBenefitDetailsCollapse();
            await I.waitFor('shortWait');
            I.dontSeeElement(this.locators.benefitDetailsContent);
        }
        return this;
    },

    // Copy referral link
    async copyReferralLink() {
        I.seeElement(this.locators.referralLink);
        I.click(this.locators.referralLink);
        await I.waitFor();
        return this;
    },

    // Click on referral program link and check external URL
    async clickReferralProgramLink() {
        I.seeElement(this.locators.referralProgramLink);
        await common.clickCardItem(this.locators.referralProgramLink, this.urls.externalReferralProgram, 'external');
    },

    // Click on LINE introduction link and check external URL
    async clickLineIntroductionLink() {
        I.seeElement(this.locators.lineIntroductionLink);

        // Click the link
        I.clickFixed(this.locators.lineIntroductionLink);
        await I.waitFor('extraLongWait'); // Wait for 5 seconds as requested
        I.activateApp();
        const localStorage = await I.getLocalStorage(LAST_EXTERNAL_URL);
        I.say(localStorage);

        return localStorage;
    },

    // Navigate to email referral and check URL
    async navigateToEmailReferral() {
        // Click the message tab first
        I.seeElement(this.locators.messageTab);
        I.click(this.locators.messageTab);
        await I.waitFor();

        // Click the email introduction button
        I.seeElement(this.locators.emailIntroductionButton);
        I.click(this.locators.emailIntroductionButton);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking email introduction: ${currentUrl}`);

        return currentUrl;
    },

    // Navigate back from email input page
    async navigateBackFromEmailInput() {
        I.seeElement(this.locators.backButton);
        I.click(this.locators.backButton);
        await I.waitFor();
        return this;
    },

    // Navigate to SNS referral and check URL
    async navigateToSnsReferral() {
        // Click the SNS tab first
        I.seeElement(this.locators.snsTab);
        I.click(this.locators.snsTab);
        await I.waitFor();

        // Click the SNS button
        I.seeElement(this.locators.snsButton);
        I.click(this.locators.snsButton);
        await I.waitFor('mediumWait');

        // Get the current URL to verify
        const currentUrl = await I.grabCurrentUrl();
        I.say(`Current URL after clicking SNS button: ${currentUrl}`);

        return currentUrl;
    },

    // Take a screenshot with custom name
    takeScreenshot(name: string) {
        I.saveScreenshot(name);
        return this;
    },
};
