import { PAGE_URL } from "../const/constant";

const { I } = inject();

/**
 * Page object representing the SOR Report Future page
 */
export = {
    /**
     * Locators for SOR Report Future page elements
     */
    locator: {
        // Common elements
        commonHeaderTitle: '//*[@data-testid="common_header_title_id"]',
        commonBackId: '//*[@data-testid="common_back_id"]',

        // Tabs
        futureTab: '//*[@data-testid="sorReportStock_future_id"]',
        futureStockTab: '//*[@data-testid="sorReportFuture_stock_id"]',

        // Display Period
        displayPeriod: '//*[@data-testid="sorReportFuture_displayPeriod_id"]',

        // Pagination
        pageNumber: '//*[@data-testid="sorReportFuture_pageNumber_id"]',
        prevPage: '//*[@data-testid="sorReportFuture_prevPage_id"]',
        nextPage: '//*[@data-testid="sorReportFuture_nextPage_id"]',

        // Calendar elements
        prevMonth: '//*[@data-testid="sorReportStock_prevMonth_id"]',
        nextMonth: '//*[@data-testid="sorReportStock_nextMonth_id"]',
        calendar: '//*[@data-testid="sorReportStock_calendar_id"]',
        reset: '//*[@data-testid="sorReportStock_reset_id"]',
        confirm: '//*[@data-testid="sorReportStock_confirm_id"]',

        // Bottom sheet
        bottomSheet: '//*[@id="bottom-sheet-container"]',
        monthHeaderText: '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[2]/div[1]/div[2]/p',
        bottomSheetContent: '//*[@id="bottom-sheet-container"]/div[2]/div[2]/div/div/div/div[1]/p',
        cancelBtn: '//button[@aria-label="cancel-btn"]',
    },

    /**
     * Navigate to SOR Report Future page
     */
    async goToSorReportFuturePage() {
        I.amOnPage(PAGE_URL.sorReportFuture);
        I.see(
            `SOR価格改善
レポート`,
            this.locator.commonHeaderTitle,
        );
        await I.waitFor();
        return this;
    },

    /**
     * Navigate to SOR Report Stock page
     */
    async goToSorReportStockPage() {
        await I.clickFixed(this.locator.futureStockTab);
        await I.waitFor();
        I.seeInCurrentUrl('/mobile/sor-report/stock');
        return this;
    },

    /**
     * Return from Stock page to Future page
     */
    async returnToFuturePage() {
        await I.clickFixed(this.locator.futureTab);
        await I.waitFor();
        I.seeInCurrentUrl('/mobile/sor-report/future');
        return this;
    },

    /**
     * Click on a label in the display period section
     * @param {number} index - The index of the label to click (1-based)
     */
    async clickDisplayPeriodLabel(index) {
        const labelLocator = `${this.locator.displayPeriod}/label[${index}]/p`;
        await I.clickFixed(labelLocator);
        await I.waitFor();
        return this;
    },

    /**
     * Get number of display period labels
     */
    async getDisplayPeriodLabelsCount() {
        return await I.grabNumberOfVisibleElements(`${this.locator.displayPeriod}/label`);
    },

    /**
     * Click on a specific page number
     * @param {number} pageNum - The page number to click
     */
    async clickPageNumber(pageNum) {
        const buttonLocator = `${this.locator.pageNumber}/button[${pageNum}]`;
        await I.clickFixed(buttonLocator);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Previous Page button
     */
    async clickPreviousPage() {
        await I.clickFixed(this.locator.prevPage);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Next Page button
     */
    async clickNextPage() {
        await I.clickFixed(this.locator.nextPage);
        await I.waitFor();
        return this;
    },

    /**
     * Open date picker by clicking on the last display period label
     */
    async openDatePicker() {
        const count = await this.getDisplayPeriodLabelsCount();
        const lastLabelLocator = `(${this.locator.displayPeriod}/label)[${count}]`;
        await I.clickFixed(lastLabelLocator);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Previous Month button in the calendar
     */
    async clickPreviousMonth() {
        await I.clickFixed(this.locator.prevMonth);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Next Month button in the calendar
     */
    async clickNextMonth() {
        await I.clickFixed(this.locator.nextMonth);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Reset button in the calendar
     */
    async clickReset() {
        await I.clickFixed(this.locator.reset);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Confirm button in the calendar
     */
    async clickConfirm() {
        await I.clickFixed(this.locator.confirm);
        await I.waitFor();
        return this;
    },

    /**
     * Click the Cancel button in the bottom sheet
     */
    async clickCancel() {
        await I.clickFixed(this.locator.cancelBtn);
        await I.waitFor();
        return this;
    },

    /**
     * Click on a specific day in the calendar
     * @param {number} weekIndex - The week index (1-based)
     * @param {string} dayPosition - "first-child" or "last-child"
     */
    async clickCalendarDay(weekIndex, dayPosition) {
        const daySelector = `.DayPicker-Body .DayPicker-Week:nth-child(${weekIndex}) .DayPicker-Day:${dayPosition}`;
        await I.clickFixed(daySelector);
        await I.waitFor();
        return this;
    },
};
