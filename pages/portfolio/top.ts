/**
 * 資産状況_資産ポートフォリオTOP
 * AssetStatus_PortfolioTop
 */

import { PAGE_URL } from '../../const/constant';
import { getProductLocator, PORTFOLIO_PRODUCT_TYPE, PortfolioProductType } from './utils';

const { I } = inject();

export = {
    locators: {
        pieChart: '$pieChart_id',
        portfolioView: '$portfolioTop_portfolioView_id',
        backToTop: '$portfolioTop_backToTop_id',
        explanatoryImage: '$portfolioTop_explanatoryImage_id',
        close: '$portfolioTop_close_id',
        help: '$common_header_help_id',
        coachModal: '$portfolioTop_coachModal_id',
        operationGuide: '$portfolioTop_operationGuide_id',
        customerSupport: '$portfolioTop_customerSupport_id',
        detailedExplanation: '$portfolioTop_detailedExplanationPortfolioScreen_id',
        assetDisplaySwitch: '$portfolioTop_assetDisplaySwitch_id',
        unreadNotification: '$portfolioTop_unreadNoti_id',
        aboutFundAssetValuation: '$portfolioTop_aboutFundAssetValuation_id',
        nextPrevPage: '$portfolio_NextPrevPage_id',
    },

    async goToPage(): Promise<void> {
        await I.amOnPage(PAGE_URL.mypagePortfolio);
        await I.waitFor('mediumWait');
    },

    async stock() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.STOCK);
    },

    async usStock() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.US_STOCK);
    },

    async fund() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.FUND);
    },

    async foreignMmf() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.FOREIGN_MMF);
    },

    async money() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.AZUKARI);
    },

    async margin() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.MARGIN);
    },

    async future() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.FUTURE);
    },

    async option() {
        await clickProductCard(PORTFOLIO_PRODUCT_TYPE.OPTION);
    },

    /**
     * 31.未読お知らせ
     */
    async clickUnreadNotification(): Promise<void> {
        const item = locate(this.locators.unreadNotification);
        const expectedUrl = 'https://notice.kabu.co.jp/notifyList';

        await I.waitForElement(item, 5);
        await I.clickFixed(item);
        await I.waitInUrl(expectedUrl, 5);
    },
};

async function clickProductCard(productType: PortfolioProductType)  {
    const locator = locate(getProductLocator(productType)).first();
    await I.waitForVisible(locator, 5);
    await I.scrollToElement(locator.toXPath());
    await I.clickFixed(locator);
    await I.waitFor('mediumWait'); // Wait for the page to load
}
