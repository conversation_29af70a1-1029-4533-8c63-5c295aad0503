/**
 * 投資信託
 * PortfolioFund
 */

import common from './common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.FUND,

    locators: {
        seeInfo: '$portfolioFund_seeInfo_id',
        positionInquiry: '$portfolioFund_positionInquiry_id',
    },

    async verifyPage() {
        await common.verifyProductName(this.productType);
    },

    async clickSymbol(symbol: string): Promise<void> {
        await common.clickSymbolCard(this.productType, symbol);
    },
};
