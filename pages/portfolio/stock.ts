/**
 * 国内株式現物
 * PortfolioStock
 */

import common from './common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.STOCK,

    locators: {
        buy: '$portfolioStock_buy_id',
        sell: '$portfolioStock_sell_id',
        petiBuy: '$portfolioStock_petitBuy_id',
        petiSell: '$portfolioStock_petitSell_id',
        seeInfo: '$portfolioStock_seeInfo_id',
        positionInquiry: '$portfolioStock_positionInquiry_id',
    },

    async buy() {
        await common.performBottomSheetAction(this.locators.buy, '/mobile/trade/stock/buy');
    },

    async sell() {
        await common.performBottomSheetAction(this.locators.sell, '/mobile/position-inquiry/stock');
    },

    async petitBuy() {
        await common.performBottomSheetAction(this.locators.petiBuy, '/mobile/trade/petit/buy');
    },

    async petitSell() {
        await common.performBottomSheetAction(this.locators.petiSell, '/mobile/position-inquiry/stock');
    },

    async seeInfo() {
        await common.performBottomSheetAction(this.locators.seeInfo, '/mobile/info/stock/basic');
    },

    async positionInquiry() {
        await common.performBottomSheetAction(this.locators.positionInquiry, '/mobile/position-inquiry/stock');
    },
};
