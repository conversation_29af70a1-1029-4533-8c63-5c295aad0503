export const DEFAULT_EMPTY = '-';

export const POR<PERSON><PERSON>IO_PRODUCT_TYPE = {
    UNSPECIFIED: 'PORTFOLIO_PRODUCT_TYPE_UNSPECIFIED',
    STOCK: 'PORTFOLIO_PRODUCT_TYPE_STOCK',
    US_STOCK: 'PORTF<PERSON>IO_PRODUCT_TYPE_US_STOCK',
    FUND: 'PORTFOLIO_PRODUCT_TYPE_FUND',
    FOREIGN_MMF: 'PORTFOLIO_PRODUCT_TYPE_FOREIGNMMF',
    A<PERSON>UKARI: 'PORTFOLIO_PRODUCT_TYPE_AZUKARI',
    MARGIN: 'PORTFOLIO_PRODUCT_TYPE_MARGIN',
    FUTURE: 'PORTFOLIO_PRODUCT_TYPE_FUTURE',
    OPTION: 'PORTFOLIO_PRODUCT_TYPE_OPTION',
} as const;

export type PortfolioProductType = (typeof PORTFOLIO_PRODUCT_TYPE)[keyof typeof PORTFOLIO_PRODUCT_TYPE];

export const PORTFOLIO_PRODUCT_TYPE_CONTENTS = {
    [PORTFOLIO_PRODUCT_TYPE.UNSPECIFIED]: DEFAULT_EMPTY,
    [PORTFOLIO_PRODUCT_TYPE.STOCK]: '国内株式現物',
    [PORTFOLIO_PRODUCT_TYPE.US_STOCK]: '米国株式現物',
    [PORTFOLIO_PRODUCT_TYPE.FUND]: '投資信託',
    [PORTFOLIO_PRODUCT_TYPE.FOREIGN_MMF]: '外貨建MMF',
    [PORTFOLIO_PRODUCT_TYPE.AZUKARI]: '預り金等現金',
    [PORTFOLIO_PRODUCT_TYPE.MARGIN]: '国内株式信用',
    [PORTFOLIO_PRODUCT_TYPE.FUTURE]: '先物',
    [PORTFOLIO_PRODUCT_TYPE.OPTION]: 'オプション',
} as const;

export const getProductLocator = (productType: PortfolioProductType) => {
    if (productType === PORTFOLIO_PRODUCT_TYPE.UNSPECIFIED) {
        return '';
    }

    if (
        productType === PORTFOLIO_PRODUCT_TYPE.MARGIN ||
        productType === PORTFOLIO_PRODUCT_TYPE.FUTURE ||
        productType === PORTFOLIO_PRODUCT_TYPE.OPTION
    ) {
        return `$portfolioTop_investmentProductCard_id_${productType}`;
    }

    return `$portfolio_productCard_id_${productType}`;
};
