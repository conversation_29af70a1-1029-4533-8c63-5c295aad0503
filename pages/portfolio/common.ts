import common from '../search/common';
import { getProductLocator, PORTFOLIO_PRODUCT_TYPE_CONTENTS, PortfolioProductType } from './utils';

const { I } = inject();

export = {
    locators: {
        // Header
        tabInvestmentResult: '$portfolio_investmentResult_id',
        productNameLabel: '$portfolio_productName_id',
        headerBackButton: '$portfolio_back_id',
        headerNextButton: '$portfolio_next_id',

        // PieChart
        pieChart: '$pieChart_id',

        // Other
        return: '$portfolio_return_id',
        cashDisplaySwitch: '$portfolio_cashDisplaySwitch_id',
        marginDisplaySwitch: '$portfolio_marginDisplaySwitch_id',
        assetValuationTab: '$portfolio_assetValuation_id',
        currentValueTab: '$portfolio_currentValue_id',
        quantityTab: '$portfolio_quantity_id',
        valuationSort: '$portfolio_valuationSort_id',
        evaluationProfitLossSort: '$portfolio_evaluationProfitLossSort_id',
        currentSort: '$portfolio_currentSort_id',
        buySellSort: '$portfolio_buySellSort_id',
        quantitySort: '$portfolio_quantitySort_id',
        scrollButton: '//button[@aria-label="scroll-to-top-btn"]',
    },

    // Header
    async verifyInvestmentResult() {
        const item = locate(this.locators.tabInvestmentResult);
        await I.waitForVisible(item, 5);
        await I.clickFixed(item);
        await I.waitInUrl('/mobile/mypage/performance', 5);
    },

    async clickHeaderBackButton() {
        const item = locate(this.locators.headerBackButton);
        await I.waitForVisible(item, 5);
        await I.clickFixed(item);
    },

    async verifyProductName(productType: PortfolioProductType) {
        const item = locate(this.locators.productNameLabel);
        await I.waitForVisible(item, 5);
        const label = await I.grabTextFrom(item);
        await I.assertEqual(label, PORTFOLIO_PRODUCT_TYPE_CONTENTS[productType]);
    },

    async clickHeaderNextButton() {
        const item = locate(this.locators.headerNextButton);
        await I.waitForVisible(item, 5);
        await I.clickFixed(item);
        await I.waitFor();
    },

    // PieChart
    async verifyPieChart() {
        await I.waitForVisible(this.locators.pieChart, 5);
        await I.waitFor();
        await I.tapByCoordinate(this.locators.pieChart, { x: -120, y: 0 });
        await I.waitFor();
        await I.waitForVisible('//*[contains(@class, "highcharts-tooltip")]', 5);
    },

    // Other
    async clickBack() {
        await I.waitForVisible(this.locators.return, 5);
        await I.clickFixed(this.locators.return);
        await I.waitFor();
        await I.waitForInvisible(this.locators.headerBackButton, 5);
    },

    async switchAssetValuationTab() {
        await this.performSwitch('assetValuationTab');
        await I.waitForVisible(this.locators.valuationSort, 5);
        await I.waitForVisible(this.locators.evaluationProfitLossSort, 5);
    },

    async switchCurrentValueTab() {
        await this.performSwitch('currentValueTab');
        await I.waitForVisible(this.locators.currentSort, 5);
    },

    async switchQuantityTab() {
        await this.performSwitch('quantityTab');
        await I.waitForVisible(this.locators.buySellSort, 5);
        await I.waitForVisible(this.locators.quantitySort, 5);
    },

    async clickValuationSort() {
        await this.performSort('valuationSort');
        await I.waitForVisible('$common_MenuList_id', 5);
    },

    async clickEvaluationProfitLossSort() {
        await this.performSort('evaluationProfitLossSort');
        await I.waitForVisible('$common_MenuList_id', 5);
    },

    async clickCurrentSort() {
        await this.performSort('currentSort');
        await I.waitForVisible('$common_MenuList_id', 5);
    },

    async clickBuySellSort() {
        await this.performSort('buySellSort');
    },

    async clickQuantitySort() {
        await this.performSort('quantitySort');
    },

    async clickSymbolCard(productType: PortfolioProductType, symbol: string) {
        const locator = locate(getProductLocator(productType)).withText(symbol).first();
        await I.waitForVisible(locator, 5);
        await I.clickFixed(locator);
        await I.waitFor();
        await I.waitForVisible(locate('div').withAttr({ role: 'dialog', 'aria-modal': 'true' }), 5);
    },

    async performSwitch(tab: 'assetValuationTab' | 'currentValueTab' | 'quantityTab') {
        const locator = locate(this.locators[tab]);
        await I.waitForVisible(locator, 5);
        await I.clickFixed(locator);
        await I.waitFor();
    },

    async performSort(
        item: 'valuationSort' | 'evaluationProfitLossSort' | 'currentSort' | 'buySellSort' | 'quantitySort',
    ): Promise<void> {
        const locator = locate(this.locators[item]);
        await I.waitForVisible(locator, 5);
        await I.clickFixed(locator);
        await I.waitFor();
    },

    async performBottomSheetAction(
        locator: CodeceptJS.LocatorOrString,
        expectedUrl?: string,
        urlType: 'internal' | 'external' | 'kcMemberSite' = 'internal'
    ): Promise<void> {
        await common.clickCardItem(locator, expectedUrl, urlType);
    },
};
