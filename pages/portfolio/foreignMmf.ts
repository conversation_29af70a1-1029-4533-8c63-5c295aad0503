/**
 * 外貨建MMF
 * PortfolioFMMF
 */

import common from './common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.FOREIGN_MMF,

    locators: {
        seeInfo: '//button[contains(text(), "銘柄情報を見る")]',
        positionInquiry: '//button[contains(text(), "残高照会")]',
    },

    async seeInfo(symbol: string) {
        await common.performBottomSheetAction(this.locators.seeInfo, `/iPhone/TradeTool/ToushinMmf/${symbol}.asp`, 'kcMemberSite');
    },

    async positionInquiry() {
        await common.performBottomSheetAction(
            this.locators.positionInquiry,
            '/iPhone/Account/AccountStatus/MultiCurMMFList.asp',
            'kcMemberSite',
        );
    },
};
