/*
 * オプション
 * Option
 */

import common from './common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.OPTION,

    locators: {
        newOrder: '$portfolioOption_newOrder_id',
        positionInquiry: '$portfolioOption_positionInquiry_id',
    },

    async newOrder(symbol: string) {
        await common.performBottomSheetAction(
            this.locators.newOrder,
            `/ap/iPhone/Trade/OrderDeriv/OpenInput?symbol=${symbol}`,
            'kcMemberSite',
        );
    },

    async positionInquiry() {
        await common.performBottomSheetAction(
            this.locators.positionInquiry,
            '/ap/iPhone/Account/AssetDeriv/PositionSelect',
            'kcMemberSite',
        );
    },
};
