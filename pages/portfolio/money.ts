/**
 * 預り金等現金
 * PortfolioMoney
 */

import common from './common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.AZUKARI,

    locators: {
        availableAmount: '$portfolioMoney_availableAmount_id',
        foreignStockAvailableAmount: '$portfolioMoney_foreignStockAvailableAmount_id',
        futureOptionAvailableAmount: '$portfolioMoney_futureOptionAvailableAmount_id',
    },

    async availableAmount() {
        await common.performBottomSheetAction(this.locators.availableAmount, '/ap/iphone/Assets/Kanougaku/Stock', 'kcMemberSite');
    },

    async foreignStockAvailableAmount() {
        await common.performBottomSheetAction(
            this.locators.foreignStockAvailableAmount,
            '/ap/iPhone/Assets/Kanougaku/Foreign',
            'kcMemberSite',
        );
    },

    async futureOptionAvailableAmount() {
        await common.performBottomSheetAction(
            this.locators.futureOptionAvailableAmount,
            '/ap/iPhone/Assets/Kanougaku/Deriv',
            'kcMemberSite',
        );
    },
};
