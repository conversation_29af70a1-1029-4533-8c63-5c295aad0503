/**
 * 国内株式信用
 * PortfolioMargin
 */

import common from './common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.MARGIN,

    locators: {
        newBuy: '$portfolioMargin_newBuy_id',
        newSell: '$portfolioMargin_newSell_id',
        payment: '$portfolioMargin_payment_id',
        receipt: '$portfolioMargin_receipt_id',
        delivery: '$portfolioMargin_delivery_id',
        seeInfo: '$portfolioMargin_seeInfo_id',
        positionInquiry: '$portfolioMargin_positionInquiry_id',
    },

    async newBuy() {
        await common.performBottomSheetAction(this.locators.newBuy, '/mobile/trade/margin/new');
    },

    async newSell() {
        await common.performBottomSheetAction(this.locators.newSell, '/mobile/trade/margin/new');
    },

    async payment() {
        await common.performBottomSheetAction(this.locators.payment, '/mobile/position-inquiry/margin');
    },

    async receipt() {
        await common.performBottomSheetAction(this.locators.receipt, '/mobile/position-inquiry/margin');
    },

    async delivery() {
        await common.performBottomSheetAction(this.locators.delivery, '/mobile/position-inquiry/margin');
    },

    async seeInfo() {
        await common.performBottomSheetAction(this.locators.seeInfo, '/mobile/info/stock/basic');
    },

    async positionInquiry() {
        await common.performBottomSheetAction(this.locators.positionInquiry, '/mobile/position-inquiry/margin');
    },
};
