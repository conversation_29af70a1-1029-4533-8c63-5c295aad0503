/**
 * 米国株式現物
 * PortfolioUSStock
 */

import common from './common';
import searchCommon from '../search/common';
import { PORTFOLIO_PRODUCT_TYPE } from './utils';

const { I } = inject();

export = {
    productType: PORTFOLIO_PRODUCT_TYPE.US_STOCK,

    locators: {
        buy: '$portfolioUSStock_buy_id',
        sell: '$portfolioUSStock_sell_id',
        seeInfo: '$portfolioUSStock_seeInfo_id',
        positionInquiry: '$portfolioUSStock_positionInquiry_id',
        easyElectricContract: '$portfolioUSStock_easyElectronicContract_id',
    },

    async verifyUnusablePage() {
        await I.waitForText(
            '米国株式を行うための手続きがお済みでないため、本画面を参照いただけません。',
            5,
            '#mypage-portfolio',
        );
    },

    async buy(symbol: string) {
        await common.performBottomSheetAction(
            this.locators.buy,
            `/ap/iPhone/ForeignStocks/USStock/Buy/Input?Symbol=${symbol}`,
            'kcMemberSite',
        );
    },

    async sell(symbol: string) {
        await common.performBottomSheetAction(
            this.locators.sell,
            `/ap/iPhone/ForeignStocks/USStock/Position/SellList?Symbol=${symbol}`,
            'kcMemberSite',
        );
    },

    async seeInfo() {
        await common.performBottomSheetAction(
            this.locators.seeInfo,
            `/mobile/info/usstock/summary`,
        );
    },

    async positionInquiry() {
        await common.performBottomSheetAction(
            this.locators.positionInquiry,
            '/ap/iPhone/ForeignStocks/USStock/Position/List',
            'kcMemberSite',
        );
    },

    async clickEasyElectricContract(): Promise<void> {
        const item = locate(this.locators.easyElectricContract);
        await searchCommon.clickCardItem(item, '/members/personal/dkstatus/dk01101.asp#tradeservice', 'external');
    },
};
