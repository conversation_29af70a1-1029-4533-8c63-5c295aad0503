import { PAGE_URL } from "../const/constant";

const { I } = inject();

export = {
    async goToPage(): Promise<void> {
        I.amOnPage(`${PAGE_URL.tradeStockBuy}?symbol=8306&exchange=EXCHANGE_TSE`);
        await I.waitFor();
        I.waitForText('現物買', 1, 'body');
    },
    async fillValueBeforeOrderConfirm(): Promise<void> {
        await I.executeScript(function () {
            window.scrollTo(0, 0);
        });
        await I.waitFor();
        //scroll to quantity section
        const quantitySection = '//p[contains(text(), "数量")]';
        I.waitForElement(quantitySection, 2);
        I.scrollToElement(quantitySection);
        // Click quantity increase button
        const quantityPlusBtn = '(//button[.//img[@alt="plus" or contains(@src, "/img/plus.svg")]])[1]';
        I.waitForElement(quantityPlusBtn, 2);
        I.clickFixed(quantityPlusBtn);
        await <PERSON>.waitFor('shortWait');
        
        // Click quantity decrease button
        const quantityMinusBtn = '(//button[.//img[@alt="minus" or contains(@src, "/img/minus.svg")]])[1]';
        I.waitForElement(quantityMinusBtn, 2);
        I.clickFixed(quantityMinusBtn);
        await I.waitFor('shortWait');
        // --- Part 3: Check price increase/decrease ---
        // Reset scroll position
        await I.executeScript(function () {
            window.scrollTo(0, 0);
        });
        await I.waitFor();

        // Scroll to price section
        const priceLabel = '//p[contains(text(), "価格")]';
        I.waitForElement(priceLabel, 2);
        I.scrollToElement(priceLabel);
        await I.waitFor();

        // Click price increase button
        const pricePlusBtn = '(//button[.//img[@alt="plus" or contains(@src, "/img/plus.svg")]])[2]';
        I.waitForElement(pricePlusBtn, 2);
        I.clickFixed(pricePlusBtn);
        await I.waitFor('shortWait');

        // Click price decrease button
        const priceMinusBtn = '(//button[.//img[@alt="minus" or contains(@src, "/img/minus.svg")]])[2]';
        I.waitForElement(priceMinusBtn, 2);
        I.clickFixed(priceMinusBtn);
        await I.waitFor('shortWait');

        //Check confirm checkbox
        const insiderInfoText = '//p[contains(text(), "インサイダー情報に基づく")]';
        I.waitForElement(insiderInfoText, 2);
        I.scrollToElement(insiderInfoText);

        await I.waitFor();
        // Click check box
        const checkButton = '//button[@aria-label="Check"]';
        //check checkbox is checked
        const isChecked = await I.executeScript(function(selector) {
            const element = document.evaluate(selector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue as HTMLElement;
            return element && element.classList.contains('css-1lej4gx') ? 'true' : 'false';
        }, checkButton);
        if (isChecked !== 'true') {
            I.clickFixed(checkButton);
        }
        await I.waitFor();


    },
    async gotoCompletedPage(): Promise<void> {

        // click checkbox "銘柄、数量、執行条件を改めて確認し、間違いありません。"
        const IChecked = '//div[.//p[contains(text(), "銘柄、数量、執行条件を改めて確認し、間違いありません。")]]//button[@aria-label="Check"]';
        I.waitForElement(IChecked, 2);
        I.scrollAndClick(IChecked);
        // Scroll to checkbox "次の注文からパスワードを入力する" and click it
        const checkInputPassword = '//*[@data-testid="buyConfirm_checkInputPassword_id"]';
        I.waitForElement(checkInputPassword, 5);
        I.scrollAndClick(checkInputPassword);
        //reset scroll position
        await I.executeScript(function () {
            window.scrollTo(0, 0);
        });
    
        // Click button "注文確定"
        const confirmOrderButton = '//*[@data-testid="buyConfirm_confirmOrderButton_id"]';
        I.waitForElement(confirmOrderButton, 2);
        I.scrollAndClick(confirmOrderButton);
       

    }
};
