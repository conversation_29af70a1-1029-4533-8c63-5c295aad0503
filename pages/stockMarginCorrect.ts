import { SCREENSHOT_PREFIX, SESSION_STORAGE_KEY, SESSION_STORAGE_VALUE } from '../const/constant';

const { I, stockMarginCorrect } = inject();

export = {
    locator: {
        marginCorrectContent: '//*[@data-testid="marginCorrection_correctContent_id"]',
        marginCorrectionDoCorrectionButton: '//*[@data-testid="marginCorrection_doCorrect_id"]',
    },
    urls: {
        marginCorrectOrderInput: '/mobile/trade/margin/correction',
    },
    inputValues: {
        password: '111111',
    },
    takeScreenshot: {
        marginCorrectInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingCorrectInput}_${suffix}.png`);
        },
        marginCorrectConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingCorrectConfirm}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToMarginCorrectInput(): Promise<void> {
        await I.setSessionStorage(SESSION_STORAGE_KEY.tradeMarginCorrect, SESSION_STORAGE_VALUE.tradeMarginCorrect());
        I.amOnPage(this.urls.marginCorrectOrderInput);
        await I.waitFor('mediumWait');
        I.waitForText('信用訂正', 3, 'body');
        I.waitForElement(this.locator.marginCorrectContent);
    },
    async goToMarginCorrectConfirm(): Promise<void> {
        await stockMarginCorrect.goToMarginCorrectInput();
        await I.scrollToElement(this.locator.marginCorrectionDoCorrectionButton);
        await I.clickFixed(this.locator.marginCorrectionDoCorrectionButton);
        await I.waitFor('mediumWait');
        I.waitForText('信用訂正確認', 3, 'body');
    },
};
