import { SCREENSHOT_PREFIX } from '../const/constant';

const { I, stockMarginOrder } = inject();

export = {
    locator: {
        marginNewDepositRequest: '//*[@data-testid="marginNew_depositRequest_id"]',
        marginNewCautionInfo: '//*[@data-testid="marginNew_tradeRestrictionTradeCautionInfo_id"]',
        marginNewOrderConfirmButton: '//*[@data-testid="marginNew_orderConfirmButton_id"]',
        marginNewOrderQuantityInput:
            '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "数量を入力")]',
        marginNewOrderPriceInput:
            '//input[@data-testid="groupInputNumber_input_id"][contains(@placeholder, "価格を入力")]',
        marginNewSpecialCondition: '//*[@data-testid="buyInput_SpecialCondition_id"]',
        marginNewUturnConditionItem: '//button[contains(@value, "SPECIAL_CONDITION_U_TURN")]',
        marginNewRelayConditionItem: '//button[contains(@value, "SPECIAL_CONDITION_RELAY")]',
        marginNewSoftLimitConfirm: '//*[@data-testid="marginNew_softlimitConflit_id"]//button',
        marginNewPasswordInput: '//*[@data-testid="marginNew_passwordInput_id"]',
        marginNewOmmitPassword: '//*[@data-testid="marginNew_passwordInputCheckBox_id"]',
    },
    urls: {
        stockDetailMarginNewOrderInput: '/mobile/trade/margin/new?symbol=8306&exchange=EXCHANGE_TSE',
    },
    inputValues: {
        quantity: '100',
        price: '1,130.5',
        password: '111111',
        trailWidth: '0.1',
    },
    takeScreenshot: {
        marginNewOrderInput: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingNewOrderInput}_${suffix}.png`);
        },
        marginNewOrderConfirm: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingNewOrderConfirm}_${suffix}.png`);
        },
        marginNewOrderCompleted: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingNewOrderCompleted}_${suffix}.png`);
        },
        marginRelaySelection: async (suffix: string) => {
            await I.saveScreenshot(`${SCREENSHOT_PREFIX.domesticStockMarginTradingRelaySelection}_${suffix}.png`);
        },
    },
    async compareUrl(url: string): Promise<void> {
        I.assertContain(await I.grabCurrentUrl(), url, 'URL does not contain expected path');
    },
    async goToMarginNewOrderInput(): Promise<void> {
        I.amOnPage(this.urls.stockDetailMarginNewOrderInput);
        await I.waitFor('mediumWait');
        I.waitForText('信用新規', 3, 'body');
    },
    async goToMarginNewOrderConfirm(): Promise<void> {
        await stockMarginOrder.goToMarginNewOrderInput();
        I.fillField(this.locator.marginNewOrderQuantityInput, this.inputValues.quantity);
        I.fillField(this.locator.marginNewOrderPriceInput, this.inputValues.price);
        await I.clickFixed(this.locator.marginNewOrderConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToMarginNewOrderCompleted(): Promise<void> {
        await stockMarginOrder.goToMarginNewOrderConfirm();
        if ((await I.grabNumberOfVisibleElements(this.locator.marginNewSoftLimitConfirm)) > 0) {
            await I.clickFixed(this.locator.marginNewSoftLimitConfirm);
        }
        I.fillField(this.locator.marginNewPasswordInput, this.inputValues.password);
        await I.clickFixed(this.locator.marginNewOmmitPassword);
        await I.waitFor();
        await I.clickFixed(this.locator.marginNewOrderConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToMarginNewUturnOrderConfirm(): Promise<void> {
        await stockMarginOrder.goToMarginNewOrderInput();
        I.fillField(this.locator.marginNewOrderQuantityInput, this.inputValues.quantity);
        await I.waitFor('shortWait');
        I.fillField(this.locator.marginNewOrderPriceInput, this.inputValues.price);
        await I.waitFor('shortWait');
        await I.clickFixed(this.locator.marginNewSpecialCondition);
        await I.waitFor();
        await I.clickFixed(this.locator.marginNewUturnConditionItem);
        await I.waitFor();
        await I.clickFixed(this.locator.marginNewOrderConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToMarginPaymentUturnOrderInput(): Promise<void> {
        await stockMarginOrder.goToMarginNewUturnOrderConfirm();
        if ((await I.grabNumberOfVisibleElements(this.locator.marginNewSoftLimitConfirm)) > 0) {
            await I.clickFixed(this.locator.marginNewSoftLimitConfirm);
        }
        I.fillField(this.locator.marginNewPasswordInput, this.inputValues.password);
        await I.clickFixed(this.locator.marginNewOmmitPassword);
        await I.waitFor();
        await I.clickFixed(this.locator.marginNewOrderConfirmButton);
        await I.waitFor('mediumWait');
    },
    async goToMarginNewRelaySelection(): Promise<void> {
        await stockMarginOrder.goToMarginNewOrderInput();
        I.fillField(this.locator.marginNewOrderQuantityInput, this.inputValues.quantity);
        I.fillField(this.locator.marginNewOrderPriceInput, this.inputValues.price);
        await I.clickFixed(this.locator.marginNewSpecialCondition);
        await I.waitFor();
        await I.clickFixed(this.locator.marginNewRelayConditionItem);
        await I.waitFor();
        await I.clickFixed(this.locator.marginNewOrderConfirmButton);
        await I.waitFor('mediumWait');
    },
    async setSessionData(sessionId: string, sessionData: Record<string, any>): Promise<void> {
        const data = await I.executeScript((key) => {
            return window.sessionStorage.getItem(key);
        }, sessionId);
        const newData = { ...JSON.parse(data), ...sessionData };
        await I.executeScript(
            (key, value) => {
                return window.sessionStorage.setItem(key, JSON.stringify(value));
            },
            sessionId,
            newData,
        );
        I.refreshPage();
        await I.waitFor();
    },
};
