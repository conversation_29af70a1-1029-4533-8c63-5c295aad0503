# kCMSR E2E Test Project

This project is a project for running KCMSR end-to-end (E2E) tests using CodeceptJS.

## Setting Up

### Install Apium

```bash
npm i -g appium
```

### Install an Appium driver

```bash
appium driver install xcuitest
appium driver install uiautomator2
```

### Check if your system is ready for mobile testing

```bash
npm i -g appium-doctor
appium-doctor
```

### Launch Appium

```bash
appium --base-path=/wd/hub
```

#### Android

##### How to fix chromedriver download error

If the following error occurs

bug: No Chromedriver found that can automate Chrome error on MacOS - Appium 2 #18368  
<https://github.com/appium/appium/issues/18368#issuecomment-1481577283>

```bash
appium --base-path=/wd/hub --allow-insecure chromedriver_autodownload
```

#### iOS

##### Setup iOS Mobile Device

After enabling Developer Mode (if applicable), please turn on Settings -> Developer -> Enable UI Automation  
WebViews will not be testable unless the Safari Inspector is enabled.  
Please turn it on in Settings -> Safari -> Advanced -> Web Inspector.  
Similarly, you may want to turn on the adjacent option Settings -> Safari -> Advanced -> Remote Automation.

##### Setup WebDriverAgent

Download the [iOS Development Certificate ](/setup/Iphone-dev-cer.p12) and [Profile](/setup/IphoneDevProvisioning-2.mobileprovision) (password: Guide123)

```bash
appium driver run xcuitest open-wda
```

Open WebDriverAgent.xcodeproj in Xcode and setup follow
<br >
<img src="./setup/Setup.png" width="500" height="300" alt="XCode WebDriverAgent Setup"/>

In the file browser on the left side, select the root WebDriverAgent project, which will open it in the editor.  
Then, under Targets, select WebDriverAgentRunner (or WebDriverAgentRunner_tvOS for tvOS), and switch to the Signing & Capabilities tab.  
Check Automatically manage signing, and then select your Team (you may need to first sign into Xcode).

Full Manual Configuration  
<https://appium.github.io/appium-xcuitest-driver/latest/preparation/prov-profile-full-manual/>

Unable to launch WebDriverAgent because of xcodebuild failure: xcodebuild failed with code 65  
<https://stackoverflow.com/questions/44159951/unable-to-launch-webdriveragent-because-of-xcodebuild-failure-xcodebuild-failed>

### Project Setup

Install the necessary packages

```bash
npm install
```

Place test target app in the following directory

- android app: `./apps/android/app-develop-release.apk`
- ios app (for real device): `./apps/ios/iphoneos/Runner.app`
- ios app (for simulator): `./apps/ios/iphonesimulator/Runner.app`

## Running Tests

Run tests using the following commands

```bash
npm run test-ios
npm run test-android
```

## Generating Reports

Install Allure Report

```bash
npm install -g allure-commandline
allure --version
```

To view the report

```bash
npx allure serve allure-results
```

## CodeceptJS

CodeceptJS is a modern end to end testing framework with a special BDD-style syntax. The tests are written as a linear scenario of the user's action on a site.

see <https://codecept.io/mobile/>

CodeceptJS are using Appium and WebDriverIO

Appium  
<http://appium.io/docs/en/latest/>

WebDriverIO  
<https://webdriver.io/>

## Locating Elements

### Native Context

see <https://codecept.io/mobile/#locating-elements>

- iOS uses UIAccessibilityIdentification
- Android accessibility id matches the content-description
- XPath locators

Use Appium Inspector  
<https://github.com/appium/appium-inspector>

### WebVew Context

see <https://codecept.io/locators/>

- Semantic locators: by link text, by button text, by field names, etc.
- CSS and XPath locators
- WebView uses [aria-label] attribute as accessibility id

Use Chrome DevTools

=================== Run Test ===================
# KC Member Site E2E Test

## How to Run Tests

### 1. Run Single Test File

Run single test file specified in `.env` file:

```bash
# Run single test on iOS (file specified in .env)
npm run test-ios

# Run single test on Android (file specified in .env)  
npm run test-android
```

To change which file to run, modify `TEST_PATH_PATTERN` in `.env` file:

```bash
# Example: Run specific test file
TEST_PATH_PATTERN=./tests/101.date_picker_test.ts
```

### 2. Run All Tests

```bash
# Run all tests on iOS
npm run test:all-ios

# Run all tests on Android
npm run test:all-android
```

### 3. Run Tests by Range

Run tests from specific file number to another file number:

```bash
# Run tests from file 1 to 50 on iOS
npm run test-ios-files 1 50

# Run tests from file 10 to 25 on Android
npm run test-android-files 10 25

# Run tests from file 75 to 101 on iOS
npm run test-ios-files 75 101

# Run single test file (e.g., only file 101) same as test-ios/android (run single test file in .env)
npm run test-ios-files 101 101
```

### 4. Run Specific Test Group

Run predefined group of specific test files:

```bash
# Run specific test group on iOS
npm run test-ios-group

# Run specific test group on Android
npm run test-android-group
```

To customize the test group, edit the files list in `scripts/run-platform-tests.sh` at text `"$option" = "group"`:

```bash
test_files=$(cat << EOF
./tests/276.chart_test.ts
./tests/101.date_picker_test.ts
./tests/138.hamburger_menu_test.ts
./tests/365.favorites_list_test.ts
EOF
)
```

### 5. isOpe Mode Tests

```bash
# Run isOpe tests on iOS
npm run test:isope-ios

# Run isOpe tests on Android
npm run test:isope-android
```
> **⚠️ Important Note:** Before running isOpe mode tests, make sure to **uninstall/delete the app** from your device. Similarly, after completing isOpe tests and switching back to regular mode, you must **uninstall the app again** to ensure a clean testing environment. This prevents any residual data or state conflicts between different test modes.

## Examples

```bash
# Single file test
echo "TEST_PATH_PATTERN=./tests/101.date_picker_test.ts" > .env
npm run test-ios

# Range of files 
npm run test-ios-files 1 10

# Specific group
npm run test-ios-group

# All files
npm run test:all-ios
```

## 📊 Upload report to S3

**All test cases if need to notify to slack and upload report to s3, just add `s3` at the end of each command, for example:**

```bash
# Range of files 
npm run test-ios-files 1 10 s3

# Specific group
npm run test-ios-group s3

# All files iOS 
npm run test:all-ios s3

# All files Android
npm run test:all-android s3

# isOpe test
npm run test:isope-ios s3
npm run test:isope-android s3
```

## 🔄 Test Flow: Handling Failures & Crashes

### ❌ Test Failures
When a test fails due to **element not found**, **class issues**, etc., the system:
- ✅ Continues executing remaining tests
- ✅ After completing all files, automatically retries failed tests
- ⚙️ Retry up to `MAX_RETRY_ATTEMPTS` times (default: **2**, configurable in `.env`)

### 💥 Crash Recovery  
When a test crashes, the system:
- 🔄 Attempts to recover and retry that specific test  
- ⚙️ Retry up to `MAX_CRASH_RETRIES` times (default: **3**, configurable in `.env`)
- ⏭️ If continues crashing → skips and proceeds to next file
- 🔁 Crashed tests are included in final retry phase with other failed tests

## 📝 Notes

- 📄 **Single file tests** (`test-ios`/`test-android`) read from `.env` file
- 📁 **Range tests** (`test-ios-files`/`test-android-files`) run multiple files automatically  
- 👥 **Group tests** run predefined specific files
- 🔐 Tests will automatically run **autoLogin** first before executing main test files
- 🔄 If a test fails due to crash, the process will **restart the app** and continue
- 🎨 All test results and errors will be displayed in **colored output** for better readability
- ⚙️ If you want to run again if failed, you can adjust `MAX_RETRY_ATTEMPTS` in `.env` file (default is **2** - run 2 times including the first run)