# .github/workflows/backlog-notification.yml
name: Backlog Notification

on:
  push:
    branches:
      - '**'
  pull_request:
    types: [opened, closed, merged]

jobs:
  notify-backlog:
    runs-on: ubuntu-latest
    # guide-inc-org組織内のリポジトリでのみ実行
    # Run only for repositories within the guide-inc-org organization
    if: github.repository_owner == 'guide-inc-org'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Extract commit and PR information
        id: info
        run: |
          # イベントタイプに応じた情報取得
          # Get information based on event type
          if [ "${{ github.event_name }}" = "push" ]; then
            # Push イベントの場合
            # For push events
            COMMIT_MESSAGE=$(git log -1 --pretty=format:"%s")
            COMMIT_AUTHOR=$(git log -1 --pretty=format:"%an")
            COMMIT_EMAIL=$(git log -1 --pretty=format:"%ae")
            COMMIT_HASH=$(git log -1 --pretty=format:"%H")
            COMMIT_DATE=$(git log -1 --pretty=format:"%ai")
            EVENT_TYPE="push"
            EVENT_DESCRIPTION="push"
          
            # 変更ファイル数
            # Number of changed files
            CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD | wc -l)
          
          elif [ "${{ github.event_name }}" = "pull_request" ]; then
            # Pull Request イベントの場合
            # For pull request events
            COMMIT_MESSAGE="${{ github.event.pull_request.title }}"
            COMMIT_AUTHOR="${{ github.event.pull_request.user.login }}"
            COMMIT_EMAIL="${{ github.event.pull_request.user.email }}"
            COMMIT_HASH="${{ github.event.pull_request.head.sha }}"
            COMMIT_DATE=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
            CHANGED_FILES="${{ github.event.pull_request.changed_files }}"
          
            # PRのアクションに応じた設定
            # Settings based on PR action
            if [ "${{ github.event.action }}" = "opened" ]; then
              EVENT_TYPE="pr_opened"
              EVENT_DESCRIPTION="created pull request"
            elif [ "${{ github.event.action }}" = "closed" ] && [ "${{ github.event.pull_request.merged }}" = "true" ]; then
              EVENT_TYPE="pr_merged"
              EVENT_DESCRIPTION="merged pull request"
            elif [ "${{ github.event.action }}" = "closed" ]; then
              EVENT_TYPE="pr_closed"
              EVENT_DESCRIPTION="closed pull request"
            fi
          fi
          
          # 短縮ハッシュ
          # Short hash
          COMMIT_SHORT_HASH=${COMMIT_HASH:0:7}
          
          # 日本時間に変換
          # Convert to Japan time
          COMMIT_DATE_JST=$(TZ='Asia/Tokyo' date -d "$COMMIT_DATE" '+%Y-%m-%d %H:%M:%S')
          
          # URL生成
          # Generate URL
          COMMIT_URL="https://github.com/${{ github.repository }}/commit/${COMMIT_HASH}"
          
          # チケット番号抽出（Backlog形式: PROJECT-123 or #PROJECT-123）
          # Extract ticket numbers (Backlog format: PROJECT-123 or #PROJECT-123)
          TICKET_NUMBERS=$(echo "$COMMIT_MESSAGE" | grep -oE '#?[A-Z]+-[0-9]+' | sed 's/^#//' | sort -u | tr '\n' ' ')
          
          # 出力設定
          # Set outputs
          echo "commit_message=$COMMIT_MESSAGE" >> $GITHUB_OUTPUT
          echo "commit_author=$COMMIT_AUTHOR" >> $GITHUB_OUTPUT
          echo "commit_email=$COMMIT_EMAIL" >> $GITHUB_OUTPUT
          echo "commit_hash=$COMMIT_HASH" >> $GITHUB_OUTPUT
          echo "commit_short_hash=$COMMIT_SHORT_HASH" >> $GITHUB_OUTPUT
          echo "commit_url=$COMMIT_URL" >> $GITHUB_OUTPUT
          echo "commit_date_jst=$COMMIT_DATE_JST" >> $GITHUB_OUTPUT
          echo "changed_files=$CHANGED_FILES" >> $GITHUB_OUTPUT
          echo "ticket_numbers=$TICKET_NUMBERS" >> $GITHUB_OUTPUT
          echo "event_type=$EVENT_TYPE" >> $GITHUB_OUTPUT
          echo "event_description=$EVENT_DESCRIPTION" >> $GITHUB_OUTPUT
          
          echo "Detected ticket numbers: [$TICKET_NUMBERS]"

      - name: Post comments to Backlog tickets
        if: steps.info.outputs.ticket_numbers != ''
        env:
          BACKLOG_API_KEY: ${{ secrets.BACKLOG_API_KEY }}
        run: |
          TICKET_NUMBERS="${{ steps.info.outputs.ticket_numbers }}"
          SUCCESS_COUNT=0
          FAILED_COUNT=0
          
          for TICKET_ID in $TICKET_NUMBERS; do
            echo "===================="
            echo "Starting notification to ticket #$TICKET_ID"
          
            # BacklogGit形式のコメント作成
            # Create comment in BacklogGit format
            # コミット情報をBacklogコメント形式で生成
            # Generate commit information in Backlog comment format
            BACKLOG_COMMENT="[[${{ github.repository }}:${{ steps.info.outputs.commit_url }}]] ${{ steps.info.outputs.commit_author }} ${{ steps.info.outputs.event_description }} [[${{ steps.info.outputs.commit_short_hash }}:${{ steps.info.outputs.commit_url }}]]

          ${{ steps.info.outputs.commit_message }}

          Details:
          - Revision: ${{ steps.info.outputs.commit_short_hash }}
          - Branch: ${{ github.ref_name }}
          - Author: ${{ steps.info.outputs.commit_author }}"
          
          # メール情報（空でない場合のみ追加）
          # Add email information (only if not empty)
          if [ -n "${{ steps.info.outputs.commit_email }}" ] && [ "${{ steps.info.outputs.commit_email }}" != "null" ]; then
            BACKLOG_COMMENT="$BACKLOG_COMMENT <${{ steps.info.outputs.commit_email }}>"
          fi
          
          BACKLOG_COMMENT="$BACKLOG_COMMENT
          - Date: ${{ steps.info.outputs.commit_date_jst }}
          - Changed files: ${{ steps.info.outputs.changed_files }} files"
          
          # Pull Requestの場合は追加情報
          # Additional information for Pull Requests
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            BACKLOG_COMMENT="$BACKLOG_COMMENT
          - Pull request: [[#${{ github.event.pull_request.number }}:${{ github.event.pull_request.html_url }}]]
          - Status: ${{ github.event.action }}"
          fi
          
          BACKLOG_COMMENT="$BACKLOG_COMMENT

          [[View repository:https://github.com/${{ github.repository }}]]"
          
          # Backlog API呼び出し
          # Call Backlog API
          echo "Executing API call..."
          RESPONSE=$(curl -s -w "%{response_code}" -o backlog_response.json \
            -X POST "https://guide.backlog.com/api/v2/issues/${TICKET_ID}/comments?apiKey=${BACKLOG_API_KEY}" \
            -H "Content-Type: application/x-www-form-urlencoded" \
            --data-urlencode "content=${BACKLOG_COMMENT}")
          
          HTTP_CODE="${RESPONSE: -3}"
          
          # レスポンス処理
          # Process response
          case "$HTTP_CODE" in
            "201")
              echo "✅ Successfully posted comment to ticket #$TICKET_ID"
              SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
              ;;
            "400")
              echo "❌ Invalid request (HTTP: $HTTP_CODE)"
              echo "Response content:"
              cat backlog_response.json
              FAILED_COUNT=$((FAILED_COUNT + 1))
              ;;
            "401")
              echo "❌ Invalid API key (HTTP: $HTTP_CODE)"
              FAILED_COUNT=$((FAILED_COUNT + 1))
              ;;
            "403")
              echo "❌ No access permission to ticket #$TICKET_ID (HTTP: $HTTP_CODE)"
              FAILED_COUNT=$((FAILED_COUNT + 1))
              ;;
            "404")
              echo "❌ Ticket #$TICKET_ID not found (HTTP: $HTTP_CODE)"
              FAILED_COUNT=$((FAILED_COUNT + 1))
              ;;
            *)
              echo "❌ Unexpected error occurred (HTTP: $HTTP_CODE)"
              echo "Response content:"
              cat backlog_response.json
              FAILED_COUNT=$((FAILED_COUNT + 1))
              ;;
          esac
          
          # API制限対策（1秒待機）
          # API rate limit protection (wait 1 second)
          sleep 1
          done
          
          # 結果サマリー
          # Result summary
          echo "===================="
          echo "📊 Processing result summary"
          echo "Success: $SUCCESS_COUNT items"
          echo "Failed: $FAILED_COUNT items"
          echo "Target tickets: ${{ steps.info.outputs.ticket_numbers }}"
          
          # 全て失敗した場合はエラーで終了
          # Exit with error if all failed
          if [ "$SUCCESS_COUNT" -eq 0 ] && [ "$FAILED_COUNT" -gt 0 ]; then
            echo "❌ Failed to post to all tickets"
            exit 1
          fi

      - name: No tickets found notification
        if: steps.info.outputs.ticket_numbers == ''
        run: |
          echo "ℹ️ No ticket numbers found"
          echo "Commit message: ${{ steps.info.outputs.commit_message }}"
          echo ""
          echo "Examples of including ticket numbers:"
          echo "  git commit -m 'fix KCMSR-123: Bug fix'"
          echo "  git commit -m 'feat #KCMSR-456: Add new feature'"
          echo "  git commit -m 'docs KCMSR-789: Update documentation'"

      - name: Error notification
        if: failure()
        run: |
          echo "🚨 An error occurred during workflow execution"
          echo "Event: ${{ github.event_name }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo ""
          echo "Please check the action logs for details:"
          echo "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"