import { clickPie<PERSON><PERSON> } from './custom-steps/clickPieChart';
import { login, loginWithFilledInput } from './custom-steps/login';
import { seeInFieldEqualText } from './custom-steps/seeInFieldEqualText';
import { seeInFieldEqualValue } from './custom-steps/seeInFieldEqualValue';
import { waitFor } from './custom-steps/waitFor';
import { seeAndClickElement } from './custom-steps/seeAndClickElement';

// in this file you can append custom step methods to 'I' object

type CustomSteps = {
    login: typeof login;
    loginWithFilledInput: typeof loginWithFilledInput;
    clickPieChart: typeof clickPieChart;
    seeInFieldEqualText: typeof seeInFieldEqualText;
    seeInFieldEqualValue: typeof seeInFieldEqualValue;
    waitFor: typeof waitFor;
    seeAndClickElement: typeof seeAndClickElement;
    sampleCustomStep: (arg: string) => void;
};

export = function () {
    return actor({
        // Define custom steps here, use 'this' to access default methods of I.
        // It is recommended to place a general 'login' function here.
        login,
        loginWithFilledInput,
        clickPieChart,
        seeInFieldEqualText,
        seeInFieldEqualValue,
        seeAndClickElement,
        waitFor,
        sampleCustomStep: (arg: string) => {
            console.debug('sampleCustomStep:', arg);
        },
    }) as CodeceptJS.WithTranslation<CodeceptJS.Methods & CustomSteps>;
    // "as CodeceptJS.WithTranslation<..." is required to resolve type errors in your scenario.
    // The following is about type error resolution and intellisense.
    // see. https://codecept.discourse.group/t/custom-steps-error/771
};
