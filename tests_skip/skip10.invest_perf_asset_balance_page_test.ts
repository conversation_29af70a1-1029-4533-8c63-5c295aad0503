import { COOKIE_KEY, USER_ID } from '../const/constant';
import { modalDialog } from '../const/locator';

Feature('investPerfAssetBalancePage');

Before(async ({ I, loginAndSwitchToWebAs, portfolioTopPage }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await portfolioTopPage.goToPage();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario.skip('test invest perf asset balance page', async ({ I, investPerfAssetBalancePage }) => {
    await investPerfAssetBalancePage.goToPage();

    const assetBalanceHelpButton = locate('div').withChild(locate('p').withText('収支の見方'));
    const assetBalanceHelpSwipeTarget = locate('div').after(locate('p').withText('収支の見方').inside(modalDialog));
    const assetBalanceDetailHelpLink1 = locate('a').withText('詳しくはこちら').inside(modalDialog);
    const assetBalanceDetailHelpLink2 = locate('a').withText('収支画面に関する詳細説明').inside(modalDialog);
    I.click(assetBalanceHelpButton);
    await I.waitFor();
    I.waitForText('収支の見方1', 3, 'body');
    await I.clickFixed(assetBalanceDetailHelpLink1);
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
    await I.swipeLeftFixed(assetBalanceHelpSwipeTarget);
    await I.waitFor();
    I.waitForText('収支の見方2', 3, 'body');
    await I.clickFixed(assetBalanceDetailHelpLink2);
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
    await I.swipeRightFixed(assetBalanceHelpSwipeTarget);
    await I.waitFor();
    I.waitForText('収支の見方1', 3, 'body');
    await I.clickFixed('//button[@aria-label="cancel-btn"]');
    await I.waitFor();

    const sortButton = locate('.button').withText('収支').withChild('#updown');
    const productListFirstItem = locate('p').inside('.tr.button').first();
    I.click(sortButton);
    await I.waitFor();
    const productListFirstItemText1 = await I.grabTextFrom(productListFirstItem);
    I.assertNotEqual(productListFirstItemText1, '国内株式現物');
    I.click(sortButton);
    await I.waitFor();
    const productListFirstItemText2 = await I.grabTextFrom(productListFirstItem);
    I.assertEqual(productListFirstItemText2, '国内株式現物');

    const productListItem = locate('.button').withDescendant(locate('p').withText('国内株式現物'));
    const detailPageHeader = locate('div').withChild('#arrow-left');
    const detailPageBackButton = locate('button').withText('戻る');
    I.click(productListItem);
    await I.waitFor('longWait');
    let prevButton = locate('#arrow-left').before(locate('p').withText('国内株式現物'));
    I.click(prevButton);
    await I.waitFor();
    I.waitForText('オプション', 3, detailPageHeader);
    prevButton = locate('#arrow-left').before(locate('p').withText('オプション'));
    I.click(prevButton);
    await I.waitFor();
    I.waitForText('先物', 3, detailPageHeader);
    prevButton = locate('#arrow-left').before(locate('p').withText('先物'));
    I.click(prevButton);
    await I.waitFor();
    I.waitForText('国内株式信用', 3, detailPageHeader);
    prevButton = locate('#arrow-left').before(locate('p').withText('国内株式信用'));
    I.click(prevButton);
    await I.waitFor();
    I.waitForText('投資信託', 3, detailPageHeader);
    prevButton = locate('#arrow-left').before(locate('p').withText('投資信託'));
    I.click(prevButton);
    await I.waitFor();
    I.waitForText('米国株式現物', 3, detailPageHeader);
    prevButton = locate('#arrow-left').before(locate('p').withText('米国株式現物'));
    I.click(prevButton);
    await I.waitFor();
    I.waitForText('国内株式現物', 3, detailPageHeader);

    let nextButton = locate('#arrow-left').after(locate('p').withText('国内株式現物'));
    I.click(nextButton);
    await I.waitFor();
    I.waitForText('米国株式現物', 3, detailPageHeader);
    nextButton = locate('#arrow-left').after(locate('p').withText('米国株式現物'));
    I.click(nextButton);
    await I.waitFor();
    I.waitForText('投資信託', 3, detailPageHeader);
    nextButton = locate('#arrow-left').after(locate('p').withText('投資信託'));
    I.click(nextButton);
    await I.waitFor();
    I.waitForText('国内株式信用', 3, detailPageHeader);
    nextButton = locate('#arrow-left').after(locate('p').withText('国内株式信用'));
    I.click(nextButton);
    await I.waitFor();
    I.waitForText('先物', 3, detailPageHeader);
    nextButton = locate('#arrow-left').after(locate('p').withText('先物'));
    I.click(nextButton);
    await I.waitFor();
    I.waitForText('オプション', 3, detailPageHeader);
    nextButton = locate('#arrow-left').after(locate('p').withText('オプション'));
    I.click(nextButton);
    await I.waitFor();
    I.waitForText('国内株式現物', 3, detailPageHeader);

    I.click(detailPageBackButton);
    await I.waitFor();

    const assetBalanceLegend = locate('p').withText('収支');
    const profitAmountLegend = locate('p').withText('利益額');
    const lossAmountLegend = locate('p').withText('損失額');
    I.click(assetBalanceLegend);
    await I.waitFor();
    I.click(profitAmountLegend);
    await I.waitFor();
    I.click(lossAmountLegend);
    await I.waitFor();
    I.click(assetBalanceLegend);
    await I.waitFor();
    I.click(profitAmountLegend);
    await I.waitFor();
    I.click(lossAmountLegend);
    await I.waitFor();
});

Scenario.skip('test invest perf asset balance page [playground]', async ({ I, investPerfAssetBalancePage }) => {
    await investPerfAssetBalancePage.goToPage();

    await I.waitFor();
    // test some code here
});
