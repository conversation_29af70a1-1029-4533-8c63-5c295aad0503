import { COOKIE_KEY } from '../const/constant';
import { menuButton, menuItem, menuList, radioButton, radioGroup, slideDialog, tabButton } from '../const/locator';

Feature('stockBuyInputPage');

Before(async ({ I, loginAndSwitchToWebAs, portfolioTopPage }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await portfolioTopPage.goToPage();
    I.setCookie({ name: COOKIE_KEY.userId, value: 'e2etestuser1' });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario.skip('test stock buy input page', async ({ I, stockBuyInputPage }) => {
    await stockBuyInputPage.goToPage();

    I.click('取引制限・取引注意情報');
    await I.waitFor();
    I.waitForText('取引制限・取引注意情報', 3, slideDialog);
    const restrictionSlideDialog = slideDialog.withDescendant(locate('p').withText('取引制限・取引注意情報'));
    I.click('~close-btn', restrictionSlideDialog);
    await I.waitFor();

    const exchangeGroup = locate('div').after(locate('p').withText('市場'));
    const exchange = menuButton.inside(exchangeGroup);
    const exchangeMenuList = menuList.inside(exchangeGroup);
    const exchangeMenuItems = menuItem.inside(exchangeMenuList);
    I.see('東京 プライム', exchange);
    I.dontSee('名証', exchange);
    I.click(exchange);
    await I.waitFor();
    I.see('東京 プライム', exchangeMenuItems);
    I.see('名古屋 プレミア', exchangeMenuItems);
    I.see('福岡', exchangeMenuItems);
    I.see('札幌', exchangeMenuItems);
    const exchangeNagoya = menuItem.withText('名古屋 プレミア').inside(exchangeGroup);
    I.click(exchangeNagoya);
    await I.waitFor();
    I.dontSee('東京 プライム', exchange);
    I.see('名古屋 プレミア', exchange);

    I.click(exchange);
    await I.waitFor();
    const exchangeTokyo = menuItem.withText('東京 プライム').inside(exchangeGroup);
    I.click(exchangeTokyo);
    await I.waitFor();
    I.see('東京 プライム', exchange);

    const duringWeek = locate('p').withText('今週中').inside(radioGroup);
    const duringWeekButton = locate('.button').withChild(duringWeek);
    const duringWeekRadioButton = radioButton.before(duringWeek);
    I.click(duringWeekButton);
    await I.waitFor();
    const isRadioChecked = await I.grabAttributeFrom(duringWeekRadioButton, 'checked');
    console.debug('isRadioChecked:', isRadioChecked);
    I.assertEqual(isRadioChecked, 'true');

    const specifyPeriod = locate('p').withText('期間指定').inside(radioGroup);
    const specifyPeriodButton = locate('.button').withChild(specifyPeriod);
    I.click(specifyPeriodButton);
    await I.waitFor();
    const lastAvailableDay = locate('.DayPicker-Day')
        .withAttr({ 'aria-disabled': 'false' })
        .inside('.DayPicker')
        .last();
    I.click(lastAvailableDay);
    await I.waitFor();
    I.seeElement('#calendar');

    const orderPeriod = locate('div').withChild('#calendar');
    I.click(orderPeriod);
    await I.waitFor();
    I.seeElement('.DayPicker-Day');
    I.click('~closeIcon');
    await I.waitFor();

    const marketOrderTab = tabButton.withText('成行');
    I.click(marketOrderTab);
    await I.waitFor();
    const isTabSelected = await I.grabAttributeFrom(marketOrderTab, 'aria-selected');
    console.debug('isTabSelected:', isTabSelected);
    I.assertEqual(isTabSelected, 'true');
});

// 指値
Scenario.skip('test stock buy input page [limit order]', async ({ I, stockBuyInputPage }) => {
    await stockBuyInputPage.goToPage();

    const limitOrderTab = tabButton.withText('指値');
    I.click(limitOrderTab);
    await I.waitFor();

    const quantityGroup = locate('div').withChild(locate('p').withText('数量'));
    const quantity = locate('#groupInputNumber').inside(quantityGroup);
    const quantityMinusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'minus' }))
        .inside(quantityGroup);
    const quantityPlusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'plus' }))
        .inside(quantityGroup);
    I.click(quantity);
    I.fillField(quantity, '');
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualText(quantity, '');
    I.click(quantity);
    I.fillField(quantity, 0);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');

    I.click(quantity);
    I.fillField(quantity, 10000);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '10,000');
    await I.clickFixed(quantityMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');
    await I.clickFixed(quantityMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '10,000');

    I.click(quantity);
    I.fillField(quantity, 9999995000);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,995,000');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,999,999');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,999,999');

    const priceGroup = locate('div').withChild(locate('div').withChild(locate('p').withText('価格')));
    const price = locate('#groupInputNumber').inside(priceGroup);
    const priceMinusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'minus' }))
        .inside(priceGroup);
    const pricePlusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'plus' }))
        .inside(priceGroup);
    I.click(price);
    I.fillField(price, '');
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualText(price, '');
    I.click(price);
    I.fillField(price, 0);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '912.20');

    I.click(price);
    I.fillField(price, 1000);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '1,000.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '950.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '912.20');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '912.20');

    I.click(price);
    I.fillField(price, 2950);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '2,950.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '3,000.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '3,001.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '3,000.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '2,950.00');

    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    I.click(price);
    I.fillField(price, 7999999.0);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '7,999,999.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '8,000,000.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '8,000,000.20');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '8,000,000.20');

    const boardInput = locate('div').withChild(locate('p').withText('板入力')).inside(priceGroup);
    const boardInputDialog = slideDialog.withDescendant(locate('p').withText('板入力'));
    const askPriceFirst = locate('p').withAttrContains('id', 'ask_').inside(boardInputDialog);
    const askPriceRowFirst = locate('div').withAttrContains('id', 'ask_').inside(boardInputDialog);
    I.click(boardInput);
    await I.waitFor();
    const askPriceText = await I.grabTextFrom(askPriceFirst);
    console.debug('askPriceText:', askPriceText);
    await I.clickFixed(askPriceRowFirst);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, askPriceText);
    I.click(price);
    I.fillField(price, '');
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualText(price, '');

    const chartInput = locate('div').withChild(locate('p').withText('チャート入力')).inside(priceGroup);
    const chartInputDialog = slideDialog.withDescendant(locate('p').withText('チャート入力'));
    const chart = locate('div').withClassAttr('highcharts-container').inside(chartInputDialog);
    const chartPrice = locate('#price-value').inside(chart);
    I.click(chartInput);
    await I.waitFor();
    await I.pressWithoutReleaseLocationOfElement(chart); // Same as tap on iOS
    await I.waitFor();
    I.runOnAndroid(undefined, async () => {
        // TODO: can't separate press and release on iOS [as of 2024/03/13]
        const chartPriceText = await I.grabTextFrom(chartPrice);
        const chartPriceTextRounded = chartPriceText.replace(/\.\d{2}$/, '.00');
        console.debug('chartPriceTextRounded:', chartPriceTextRounded);
        await I.releaseAction();
        await I.waitFor();
        await I.seeInFieldEqualValue(price, chartPriceTextRounded);
    });
    const priceText = await I.grabValueFrom(price);
    const priceValue = Number(priceText.replace(/,/g, ''));
    I.assertAbove(priceValue, 0);

    const executeCondition = menuButton.after(locate('p').withText('執行条件'));
    const limitOrderOnMorningMarketOpen = menuItem.withChild(locate('span').withText('寄指(前場)'));
    const selectedExecuteCondition = locate('span').inside(executeCondition);
    I.click(executeCondition);
    await I.waitFor();
    I.click(limitOrderOnMorningMarketOpen);
    await I.waitFor();
    const selectedExecuteConditionText = await I.grabTextFrom(selectedExecuteCondition);
    I.assertEqual(selectedExecuteConditionText, '寄指(前場)');

    const specialCondition = menuButton.after(locate('p').withText('特殊条件'));
    const relayOrder = menuItem.withChild(locate('span').withText('リレー注文'));
    const selectedSpecialCondition = locate('span').inside(specialCondition);
    I.click(specialCondition);
    await I.waitFor();
    I.click(relayOrder);
    await I.waitFor();
    const selectedSpecialConditionText = await I.grabTextFrom(selectedSpecialCondition);
    I.assertEqual(selectedSpecialConditionText, 'リレー注文');
});

// 成行
Scenario.skip('test stock buy input page [market order]', async ({ I, stockBuyInputPage }) => {
    await stockBuyInputPage.goToPage();

    const marketOrderTab = tabButton.withText('成行');
    I.click(marketOrderTab);
    await I.waitFor();

    const quantityGroup = locate('div').withChild(locate('div').withChild(locate('p').withText('数量')));
    const quantity = locate('#groupInputNumber').inside(quantityGroup);
    const quantityMinusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'minus' }))
        .inside(quantityGroup);
    const quantityPlusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'plus' }))
        .inside(quantityGroup);
    I.click(quantity);
    I.fillField(quantity, '');
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualText(quantity, '');
    I.click(quantity);
    I.fillField(quantity, 0);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');

    I.click(quantity);
    I.fillField(quantity, 10000);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '10,000');
    await I.clickFixed(quantityMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');
    await I.clickFixed(quantityMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '10,000');

    I.click(quantity);
    I.fillField(quantity, 9999995000);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,995,000');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,999,999');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,999,999');

    const executeCondition = menuButton.after(locate('p').withText('執行条件'));
    const marketOrderOnMorningMarketOpen = menuItem.withChild(locate('span').withText('寄成(前場)'));
    const selectedExecuteCondition = locate('span').inside(executeCondition);
    I.click(executeCondition);
    await I.waitFor();
    I.click(marketOrderOnMorningMarketOpen);
    await I.waitFor();
    const selectedExecuteConditionText = await I.grabTextFrom(selectedExecuteCondition);
    I.assertEqual(selectedExecuteConditionText, '寄成(前場)');

    const specialCondition = menuButton.after(locate('p').withText('特殊条件'));
    const relayOrder = menuItem.withChild(locate('span').withText('リレー注文'));
    const selectedSpecialCondition = locate('span').inside(specialCondition);
    I.click(specialCondition);
    await I.waitFor();
    I.click(relayOrder);
    await I.waitFor();
    const selectedSpecialConditionText = await I.grabTextFrom(selectedSpecialCondition);
    I.assertEqual(selectedSpecialConditionText, 'リレー注文');
});

// 自動売買: 逆指値
Scenario.skip('test stock buy input page [automation trading: stop loss]', async ({ I, stockBuyInputPage }) => {
    await stockBuyInputPage.goToPage();

    const automaticTradingTab = tabButton.withText('自動売買');
    I.click(automaticTradingTab);
    await I.waitFor();

    const executionMethod = locate('button').inside(locate('div').withClassAttr('select-collapse'));
    const executionMethodMenuList = locate('div').withClassAttr('chakra-collapse').after(executionMethod);
    const executionMethodMenuItems = locate('p').inside(executionMethodMenuList);
    const stopLoss = locate('p').withText('逆指値').inside(executionMethodMenuList);
    const selectedExecutionMethod = locate('p').inside(executionMethod);
    I.click(executionMethod);
    await I.waitFor();
    const executionMethodTexts = await I.grabTextFromAll(executionMethodMenuItems);
    console.debug('executionMethodTexts:', executionMethodTexts);
    I.assertContain(executionMethodTexts, '逆指値');
    I.assertContain(executionMethodTexts, 'W指値');
    I.assertContain(executionMethodTexts, '±指値');
    I.assertContain(executionMethodTexts, 'トレーリングストップ');
    const executionMethodMenuListDisplay = await I.grabCssPropertyFrom(executionMethodMenuList, 'display');
    I.assertNotEqual(executionMethodMenuListDisplay, 'none');
    I.click(stopLoss);
    await I.waitFor();
    const selectedExecutionMethodText = await I.grabTextFrom(selectedExecutionMethod);
    I.assertEqual(selectedExecutionMethodText, '逆指値');

    const quantityGroup = locate('div').withChild(locate('div').withChild(locate('p').withText('数量')));
    const quantity = locate('#groupInputNumber').inside(quantityGroup);
    const quantityMinusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'minus' }))
        .inside(quantityGroup);
    const quantityPlusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'plus' }))
        .inside(quantityGroup);
    I.click(quantity);
    I.fillField(quantity, '');
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualText(quantity, '');
    I.click(quantity);
    I.fillField(quantity, 0);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');

    I.click(quantity);
    I.fillField(quantity, 10000);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '10,000');
    await I.clickFixed(quantityMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');
    await I.clickFixed(quantityMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '5,000');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '10,000');

    I.click(quantity);
    I.fillField(quantity, 9999995000);
    I.blur(quantity);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,995,000');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,999,999');
    await I.clickFixed(quantityPlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(quantity, '9,999,999,999');

    const priceGroup = locate('div').withChild(locate('div').withChild(locate('p').withText('価格')));
    const indicator = menuButton.inside(locate('div').before(priceGroup));
    const indicatorMenuList = menuList.inside(locate('div').after(indicator));
    const stockPrice = menuItem.withChild(locate('span').withText('株価')).inside(indicatorMenuList);
    I.click(indicator);
    await I.waitFor();
    const indicatorMenuListVisibility = await I.grabCssPropertyFrom(indicatorMenuList, 'visibility');
    I.assertEqual(indicatorMenuListVisibility, 'visible');
    I.click(stockPrice);
    const indicatorText = await I.grabTextFrom(indicator);
    I.assertEqual(indicatorText, '株価');

    const price = locate('#groupInputNumber').inside(priceGroup);
    const priceMinusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'minus' }))
        .inside(priceGroup);
    const pricePlusButton = locate('~chevron-right-btn')
        .withChild(locate('img').withAttr({ alt: 'plus' }))
        .inside(priceGroup);
    I.click(price);
    I.fillField(price, '');
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualText(price, '');
    I.click(price);
    I.fillField(price, 0);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '0.00');

    I.click(price);
    I.fillField(price, 100);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '100.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '50.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '0.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '0.00');

    I.click(price);
    I.fillField(price, 2950);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '2,950.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '3,000.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '3,001.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '3,000.00');
    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '2,950.00');

    await I.clickFixed(priceMinusButton);
    await I.waitFor();
    I.click(price);
    I.fillField(price, 9999999997);
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '9,999,999,997.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '9,999,999,998.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '9,999,999,999.00');
    await I.clickFixed(pricePlusButton);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, '9,999,999,999.00');

    const moreOrLess = menuButton.inside(locate('div').after(priceGroup));
    const moreOrLessMenuList = menuList.inside(locate('div').after(moreOrLess));
    const moreThan = menuItem.withChild(locate('span').withText('以上')).inside(moreOrLessMenuList);
    I.click(moreOrLess);
    await I.waitFor();
    const moreOrLessMenuListVisibility = await I.grabCssPropertyFrom(moreOrLessMenuList, 'visibility');
    I.assertEqual(moreOrLessMenuListVisibility, 'visible');
    I.click(moreThan);
    const moreOrLessText = await I.grabTextFrom(moreOrLess);
    I.assertEqual(moreOrLessText, '以上');

    const boardInput = locate('div').withChild(locate('p').withText('板入力')).inside(priceGroup);
    const boardInputDialog = slideDialog.withDescendant(locate('p').withText('板入力'));
    const askPriceFirst = locate('p').withAttrContains('id', 'ask_').inside(boardInputDialog);
    const askPriceRowFirst = locate('div').withAttrContains('id', 'ask_').inside(boardInputDialog);
    I.click(boardInput);
    await I.waitFor();
    const askPriceText = await I.grabTextFrom(askPriceFirst);
    console.debug('askPriceText:', askPriceText);
    await I.clickFixed(askPriceRowFirst);
    await I.waitFor();
    await I.seeInFieldEqualValue(price, askPriceText);
    I.click(price);
    I.fillField(price, '');
    I.blur(price);
    await I.waitFor();
    await I.seeInFieldEqualText(price, '');

    const chartInput = locate('div').withChild(locate('p').withText('チャート入力')).inside(priceGroup);
    const chartInputDialog = slideDialog.withDescendant(locate('p').withText('チャート入力'));
    const chart = locate('div').withClassAttr('highcharts-container').inside(chartInputDialog);
    const chartPrice = locate('#price-value').inside(chart);
    I.click(chartInput);
    await I.waitFor();
    await I.pressWithoutReleaseLocationOfElement(chart); // Same as tap on iOS
    await I.waitFor();
    I.runOnAndroid(undefined, async () => {
        // TODO: can't separate press and release on iOS [as of 2024/03/13]
        const chartPriceText = await I.grabTextFrom(chartPrice);
        const chartPriceTextRounded = chartPriceText.replace(/\.\d{2}$/, '.00');
        console.debug('chartPriceTextRounded:', chartPriceTextRounded);
        await I.releaseAction();
        await I.waitFor();
        await I.seeInFieldEqualValue(price, chartPriceTextRounded);
    });
    const priceText = await I.grabValueFrom(price);
    const priceValue = Number(priceText.replace(/,/g, ''));
    I.assertAbove(priceValue, 0);

    const specialCondition = menuButton.after(locate('p').withText('特殊条件'));
    const relayOrder = menuItem.withChild(locate('span').withText('リレー注文'));
    const selectedSpecialCondition = locate('span').inside(specialCondition);
    I.click(specialCondition);
    await I.waitFor();
    I.click(relayOrder);
    await I.waitFor();
    const selectedSpecialConditionText = await I.grabTextFrom(selectedSpecialCondition);
    I.assertEqual(selectedSpecialConditionText, 'リレー注文');

    const pricePanel = locate('div').withChild(priceGroup);
    const executeMethodPanel = locate('div').withDescendant(radioGroup).after(pricePanel);
    // In Execute Method Panel
    await within(executeMethodPanel, async () => {
        const marketOrder = radioButton
            .before(locate('div').withChild(locate('div').withText('成行')))
            .inside(executeMethodPanel);
        const limitOrder = radioButton
            .before(locate('div').withChild(locate('div').withText('指値')))
            .inside(executeMethodPanel);
        I.click('指値'); // I.click(limitOrder) is not working on Android
        await I.waitFor();
        let limitOrderChecked = await I.grabAttributeFrom(limitOrder, 'checked');
        console.debug('limitOrderChecked:', limitOrderChecked);
        I.assertEqual(limitOrderChecked, 'true');
        I.click('成行'); // I.click(marketOrder) is not working on Android
        await I.waitFor();
        const marketOrderChecked = await I.grabAttributeFrom(marketOrder, 'checked');
        console.debug('marketOrderChecked:', marketOrderChecked);
        I.assertEqual(marketOrderChecked, 'true');

        I.click('指値'); // I.click(limitOrder) is not working on Android
        await I.waitFor();
        limitOrderChecked = await I.grabAttributeFrom(limitOrder, 'checked');
        console.debug('limitOrderChecked:', limitOrderChecked);
        I.assertEqual(limitOrderChecked, 'true');

        const executeCondition = menuButton.inside(executeMethodPanel);
        const noCondition = menuItem.withChild(locate('span').withText('条件なし'));
        const executeConditionMenuList = menuList.inside(executeMethodPanel);
        I.click(executeCondition);
        await I.waitFor();
        const executeConditionMenuListVisibility = await I.grabCssPropertyFrom(executeConditionMenuList, 'visibility');
        I.assertEqual(executeConditionMenuListVisibility, 'visible');
        I.click(noCondition);
        await I.waitFor();
        const executeConditionText = await I.grabTextFrom(executeCondition);
        I.assertEqual(executeConditionText, '条件なし');

        // 執行方法: 指値、条件なし
        const executeMethodPriceGroup = locate('div')
            .withChild(locate('div').withChild(locate('p').withText('価格')))
            .inside(executeMethodPanel);
        const price = locate('#groupInputNumber').inside(executeMethodPriceGroup);
        const priceMinusButton = locate('~chevron-right-btn')
            .withChild(locate('img').withAttr({ alt: 'minus' }))
            .inside(executeMethodPriceGroup);
        const pricePlusButton = locate('~chevron-right-btn')
            .withChild(locate('img').withAttr({ alt: 'plus' }))
            .inside(executeMethodPriceGroup);
        I.click(price);
        I.fillField(price, '');
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualText(price, '');
        I.click(price);
        I.fillField(price, 0);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '0.00');

        I.click(price);
        I.fillField(price, 100);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '100.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '50.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '0.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '0.00');

        I.click(price);
        I.fillField(price, 2950);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '2,950.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '3,000.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '3,001.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '3,000.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '2,950.00');

        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        I.click(price);
        I.fillField(price, 9999999997);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,997.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,998.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,999.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,999.00');

        const boardInput = locate('div').withChild(locate('p').withText('板入力')).inside(executeMethodPriceGroup);
        I.click(boardInput);
        await I.waitFor();
        const askPriceText = await I.grabTextFrom(askPriceFirst);
        console.debug('askPriceText:', askPriceText);
        await I.clickFixed(askPriceRowFirst);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, askPriceText);
        I.click(price);
        I.fillField(price, '');
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualText(price, '');

        const priceChartInput = locate('div')
            .withChild(locate('p').withText('チャート入力'))
            .inside(executeMethodPriceGroup);
        I.click(priceChartInput);
        await I.waitFor();
        I.tapLocationOfElement(chart);
        await I.waitFor();
        // TODO: (CodeceptJS Bug) within and I.runOnAndroid can't be used together on Android
        // await I.pressWithoutReleaseLocationOfElement(chart); // Same as tap on iOS
        // await I.waitFor();
        // I.runOnAndroid(undefined, async () => {
        //     // TODO: can't separate press and release on iOS [as of 2024/03/13]
        //     const chartPriceText = await I.grabTextFrom(chartPrice);
        //     const chartPriceTextRounded = chartPriceText.replace(/\.\d{2}$/, '.00');
        //     console.debug('chartPriceTextRounded:', chartPriceTextRounded);
        //     await I.releaseAction();
        //     await I.waitFor();
        //     await I.seeInFieldEqualValue(price, chartPriceTextRounded);
        // });
        const priceText = await I.grabValueFrom(price);
        const priceValue = Number(priceText.replace(/,/g, ''));
        I.assertAbove(priceValue, 0);
    });
});

Scenario.skip('test stock buy input page [playground]', async ({ I, stockBuyInputPage }) => {
    await stockBuyInputPage.goToPage();

    const automaticTradingTab = tabButton.withText('自動売買');
    I.click(automaticTradingTab);
    await I.waitFor();

    const executionMethod = locate('button').inside(locate('div').withClassAttr('select-collapse'));
    const executionMethodMenuList = locate('div').withClassAttr('chakra-collapse').after(executionMethod);
    const stopLoss = locate('p').withText('逆指値').inside(executionMethodMenuList);
    const selectedExecutionMethod = locate('p').inside(executionMethod);
    I.click(executionMethod);
    await I.waitFor();
    I.click(stopLoss);
    await I.waitFor();
    const selectedExecutionMethodText = await I.grabTextFrom(selectedExecutionMethod);
    I.assertEqual(selectedExecutionMethodText, '逆指値');

    const priceGroup = locate('div').withChild(locate('div').withChild(locate('p').withText('価格')));

    const moreOrLess = menuButton.inside(locate('div').after(priceGroup));
    const moreOrLessMenuList = menuList.inside(locate('div').after(moreOrLess));
    const moreThan = menuItem.withChild(locate('span').withText('以上')).inside(moreOrLessMenuList);
    I.click(moreOrLess);
    await I.waitFor();
    const moreOrLessMenuListVisibility = await I.grabCssPropertyFrom(moreOrLessMenuList, 'visibility');
    I.assertEqual(moreOrLessMenuListVisibility, 'visible');
    I.click(moreThan);
    const moreOrLessText = await I.grabTextFrom(moreOrLess);
    I.assertEqual(moreOrLessText, '以上');

    const boardInputDialog = slideDialog.withDescendant(locate('p').withText('板入力'));
    const askPriceFirst = locate('p').withAttrContains('id', 'ask_').inside(boardInputDialog);
    const askPriceRowFirst = locate('div').withAttrContains('id', 'ask_').inside(boardInputDialog);

    const chartInputDialog = slideDialog.withDescendant(locate('p').withText('チャート入力'));
    const chart = locate('div').withClassAttr('highcharts-container').inside(chartInputDialog);
    const chartPrice = locate('#price-value').inside(chart);

    const pricePanel = locate('div').withChild(priceGroup);
    const executeMethodPanel = locate('div').withDescendant(radioGroup).after(pricePanel);
    // In Execute Method Panel
    // I.moveCursorTo(executeMethodPanel);
    await within(executeMethodPanel, async () => {
        const marketOrder = radioButton
            .before(locate('div').withChild(locate('div').withText('成行')))
            .inside(executeMethodPanel);
        const limitOrder = radioButton
            .before(locate('div').withChild(locate('div').withText('指値')))
            .inside(executeMethodPanel);
        I.click('指値');
        await I.waitFor();
        let limitOrderChecked = await I.grabAttributeFrom(limitOrder, 'checked');
        console.debug('limitOrderChecked:', limitOrderChecked);
        I.assertEqual(limitOrderChecked, 'true');
        I.click('成行');
        await I.waitFor();
        const marketOrderChecked = await I.grabAttributeFrom(marketOrder, 'checked');
        console.debug('marketOrderChecked:', marketOrderChecked);
        I.assertEqual(marketOrderChecked, 'true');

        I.click('指値');
        await I.waitFor();
        limitOrderChecked = await I.grabAttributeFrom(limitOrder, 'checked');
        console.debug('limitOrderChecked:', limitOrderChecked);
        I.assertEqual(limitOrderChecked, 'true');

        const executeCondition = menuButton.inside(executeMethodPanel);
        const noCondition = menuItem.withChild(locate('span').withText('条件なし'));
        const executeConditionMenuList = menuList.inside(executeMethodPanel);
        I.click(executeCondition);
        await I.waitFor();
        const executeConditionMenuListVisibility = await I.grabCssPropertyFrom(executeConditionMenuList, 'visibility');
        I.assertEqual(executeConditionMenuListVisibility, 'visible');
        I.click(noCondition);
        await I.waitFor();
        const executeConditionText = await I.grabTextFrom(executeCondition);
        I.assertEqual(executeConditionText, '条件なし');

        // 執行方法: 指値、条件なし
        const priceGroupInExecuteMethodPanel = locate('div')
            .withChild(locate('div').withChild(locate('p').withText('価格')))
            .inside(executeMethodPanel);
        const price = locate('#groupInputNumber').inside(priceGroupInExecuteMethodPanel);
        const priceMinusButton = locate('~chevron-right-btn')
            .withChild(locate('img').withAttr({ alt: 'minus' }))
            .inside(priceGroupInExecuteMethodPanel);
        const pricePlusButton = locate('~chevron-right-btn')
            .withChild(locate('img').withAttr({ alt: 'plus' }))
            .inside(priceGroupInExecuteMethodPanel);
        I.click(price);
        I.fillField(price, '');
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualText(price, '');
        I.click(price);
        I.fillField(price, 0);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '0.00');

        I.click(price);
        I.fillField(price, 100);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '100.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '50.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '0.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '0.00');

        I.click(price);
        I.fillField(price, 2950);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '2,950.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '3,000.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '3,001.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '3,000.00');
        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '2,950.00');

        await I.clickFixed(priceMinusButton);
        await I.waitFor();
        I.click(price);
        I.fillField(price, 9999999997);
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,997.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,998.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,999.00');
        await I.clickFixed(pricePlusButton);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, '9,999,999,999.00');

        const boardInput = locate('div')
            .withChild(locate('p').withText('板入力'))
            .inside(priceGroupInExecuteMethodPanel);
        I.click(boardInput);
        await I.waitFor();
        const askPriceText = await I.grabTextFrom(askPriceFirst);
        console.debug('askPriceText:', askPriceText);
        await I.clickFixed(askPriceRowFirst);
        await I.waitFor();
        await I.seeInFieldEqualValue(price, askPriceText);
        I.click(price);
        I.fillField(price, '');
        I.blur(price);
        await I.waitFor();
        await I.seeInFieldEqualText(price, '');

        const priceChartInput = locate('div')
            .withChild(locate('p').withText('チャート入力'))
            .inside(priceGroupInExecuteMethodPanel);
        I.click(priceChartInput);
        await I.waitFor();
        await I.pressWithoutReleaseLocationOfElement(chart); // Same as tap on iOS
        await I.waitFor();
        // await I.runOnAndroid(undefined, async () => {
        // TODO: can't separate press and release on iOS [as of 2024/03/13]
        const chartPriceText = await I.grabTextFrom(chartPrice);
        const chartPriceTextRounded = chartPriceText.replace(/\.\d{2}$/, '.00');
        console.debug('chartPriceTextRounded:', chartPriceTextRounded);
        await I.releaseAction();
        await I.waitFor();
        await I.seeInFieldEqualValue(price, chartPriceTextRounded);
        // });
        const priceText = await I.grabValueFrom(price);
        const priceValue = Number(priceText.replace(/,/g, ''));
        I.assertAbove(priceValue, 0);
    });

    // TODO: (node:42059) MaxListenersExceededWarning: Possible EventEmitter memory leak detected.
    // test some code here
});
