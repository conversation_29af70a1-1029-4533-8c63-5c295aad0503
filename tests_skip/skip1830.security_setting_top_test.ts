Feature('securitySettingTopPage');
import { COOKIE_KEY, SCREENSHOT_PREFIX, USER_ID } from '../const/constant';
import SecuritySettingTop from '../pages/securitySettingTop';

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.user42 });
    await I.waitFor();
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario('go to Security Setting Top page', async () => {
    await SecuritySettingTop.goToSecuritySettingTopPage();
});

Scenario('Item 8: Caution Message', async ({ I }) => {
    // We are already on the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_8_Caution_Message_Before_Click.png`,
    );

    // Click on the caution message
    await SecuritySettingTop.clickCaution();

    // Check if the caution content is displayed with "display: block"
    const displayStyle = await I.grabCssPropertyFrom(SecuritySettingTop.locator.cautionContent, 'display');
    I.say(`Caution content display style: ${displayStyle}`);
    I.assertEqual(displayStyle, 'block', 'Caution content should be displayed with display: block');

    // Take a screenshot after clicking
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_8_Caution_Message_After_Click.png`);
});

Scenario('Item 10: Resend Mail', async ({ I }) => {
    // We are already on the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_10_Resend_Mail_Before_Click.png`);

    // Click on the resend mail button
    await SecuritySettingTop.clickResendMail();

    // Check if the toast message appears
    I.seeElement(SecuritySettingTop.locator.toastMessage);
    I.say('Toast message is visible');

    // Take a screenshot after clicking to show the toast
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_10_Resend_Mail_After_Click.png`);
});

Scenario('Item 9: Change', async ({ I }) => {
    // Navigate to the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_9_Change_Before_Click.png`);

    // Get current context, click change button, and handle context switching
    await SecuritySettingTop.clickChangeAndSwitchContext();

    // Take a screenshot after context switching
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_9_Change_After_Context_Switch.png`);

    // Get current URL to verify we're in a different context
    const currentUrl = await I.grabCurrentUrl();
    I.say(`Current URL after context switch: ${currentUrl}`);
});

Scenario('Item 15: Change/Register', async ({ I }) => {
    // Navigate to the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_15_Change_Register_Before_Click.png`,
    );

    // Click on the change/register button
    await SecuritySettingTop.clickChangeRegister();

    // Check if the URL contains the expected path
    I.seeInCurrentUrl('/setting/security/email-registration');
    I.say('Successfully navigated to email registration page');

    // Take a screenshot after navigation
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_15_Change_Register_After_Click.png`,
    );

    // Click back to return
    await SecuritySettingTop.clickBack();
});

Scenario('Item 18: Login Notify Setting Toggle', async ({ I }) => {
    // Navigate to the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_18_Login_Notify_Before_Click.png`);

    // Click on the login notify switch
    await SecuritySettingTop.clickLoginNotifySwitch();

    // Check the login notify status text
    const statusText = await SecuritySettingTop.getLoginNotifyStatus();
    I.say(`Login notify status text: ${statusText}`);
    I.assertEqual(statusText, '有効', 'Login notify status should be "有効" (enabled)');

    // Take a screenshot after clicking
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_18_Login_Notify_After_Click.png`);
});

Scenario('Item 19: Change Registration', async ({ I }) => {
    // Navigate to the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_19_Change_Registration_Before_Click.png`,
    );

    // Get current context, click change registration button, and handle context switching
    await SecuritySettingTop.clickChangeRegistrationAndSwitchContext();

    // Take a screenshot after context switching
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_19_Change_Registration_After_Context_Switch.png`,
    );

    // Get current URL to verify we're in a different context
    const currentUrl = await I.grabCurrentUrl();
    I.say(`Current URL after context switch: ${currentUrl}`);
});

Scenario('Item 22: Authentication App Delete', async ({ I }) => {
    // Navigate to the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_22_Auth_App_Delete_Before_Click.png`,
    );

    // Click on the delete authentication app button
    await I.clickFixed(SecuritySettingTop.locator.deleteAuthApp);
    await I.waitFor('mediumWait');

    // Check if the confirmation button exists
    I.seeElement(SecuritySettingTop.locator.confirmDeleteBtn);
    I.say('Confirmation button with text "削除する" is visible');

    // Take a screenshot before confirming
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_22_Auth_App_Delete_Confirmation.png`,
    );

    // Click on the confirm delete button
    await I.clickFixed(SecuritySettingTop.locator.confirmDeleteBtn);
    await I.waitFor('extraLongWait');
    await I.waitFor('extraLongWait'); // Wait for deletion process to complete

    // Take a screenshot after deletion
    SecuritySettingTop.takeScreenshot(
        `${SCREENSHOT_PREFIX.securitySettingTop}_Item_22_Auth_App_Delete_After_Confirm.png`,
    );
});

// TODO
// Because Item 22 and 24 have opposite condition,
// Before running this test, please remove data to show button SecuritySettingTop.locator.registerAuthApp
// https://auth0-stub.kcmsr.dev.guide.inc/views/endpoints => API /mfa/authenticators => change Item active to false
Scenario('Item 24: Register', async ({ I }) => {
    // Navigate to the Security Setting Top page
    await SecuritySettingTop.goToSecuritySettingTopPage();

    // Take a screenshot before clicking
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_24_Register_Before_Click.png`);

    // Click on the register authentication app button
    await I.clickFixed(SecuritySettingTop.locator.registerAuthApp);
    await I.waitFor('mediumWait');

    // Check if the URL contains the expected path
    I.seeInCurrentUrl('/setting/security/app-registration');
    I.say('Successfully navigated to app registration page');

    // Take a screenshot after navigation
    SecuritySettingTop.takeScreenshot(`${SCREENSHOT_PREFIX.securitySettingTop}_Item_24_Register_After_Click.png`);
});
