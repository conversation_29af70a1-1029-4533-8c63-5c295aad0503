import { COOKIE_KEY, USER_ID } from '../const/constant';
import { modalDialog } from '../const/locator';

Feature('portfolioTopPage');

Before(async ({ I, loginAndSwitchToWebAs }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await I.closeBrowser();
});

Scenario.skip('test portfolio top page', async ({ I, portfolioTopPage }) => {
    await I.clearAppSettings();
    await portfolioTopPage.goToPage();

    // Menu Click Test
    I.click('~menu');
    await I.waitFor();
    I.click('~close-btn');

    await I.clickPieChart('.highcharts-color-0');
    await I.waitFor();
    I.waitForElement('.highcharts-tooltip', 3);

    const portfolioHelpButton = locate('.button').withChild(locate('p').withText('ポートフォリオの見方'));
    const portfolioHelpSwipeTarget = locate('div').after(
        locate('p').withText('ポートフォリオの見方').inside(modalDialog),
    );
    const portfolioDetailHelpLink1 = locate('a').withText('詳しくはこちら').inside(modalDialog);
    const portfolioDetailHelpLink2 = locate('a').withText('ポートフォリオ画面に関する詳細説明').inside(modalDialog);
    I.click(portfolioHelpButton);
    await I.waitFor();
    I.waitForText('ポートフォリオの見方1', 3, 'body');
    await I.clickFixed(portfolioDetailHelpLink1);
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
    await I.swipeLeftFixed(portfolioHelpSwipeTarget);
    await I.waitFor();
    I.waitForText('ポートフォリオの見方2', 3, 'body');
    await I.clickFixed(portfolioDetailHelpLink2);
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
    await I.swipeRightFixed(portfolioHelpSwipeTarget);
    await I.waitFor();
    I.waitForText('ポートフォリオの見方1', 3, 'body');
    await I.clickFixed('//button[@aria-label="cancel-btn"]');
    await I.waitFor();

    const productListItem = locate('.button').withDescendant(locate('p').withText('国内株式現物'));
    const detailPageHeader = locate('div').withChild('#arrow-left');
    const detailPageBackButton = locate('button').withText('戻る');
    I.click(productListItem);
    await I.waitFor();
    I.waitForText('国内株式現物', 3, detailPageHeader);
    I.click(detailPageBackButton);
    await I.waitFor();

    const profitLossItem = locate('p').withText('円').inside(productListItem);
    const profitLossDisplaySwitch = locate('.button').withChild(locate('p').withText('金額表示'));
    const profitLossItemText = await I.grabTextFrom(profitLossItem);
    I.assertNotContain(profitLossItemText, '---');
    I.click(profitLossDisplaySwitch);
    await I.waitFor();
    const profitLossItemHiddenText = await I.grabTextFrom(profitLossItem);
    I.assertContain(profitLossItemHiddenText, '---');
    I.click(profitLossDisplaySwitch);
    await I.waitFor();

    await I.swipeDownFixed('#mypage-portfolio');
    const chartBeforeSwipeRect = await I.grabElementBoundingRectTyped('.hc-container');
    console.debug('chartBeforeScrollRect:', chartBeforeSwipeRect);
    await I.swipeUpFixed('#mypage-portfolio');
    await I.waitFor();
    I.waitForVisible('//button[@aria-label="scroll-to-top-btn"]');
    const chartAfterSwipeUpRect = await I.grabElementBoundingRectTyped('.hc-container');
    console.debug('chartAfterSwipeUpRect:', chartAfterSwipeUpRect);
    I.assertBelow(chartAfterSwipeUpRect.y, chartBeforeSwipeRect.y);
    I.click('//button[@aria-label="scroll-to-top-btn"]');
    await I.waitFor();
    I.waitForInvisible('//button[@aria-label="scroll-to-top-btn"]');
    const chartAfterClickToTopRect = await I.grabElementBoundingRectTyped('.hc-container');
    console.debug('chartAfterClickToTopRect:', chartAfterClickToTopRect);
    I.assertAbove(chartAfterClickToTopRect.y, chartAfterSwipeUpRect.y);

    const helpButton = locate('~help').withChild(locate('img').withAttr({ alt: 'help-icon' }));
    const operationGuide = locate('.button').withChild(locate('p').withText('操作ガイド')).inside(modalDialog);
    const customerSupport = locate('.button').withChild(locate('p').withText('お客さまサポート')).inside(modalDialog);
    I.click(helpButton);
    await I.waitFor();
    await I.clickFixed(operationGuide);
    // TODO: find how to assert page in an external browser
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
    await I.clickFixed(customerSupport);
    // TODO: find how to assert page in an external browser
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
    I.click('//button[@aria-label="cancel-btn"]');
    await I.waitFor();

    const unreadNotification = locate('.button').withChild(locate('p').withText('未読（未回答）の通知があります。'));
    await I.clickFixed(unreadNotification);
    // TODO: find how to assert page in an external browser
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();
});

Scenario.skip('test portfolio top page [playground]', async ({ I, portfolioTopPage }) => {
    await portfolioTopPage.goToPage();

    await I.waitFor();
    // test some code here
});
