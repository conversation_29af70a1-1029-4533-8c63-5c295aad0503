import { COOKIE_KEY, USER_ID } from "../const/constant";

Feature('marketIndicatorPage');

Before(async ({ I, loginAndSwitchToWebAs, portfolioTopPage }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await portfolioTopPage.goToPage();
    I.setCookie({ name: COOKIE_KEY.userId, value: USER_ID.userNone });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario.skip('test market indicator page', async ({ I }) => {
    I.amOnPage('/mobile/market/indicator');
    await <PERSON>.waitFor('longWait');
    <PERSON>.waitForText('主要指標', 3, 'body');
    I.waitForElement('#market-main-indicator');
});
