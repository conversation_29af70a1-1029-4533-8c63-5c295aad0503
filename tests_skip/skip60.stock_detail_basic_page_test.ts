import { COOKIE_KEY } from '../const/constant';
import { modalDialog, radioGroup, slideDialog, tabButton } from '../const/locator';

Feature('stockDetailBasicPage');

Before(async ({ I, loginAndSwitchToWebAs, portfolioTopPage }) => {
    console.debug('before');
    // reset context
    // await I.resetAppFixed();
    await I.activateApp();
    await loginAndSwitchToWebAs('user1');
    await portfolioTopPage.goToPage();
    I.setCookie({ name: COOKIE_KEY.userId, value: 'e2etestuser1' });
});

// To avoid [unknown method: Not implemented yet for script.] on Android
// see. https://codecept.discourse.group/t/appium-codeceptjs-cant-recognize-the-page-element-if-previous-test-against-webview/919_
After(async ({ I }) => {
    console.debug('after');
    // reset context
    I.switchToNative();
    await <PERSON><PERSON>closeBrowser();
});

Scenario.skip('test stock detail basic page', async ({ I, stockDetailBasicPage }) => {
    await stockDetailBasicPage.goToPage();

    const selectedTabTitle = locate('#tabtitle').inside(tabButton.withAttr({ 'aria-selected': 'true' }));
    I.click('.slick-prev');
    await I.waitFor();
    let selectedTabTitleTexts: string[] = await I.grabTextFromAll(selectedTabTitle);
    I.assertContain(selectedTabTitleTexts, 'ニュース');
    I.click('.slick-next');
    await I.waitFor();
    selectedTabTitleTexts = await I.grabTextFromAll(selectedTabTitle);
    I.assertContain(selectedTabTitleTexts, '基本情報');
    I.click('.slick-next');
    await I.waitFor();
    selectedTabTitleTexts = await I.grabTextFromAll(selectedTabTitle);
    I.assertContain(selectedTabTitleTexts, '詳細情報');
    I.click('.slick-prev');
    await I.waitFor();
    selectedTabTitleTexts = await I.grabTextFromAll(selectedTabTitle);
    I.assertContain(selectedTabTitleTexts, '基本情報');
    await I.waitFor();

    const exchangeName = locate('div').withChild('#arrow-down');
    const fukuokaStockExchange = locate('div').withChild(locate('p').withText('福岡'));
    I.click(exchangeName);
    await I.waitFor();
    I.seeElement(modalDialog);
    await I.waitFor();
    I.click('キャンセル');
    await I.waitFor();

    I.click(exchangeName);
    await I.waitFor();
    I.click(fukuokaStockExchange);
    await I.waitFor();
    const exchangeNameText = await I.grabTextFrom(exchangeName);
    I.assertEqual(exchangeNameText, '福岡');

    I.click('取引制限・取引注意情報');
    await I.waitFor();
    I.waitForText('取引制限・取引注意情報', 3, slideDialog);
    I.click('~close-btn');
    await I.waitFor();

    I.click('取引する');
    await I.waitFor();
    I.waitForText('取引方法', 3, modalDialog);
    I.click('//button[@aria-label="cancel-btn"]');
    await I.waitFor();

    const favoriteButton = locate('button').withChild('#star');
    const favoriteDialog = slideDialog.withDescendant(locate('p').withText('お気に入り追加'));
    const closeFavoriteDialogButton = locate('~close-btn').inside(favoriteDialog);
    I.click(favoriteButton);
    await I.waitFor();
    I.waitForText('お気に入り追加', 3, favoriteDialog);
    I.click(closeFavoriteDialogButton);
    await I.waitFor('longWait');

    const allRelatedThemes = locate('p').withText('すべて見る');
    I.click(allRelatedThemes);
    await I.waitFor();
    I.waitForText('関連テーマ', 3, modalDialog);
    I.click('//button[@aria-label="cancel-btn"]');
    await I.waitFor();

    I.click(allRelatedThemes);
    await I.waitFor();
    const relatedThemeFirstItem = locate('p').inside(radioGroup.inside(modalDialog)).first();
    I.click(relatedThemeFirstItem);
    await I.waitFor('longWait');
    I.waitForText('テーマ詳細', 3, 'body');
    I.click('~back');
    await I.waitFor('longWait');

    const shortSaleRestricted = locate('a').withText('空売規制中');
    await I.clickFixed(shortSaleRestricted);
    await I.waitFor('longWait');
    await I.activateApp();
    await I.waitFor();

    const stockThemeSwipeTarget = locate('p')
        .withText('イベント')
        .inside(locate('div').after(locate('div').withChild(locate('p').withText('関連テーマ'))));
    I.runOnAndroid(undefined, () => I.moveCursorTo(stockThemeSwipeTarget)); // for Android with small viewport
    await I.swipeUpFixed('#info-root');
    await I.waitFor();
    const themeBeforeSwipeRect = await I.grabElementBoundingRectTyped(stockThemeSwipeTarget);
    console.debug('themeBeforeSwipeRect:', themeBeforeSwipeRect);
    await I.swipeLeftFixedWithOffset(stockThemeSwipeTarget, 100);
    await I.waitFor();
    const themeAfterSwipeLeftRect = await I.grabElementBoundingRectTyped(stockThemeSwipeTarget);
    console.debug('themeAfterSwipeLeftRect:', themeAfterSwipeLeftRect);
    I.assertBelow(themeAfterSwipeLeftRect.x, themeBeforeSwipeRect.x);
    await I.swipeRightFixedWithOffset(stockThemeSwipeTarget, 100);
    await I.waitFor();
    const themeAfterSwipeRightRect = await I.grabElementBoundingRectTyped(stockThemeSwipeTarget);
    console.debug('themeAfterSwipeRightRect:', themeAfterSwipeRightRect);
    I.assertAbove(themeAfterSwipeRightRect.x, themeAfterSwipeLeftRect.x);

    const analystForecast = locate('a').withText('アナリスト予想詳細');
    await I.clickFixed(analystForecast);
    await I.waitFor('longWait');
    I.waitForElement('#rating-star-section');
    await stockDetailBasicPage.goToPage();

    const chartBeforeSwipeRect = await I.grabElementBoundingRectTyped('.highcharts-container');
    console.debug('chartBeforeSwipeRect:', chartBeforeSwipeRect);
    await I.swipeUpFixed('#info-root');
    await I.waitFor();
    const chartAfterSwipeUpRect = await I.grabElementBoundingRectTyped('.highcharts-container');
    console.debug('chartAfterSwipeUpRect:', chartAfterSwipeUpRect);
    I.assertBelow(chartAfterSwipeUpRect.y, chartBeforeSwipeRect.y);
    await I.swipeDownFixed('#info-root');
    await I.waitFor();
    const chartAfterSwipeDownRect = await I.grabElementBoundingRectTyped('.highcharts-container');
    console.debug('chartAfterSwipeDownRect:', chartAfterSwipeDownRect);
    I.assertAbove(chartAfterSwipeDownRect.y, chartAfterSwipeUpRect.y);
});

Scenario.skip('test stock detail basic page [playground]', async ({ I, stockDetailBasicPage }) => {
    await stockDetailBasicPage.goToPage();

    const stockThemeSwipeTarget = locate('p')
        .withText('イベント')
        .inside(locate('div').after(locate('div').withChild(locate('p').withText('関連テーマ'))));

    // const stockThemes = locate('div').inside(
    //     locate('div').after(locate('div').withChild(locate('p').withText('関連テーマ'))),
    // );
    // const stockThemes = locate('.hide-scroll-bar');

    // I.scrollIntoView(stockThemeSwipeTarget, { block: 'end', inline: 'end' });
    I.runOnAndroid(undefined, () => I.moveCursorTo(stockThemeSwipeTarget));
    await I.swipeUpFixed('#info-root');
    await I.waitFor();
    const themeBeforeSwipeRect = await I.grabElementBoundingRectTyped(stockThemeSwipeTarget);
    console.debug('themeBeforeSwipeRect:', themeBeforeSwipeRect);
    await I.swipeLeftFixedWithOffset(stockThemeSwipeTarget, 100);
    await I.waitFor();
    const themeAfterSwipeLeftRect = await I.grabElementBoundingRectTyped(stockThemeSwipeTarget);
    console.debug('themeAfterSwipeLeftRect:', themeAfterSwipeLeftRect);
    I.assertBelow(themeAfterSwipeLeftRect.x, themeBeforeSwipeRect.x);
    await I.swipeRightFixedWithOffset(stockThemeSwipeTarget, 100);
    await I.waitFor();
    const themeAfterSwipeRightRect = await I.grabElementBoundingRectTyped(stockThemeSwipeTarget);
    console.debug('themeAfterSwipeRightRect:', themeAfterSwipeRightRect);
    I.assertAbove(themeAfterSwipeRightRect.x, themeAfterSwipeLeftRect.x);

    await I.swipeDownFixed('#info-root');

    const chartBeforeSwipeRect = await I.grabElementBoundingRectTyped('.highcharts-container');
    console.debug('chartBeforeSwipeRect:', chartBeforeSwipeRect);
    await I.swipeUpFixed('#info-root');
    await I.waitFor();
    const chartAfterSwipeUpRect = await I.grabElementBoundingRectTyped('.highcharts-container');
    console.debug('chartAfterSwipeUpRect:', chartAfterSwipeUpRect);
    I.assertBelow(chartAfterSwipeUpRect.y, chartBeforeSwipeRect.y);
    await I.swipeDownFixed('#info-root');
    await I.waitFor();
    const chartAfterSwipeDownRect = await I.grabElementBoundingRectTyped('.highcharts-container');
    console.debug('chartAfterSwipeDownRect:', chartAfterSwipeDownRect);
    I.assertAbove(chartAfterSwipeDownRect.y, chartAfterSwipeUpRect.y);

    // test some code here
});
