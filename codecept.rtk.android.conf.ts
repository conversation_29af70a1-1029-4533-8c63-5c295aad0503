import dotenv from 'dotenv';
import { baseConfig } from './codecept.base.conf';
import { CapabilityFactory } from './helpers/CapabilityFactory';

// Load Android environment variables
const result = dotenv.config({ path: '.env.rtk.android', override: true });

if (result.error) {
    console.error('Environment file not found: .env.rtk.android');
    process.exit(1);
}

const androidAppiumHelperConfig = {
    Appium: {
        ...baseConfig.helpers.Appium,
        // Remote TestKit
        hostname: "gwjp.appkitbox.com",
        port: 443, // Port for HTTPS
        path: "/wd/hub",
        protocol: "https",
        // Android
        platform: 'android',
        desiredCapabilities: {
            ...CapabilityFactory.createAndroidCapabilities(),
            accessToken: process.env.RTK_ACCESS_TOKEN,
            app: process.env.ANDROID_APP_PATH,
        },
    },
};

const config = { ...baseConfig };
config.helpers = { ...config.helpers, ...androidAppiumHelperConfig };

exports.config = config;
