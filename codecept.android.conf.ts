import dotenv from 'dotenv';
import { baseConfig } from './codecept.base.conf';
import { CapabilityFactory } from './helpers/CapabilityFactory';

// Load Android environment variables
const result = dotenv.config({ path: '.env.android', override: true });

if (result.error) {
    console.error('Environment file not found: .env.android');
    process.exit(1);
}

const androidAppiumHelperConfig = {
    Appium: {
        ...baseConfig.helpers.Appium,
        platform: 'android',
        desiredCapabilities: CapabilityFactory.createAndroidCapabilities(),
    },
};

const config = { ...baseConfig };
config.helpers = { ...config.helpers, ...androidAppiumHelperConfig };

exports.config = config;
