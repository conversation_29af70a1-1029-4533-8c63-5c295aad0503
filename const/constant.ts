export interface ProfitLossAll {
    investmentProfitLossAll: Decimal;
    dataFromDate: string;
    dataToDate: string;
    profitLossAllList: {
        portfolioProductType: string;
        profitLoss: Decimal;
    }[];
    isNisafundRolloverAccount: boolean;
}

export interface Decimal {
    units: string;
    nanos: number;
}

export const API_NAME_LIST = [
    'BalancePaymentsService/GetBalancePaymentsStock',
    'BalancePaymentsService/GetBalancePaymentsOption',
    'BalancePaymentsService/GetBalancePaymentsFuture',
    'BalancePaymentsService/GetBalancePaymentsMargin',
    'BalancePaymentsService/GetBalancePaymentsInvestmentTrust',
    'BalancePaymentsService/GetBalancePaymentsUSStock',
];

export const COLOR = {
    mainColor: 'rgba(255, 86, 0, 1)',
    mainColorRGB: 'rgb(255, 86, 0)',
};

export const LAST_EXTERNAL_URL = 'lastExternalUrl';
export const LAST_KC_MEMBER_SITE_URL = 'lastKcMemberSiteUrl';

export const SCREENSHOT_PREFIX = {
    portfolioStockPage: '16.portfolio_stock_page',
    portfolioUsStockPage: '24.portfolio_usstock_page',
    portfolioFundPage: '31.portfolio_fund_page',
    portfolioForeignMmfPage: '35.portfolio_foreignmmf_page',
    portfolioMoneyPage: '39.portfolio_money_page',
    portfolioMarginPage: '44.portfolio_margin_page',
    portfolioFuturePage: '53.portfolio_future_page',
    portfolioOptionPage: '57.portfolio_option_page',
    portfolioCommonUi: '61.portfolio_top_page_common_ui',
    hamburgerMenu: '138.hamburger_menu',
    favoriteRegisterModal: '287.favorite_register_modal',
    marketRankingList: '309.market_ranking_list',
    marketBenefitList: '317.market_benefit_list',
    marketBenefitSearch: '330.market_benefit_search',
    marketBenefitSearchResult: '351.market_benefit_search_result',
    marketBenefitDetail: '357.market_benefit_detail',
    searchGeneralPage: '405.search_general_page',
    searchSuggestion: '437.search_suggestion',
    searchResultList: '448.search_result_list',
    searchDomesticStockThemeList: '459.search_domestic_stock_theme_list',
    searchDomesticStockThemeDetail: '462.search_domestic_stock_theme_detail',
    marginTradingSearchTop: '513.margin_search_top',
    marginTradingSearchResult: '531.margin_search_result',
    domesticStockDetailBasic: '545.domestic_stock_detail_basic',
    domesticStockDetailInformation: '563.domestic_stock_detail_information',
    domesticStockDetailQuote: '570.domestic_stock_detail_quote',
    domesticStockDetailNews: '575.domestic_stock_detail_news',
    domesticStockDetailQuarterlyReport: '580.domestic_stock_detail_quarterly_report',
    domesticStockDetailFinancialResult: '589.domestic_stock_detail_financial_result',
    domesticStockTradingMethodSelection: '595.domestic_stock_trading_method_selection',
    domesticStockCashTradingBuyInput: '628.domestic_stock_cash_trading_buy_input',
    domesticStockCashTradingBuyOrderConfirm: '674.domestic_stock_cash_trading_buy_order_confirm',
    domesticStockCashTradingBuyOrderCompleted: '685.domestic_stock_cash_trading_buy_order_completed',
    domesticStockPetitTradingBuyOrderInput: '754.domestic_stock_petit_trading_buy_order_input',
    domesticStockPetitTradingBuyOrderConfirm: '770.domestic_stock_petit_trading_buy_order_confirm',
    domesticStockPetitTradingBuyOrderCompleted: '777.domestic_stock_petit_trading_buy_order_completed',
    domesticStockPetitTradingSellOrderInput: '782.domestic_stock_petit_trading_sell_order_input',
    domesticStockPetitTradingSellOrderConfirm: '787.domestic_stock_petit_trading_sell_order_confirm',
    domesticStockPetitTradingSellOrderCompleted: '794.domestic_stock_petit_trading_sell_order_completed',
    domesticStockPetitTradingCancel: '799.domestic_stock_petit_trading_cancel',
    domesticStockMarginTradingNewOrderInput: '805.domestic_stock_margin_trading_new_order_input',
    domesticStockMarginTradingNewOrderConfirm: '817.domestic_stock_margin_trading_new_order_confirm',
    domesticStockMarginTradingNewOrderCompleted: '828.domestic_stock_margin_trading_new_order_completed',
    domesticStockMarginTradingPaymentOrderInput: '833.domestic_stock_margin_trading_payment_order_input',
    domesticStockMarginTradingPaymentOrderConfirm: '847.domestic_stock_margin_trading_payment_order_confirm',
    domesticStockMarginTradingPaymentOrderCompleted: '857.domestic_stock_margin_trading_payment_order_completed',
    domesticStockMarginTradingPaymentUturnOrderInput: '864.domestic_stock_margin_trading_payment_uturn_order_input',
    domesticStockMarginTradingRelaySelection: '870.domestic_stock_margin_trading_relay_selection',
    domesticStockMarginTradingCorrectInput: '876.domestic_stock_margin_trading_correct_input',
    domesticStockMarginTradingCorrectConfirm: '888.domestic_stock_margin_trading_correct_confirm',
    domesticStockMarginTradingCancel: '894.domestic_stock_margin_trading_cancel',
    domesticStockMarginTradingCancelReceiptDelivery: '900.domestic_stock_margin_trading_cancel_receipt_delivery',
    domesticStockMarginTradingReceiptOrderInput: '907.domestic_stock_margin_trading_receipt_order_input',
    domesticStockMarginTradingReceiptOrderConfirm: '917.domestic_stock_margin_trading_receipt_order_confirm',
    domesticStockMarginTradingReceiptOrderCompleted: '923.domestic_stock_margin_trading_receipt_order_completed',
    domesticStockMarginTradingDeliveryOrderInput: '928.domestic_stock_margin_trading_delivery_order_input',
    domesticStockMarginTradingDeliveryOrderConfirm: '939.domestic_stock_margin_trading_delivery_order_confirm',
    domesticStockMarginTradingDeliveryOrderCompleted: '945.domestic_stock_margin_trading_delivery_order_completed',
    infoFundDetail: '1004.fund_detail',
    securitySettingTop: '1830.security_setting_top',
    settingPasswordList: '1854.setting_password_list',
    changeLoginPassword: '1857.change_login_password',
    changeWithdrawalPassword: '1863.change_withdrawal_password',
    resetWithdrawalPassword: '1870.reset_withdrawal_password',
    referral: '1883.referral',
    referralMailInput: '1893.referral_mail_input',
    referralMailConfirm: '1899.referral_mail_confirm',
    referralMailComplete: '1904.referral_mail_complete',
    referralGenerateURL: '1907.referral_generate_url',
    sorReportStock: '1911.sor_report_stock',
    sorReportFuture: '1923.sor_report_future',
};

export const COMMON_HEADER_TITLE: string = '//*[@data-testid="common_header_title_id"]';
export const COMMON_CLOSE_RIGHT_SLIDE: string = '//*[@data-testid="common_rightSlide_close_id"]';
export const COMMON_BACK_BUTTON: string = '//*[@data-testid="common_back_id"]';

export const PAGE_URL = {
    reservePlan: '/mobile/reserve/inquiry/reserve-plan',
    reserveCalendar: '/mobile/reserve/inquiry/reserve-calendar',
    reserveHistory: '/mobile/reserve/inquiry/reserve-history',
    reservePetitSearch: '/mobile/reserve/petit/search',
    fundSearch: '/mobile/info/fund/search',
    reservePointSetting: '/mobile/reserve/point-setting',
    marketIndicator: '/mobile/market/indicator',
    orderInquiryStock: '/mobile/order-inquiry/stock',
    orderInquiryPetit: '/mobile/order-inquiry/petit',
    orderInquiryMargin: '/mobile/order-inquiry/margin',
    orderInquiryFund: '/mobile/order-inquiry/fund',
    positionInquiryStock: '/mobile/position-inquiry/stock',
    positionInquiryMargin: '/mobile/position-inquiry/margin',
    positionInquiryFund: '/mobile/position-inquiry/fund',
    mypagePortfolio: '/mobile/mypage/portfolio',
    timeline: '/mobile/timeline',
    orderInquiryPetitDetail: '/mobile/order-inquiry/petit/detail',
    tradeFundCancelConfirm: '/mobile/trade/fund/cancel/confirm',
    favorite: '/mobile/favorite',
    search: '/mobile/search',
    mypagePerformance: '/mobile/mypage/performance',
    benefit: '/mobile/benefit',
    settingSecurityTop: '/mobile/setting/security/top',
    sorReportFuture: '/mobile/sor-report/future',
    tradeStockBuy: '/mobile/trade/stock/buy',
    bankAccountRegistrationConfirm: '/mobile/bank-account-registration/depositbank/confirm',
    referral: '/mobile/referral',
    referralMailInput: '/mobile/referral/mail-input',
    sorReportStock: '/mobile/sor-report/stock',
};

export const LOCAL_STORAGE_KEY = {
    appSettingList: 'app-setting-list',
};

export const SESSION_STORAGE_KEY = {
    infoFundDetail: '/info/fund/detail',
    tradeStockBuy: '/trade/stock/buy',
    tradeStockSell: '/trade/stock/sell',
    tradeStockCancel: '/trade/stock/cancel',
    tradeMarginCancel: '/trade/margin/cancel',
    tradeMarginCancelReceiptDelivery: '/trade/margin/cancel-receipt-delivery',
    tradeMarginCorrect: '/trade/margin/correction',
    tradeMarginReceipt: '/trade/margin/receipt',
    tradeMarginReceiptConfirm: '/trade/margin/receipt/confirm',
    tradeMarginDelivery: '/trade/margin/delivery',
    tradeMarginDeliveryConfirm: '/trade/margin/delivery/confirm',
    tradeMarginRepayment: '/trade/margin/repayment',
    tradeFundBuyConfirm: '/trade/fund/buy/confirm',
    tradeStockCorrectionConfirm: '/trade/stock/correction/confirm',
    depositBankInput: '/bank-account-registration/depositbank/input',
    tradePetitDetail: '/order-inquiry/petit/detail',
    marginDetailList: '/position-inquiry/margin/detail-list',
    cardAgree: '/setting/card/agree',
    storedStateSettingCardAgree: 'storedState/setting/card/agree',
    themeDetail: '/search/theme/detail',
    referralMailInput : '/referral/mail-input',
    depositBankConfirm: '/bank-account-registration/depositbank/confirm',
};

export const SESSION_STORAGE_VALUE = {
    infoFundDetail: (data?: {}) => JSON.stringify({ "fundCode": "********", "scrollY": -151, ...data }),
    tradeStockBuy: (data?: {}) => JSON.stringify({
        symbol: "8306",
        exchange: "EXCHANGE_TSE",
        ...data,
    }),
    tradeStockSell: (data?: {}) => JSON.stringify({ symbol: '8306', accountType: 'ACCOUNT_TYPE_IPPAN', ...data }),
    tradeStockCancel: (data?: {}) => JSON.stringify({ ...data }),
    tradeMarginCancel: (data?: {}) => JSON.stringify({
        orderId: '20250116A01N90651052',
        ...data,
    }),
    tradeMarginCancelReceiptDelivery: (data?: {}) => JSON.stringify({
        orderId: '20250116A01N90651052',
        ...data,
    }),
    tradeMarginCorrect: (data?: {}) => JSON.stringify({
        orderId: '20250116A01N90651052',
        ...data,
    }),
    tradeMarginReceipt: (data?: {}) => JSON.stringify({
        accountType: "ACCOUNT_TYPE_NISA",
        symbol: "2801",
        exchange: "EXCHANGE_TSE",
        marginTradeType: "MARGIN_TRADE_TYPE_COMPANY_SHORT",
        ...data,
    }),
    tradeMarginReceiptConfirm: (data?: {}) => JSON.stringify({
        symbolName: "キリンHD2LongName",
        quantityUnit: "QUANTITY_UNIT_KABU",
        fundType: "FUND_TYPE_HOGOAZUKARI",
        isOmmitPassword: false,
        requestId: "fb393945221c4b029d6fa3fc2ff7d08398a660d05dfbbab3e41c317f47d0b5f0",
        symbol: "2801",
        exchange: "EXCHANGE_TSE",
        stockAccountType: "ACCOUNT_TYPE_TOKUTEI",
        orderExecType: "ORDER_EXEC_TYPE_CHECK_ONLY",
        marginClosePosition: {
            marginAccountType: "ACCOUNT_TYPE_TOKUTEI",
            marginTradeType: "MARGIN_TRADE_TYPE_COMPANY_SHORT",
            dateType: "POSITION_DATE_TYPE_RETURN_DATE",
            specifiedDate: "********",
            quantity: {
                units: "100"
            }
        },
        marginChangeDate: "********",
        deliveryDate: "********",
        openDeliveryAmount: {
            units: "*************",
            nanos: 0
        },
        expenses: {
            units: "**************",
            nanos: 0
        },
        commission: {
            units: "***************",
            nanos: 0
        },
        commissionTax: {
            units: "123445654323214243",
            nanos: 0
        },
        paymentAmount: {
            units: "***************",
            nanos: 0
        },
        orderId: "***********",
        ...data,
    }),
    tradeMarginDelivery: (data?: {}) => JSON.stringify({
        accountType: "ACCOUNT_TYPE_TOKUTEI",
        symbol: "2801",
        exchange: "EXCHANGE_TSE",
        marginTradeType: "MARGIN_TRADE_TYPE_GENERAL",
        ...data,
    }),
    tradeMarginDeliveryConfirm: (data?: {}) => JSON.stringify({
        symbolName: "キッコーマン",
        quantityUnit: "QUANTITY_UNIT_KABU",
        isOmmitPassword: false,
        requestId: "2c9cdf879eba8d1a257715ddb4ac4cf93a45079f7f645c5bf5f46e39ccdd11f5",
        symbol: "2801",
        exchange: "EXCHANGE_TSE",
        marginClosePosition: {
            marginAccountType: "ACCOUNT_TYPE_TOKUTEI",
            marginTradeType: "MARGIN_TRADE_TYPE_GENERAL",
            dateType: "POSITION_DATE_TYPE_RETURN_DATE",
            specifiedDate: "********",
            quantity: {
                units: "100",
                nanos: 0
            }
        },
        stockAccountType: "ACCOUNT_TYPE_IPPAN",
        orderExecType: "ORDER_EXEC_TYPE_CHECK_ONLY",
        marginChangeDate: "********",
        accountTypeName: "",
        deliveryDate: "********",
        openDeliveryAmount: {
            units: "100000",
            nanos: 0
        },
        expenses: {
            units: "100000",
            nanos: 0
        },
        commission: {
            units: "100000",
            nanos: 0
        },
        commissionTax: {
            units: "100000",
            nanos: 0
        },
        receiveAmount: {
            units: "100000",
            nanos: 0
        },
        orderId: "orderID",
        ...data,
    }),
    tradeMarginRepayment: (data?: {}) => JSON.stringify({
        accountType: 'ACCOUNT_TYPE_TOKUTEI',
        symbol: '8306',
        exchange: 'EXCHANGE_NSE',
        marginTradeType: 'MARGIN_TRADE_TYPE_GENERAL',
        buySell: 'BUY_SELL_BUY',
        ...data,
    }),
    tradeFundBuyConfirm: (data?: {}) => JSON.stringify({ ...data }),
    tradeStockCorrectionConfirm: (data?: {}) => JSON.stringify({ ...data }),
    depositBankInput: (data?: {}) => JSON.stringify({ bankcode: '0009', ...data }),
    tradePetitDetail: (data?: {}) => JSON.stringify({ ...data }),
    marginDetailList: (data?: {}) => JSON.stringify({
        accountType: "ACCOUNT_TYPE_TOKUTEI",
        symbol: "2801",
        exchange: "EXCHANGE_SSE",
        marginTradeType: "MARGIN_TRADE_TYPE_COMPANY_SHORT",
        buySell: "BUY_SELL_BUY"
        , ...data
    }),
    cardAgree: (data?: {}) => JSON.stringify({ ...data }),
    storedStateSettingCardAgree: (data?: {}) => JSON.stringify({ aupayErrorCode: '52164', ...data }),
    themeDetail: (data?: {}) => JSON.stringify({ themeId: 828, ...data }),
};

export const COOKIE_KEY = {
    userId: 'userId',
    siteId: 'siteid',
};

export const USER_ID = {
    userNone: process.env.USERID_NONE,
    /* ******** */
    user1: process.env.USERID_01,
    /* ******** */
    user2: process.env.USERID_02,
    /* ******** */
    user3: process.env.USERID_03,
    /* ******** */
    user4: process.env.USERID_04,
    /* ******** */
    user5: process.env.USERID_05,
    /* ******** */
    user6: process.env.USERID_06,
    /* ******** */
    user7: process.env.USERID_07,
    /* ******** */
    user8: process.env.USERID_08,
    /* 08309916 */
    user9: process.env.USERID_09,
    /* 09609910 */
    user10: process.env.USERID_10,
    /* 09709910 */
    user11: process.env.USERID_11,
    /* 09809910 */
    user12: process.env.USERID_12,
    /* 09909910 */
    user13: process.env.USERID_13,
    /* 09119910 */
    user14: process.env.USERID_14,
    /* 09129910 */
    user15: process.env.USERID_15,
    /* 09109920 */
    user16: process.env.USERID_16,
    /* 09109921 */
    user17: process.env.USERID_17,
    /* 09139910 */
    user18: process.env.USERID_18,
    /* 09159910 */
    user19: process.env.USERID_19,
    /* 09159911 */
    user20: process.env.USERID_20,
    /* 09169910 */
    user21: process.env.USERID_21,
    /* 09179910 */
    user22: process.env.USERID_22,
    /* 09179911 */
    user23: process.env.USERID_23,
    /* 09189910 */
    user24: process.env.USERID_24,
    /* 09199910 */
    user25: process.env.USERID_25,
    /* 09199912 */
    user26: process.env.USERID_26,
    /* 09199914 */
    user27: process.env.USERID_27,
    /* 09199915 */
    user28: process.env.USERID_28,
    /* 09111910 */
    user29: process.env.USERID_29,
    /* 09111911 */
    user30: process.env.USERID_30,
    /* 09111912 */
    user31: process.env.USERID_31,
    /* 09111913 */
    user32: process.env.USERID_32,
    /* 09112913 */
    user33: process.env.USERID_33,
    /* 09112914 */
    user34: process.env.USERID_34,
    /* 09113910 */
    user35: process.env.USERID_35,
    /* 09113911 */
    user36: process.env.USERID_36,
    /* 09113912 */
    user37: process.env.USERID_37,
    /* 09113913 */
    user38: process.env.USERID_38,
    /* 09115910 */
    user39: process.env.USERID_39,
    /* 09118910 */
    user40: process.env.USERID_40,
    /* 08109910 */
    user41: process.env.USERID_41,
    /* 09109903 */
    user42: process.env.USERID_42,
    /* 08209910 */
    user43: process.env.USERID_43,
    /* 08209911 */
    user44: process.env.USERID_44,
    /* 08309913 */
    user45: process.env.USERID_45,
    /* 08309910 */
    user46: process.env.USERID_46,
    /* 08309911 */
    user47: process.env.USERID_47,
    /* 08409910 */
    user48: process.env.USERID_48,
    /* 08409911 */
    user49: process.env.USERID_49,
    /* 08409912 */
    user50: process.env.USERID_50,
    /* 08409913 */
    user51: process.env.USERID_51,
    /* 09050360 */
    user52: process.env.USERID_52,
    /* 09050361 */
    user53: process.env.USERID_53,
    /* 09050362 */
    user54: process.env.USERID_54,
    /* 08509910 */
    user55: process.env.USERID_55,
    /* 08509911 */
    user56: process.env.USERID_56,
    /* 08909910 */
    user57: process.env.USERID_57,
    /* 08609910 */
    user58: process.env.USERID_58,
    /* 08709910 */
    user59: process.env.USERID_59,
    /* 08809910 */
    user60: process.env.USERID_60,
    /* 08809911 */
    user61: process.env.USERID_61,
    /* 08809912 */
    user62: process.env.USERID_62,
    /* 08809913 */
    user63: process.env.USERID_63,
    /* 08809915 */
    user64: process.env.USERID_64,
    /* 08809917 */
    user65: process.env.USERID_65,
    /* 08809916 */
    user66: process.env.USERID_66,
    /* 08309960 */
    user67: process.env.USERID_67,
    /* 08509913 */
    user68: process.env.USERID_68,
    /* 08309912 */
    user69: process.env.USERID_69,
    /* 08709911 */
    user70: process.env.USERID_70,
    /* 09109940 */
    user71: process.env.USERID_71,
    /* 09409910 */
    user72: process.env.USERID_72,
    /* 08209912 */
    user73: process.env.USERID_73,
    /* 08809914 */
    user74: process.env.USERID_74,
    /* 09109912 */
    user75: process.env.USERID_75,
    /* 09109956 */
    user76: process.env.USERID_76,
    /* 09179912 */
    user77: process.env.USERID_77,
    /* 09111914 */
    user78: process.env.USERID_78,
    /* 09111916 */
    user79: process.env.USERID_79,
    /* 09509911 */
    user80: process.env.USERID_80,
    /* 08509916 */
    user81: process.env.USERID_81,
    /* 09112915 */
    user82: process.env.USERID_82,
    /* 09112916 */
    user83: process.env.USERID_83,
    /* 09112917 */
    user84: process.env.USERID_84,
    /* 09112918 */
    user85: process.env.USERID_85,
    /* 08509914 */
    user86: process.env.USERID_86,
    /* 08509915 */
    user87: process.env.USERID_87,
    /* 08209940 */
    user88: process.env.USERID_88,
    /* 08509912 */
    user100: process.env.USERID_100,
    /* 08209913 */
    user101: process.env.USERID_101,
    /* 08309950 */
    user102: process.env.USERID_102,
};
