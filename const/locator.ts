export const modalDialog = locate('div').withAttr({ role: 'dialog' }).withAttr({ 'aria-modal': 'true' });
export const slideDialog = locate('.chakra-slide');
export const radioButton = locate('input').withAttrContains('id', 'radio-');
export const radioGroup = locate('div').withAttr({ role: 'radiogroup' });
export const tabButton = locate('button').withAttrContains('id', 'tabs-');
export const menuButton = locate('button').withAttrContains('id', 'menu-button-');
export const menuList = locate('div').withAttrContains('id', 'menu-list-');
export const menuItem = locate('button').withAttr({ role: 'menuitemradio' });
