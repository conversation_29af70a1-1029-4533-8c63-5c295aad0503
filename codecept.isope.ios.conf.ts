import dotenv from 'dotenv';
import { isOpeBaseConfig } from './codecept.isope.base.conf'; 
import { CapabilityFactory } from './helpers/CapabilityFactory';

// Load iOS environment variables
const result = dotenv.config({ path: '.env.isope.ios', override: true });

if (result.error) {
    console.error('Environment file not found: .env.isope.ios');
    process.exit(1);
}

const iosAppiumHelperConfig = {
    Appium: {
        ...isOpeBaseConfig.helpers.Appium,
        platform: 'ios',
        desiredCapabilities: CapabilityFactory.createIOSCapabilities(),
    },
};

const config = { ...isOpeBaseConfig };
config.helpers = { ...config.helpers, ...iosAppiumHelperConfig };

exports.config = config;
