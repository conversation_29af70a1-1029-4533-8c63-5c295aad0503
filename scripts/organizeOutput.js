// organizeOutput.js
const fs = require('fs');
const path = require('path');

const OUTPUT_DIR = path.join(__dirname, '..', 'output');

const TESTS_DIR = path.join(__dirname, '..',  'tests');

// Example: map with folder
// const folderMap = {
//   AssetStatus: [1, 113],
//   Timeline: [116, 137],
//   HamburgerMenu: [138, 138],
//   NISAMenu: [199, 199],
//   NISAAccountStatus: [224, 224],
//   Notify: [232, 257],
//   Market: [261, 357],
//   Favorite: [365, 365],
//   Symbol_ProductSearch: [405, 531],
//   InvestmentProducts: [545, 1082],
//   OrderInquiry: [1087, 1141],
//   PositionInquiry: [1156, 1207],
//   DividendHistory: [1225, 1225],
//   DistributionHistory: [1236, 1236],
//   Reserve: [1250, 1537],
//   Deposits_Withdrawals: [1618, 1650],
//   Settings_Entry: [1671, 1870],
//   Referral: [1883, 1907],
//   SORReport: [1911, 1923],
// };
console.log('organizeOutput.js');
const folderMap = {};

fs.readdirSync(TESTS_DIR).forEach((file) => {
  const filePath = path.join(TESTS_DIR, file);
  // Get prefix from file name
  const fileMatch = file.match(/^(\d+)\./);
  if (!fileMatch) return;

  const prefix = parseInt(fileMatch[1], 10);
  const content = fs.readFileSync(filePath, 'utf8');

  // Find all Feature('PageName - Anything') rows
  const regex = /Feature\(['"]([^'"]+?)\s*-\s*[^'"]+['"]\)/g;
  let match;
  while ((match = regex.exec(content)) !== null) {
    const pageName = match[1].trim();
    if (!folderMap[pageName]) {
      folderMap[pageName] = [prefix, prefix];
    } else {
      const [min, max] = folderMap[pageName];
      folderMap[pageName] = [
        Math.min(min, prefix),
        Math.max(max, prefix),
      ];
    }
  }
});

console.log(folderMap);

// 1. Delete folder record_*
fs.readdirSync(OUTPUT_DIR).forEach((item) => {
  const itemPath = path.join(OUTPUT_DIR, item);
  if (fs.statSync(itemPath).isDirectory() && item.startsWith('record_') || item.startsWith('user1_')) {
    fs.rmSync(itemPath, { recursive: true, force: true });
    console.log(`🗑️ Deleted folder: ${item}`);
  }
});

// 2.Create destination folder
Object.keys(folderMap).forEach((folderName) => {
  const dirPath = path.join(OUTPUT_DIR, folderName);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath);
  }
});

// 3. Create Others folder
const othersDir = path.join(OUTPUT_DIR, 'Others');
if (!fs.existsSync(othersDir)) {
  fs.mkdirSync(othersDir);
}

// 4. Move .png file
fs.readdirSync(OUTPUT_DIR).forEach((file) => {
  const filePath = path.join(OUTPUT_DIR, file);
  if (fs.statSync(filePath).isFile() && file.endsWith('.png')) {
    const prefixMatch = file.match(/^(\d+)/);
    if (prefixMatch) {
      const prefix = parseInt(prefixMatch[1], 10);
      let moved = false;

      for (const [folderName, [min, max]] of Object.entries(folderMap)) {
        if (prefix >= min && prefix <= max) {
          const destPath = path.join(OUTPUT_DIR, folderName, file);
          fs.renameSync(filePath, destPath);
          console.log(`✅ ${file} → ${folderName}`);
          moved = true;
          break;
        }
      }

      if (!moved) {
        fs.renameSync(filePath, path.join(othersDir, file));
        console.log(`→ ${file} → Others`);
      }
    } else {
      fs.renameSync(filePath, path.join(othersDir, file));
      console.log(`→ ${file} → Others`);
    }
  }
});
