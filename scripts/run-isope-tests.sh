#!/bin/bash
# run-isope-tests.sh - Enhanced version with retry logic, crash recovery and detailed reporting

# Source .env file if it exists
if [ -f ".env" ]; then
    source .env
fi

TARGET="$1"
shift # Remove $1 from the arguments so $@ contains only options

# ===== Script Execution Context Detection =====
should_upload_to_s3=false

# Check if "s3" parameter is provided in the remaining arguments
for arg in "$@"; do
    if [ "$arg" = "s3" ]; then
        should_upload_to_s3=true
        echo -e "${BLUE}📊 S3 parameter detected - S3 upload will be performed${RESET}"
        break
    fi
done

if [ "$should_upload_to_s3" = false ]; then
    echo -e "${YELLOW}📝 No S3 parameter - S3 upload will be skipped${RESET}"
fi

# Color codes
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
MAGENTA='\033[35m'
CYAN='\033[36m'
WHITE='\033[37m'
BOLD='\033[1m'
RESET='\033[0m'

# ===== Performance Optimization Settings =====
export NODE_OPTIONS="${NODE_OPTIONS:---max-old-space-size=4096}"
export APPIUM_TIMEOUT="${APPIUM_TIMEOUT:-60000}"
export IMPLICIT_WAIT="${IMPLICIT_WAIT:-10000}"
export PAGE_LOAD_TIMEOUT="${PAGE_LOAD_TIMEOUT:-30000}"

# ===== Test Execution Settings =====
MAX_RETRY_ATTEMPTS="${MAX_RETRY_ATTEMPTS:-3}"
CRASH_RECOVERY_ENABLED="${CRASH_RECOVERY_ENABLED:-true}"
MAX_CRASH_RETRIES="${MAX_CRASH_RETRIES:-3}"

# ===== S3 Configuration =====
S3_BUCKET="${S3_BUCKET:-dev-kcmsr-universal-link}"
S3_BASE_PATH="${S3_BASE_PATH:-e2e-test-report}"
AWS_PROFILE="${AWS_PROFILE:-guide-s3-sync-user}"
REPORT_DOMAIN="${REPORT_DOMAIN:-universal-link.kcmsr.dev.guide.inc}"

# ===== Slack Configuration =====
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

if [ -z "$SLACK_WEBHOOK" ]; then
    echo -e "${YELLOW}⚠️ SLACK_WEBHOOK environment variable not set - Slack notifications will be disabled${RESET}"
fi

echo -e "${BLUE}🚀 IsOpe Test Runner - Enhanced version:${RESET}"
echo -e "${CYAN}   - Node memory: $(echo $NODE_OPTIONS | grep -o '[0-9]\+' | awk '{print $1/1024}')GB${RESET}"
echo -e "${CYAN}   - Max retry attempts: ${MAX_RETRY_ATTEMPTS}${RESET}"
echo -e "${CYAN}   - Crash recovery: ${CRASH_RECOVERY_ENABLED}${RESET}"
echo -e "${CYAN}   - Enhanced error handling and reporting${RESET}"

# ===== Arrays to store test results =====
declare -a first_run_failed_tests=()
declare -a first_run_error_details=()
declare -a final_failed_tests=()
declare -a final_error_details=()
declare -a crash_recovery_tests=()
declare -a original_test_files=()

# Function to detect crash from log
detect_crash_from_log() {
    local log_file=$1
    
    if [ ! -f "$log_file" ]; then
        return 1  # No log file, no crash
    fi
        # ===== EXCLUDE SPECIFIC WARNING PATTERNS (NOT CRASHES) =====
    # Check for specific Android warning that should NOT trigger crash recovery
    if grep -q "Not implemented yet for script.*timeouts.*script.*0" "$log_file"; then
        echo -e "${YELLOW}⚠️ Android timeouts warning detected (not a crash) - skipping crash recovery${RESET}" >&2
        return 1  # Not a crash, just a warning
    fi

    # ===== WEBDRIVER CRASHES =====
    if grep -q "ERROR webdriver:" "$log_file" || \
       grep -q "ERROR.*webdriver" "$log_file" || \
       grep -q "WebDriverError:" "$log_file" || \
       grep -q "WebDriverError.*Method is not implemented" "$log_file" || \
       grep -q "WebDriverError.*Invalid locator" "$log_file" || \
       grep -q "WebDriverError.*Timeout" "$log_file" || \
       grep -q "WebDriverError.*Connection refused" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== SESSION CRASHES =====
    if grep -q "session is either terminated or not started" "$log_file" || \
       grep -q "WebDriverError.*session.*terminated" "$log_file" || \
       grep -q "WebDriverError: A session is either terminated" "$log_file" || \
       grep -q "Session.*not found" "$log_file" || \
       grep -q "Invalid session ID" "$log_file" || \
       grep -q "Session ID.*does not exist" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== NETWORK/CONNECTION CRASHES =====
    if grep -q "ECONNREFUSED" "$log_file" || \
       grep -q "ECONNRESET" "$log_file" || \
       grep -q "ETIMEDOUT" "$log_file" || \
       grep -q "ENETUNREACH" "$log_file" || \
       grep -q "EHOSTUNREACH" "$log_file" || \
       grep -q "Request failed with error code" "$log_file" || \
       grep -q "Connection lost" "$log_file" || \
       grep -q "connect ECONNREFUSED" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== APPIUM SERVER CRASHES =====
    if grep -q "An unknown server-side error occurred" "$log_file" || \
       grep -q "Could not find a connected Android device" "$log_file" || \
       grep -q "Appium.*not running" "$log_file" || \
       grep -q "Unable to create session" "$log_file" || \
       grep -q "Appium server.*stopped" "$log_file" || \
       grep -q "Could not start Appium session" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== DEVICE/SIMULATOR CRASHES =====
    if grep -q "Device.*disconnected" "$log_file" || \
       grep -q "Simulator.*shutdown" "$log_file" || \
       grep -q "Device not found" "$log_file" || \
       grep -q "Simulator not found" "$log_file" || \
       grep -q "xcrun.*error" "$log_file" || \
       grep -q "adb.*device.*offline" "$log_file" || \
       grep -q "adb.*device not found" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== APP CRASHES =====
    if grep -q "Application.*crashed" "$log_file" || \
       grep -q "App.*terminated unexpectedly" "$log_file" || \
       grep -q "Process.*died" "$log_file" || \
       grep -q "Application not responding" "$log_file" || \
       grep -q "App.*not found" "$log_file" || \
       grep -q "Unable to launch app" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== TIMEOUT CRASHES =====
    if grep -q "TimeoutError" "$log_file" || \
       grep -q "Timeout.*exceeded" "$log_file" || \
       grep -q "Command timeout" "$log_file" || \
       grep -q "Script timeout" "$log_file" || \
       grep -q "Page load timeout" "$log_file" || \
       grep -q "Element.*timeout" "$log_file" || \
       grep -q "waiting for element.*timeout" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== ELEMENT/LOCATOR CRASHES =====
    if grep -q "StaleElementReferenceError" "$log_file" || \
       grep -q "NoSuchElementException" "$log_file" || \
       grep -q "ElementNotVisibleException" "$log_file" || \
       grep -q "ElementNotInteractableException" "$log_file" || \
       grep -q "Element is not clickable" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== MEMORY/RESOURCE CRASHES =====
    if grep -q "Out of memory" "$log_file" || \
       grep -q "JavaScript heap out of memory" "$log_file" || \
       grep -q "FATAL ERROR" "$log_file" || \
       grep -q "Heap.*exceeded" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== CODECEPT/FRAMEWORK CRASHES =====
    if grep -q "CodeceptJS.*crashed" "$log_file" || \
       grep -q "Helper.*crashed" "$log_file" || \
       grep -q "Browser.*crashed" "$log_file" || \
       grep -q "Driver.*crashed" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== IOS SPECIFIC CRASHES =====
    if grep -q "iPhone Simulator.*crashed" "$log_file" || \
       grep -q "CoreSimulator.*error" "$log_file" || \
       grep -q "xcrun simctl.*error" "$log_file" || \
       grep -q "xcodebuild.*failed" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== ANDROID SPECIFIC CRASHES =====
    if grep -q "adb.*error" "$log_file" || \
       grep -q "UiAutomator.*crashed" "$log_file" || \
       grep -q "AndroidDriver.*error" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== PERMISSION/SECURITY CRASHES =====
    if grep -q "Permission denied" "$log_file" || \
       grep -q "Access denied" "$log_file" || \
       grep -q "Unauthorized" "$log_file" || \
       grep -q "Security.*error" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== GENERIC FATAL ERRORS =====
    if grep -q "FATAL:" "$log_file" || \
       grep -q "CRITICAL:" "$log_file" || \
       grep -q "Segmentation fault" "$log_file" || \
       grep -q "Core dumped" "$log_file" || \
       grep -q "Abort trap" "$log_file"; then
        return 0  # Crash detected
    fi
    
    return 1  # No crash detected
}
# Function to recover from crash
recover_from_crash() {
    local platform=$1
    local current_file=$2
    local attempt=$3
    
    echo -e "\n${RED}${BOLD}💥 CRASH DETECTED - Attempt ${attempt}/${MAX_CRASH_RETRIES}${RESET}"
    echo -e "${YELLOW}${BOLD}🔧 Initiating crash recovery for $(basename $current_file)...${RESET}"
    
    if [ "$platform" = "ios" ]; then
        echo -e "${BLUE}🔄 iOS Crash Recovery:${RESET}"
        xcrun simctl terminate booted inc.guide.kabuappNext.dev
        sleep 3
        xcrun simctl launch booted inc.guide.kabuappNext.dev
        sleep 3
    else
        echo -e "${BLUE}🔄 Android Crash Recovery:${RESET}"
        adb shell am force-stop inc.guide.kabuappNext.dev
        sleep 1
        adb shell monkey -p inc.guide.kabuappNext.dev -c android.intent.category.LAUNCHER 1
        sleep 1
        # Grant permissions after app restart
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.CAMERA
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.ACCESS_FINE_LOCATION
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.READ_EXTERNAL_STORAGE
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.WRITE_EXTERNAL_STORAGE
        sleep 1
    fi
    
    sleep 3
    echo -e "${GREEN}✅ Crash recovery completed${RESET}"
}

# Function to extract error details from log
extract_error_details() {
    local file=$1
    local failed_array_name=$2
    local error_array_name=$3
    local log_suffix=$4
    
    local log_file="test_output_$(basename $file .ts)${log_suffix}.log"
    
    if [ -f "$log_file" ]; then
        local failures_section=$(awk '/-- FAILURES:/,/^o File:/' "$log_file" 2>/dev/null)
        
        if [ ! -z "$failures_section" ]; then
            eval "${failed_array_name}+=(\"$(basename $file)\")"
            eval "${error_array_name}+=(\"$failures_section\")"
        fi
    fi
}

# Function to colorize test output
colorize_output() {
    local current_file=$1
    local file_name=$(basename "$current_file" .ts)

    while IFS= read -r line; do
        # Skip duplicate screenshot messages
        if [[ $line == *"Screenshot has been saved to"* ]] && [[ $line == *"size:"* ]]; then
            continue
        fi

        # Screenshot messages
        if [[ $line == *"save screenshot"* ]] || [[ $line == *"Screenshot has been saved to"* ]]; then
            echo -e "${BLUE}$line${RESET}"
            continue
        fi

        # Other patterns remain the same
        if [[ $line =~ .*OK.*in.*[0-9]+ms.* ]]; then
            echo -e "${GREEN}${BOLD}$line${RESET}"
        elif [[ $line == *"✖ FAILED in"* ]]; then
            echo -e "${RED}${BOLD}[${file_name}] $line${RESET}"
        elif [[ $line == *"Test Item"* ]]; then
            echo -e "${MAGENTA}${BOLD}$line${RESET}"
        elif [[ $line == *"Scenario()"* ]]; then
            echo -e "${CYAN}[${file_name}] $line${RESET}"
        elif [[ $line == *"Before()"* ]] || [[ $line == *"After()"* ]]; then
            echo -e "${YELLOW}$line${RESET}"
        elif [[ $line =~ ^[[:space:]]*I[[:space:]] ]]; then
            echo -e "${WHITE}$line${RESET}"
        elif [[ $line =~ ^\{.*x:.*y:.*\}$ ]]; then
            echo -e "${BLUE}$line${RESET}"
        elif [[ $line == *"deviceScreenInfo"* ]]; then
            echo -e "${BLUE}$line${RESET}"
        elif [[ $line == *"ERROR webdriver:"* ]]; then
            echo -e "${RED}${BOLD}💥 WEBDRIVER ERROR: $line${RESET}"
        elif [[ $line == *"ERROR"* ]] && [[ $line == *"session is either terminated"* ]]; then
            echo -e "${RED}${BOLD}💥 CRASH: $line${RESET}"
        else
            echo "$line"
        fi
    done
}

# Enhanced function to run a single test with crash recovery
run_single_test_with_crash_recovery() {
    local file=$1
    local platform=$2
    local log_suffix=$3
    local crash_attempt=${4:-1}
    
    echo -e "\n${BLUE}${BOLD}=== Running test: $(basename $file) on $platform ===${RESET}"
    if [ $crash_attempt -gt 1 ]; then
        echo -e "${YELLOW}${BOLD}🔄 Crash recovery attempt: ${crash_attempt}/${MAX_CRASH_RETRIES}${RESET}"
    fi
    echo ""
    
    local log_file="test_output_$(basename $file .ts)${log_suffix}.log"
    
    # Add verbose flag and force color for more detailed output
    FORCE_COLOR=1 npx codeceptjs run "$file" --config "$CONFIG_FILE" --verbose 2>&1 | colorize_output "$file" | tee "$log_file"
    test_result=${PIPESTATUS[0]}
    
    # Check for crash
    if detect_crash_from_log "$log_file"; then
        echo -e "\n${RED}${BOLD}💥 CRASH DETECTED in $(basename $file)${RESET}"
        
        if [ "$CRASH_RECOVERY_ENABLED" = true ] && [ $crash_attempt -le $MAX_CRASH_RETRIES ]; then
            crash_recovery_tests+=("$(basename $file)")
            recover_from_crash "$platform" "$file" "$crash_attempt"
            
            # Retry the test after crash recovery
            local next_attempt=$((crash_attempt + 1))
            echo -e "${YELLOW}🔄 Retrying test after crash recovery...${RESET}"
            run_single_test_with_crash_recovery "$file" "$platform" "${log_suffix}_crash_retry_${crash_attempt}" "$next_attempt"
            return $?
        else
            echo -e "${RED}❌ Max crash recovery attempts reached for $(basename $file)${RESET}"
            return 1
fi
    fi
    
    return $test_result
}

# Function to run a single test (wrapper)
run_single_test() {
    local file=$1
    local platform=$2
    local log_suffix=$3
    
    run_single_test_with_crash_recovery "$file" "$platform" "$log_suffix"
    return $?
}

# Function for deep cleanup
deep_cleanup() {
    local platform=$1
    echo -e "\n${YELLOW}${BOLD}🧹 Performing deep cleanup...${RESET}"
    
    if [ "$platform" = "ios" ]; then
        echo -e "${BLUE}Deep cleaning iOS simulator...${RESET}"
        xcrun simctl terminate booted inc.guide.kabuappNext.dev 2>/dev/null || true
        sleep 2
        xcrun simctl privacy booted reset all inc.guide.kabuappNext.dev 2>/dev/null || true
        sleep 2
        xcrun simctl launch booted inc.guide.kabuappNext.dev
        sleep 3
    else
        # Grant all necessary permissions for isOpe mode
        echo -e "${CYAN}📱 Granting Android permissions for isOpe mode...${RESET}"
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.CAMERA
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.ACCESS_FINE_LOCATION
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.READ_EXTERNAL_STORAGE
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.WRITE_EXTERNAL_STORAGE
        sleep 1
        echo -e "${GREEN}✅ Android permissions granted${RESET}"
    fi
    
    echo -e "${GREEN}✅ Deep cleanup completed${RESET}"
}

# Function to display summary after first run
display_first_run_summary() {
    local total_files=${#original_test_files[@]}
    local failed_count=${#first_run_failed_tests[@]}
    local passed_count=$((total_files - failed_count))
    local crash_count=${#crash_recovery_tests[@]}
    
    echo -e "\n${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    echo -e "${MAGENTA}${BOLD}🎯 ISOPE FIRST RUN SUMMARY${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    echo -e "${WHITE}📊 ${BOLD}First Run Results:${RESET}"
    echo -e "   ✅ Passed:      ${GREEN}${BOLD}${passed_count}${RESET} tests"
    echo -e "   ❌ Failed:      ${RED}${BOLD}${failed_count}${RESET} tests"
    echo -e "   📈 Total:       ${BLUE}${BOLD}${total_files}${RESET} tests"
    if [ $crash_count -gt 0 ]; then
        echo -e "   💥 Crash Recovery: ${YELLOW}${BOLD}${crash_count}${RESET} tests"
    fi
    
    if [ ${failed_count} -gt 0 ]; then
        echo -e "\n${RED}${BOLD}❌ Failed Tests (will be retried):${RESET}"
        for i in "${!first_run_failed_tests[@]}"; do
            echo -e "${RED}${BOLD}$((i + 1)). ${first_run_failed_tests[$i]}${RESET}"
        done
        echo -e "\n${YELLOW}${BOLD}🔄 Preparing to retry failed tests...${RESET}"
    else
        echo -e "\n${GREEN}${BOLD}🎉 ALL ISOPE TESTS PASSED ON FIRST RUN!${RESET}"
    fi
    
    if [ $crash_count -gt 0 ]; then
        echo -e "\n💥 Tests with Crash Recovery:${RESET}"
        for i in "${!crash_recovery_tests[@]}"; do
            echo -e "${YELLOW}${BOLD}$((i + 1)). ${crash_recovery_tests[$i]}${RESET}"
        done
    fi
    
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
}

# Function to display final summary
display_final_summary() {
    local total_files=${#original_test_files[@]}
    local final_failed_count=${#final_failed_tests[@]}
    local final_passed_count=$((total_files - final_failed_count))
    local first_run_failed_count=${#first_run_failed_tests[@]}
    
    # Ensure recovered_count is never negative
    local recovered_count=0
    if [ $first_run_failed_count -gt $final_failed_count ]; then
        recovered_count=$((first_run_failed_count - final_failed_count))
    fi
    
    local crash_count=${#crash_recovery_tests[@]}
    
    local success_rate=0
    if [ $total_files -gt 0 ]; then
        success_rate=$((final_passed_count * 100 / total_files))
    fi

    echo -e "\n${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    echo -e "${MAGENTA}${BOLD}🎯 FINAL ISOPE TEST EXECUTION SUMMARY${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    echo -e "${WHITE}📊 ${BOLD}Final Results:${RESET}"
    echo -e "   ✅ Passed:      ${GREEN}${BOLD}${final_passed_count}${RESET} tests"
    echo -e "   ❌ Failed:      ${RED}${BOLD}${final_failed_count}${RESET} tests"
    echo -e "   📈 Total:       ${BLUE}${BOLD}${total_files}${RESET} tests"
    echo -e "   🎯 Success Rate: ${CYAN}${BOLD}${success_rate}%${RESET}"
    
    # Only show recovered count if there were actually recovered tests
    if [ $recovered_count -gt 0 ]; then
        echo -e "   🔄 Recovered:   ${YELLOW}${BOLD}${recovered_count}${RESET} tests"
    fi
    
    if [ $crash_count -gt 0 ]; then
        echo -e "   💥 Crash Recovery: ${YELLOW}${BOLD}${crash_count}${RESET} tests"
    fi
    
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    if [ ${final_failed_count} -gt 0 ]; then
        echo -e "\n${RED}${BOLD}❌ Final Failed Tests (after retry):${RESET}"
        for i in "${!final_failed_tests[@]}"; do
            echo -e "${RED}${BOLD}$((i + 1)). ${final_failed_tests[$i]}${RESET}"
        done
        echo -e "\n${YELLOW}${BOLD}💡 These tests failed both runs - may need investigation${RESET}"
    fi
    
    if [ $recovered_count -gt 0 ]; then
        echo -e "\n${GREEN}${BOLD}🎉 Recovered Tests (failed first run, passed on retry):${RESET}"
        local recovered_tests=()
        for test in "${first_run_failed_tests[@]}"; do
            local found=false
            for final_test in "${final_failed_tests[@]}"; do
                if [ "$test" = "$final_test" ]; then
                    found=true
                    break
                fi
            done
            if [ "$found" = false ]; then
                recovered_tests+=("$test")
            fi
        done
        
        for i in "${!recovered_tests[@]}"; do
            echo -e "${GREEN}${BOLD}$((i + 1)). ${recovered_tests[$i]}${RESET}"
        done
    fi
    
    if [ $crash_count -gt 0 ]; then
        echo -e "\n💥 Tests Recovered from Crash:${RESET}"
        for i in "${!crash_recovery_tests[@]}"; do
            echo -e "${YELLOW}${BOLD}$((i + 1)). ${crash_recovery_tests[$i]}${RESET}"
        done
    fi
    
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    echo -e "${WHITE}🕐 Completed at: ${BOLD}$(date '+%Y-%m-%d %H:%M:%S')${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
}

# Function to check AWS CLI and profile configuration
check_aws_setup() {
    echo -e "${BLUE}🔍 Checking AWS setup...${RESET}"
    
    if ! command -v aws &> /dev/null; then
        echo -e "${RED}❌ AWS CLI not found. Please install AWS CLI first.${RESET}"
        return 1
    fi
    
    if ! aws configure list-profiles | grep -q "$AWS_PROFILE"; then
        echo -e "${YELLOW}⚠️ AWS profile '$AWS_PROFILE' not found.${RESET}"
        return 1
    fi
    
    if ! aws sts get-caller-identity --profile "$AWS_PROFILE" &> /dev/null; then
        echo -e "${RED}❌ AWS credentials verification failed${RESET}"
        return 1
    fi
    
    echo -e "${GREEN}✅ AWS setup verified successfully${RESET}"
    return 0
}

# Function to upload Allure report to S3
upload_report_to_s3() {
    local platform=$1
    local date_folder=$(date +%Y%m%d)
    local os_folder=""
    if [ "$platform" = "ios" ]; then
        os_folder="ios"
    else
        os_folder="android"
    fi
    local s3_path="s3://${S3_BUCKET}/${S3_BASE_PATH}/${date_folder}/${os_folder}/isope/"
    
    echo -e "\n${BLUE}${BOLD}📤 Uploading isOpe test report to S3...${RESET}"
    echo -e "${CYAN}📅 Date folder: ${date_folder}${RESET}"
    echo -e "${CYAN}📱 Platform: ${os_folder}${RESET}"
    echo -e "${CYAN}🔗 S3 destination: ${s3_path}${RESET}"
    
    if [ ! -d "allure-report" ] || [ ! "$(ls -A allure-report)" ]; then
        echo -e "${YELLOW}⚠️ Generating Allure report...${RESET}"
        allure generate allure-results --clean -o allure-report
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ Failed to generate Allure report${RESET}"
            return 1
        fi
    fi
    
    echo -e "${YELLOW}📤 Syncing files to S3...${RESET}"
    aws s3 sync ./allure-report "$s3_path" --profile "$AWS_PROFILE" --delete
    
    if [ $? -eq 0 ]; then
        local report_url="https://${REPORT_DOMAIN}/${S3_BASE_PATH}/${date_folder}/${os_folder}/isope/index.html"
        echo -e "${GREEN}✅ Report uploaded successfully!${RESET}"
        echo -e "${BLUE}🌐 Report URL: ${report_url}${RESET}"
        
        if command -v pbcopy &> /dev/null; then
            echo "$report_url" | pbcopy
            echo -e "${GREEN}📋 URL copied to clipboard${RESET}"
        fi
        
        # Send notification after upload s3 successfully 
        send_s3_success_notification "$platform" "$date_folder" "$report_url"
        return 0
    else
        echo -e "${RED}❌ Failed to upload report to S3${RESET}"
        return 1
    fi
}

# Function to send Slack notification
send_slack_notification() {
    local test_name=$1
    local status=$2
    local error_message=$3
    
    # Check if SLACK_WEBHOOK is set
    if [ -z "$SLACK_WEBHOOK" ]; then
        echo -e "${YELLOW}⚠️ SLACK_WEBHOOK not set - skipping Slack notification${RESET}"
        return 0
    fi
    
    if [ "$status" = "passed" ]; then
        local emoji="✅"
        local color="#36a64f"
    elif [ "$status" = "info" ]; then
        local emoji="ℹ️"
        local color="#0088cc"
    else
        local emoji="❌"
        local color="#dc3545"
    fi
    
    local message=""
    if [ "$test_name" = "KC member site isOpe e2e test" ]; then
        message="$emoji *KC member site isOpe e2e test*"
    else
        message="$emoji Test: **${test_name}**"
    fi
    
    if [ "$status" != "info" ]; then
        message="$message\nStatus: **${status}**"
    fi
    if [ ! -z "$error_message" ]; then
        message="$message\nDetails: \`\`\`$error_message\`\`\`"
    fi
    
    curl -X POST -H 'Content-type: application/json' \
        --data "{
            \"attachments\": [
                {
                    \"color\": \"$color\",
                    \"text\": \"$message\",
                    \"footer\": \"IsOpe Test Runner\",
                    \"ts\": $(date +%s)
                }
            ]
        }" "$SLACK_WEBHOOK"
}

# Send notification to slack after upload s3 successfully
send_s3_success_notification() {
    local platform=$1
    local date_folder=$2
    local report_url=$3
    
    # Check if SLACK_WEBHOOK is set
    if [ -z "$SLACK_WEBHOOK" ]; then
        echo -e "${YELLOW}⚠️ SLACK_WEBHOOK not set - skipping Slack notification${RESET}"
        return 0
    fi
    
    local total_files=${#original_test_files[@]}
    local final_failed_count=${#final_failed_tests[@]}
    local final_passed_count=$((total_files - final_failed_count))
    local first_run_failed_count=${#first_run_failed_tests[@]}
    
    # Ensure recovered_count is never negative
    local recovered_count=0
    if [ $first_run_failed_count -gt $final_failed_count ]; then
        recovered_count=$((first_run_failed_count - final_failed_count))
    fi
    
    local crash_count=${#crash_recovery_tests[@]}
    
    local success_rate=0
    if [ $total_files -gt 0 ]; then
        success_rate=$((final_passed_count * 100 / total_files))
    fi
    
    local platform_upper=$(echo "$platform" | tr '[:lower:]' '[:upper:]')
    
    local message="📊 *IsOpe E2E Test Report Published Successfully*\n"
    message+="═══════════════════════════════════\n"
    message+="🗓️ Date: *${date_folder}*\n"
    message+="📱 Platform: *${platform_upper} (IsOpe Mode)*\n"
    message+="🔧 Test Type: *IsOpe Automation*\n"
    message+="═══════════════════════════════════\n"
    message+="📈 *Final Results:*\n\n"
    message+="✅ Passed: *${final_passed_count}* tests\n\n"
    message+="❌ Failed: *${final_failed_count}* tests\n\n"
    message+="📊 Total: *${total_files}* tests\n\n"
    message+="🎯 Success Rate: *${success_rate}%*\n\n"
    
    # Only show recovered count if there were actually recovered tests
    if [ $recovered_count -gt 0 ]; then
        message+="   🔄 Recovered: *${recovered_count}* tests\n\n"
    fi
    
    if [ $crash_count -gt 0 ]; then
        message+="   💥 Crash Recovery: *${crash_count}* tests\n\n"
    fi
    
    message+="═══════════════════════════════════\n\n"
    message+="🌐 *View Full Report:* ${report_url}\n\n"
    message+="═══════════════════════════════════\n\n"
    
    if [ $final_failed_count -gt 0 ]; then
        message+="\n\n⚠️ *Failed Tests (after retry):*\n"
        for test in "${final_failed_tests[@]}"; do
            message+="   • ${test}\n"
        done
    fi
    
    if [ $recovered_count -gt 0 ]; then
        message+="\n✅ *Recovered Tests:*\n"
        local recovered_tests=()
        for test in "${first_run_failed_tests[@]}"; do
            local found=false
            for final_test in "${final_failed_tests[@]}"; do
                if [ "$test" = "$final_test" ]; then
                    found=true
                    break
                fi
            done
            if [ "$found" = false ]; then
                recovered_tests+=("$test")
            fi
        done
        
        for test in "${recovered_tests[@]}"; do
            message+="   • ${test}\n"
        done
    fi
    
    if [ $crash_count -gt 0 ]; then
        message+="\n💥 *Crash Recovery Applied:*\n"
        for test in "${crash_recovery_tests[@]}"; do
            message+="   • ${test}\n"
        done
    fi
    
    local notification_color="#36a64f"  # Green color like Backlog
    
    echo -e "${GREEN}📤 Sending isOpe notification to Slack with report URL...${RESET}"
    
    curl -X POST -H 'Content-type: application/json' \
        --data "$(cat << EOF
{
    "attachments": [
        {
            "color": "$notification_color",
            "text": "$message",
            "mrkdwn_in": ["text"],
            "footer": "IsOpe E2E Test Runner with Retry & Crash Recovery",
            "ts": $(date +%s)
        }
    ]
}
EOF
)" "$SLACK_WEBHOOK"
    
    local curl_exit_code=$?
    if [ $curl_exit_code -eq 0 ]; then
        echo -e "${GREEN}✅ Slack notification sent successfully${RESET}"
    else
        echo -e "${RED}❌ Failed to send Slack notification (exit code: $curl_exit_code)${RESET}"
    fi
}

# Function to clean up previous test results
cleanup_previous_results() {
    echo -e "\n${YELLOW}${BOLD}🧹 Cleaning up previous isOpe test results...${RESET}"
    
    # Clean allure-results folder
    if [ -d "allure-results" ]; then
        echo -e "${BLUE}🗑️  Removing old allure-results...${RESET}"
        rm -rf allure-results/*
        echo -e "${GREEN}✅ allure-results cleaned${RESET}"
    else
        echo -e "${BLUE}📁 Creating allure-results folder...${RESET}"
        mkdir -p allure-results
    fi
    
    # Clean allure-report folder
    if [ -d "allure-report" ]; then
        echo -e "${BLUE}🗑️  Removing old allure-report...${RESET}"
        rm -rf allure-report/*
        echo -e "${GREEN}✅ allure-report cleaned${RESET}"
    else
        echo -e "${BLUE}📁 Creating allure-report folder...${RESET}"
        mkdir -p allure-report
    fi
    
    # Clean any leftover log files
    if ls test_output_*.log* 1> /dev/null 2>&1; then
        echo -e "${BLUE}🗑️  Removing old log files...${RESET}"
        rm -f test_output_*.log*
        echo -e "${GREEN}✅ Log files cleaned${RESET}"
    fi
    
    echo -e "${GREEN}${BOLD}✅ Previous isOpe test results cleaned successfully${RESET}\n"
}

# Main execution flow
cleanup_previous_results

# Construct the config file path
CONFIG_FILE="./codecept.isope.${TARGET}.conf.ts"
ENV_FILE=".env.isope.${TARGET}"

# Check if the config file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}❌ Config file not found: $CONFIG_FILE${RESET}"
    exit 1
fi

# Check if the env file exists, create if not
if [[ ! -f "$ENV_FILE" ]]; then
    echo -e "${YELLOW}⚠️ Environment file not found: $ENV_FILE. Creating a default one.${RESET}"
    echo "PLATFORM=$TARGET" > "$ENV_FILE"
fi

# Test Slack connection at startup if S3 upload is enabled
if [ "$should_upload_to_s3" = true ]; then
    echo -e "${BLUE}Testing Slack connection...${RESET}"
    send_slack_notification "KC member site isOpe e2e test" "info" "🚀 Starting isOpe test execution on $TARGET platform with retry logic and crash recovery..."
fi

# Get list of test files (find all .ts files in test_isope folder)
original_test_files=($(find test_isope -name "*.ts" -type f | sort))

if [ ${#original_test_files[@]} -eq 0 ]; then
    echo -e "${RED}❌ No test files found in test_isope directory${RESET}"
    exit 1
fi

echo -e "\n${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
echo -e "${MAGENTA}${BOLD}🧪 ISOPE TEST EXECUTION${RESET}"
echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"

echo -e "${CYAN}🎯 Running tests for ${BOLD}$TARGET${RESET}${CYAN} using ${BOLD}$CONFIG_FILE${RESET}"
echo -e "${CYAN}🔧 Environment file: ${BOLD}$ENV_FILE${RESET}"
echo -e "${CYAN}📋 Additional options: ${BOLD}$@${RESET}"

# Display execution order
echo -e "\n${CYAN}${BOLD}Files will be executed in this order:${RESET}"
for file in "${original_test_files[@]}"; do
    echo "$file"
done
echo -e "${CYAN}${BOLD}===========================${RESET}"

# ===== FIRST RUN - ALL TESTS =====
echo -e "\n${MAGENTA}${BOLD}🚀 STARTING FIRST RUN - ALL ISOPE TESTS${RESET}"
echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"

# Perform initial cleanup and setup permissions
echo -e "\n${YELLOW}${BOLD}🔧 Initial setup and permissions...${RESET}"
deep_cleanup "$TARGET"

# Additional permissions setup for Android isOpe mode
if [ "$TARGET" = "android" ]; then
    echo -e "${CYAN}📱 Setting up Android permissions for isOpe mode...${RESET}"
    adb shell pm grant inc.guide.kabuappNext.dev android.permission.CAMERA 2>/dev/null || true
    adb shell pm grant inc.guide.kabuappNext.dev android.permission.ACCESS_FINE_LOCATION 2>/dev/null || true
    adb shell pm grant inc.guide.kabuappNext.dev android.permission.READ_EXTERNAL_STORAGE 2>/dev/null || true
    adb shell pm grant inc.guide.kabuappNext.dev android.permission.WRITE_EXTERNAL_STORAGE 2>/dev/null || true
    echo -e "${GREEN}✅ Android permissions configured for isOpe mode${RESET}"
fi

test_counter=0
for file in "${original_test_files[@]}"; do
    test_counter=$((test_counter + 1))
    
    echo -e "\n${MAGENTA}${BOLD}📊 Test Progress: ${test_counter}/${#original_test_files[@]} - $(basename $file)${RESET}"
    
    # Run the test with crash recovery
    if run_single_test "$file" "$TARGET" "_first_run"; then
        echo -e "\n${GREEN}${BOLD}✅ Test passed for $(basename $file)${RESET}\n"
        rm -f "test_output_$(basename $file .ts)_first_run.log"
        rm -f "test_output_$(basename $file .ts)_first_run_crash_retry_"*.log
    else
        extract_error_details "$file" "first_run_failed_tests" "first_run_error_details" "_first_run"
        echo -e "\n${RED}${BOLD}❌ Test failed for $(basename $file)${RESET}\n"
    fi
    
    sleep 1
done

# Display first run summary
display_first_run_summary

# ===== SECOND RUN - RETRY FAILED TESTS =====
if [ ${#first_run_failed_tests[@]} -gt 0 ]; then
    echo -e "\n${MAGENTA}${BOLD}🔄 STARTING SECOND RUN - RETRY FAILED ISOPE TESTS${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    # Perform deep cleanup before retry
    echo -e "\n${YELLOW}${BOLD}🧹 Deep cleanup before retry...${RESET}"
    deep_cleanup "$TARGET"
    
    # Retry each failed test
    retry_counter=0
    for failed_test in "${first_run_failed_tests[@]}"; do
        retry_counter=$((retry_counter + 1))
        
        # Find the full path of the failed test
        failed_test_path=""
        for original_file in "${original_test_files[@]}"; do
            if [[ "$(basename $original_file)" == "$failed_test" ]]; then
                failed_test_path="$original_file"
                break
            fi
        done
        
        if [ -z "$failed_test_path" ]; then
            echo -e "${RED}❌ Could not find path for failed test: $failed_test${RESET}"
            continue
        fi
        
        echo -e "\n${YELLOW}${BOLD}🔄 Retry ${retry_counter}/${#first_run_failed_tests[@]}: $(basename $failed_test_path)${RESET}"
        
        # Run the retry test with crash recovery
        if run_single_test "$failed_test_path" "$TARGET" "_retry"; then
            echo -e "\n${GREEN}${BOLD}✅ Test RECOVERED on retry: $(basename $failed_test_path)${RESET}\n"
            rm -f "test_output_$(basename $failed_test_path .ts)_retry.log"
            rm -f "test_output_$(basename $failed_test_path .ts)_retry_crash_retry_"*.log
        else
            extract_error_details "$failed_test_path" "final_failed_tests" "final_error_details" "_retry"
            echo -e "\n${RED}${BOLD}❌ Test STILL FAILED on retry: $(basename $failed_test_path)${RESET}\n"
        fi
        
        sleep 1
    done
else
    echo -e "\n${GREEN}${BOLD}🎉 NO RETRY NEEDED - ALL ISOPE TESTS PASSED ON FIRST RUN!${RESET}"
fi

# ===== POST-TEST PROCESSING =====
echo -e "\n${BLUE}${BOLD}🔨 Post-test processing...${RESET}"

# Display final summary
display_final_summary

# Clean up log files
rm -f test_output_*.log*

# Only upload to S3 and send notification if should_upload_to_s3=true
if [ "$should_upload_to_s3" = true ]; then
    echo -e "\n${BLUE}${BOLD}📊 Generating and uploading isOpe report...${RESET}"
    
    if [ -d "allure-results" ] && [ "$(ls -A allure-results)" ]; then
        allure generate allure-results --clean -o allure-report
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Allure report generated successfully${RESET}"
            echo -e "${BLUE}📁 Local report: $(pwd)/allure-report/index.html${RESET}"
            
            if check_aws_setup; then
                echo -e "${BLUE}🚀 Uploading report to S3...${RESET}"
                upload_report_to_s3 "$TARGET"
            else
                echo -e "${YELLOW}⚠️ Skipping S3 upload due to AWS configuration issues${RESET}"
                echo -e "${YELLOW}⚠️ No Slack notification will be sent (S3 upload required)${RESET}"
            fi
        else
            echo -e "${RED}❌ Failed to generate Allure report${RESET}"
            echo -e "${RED}❌ No Slack notification will be sent${RESET}"
        fi
    else
        echo -e "${YELLOW}⚠️ No test results found to generate report${RESET}"
        echo -e "${YELLOW}⚠️ No Slack notification will be sent${RESET}"
    fi
else
    echo -e "${BLUE}💡 Individual test execution - skipping S3 upload and Slack notification${RESET}"
fi

# Exit with final error count
final_exit_code=${#final_failed_tests[@]}

# Display final completion message
if [ $final_exit_code -eq 0 ]; then
    echo -e "\n${GREEN}${BOLD}✅ All isOpe tests completed successfully!${RESET}\n"
    echo -e "${BLUE}🚀 Performance optimizations, retry logic, and crash recovery helped achieve 100% success rate${RESET}"
else
    echo -e "\n${RED}${BOLD}❌ ${final_exit_code} isOpe test(s) failed after retry (exit code: $final_exit_code)${RESET}\n"
    echo -e "${YELLOW}💡 These tests may need manual investigation${RESET}\n"
fi

if [ ${#crash_recovery_tests[@]} -gt 0 ]; then
    echo -e "${YELLOW}💥 ${#crash_recovery_tests[@]} test(s) recovered from crashes${RESET}"
fi

echo -e "${BLUE}${BOLD}===========================================${RESET}"
echo -e "${BLUE}${BOLD}Enhanced IsOpe Tests with Crash Recovery${RESET}"
echo -e "${BLUE}${BOLD}            Complete !!!            ${RESET}"
echo -e "${BLUE}${BOLD}===========================================${RESET}"

exit $final_exit_code