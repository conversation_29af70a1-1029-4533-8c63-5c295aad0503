#!/bin/bash

# Function to create .env file from .env.example if it doesn't exist
create_env_file() {
  local target_file=$1
  local example_file=$2

  if [ ! -f "$target_file" ]; then
    if [ -f "$example_file" ]; then
      cp "$example_file" "$target_file"
      echo "✅ Created $target_file from $example_file"
    else
      echo "⚠️  Skipped: $example_file not found"
    fi
  else
    echo "ℹ️  $target_file already exists. Skipping."
  fi
}

echo "🔧 Setting up environment files..."

create_env_file ".env" ".env.example"
create_env_file ".env.ios" ".env.ios.example"
create_env_file ".env.android" ".env.android.example"
create_env_file ".env.isope.ios" ".env.isope.ios.example"
create_env_file ".env.isope.android" ".env.isope.android.example"
create_env_file ".env.isope" ".env.isope.example"
create_env_file ".env.rtk.ios" ".env.rtk.ios.example"
create_env_file ".env.rtk.android" ".env.rtk.android.example"

echo "✅ Environment setup complete."
