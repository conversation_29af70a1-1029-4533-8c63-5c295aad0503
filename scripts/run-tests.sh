#!/bin/bash

# --------------------------------------
# Test runner for CodeceptJS
# Supports: ios, android
# Accepts additional options: --steps, --dry-run, --grep, etc.
# Usage: ./run-tests.sh ios --steps --grep "@smoke"
# Result command: npx codeceptjs run $file --steps --grep "@smoke" --config codecept.ios.conf.ts
# --------------------------------------

# Read the first argument as the target environment
TARGET="$1"
shift # Remove $1 from the arguments so $@ contains only options

# Exit if no target is specified
if [[ -z "$TARGET" ]]; then
  echo "❌ Missing test environment (ios, android)"
  echo "👉 Usage: ./run-tests.sh ios"
  exit 1
fi

# Construct the config file path
CONFIG_FILE="./codecept.${TARGET}.conf.ts"
ENV_FILE=".env.${TARGET}"

# Check if the config file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
  echo "❌ Config file not found: $CONFIG_FILE"
  exit 1
fi

# Check if the env file exists, create if not
if [[ ! -f "$ENV_FILE" ]]; then
  echo "⚠️ Environment file not found: $ENV_FILE. Creating a default one."
  echo "PLATFORM=$TARGET" > "$ENV_FILE"
fi

echo ""
echo "Running tests for $TARGET using $CONFIG_FILE and $ENV_FILE"
echo "Additional options: $@"
echo ""

# Optional: uncomment the following to list test files in sorted order
# echo "🔍 Running tests in the following order:"
# ls tests/*.ts | sort -V 
# echo ""

# Run all test files in ./tests folder in sorted order
for file in $(ls tests/*.ts | sort -V); do
  echo "🧪 Running test file: $file"
  echo ""
  npx codeceptjs run "$file" "$@" --config "$CONFIG_FILE"
done
